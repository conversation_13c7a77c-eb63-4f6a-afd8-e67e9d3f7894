name: Create and Test a Go application

on:
  workflow_call:
    inputs:
      name:
        required: true
        type: string
        # Is this a QA or a PROD build?
      environment:
          required: true
          type: string
        # Where is the context directory?
      context_dir:
          required: false
          type: string
          default: src

jobs:
  setup-env:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout the source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Go environment
        uses: actions/setup-go@v3
        with:
          go-version: '1.21.1'

      - name: Build container locally
        uses: docker/build-push-action@v3
        with:
          push: false
          context: ${{inputs.context_dir}}
          file: ${{inputs.context_dir}}/${{inputs.name}}/Dockerfile
          tags: ${{inputs.name}}:latest
          labels: ${{inputs.environment}}

      - name: Run the build process with <PERSON><PERSON>
        uses: addnab/docker-run-action@v3
        with:
          image: ${{inputs.name}}:latest
          options: -v ${{ github.workspace }}:/var/www
          run: |
            go test ./...

