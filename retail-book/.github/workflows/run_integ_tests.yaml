name: Run the integration tests

on:
  - push
  - workflow_dispatch

jobs:
  run-tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout the source code
        uses: actions/checkout@v4

      - name: Setup Go environment
        uses: actions/setup-go@v4
        with:
          go-version: '1.24.2'

      - name: Download codegen dependencies
        working-directory: ./src/generator
        run: go mod download

      - name: Codegen
        working-directory: ./src/generator
        run: go generate ./...

      - name: Test project
        working-directory: ./src/integrationtester/cmd
        run: go test -timeout 1200s -run ^TestAllTests$