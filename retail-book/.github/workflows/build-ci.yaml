name: Build (CI)
on:
  pull_request:
    branches:
      - main
      - release/**
    types:
      - opened
      - reopened
      - synchronize
  pull_request_review:
    branches:
      - main
    types:
      - edited
      - dismissed

jobs:
  build-apigateway:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: apigateway
      test_only: true

  build-common:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: common
      test_only: true

  build-cache:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: cache
      test_only: true

  build-commandhandler:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: commandhandler
      test_only: true
      
  build-centraldata:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: centraldata
      test_only: true
  
  build-documentservice:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: documentservice
      test_only: true
 
  build-eventauditor:
    uses: ./.github/workflows/build-test-go.yaml
    with:
       package_name: eventauditor
       test_only: true
  
  build-eventprocessor:
    uses: ./.github/workflows/build-test-go.yaml
    with:
       package_name: eventprocessor
       test_only: true
  
  build-generator:
    uses: ./.github/workflows/build-test-go.yaml
    with:
       package_name: generator
       test_only: true

  build-viewserver:
    uses: ./.github/workflows/build-test-go.yaml
    with:
       package_name: viewserver
       test_only: true

  build-interappgateway:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: interappgateway
      test_only: true

  build-notificationservice:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: notification
      test_only: true

  build-integrationtester:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: integrationtester
      test_only: true

  build-integrationtester-ci:
    uses: ./.github/workflows/build-container-test-go.yaml
    with:
      context_dir: src
      name: integrationtester
      environment: ci

  build-statesyncer:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: statesyncer
      test_only: true

  build-admin:
    uses: ./.github/workflows/build-test-go.yaml
    with:
      package_name: admin
      test_only: true
