name: All Environments - Continous Delivery Pipeline
on:
    release:
        types: [released]

concurrency:
    group: ALL
    cancel-in-progress: false

jobs: 
    deliver-staging:
        name: Staging delivery
        uses: ./.github/workflows/apps-build-push.yaml
        with:
            target_environment: staging
            tag_name: ${{ github.event.release.tag_name }}
            release_name: ${{ github.event.release.release_name != '' && github.event.release.release_nam || github.event.release.tag_name }}
    deliver-preprod:
        name: Preprod delivery
        uses: ./.github/workflows/apps-build-push.yaml
        with:
            target_environment: preprod
            tag_name: ${{ github.event.release.tag_name }}
            release_name: ${{ github.event.release.release_name != '' && github.event.release.release_nam || github.event.release.tag_name }}
    deliver-prod:
        name: Prod delivery
        uses: ./.github/workflows/apps-build-push.yaml
        with:
            target_environment: prod
            tag_name: ${{ github.event.release.tag_name }}
            release_name: ${{ github.event.release.release_name != '' && github.event.release.release_nam || github.event.release.tag_name }}
