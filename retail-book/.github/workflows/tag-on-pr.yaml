name: Tag the repository (CD)
on:
  pull_request:
    types: [ closed ]

jobs:
  build:
    environment: staging
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged

    steps:
      -   name: Checkout source code
          uses: actions/checkout@v3
          with:
            fetch-depth: 0

      -   name: SemVer Tag
          id: semver_tagging
          uses: paulhatch/semantic-version@v5.0.2
          with:
            # The prefix to use to identify tags
            tag_prefix: "v"
            # A string which, if present in a git commit, indicates that a change represents a
            # major (breaking) change, supports regular expressions wrapped with '/'
            major_pattern: "Major Release: "
            # Same as above except indicating a minor change, supports regular expressions wrapped with '/'
            minor_pattern: "Minor Release: "
            # A string to determine the format of the version output
            version_format: "${major}.${minor}.${patch}-prerelease${increment}"
            # If this is set to true, *every* commit will be treated as a new version.
            bump_each_commit: false
            # If true, the body of commits will also be searched for major/minor patterns to determine the version type.
            search_commit_body: false