name: Create and Test a Go application

on:
  workflow_call:
    inputs:
      package_name:
        required: true
        type: string
      test_only:
        required: false
        type: boolean

env:
  GEN_PKG: ${{ inputs.package_name }}

jobs:
  setup-env:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout the source code
        uses: actions/checkout@v3

      - name: Setup Go environment
        uses: actions/setup-go@v3
        with:
          go-version: '1.21.1'

      - name: Download codegen dependencies
        working-directory: ./src/generator
        run: go mod download

      - name: Codegen
        working-directory: ./src/generator
        run: go generate ./...

      - name: Download dependencies
        working-directory: ./src/${{ inputs.package_name }}
        run: go mod download

      - if: ${{ !inputs.test_only }}
        name: Build project
        working-directory: ./src/${{ inputs.package_name }}
        run: go build -v -o ${{inputs.package_name}} cmd/main.go

      - name: Test project
        working-directory: ./src/${{ inputs.package_name }}
        run: go test -short ./...