name: Environment - All apps - Continous Delivery Pipeline
run-name: ${{ inputs.target_environment }} - All apps - Continous Delivery Pipeline

on:
    workflow_call:
        inputs:
            # The target environment to build
            target_environment:
                required: true
                description: The environment to deliver to
                type: string
            tag_name:
                required: true
                description: The tag to deliver to the environment (prepended with 'v')
                type: string
            release_name:
                required: false
                description: Optional release name used as an image label (defaults to the tag name)
                type: string

concurrency:
    group: ${{ inputs.target_environment }}
    cancel-in-progress: false

jobs:
    build-apisite:
        name: API Site
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: api
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-apigateway:
        name: API Gateway
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: apigateway
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-commandhandler:
        name: Command Handler
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: commandhandler
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-viewserver:
        name: View Server
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: viewserver
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-interappgateway:
        name: Inter-app Gateway
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: interappgateway
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-centraldata:
        name: Central Data Image
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: centraldata
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-documentservice:
        name: Document Service
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: documentservice
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-userservice:
        name: User Service
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: userservice
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-eventauditor:
        name: Event Auditor
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: eventauditor
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-eventprocessor:
        name: Event Processor
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: eventprocessor
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-statesyncer:
        name: State Syncer
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: statesyncer
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
    build-notificationservice:
        name: Notification Service
        uses: ./.github/workflows/app-build-push.yaml
        with:
            name: notification
            environment: ${{ inputs.target_environment }}
            tag: ${{ inputs.tag_name }} ## old: github.event.release.tag_name
            labels: ${{ inputs.release_name != '' && inputs.release_name || inputs.tag_name }} ## old: ${{ github.event.release.release_name }}
        secrets: inherit
