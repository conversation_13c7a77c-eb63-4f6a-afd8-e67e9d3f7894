name: Environment - Single app - Continous Delivery Pipeline
run-name: ${{ inputs.target_environment }} - ${{ inputs.name }} - Continous Delivery Pipeline

on:
    workflow_call:
        inputs:
            # The environment to build the application to
            environment:
                required: true
                type: string
            # Name of the image / module to build.
            name:
                required: true
                type: string
            # Does the build require a tag to build (true for prod, false for qa - most of the time).
            must_have_tag:
                required: false
                type: boolean
                default: true
            # What are the tags? Supply if must_have_tag is true.
            tag:
                required: false
                type: string
                default: latest
            # Image labels.
            labels:
                required: true
                type: string
                default: dev
            # Where is the context directory?
            context_dir:
                required: false
                type: string
                default: src
            # NPM build?
            npm_build:
                required: false
                type: boolean
                default: false

permissions:
    id-token: write
    contents: read

jobs:
    build:
        environment: ${{ inputs.environment }}
        runs-on: ubuntu-latest

        steps:
            -   name: Checkout source code
                uses: actions/checkout@v3

            -   name: Log in to Azure using OIDC
                uses: azure/login@v1
                with:
                    client-id: ${{ vars.CLIENT_ID }}
                    tenant-id: ${{ vars.TENANT_ID }}
                    subscription-id: ${{ vars.SUBSCRIPTION_ID }}

            -   name: Login to Azure ACR
                run: az acr login -n ${{ vars.REGISTRY_NAME }}

            # Labels the image with an environment - and tags it as latest.
            -   name: Build and push image - latest
                if: ${{ !inputs.npm_build }}
                uses: docker/build-push-action@v3
                with:
                    push: true
                    context: ${{ inputs.context_dir }}
                    file: ${{ inputs.context_dir }}/${{ inputs.name }}/Dockerfile
                    tags: ${{ vars.REGISTRY_NAME }}.azurecr.io/${{ inputs.name }}:latest
                    labels: ${{ inputs.labels }}

            # Labels the image with an environment - and tags it as the tag of the HEAD.
            -   name: Build and push image
                if: ${{ inputs.must_have_tag && !inputs.npm_build }}
                uses: docker/build-push-action@v3
                with:
                    push: true
                    context: ${{ inputs.context_dir }}
                    file: ${{ inputs.context_dir }}/${{ inputs.name }}/Dockerfile
                    tags: ${{ vars.REGISTRY_NAME }}.azurecr.io/${{ inputs.name }}:${{ inputs.tag }}
                    labels: ${{ inputs.labels }}
                    build-args: |
                        SEMANTIC_VERSION=${{ inputs.tag }}

            # NPM Build
            -   name: Build (npm) and push image (latest)
                if: ${{ inputs.npm_build }}
                uses: docker/build-push-action@v3
                with:
                    push: true
                    context: ${{ inputs.context_dir}}/${{inputs.name}}
                    file: ${{inputs.context_dir}}/${{inputs.name}}/Dockerfile
                    tags: ${{ vars.REGISTRY_NAME }}.azurecr.io/${{inputs.name}}:latest
                    labels: ${{ inputs.labels }}

            -   name: Build (npm) and push image (latest)
                if: ${{ inputs.must_have_tag && inputs.npm_build }}
                uses: docker/build-push-action@v3
                with:
                    push: true
                    context: ${{ inputs.context_dir}}/${{inputs.name}}
                    file: ${{inputs.context_dir}}/${{inputs.name}}/Dockerfile
                    tags: ${{ vars.REGISTRY_NAME }}.azurecr.io/${{inputs.name}}:${{ inputs.tag }}
                    labels: ${{ inputs.labels }}
