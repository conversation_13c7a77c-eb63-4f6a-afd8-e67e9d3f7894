name: Push a helm chart to ACR

on:
  workflow_dispatch:
    inputs:
      # The URL of the registry
      acr_registry_url:
        required: false
        description: Specify the login server address (see ACR summary page)
        type: string
        default: rbcontainerrepo.azurecr.io
      # The charts to build
      chart:
        required: true
        description: Select the chart to push
        type: choice
        options:
          - participant
          - haproxy
          - global
      # Environment to build
      environment:
        required: false
        description: Environment to build
        type: choice
        options:
          - prod
          - dev
      ref:
        required: false
        description: The branch, tag or SHA to checkout and push. Ensure the Chart.yaml file has a correct version on the branch
        type: string
        default: main

  workflow_call:
    inputs:
      # The URL of the registry
      acr_registry_url:
        required: false
        description: Specify the login server address (see ACR summary page)
        type: string
        default: rbcontainerrepo.azurecr.io
      chart:
        required: true
        description: Supply the chart to push
        type: string
      environment:
        required: false
        description: Environment to build
        type: string

env:
  HELM_EXPERIMENTAL_OCI: 1
  HELM_INSTALL_VERSION: 3.9.0

jobs:
  push-helm:
    name: Publish helm to ACR
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-charts

    steps:
      - uses: actions/checkout@v3
        name: checkout repo
        with:
          ref: ${{ inputs.ref }}

      - uses: Azure/setup-helm@v1
        name: helm install
        with:
          version: ${{ env.HELM_INSTALL_VERSION }}

      - uses: azure/docker-login@v1
        name: login to container registry
        with:
          login-server: ${{ inputs.acr_registry_url }}
          username: ${{ secrets.DEV_REGISTRY_USERNAME }}
          password: ${{ secrets.DEV_REGISTRY_PASSWORD }}

      - name: package helm chart
        run: helm package --dependency-update .
        working-directory: ./build/helm/${{ inputs.chart }}

      - name: push chart
        run: |
          cd ./build/helm/${{ inputs.chart }}
          PKG_NAME=`ls *.tgz`
          echo "Pushing " $PKG_NAME
          helm push $PKG_NAME oci://${{ inputs.acr_registry_url }}/helm