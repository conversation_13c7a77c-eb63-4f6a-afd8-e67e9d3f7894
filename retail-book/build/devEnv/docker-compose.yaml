version: "3.9"

services:
  # Ingress
  haproxy:
    image: "rbhaproxy:latest"
    restart: always
    ports:
      - "1000:8080"

  # RB Instances
  global-nats:
    image: "nats:latest"
    restart: always
    ports:
      - "8222:4222"
    command: "-js"

  global-centralData:
    image: "centraldata:latest"
    restart: always
    environment:
      - APP_NATS_HOST=global-nats
      - APP_NATS_PORT=4222
      - APP_ASSETS_DIR=/assets

  global-documentservice:
    image: "documentservice:latest"
    restart: always
    ports:
      - "8333:80"
    environment:
      - APP_NATS_HOST=global-nats
      - APP_NATS_PORT=4222
      - APP_PORT=80
      - AZURE_SUBSCRIPTION_ID=19d7c185-1ee1-4e9f-8466-1d8e0f34451a
      - AZURE_RESOURCE_GROUP=RG-Development
      - AZURE_STORAGE_ACCOUNT=testdocumentstorage
      - AZURE_CLIENT_ID=9c518ba9-802d-412d-a64a-163f9d87331b
      - AZURE_CLIENT_SECRET=****************************************
      - AZURE_TENANT_ID=6add0a26-d7dd-4271-8acf-b52c16ad9ea4

  global-userservice:
    image: "userservice:latest"
    restart: always
    environment:
      - APP_NATS_HOST=global-nats
      - APP_NATS_PORT=4222
      - AZURE_CLIENT_ID=0b119333-a9da-4046-baa1-be7cc0d50039
      - AZURE_CLIENT_SECRET=****************************************
      - AZURE_TENANT_ID=95e9d45b-cc24-4b1e-8d08-29b961ee092d

  global-notificationservice:
    image: "notification:latest"
    restart: always
    environment:
      - APP_EX_NATS_HOST=global-nats
      - APP_NATS_PORT=4222
      - APP_PORT=80
      - SENDGRID_API_KEY=*********************************************************************

  # Bank Instances
  bank-couchdb:
    image: "rbcouchdb:latest"
    ports:
      - "5984:5984"
    environment:
      - COUCHDB_USER=admin
      - COUCHDB_PASSWORD=admin

  bank-nats:
    image: "nats:latest"
    restart: always
    ports:
      - "4222:4222"
    command: "-js"

  bank-commandhandler:
    image: "commandhandler:latest"

    depends_on:
      - bank-couchdb
      - bank-nats
    restart: always
    environment:
      - APP_SYSTEM_ID=bank
      - APP_PORT=1001
      - APP_COUCHDB_HOST=bank-couchdb
      - APP_COUCHDB_PORT=5984
      - APP_NATS_HOST=bank-nats
      - APP_NATS_PORT=4222
      - APP_EX_NATS_HOST=global-nats
      - APP_EX_NATS_PORT=4222
      - APP_ASSETS_DIR=/assets

  bank-viewserver:
    image: "viewserver:latest"

    depends_on:
      - bank-couchdb
      - bank-nats
    restart: always
    environment:
      - APP_PORT=1002
      - APP_COUCHDB_HOST=bank-couchdb
      - APP_COUCHDB_PORT=5984
      - APP_NATS_HOST=bank-nats
      - APP_NATS_PORT=4222
      - APP_EX_NATS_HOST=global-nats
      - APP_EX_NATS_PORT=4222
      - APP_ASSETS_DIR=/assets
      - APP_SYSTEM_ROLE=bank

  apigateway.bank:
    image: "apigateway:latest"
    depends_on:
      - "bank-commandhandler"
      - "bank-viewserver"
      - "bank-couchdb"
      - "bank-nats"
    restart: always

    environment:
      - APP_SYSTEM_ID=bank
      - APP_PORT=8080
      - APP_COUCHDB_HOST=bank-couchdb
      - APP_COUCHDB_PORT=5984
      - APP_NATS_HOST=bank-nats
      - APP_NATS_PORT=4222
      - APP_EX_NATS_HOST=global-nats
      - APP_EX_NATS_PORT=4222
      - APP_ASSETS_DIR=/assets
      - APP_CONFIG_DIR=/config
      - AZURE_SUBSCRIPTION_ID=19d7c185-1ee1-4e9f-8466-1d8e0f34451a
      - AZURE_RESOURCE_GROUP=RG-Development
      - AZURE_STORAGE_ACCOUNT=testdocumentstorage
      - AZURE_CLIENT_ID=bebc792a-568d-4224-bb6d-0428a16735d6
      - AZURE_CLIENT_SECRET=****************************************
      - AZURE_TENANT_ID=6add0a26-d7dd-4271-8acf-b52c16ad9ea4
      - AZURE_STORAGE_AUTH_MODE=login

  bank-interappgateway:
    image: "interappgateway:latest"

    depends_on:
      - bank-couchdb
      - bank-nats
    restart: always
    environment:
      - APP_NAME=testclient
      - APP_PORT=1003
      - APP_COUCHDB_HOST=bank-couchdb
      - APP_COUCHDB_PORT=5984
      - APP_NATS_HOST=bank-nats
      - APP_NATS_PORT=4222
      - APP_EX_NATS_HOST=global-nats
      - APP_EX_NATS_PORT=4222

  bank-eventauditor:
    image: "eventauditor:latest"

    depends_on:
      - bank-couchdb
      - bank-nats
    restart: always
    environment:
      - APP_PORT=1004
      - APP_NATS_HOST=bank-nats
      - APP_NATS_PORT=4222
      - APP_COUCHDB_HOST=bank-couchdb
      - APP_COUCHDB_PORT=5984

  bank-eventprocessor:
    image: "eventprocessor:latest"

    depends_on:
      - bank-nats
    restart: always
    environment:
      - APP_SYSTEM_ID=bank
      - APP_PORT=1005
      - APP_NATS_HOST=bank-nats
      - APP_NATS_PORT=4222
      - APP_COUCHDB_HOST=bank-couchdb
      - APP_COUCHDB_PORT=5984
      - APP_EX_NATS_HOST=global-nats
      - APP_EX_NATS_PORT=4222

  bank-statesyncer:
    image: "statesyncer:latest"

    depends_on:
      - bank-couchdb
    restart: always
    environment:
      - APP_PORT=1006
      - APP_COUCHDB_HOST=bank-couchdb
      - APP_COUCHDB_PORT=5984

  # Retail

  retail-couchdb:
    image: "rbcouchdb:latest"
    ports:
      - "6984:5984"
    environment:
      - COUCHDB_USER=admin
      - COUCHDB_PASSWORD=admin

  retail-nats:
      image: "nats:latest"
      restart: always
      ports:
        - "5222:4222"
      command: "-js"

  retail-commandhandler:
      image: "commandhandler:latest"

      depends_on:
        - retail-couchdb
        - retail-nats
      restart: always
      environment:
        - APP_SYSTEM_ID=retail
        - APP_PORT=2001
        - APP_COUCHDB_HOST=retail-couchdb
        - APP_COUCHDB_PORT=5984
        - APP_NATS_HOST=retail-nats
        - APP_NATS_PORT=4222
        - APP_EX_NATS_HOST=global-nats
        - APP_EX_NATS_PORT=4222
        - APP_ASSETS_DIR=/assets

  retail-viewserver:
      image: "viewserver:latest"

      depends_on:
        - retail-couchdb
        - retail-nats
      restart: always
      environment:
        - APP_PORT=2002
        - APP_COUCHDB_HOST=retail-couchdb
        - APP_COUCHDB_PORT=5984
        - APP_NATS_HOST=retail-nats
        - APP_NATS_PORT=4222
        - APP_ASSETS_DIR=/assets
        - APP_EX_NATS_HOST=global-nats
        - APP_EX_NATS_PORT=4222
        - APP_SYSTEM_ROLE=intermediary

  apigateway.retail:
      image: "apigateway:latest"
      depends_on:
        - "retail-commandhandler"
        - "retail-viewserver"
        - "retail-couchdb"
        - "retail-nats"
      restart: always

      environment:
        - APP_SYSTEM_ID=retail
        - APP_PORT=8080
        - APP_COUCHDB_HOST=retail-couchdb
        - APP_COUCHDB_PORT=5984
        - APP_NATS_HOST=retail-nats
        - APP_NATS_PORT=4222
        - APP_EX_NATS_HOST=global-nats
        - APP_EX_NATS_PORT=4222
        - APP_ASSETS_DIR=/assets
        - APP_CONFIG_DIR=/config
        - AZURE_SUBSCRIPTION_ID=19d7c185-1ee1-4e9f-8466-1d8e0f34451a
        - AZURE_RESOURCE_GROUP=RG-Development
        - AZURE_STORAGE_ACCOUNT=testdocumentstorage
        - AZURE_CLIENT_ID=1f4d6d77-404d-42cc-b90e-35507af0e68d
        - AZURE_CLIENT_SECRET=****************************************
        - AZURE_TENANT_ID=6add0a26-d7dd-4271-8acf-b52c16ad9ea4
        - AZURE_STORAGE_AUTH_MODE=login

  retail-interappgateway:
      image: "interappgateway:latest"

      depends_on:
        - retail-couchdb
        - retail-nats
      restart: always
      environment:
        - APP_NAME=retail
        - APP_PORT=2003
        - APP_NATS_HOST=retail-nats
        - APP_NATS_PORT=4222
        - APP_EX_NATS_HOST=global-nats
        - APP_EX_NATS_PORT=4222
        - APP_COUCHDB_HOST=retail-couchdb
        - APP_COUCHDB_PORT=5984

  retail-eventauditor:
      image: "eventauditor:latest"

      depends_on:
        - retail-couchdb
        - retail-nats
      restart: always
      environment:
        - APP_PORT=2004
        - APP_NATS_HOST=retail-nats
        - APP_NATS_PORT=4222
        - APP_COUCHDB_HOST=retail-couchdb
        - APP_COUCHDB_PORT=5984

  retail-eventprocessor:
    image: "eventprocessor:latest"

    depends_on:
      - retail-couchdb
      - retail-nats
    restart: always
    environment:
      - APP_SYSTEM_ID=retail
      - APP_PORT=2005
      - APP_NATS_HOST=retail-nats
      - APP_NATS_PORT=4222
      - APP_COUCHDB_HOST=retail-couchdb
      - APP_COUCHDB_PORT=5984
      - APP_EX_NATS_HOST=global-nats
      - APP_EX_NATS_PORT=4222

  retail-statesyncer:
    image: "statesyncer:latest"

    depends_on:
      - retail-couchdb
    restart: always
    environment:
      - APP_PORT=2006
      - APP_COUCHDB_HOST=retail-couchdb
      - APP_COUCHDB_PORT=5984
