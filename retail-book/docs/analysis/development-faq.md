---
layout: page
title: Development FAQ
parent: Analysis
nav_order: 1
has_children: false
permalink: /analysis/development-faq
---
# General

* **Q: What units is the issue price in?  Deal currency, % of par?**
* A: strike price is not recorded anywhere?

# Business Units

* **Q: REX Business units - do we need them in RetailBook**
* A: Retail intermerdiary user selects business unit at order entry time (presumably a single user can collect orders from multiple units)
* Business unit has a 'receive commission flag' - this is needed if the business unit is acting as a wealth manager because if they do then they're 
    charging for their services, and can not receive commission (FCA rule) vs execution only way elect to receive commission
* In REX 1.0 business unit doesn't really do anything it appears on all the downloads, perhaps it's settlement related.  
* Quirks of business units:
   * **Q: There appears to be a PH_BOOK counterparty that has a business unit with a lot of different clients in it.  Does this allow the entry of orders on behalf of?**
   * A: This is a quirk for a couple of retail bonds where we wanted to add all the lines of the deal.   Not a required feature.
   * **Q: Most business units appear for comm/non-comm, is this right?**
   * A: This is feature of order entry and not necessarily required
   * **Q: There are a few business units that appear regional (seems legit)**
   * A: Other than Peel Hunt there is one business with a special region (Cannnacord).  
* **Q: Is Counterparty and email domain checking?**
* A: No reason not to add it.  This will need a carve-out if retailbook is required
* **Q: Should there be support for different currencies?**
* A: Yes, it should be a selectable deal attribute but to start with the only selectable option should be GBP!

# Application Bands

The entry of aggregated orders and special rows for large orders to give an indication of the demand.   The bands whilst indicative of the actual demand are still binding amounts.


* **Q: Do we need application bands?**
* A: Probably.  For ABBs they are cumbersome to complete given the short timescales.  The bands allow for semi-accurate calculation of allocations according to soft-premption rules.   Something that is likely to become more important as more attention is focused on rights for retail.  Hargreaves are willing to provide per -client break downs after the books close.   If all intermediaries do this it would be ideal.   However two questions remain:
   * **IPOs** - longer timescale bandings at all times pre-close, order detail post-close.  Some intermediaries refuse to enter in bandings.
   * **ABBs (no prospectus)** - short timescale not enough time to manage bands, order detail post-close.  It's anticipated that some intermediaries won't be able (or refuse to support order detail post close.)
* **Q: What to do if an intermediary won't/can't agree to providing break downs.**
* **Q: Single large orders were a specific reqiurement which might not be needed**
* **Q: What do the # applications of Orders represent?**
* A: For single application bands, the number of non-zero order for business units
* A: For multi bands the intermediary can enter the number of orders in a band
* **Q: Exactly how will we support multi-order?**
* A: There are two types of order entry: aggregated vs unaggregated
* A: Unaggregated will require a client id
* A: In each case the intermediary must provide the complete breakdown of orders on every upload
* **Q: As an intermediary how to I clear my order?**
* A: Upload a zero order or an empty list of unaggregated orders  
* **Q: What constraints should be placed on newly entered orders?**
* A: Order entry, minimum and maximum order amounts should be respected

# Follow-ons with and without existing shareholders
* **Q: What is the difference between shareholder application vs non-shareholder applications (only applies to equity-follow on)?**
* A: Route 1 - existing shareholders only 
* Route 2 - existing and new shareholders (requires financial promotions work)
* **Q: If we know that the deal can only be marketed to existing shareholders (i.e. there are no promotions and it's below the cap) then surely you can't enter an application for non-shareholder shares?**
* A: This is correct, 
* **Q: Does it affect allocations?**
* A: Yes, as an existing investor you're entitled to an equal proportion of the new issuance as your original investment (albeit rounded down)
* **Q: Activation should ideally tell you what you're missing (in terms of the required documents)?**
   * Allocator is it required or not, in the absence of the allocator what does SA currently do?
        * Allocator never really used
        * Will need a method for viewing 
   * Customised document generation for intermediaries is required
      * Companies need company logos, registered addresses, bank details that can be inserted into the docs

# Settlement
* **Q: How? When?!**

# Off-boarding
* **Q: How to off-board clients and preserve any data**

# Identifiers
* **Q: What is the lifecycle for identifiers of objects that need to be unique across participants?**
* A: Participants, each party needs a unique participant code (this will be their DNS name) across the network.  Assigned at birth, probably can not be reassigned at death.
* A: Need a unique deal identity across all deals (which could be a GUID) that can be archived.
