---
layout: page
title: Wall Crossing - Life cycle
parent: Wall Crossing
nav_order: 1
has_children: false
permalink: /analysis/wall-crossing/life-cycle
---

# Wall-crossing life-cycle for participants

## 1. Create a wall-crossing (on an offer)

* A wall-crossing is created in either the **Building** or **Pre-Launch** state of an offer
* RB Team create a new offer bank-side with approximate details and add the following to the wall-crossing object:
    * **Wall-crossing consent** script which gives the non-specific information required for gatekeepers.  Note that many gate-keepers at the same firm may be sent the consent form, but some or all may decline.  Those that decline should **not** see the offer.
    * **Offer outline** a specific outline of the offer including information that would be classed inside information and for restricted distribution.
    * **Invite list** a list of RB participants that can be invited to an offer.  Note that all gatekeepers at all intermediaries will be consulted as to whether they will consent to be inside.  

* The system creates necessary wall-crossing objects in the DB but also stores the document in a separate container which can be permissioned separately from the other offer documents
* An offer outline can be amended at any time during the wall-crossing life-cycle. When this happens parties that have consented to be wall-crossed should be notified of a change to the offer outline.  Therefore all wall-crossed parties should see the latest offer outline (not the version they first saw (as with the **Retail Offer Notice**)

![create-wallcrossing](../../assets/create-wallcrossing.png)

## 2. Wall-crossing processing

The notification will need to be idempotent therefore:

* Can not be triggered if there are no intermediaries to notify
* Checks to see which intermediaries in the list have not previously been notified
* Sends a bank-side system-message with details about the wall-crossing (including a bank-side correlation id of some sort)

The intermediary team does not have access to the offer yet just the wall-crossing.

* Intermediary system receives new wall-crossing event
* Intermediary system sends out a notification with no information in it other than to say a new wall-crossing invitation was received for each of the gate-keepers
* Gate-keepers log on and are directed to an area of the system which shows all wall-crossing invitations for all offers sorted by time (descending)
* Gate-keeper has accept / decline buttons for each offer.  
    * Decline:
        * Sends a message to the bank to say the offer is declined.  
        * If the offer is either not seen yet or not open they can still change their minds and accept though.
    * Accept:
        * Sends a message to the bank
        * Disables the decline option (in the UI and the API).
        * Adds permission for the gate-keeper to access the document storage for the offer outline

If a gate-keeper chooses to accept an offer after the offer has been received (and before it is public information) then as well as getting access to the document storage for the outline they also get access to the offer at the point of acceptance.

As per-normal the gate-keeper has the ability to give others access to the offer as and when they see fit (but this is done outside of the wall-crossing process and is part of the intermediaries process)

![invite-wallcrossing](../../assets/invite-wallcrossing.png)

## 4. Bank puts offer into pre-launch

* Any intermediaries with a person who has been wall-crossed are automatically added to the list of intermediaries by default
* The intermediaries can then be notified

* Intermediary system creates the offer as normal but:

   * Locates the wall-crossing object that was sent prior
   * Identifies all the users that consented and gives them access

![prelaunch-wallcrossing](../../assets/prelaunch-wallcrossing.png)

## 6. Offer is made public

* Bank
    * Triggers the insider list cleanse (see [here](../insider-list.md))
    * Sends a notification to all the previously not included intermediaries of the offer
* Intermediary (on receipt of any public offer state notification)
    * Cleanses all the insiders it has in relation 
    * Gives all gate-keepers access to the offer

![cleanse-wallcrossing](../../assets/cleanse-wallcrossing.png)

# Wall-crossing life-cycle for non-participants

We want to reduce the friction as much as possible for the RB team but not at the expense of giving non-participants a comparable experience to non-participants.

Options: 
* Controlled from the bank-side add users to a list and also B2C but give them access only to wall-crossings
* Create a new 'particpant-lite' where wall-crossings is the only feature but there is no 'team' .  Probably depends on consolidating on a single particpant.


# Wall-crossing life-cycle when mixed with other offer states

* Offers that are placed on hold can not have new wall-crossings (but could potentially receive new insiders for the insider list)
* Offers that are abandoned **should** be cleansed at the point of abandonment (if still inside)
