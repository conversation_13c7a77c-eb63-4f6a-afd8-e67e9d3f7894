---
layout: page
title: Wall Crossing - Description
parent: Wall Crossing
nav_order: 1
has_children: false
permalink: /analysis/wall-crossing/description
---

# Wall-crossing 

## Definition

**Wall-crossing** is a workflow that is designed to control the release of inside information to parties, without compromising the rights of the potential insider by revealing the nature of the inside information without the receiver's consent.

To achieve this there is a two stage process where some non-specific details about the inside information are shared with the potential new insider.  The potential insider then has the choice as to whether they will consent to receiving the information or not.  Typically the information will contain details such as the industry, market sector, and approximate timelines by which the information is expected to be made public.

This workflow is typically conducted through some verifiable medium such as email or recorded telephone calls.  However for **RetailBook** all wall-crossings will be conducted through the platform.

Sometimes **Wall-crossing**s are refered to as **Market soundings** this terminology comes from regulation and is really more relevant to situations where a potential investor is being canvassed to gauge an overall appetite for a new offer.  In the context of **RetailBook**, **Market soundings** are less relevant since retail investors will not be consulted prior to public-launch and if they are they will be consulted as part of the main order book if they are investors.

A wall-crossing is always associated with exactly one offer related to a single security.  There are potentially situations where two securities exist (for instance the issuer has two listed share classes) and in this case two independent wall-crossings will be maintained. 


## Life-cycle (overview)

The life-cycle of a wall-crossing is somewhat complex.  See details [here](./life-cycle.md)

## Issues


* **Intermediary** 
    * should we withold the offer information (i.e. not send it to the intermediary via NATS) until at least one person has consented?  or is that taking it too far?
    * a separate 'top level' tab for 'Wall-crossings' that shows a list of wall-crossing notifications for the logged in user and whether they were accepted or declined.
    * should it show the status of all crossings for all users or just the current user?  perhaps gatekeepers see all? probably needs to show all so that gatekeepers know that someone has 'approved' it
    * once accepted should it take you to a page with weasel word fields like 'expected open' and 'expected raise amount'? or should they all be blank?  or should the offer outline be viewed separately
    * once the offer is open the offer outline and market sounding information is of historic interest, but how much?  should it show acceptances as well?
    * how to add intermediaries that are not platform participants?
    * don't grant gatekeeper access to new offers that are 'inside' where they haven't consented.   they get access when they consent only.  when the offer is public they gain access like anyone else
    * anyone can be explicitly given access if a consenting gatekeeper does so.  doesn't require the new gatekeeper to accept?
    * do we need to acknowledge receipt of inside information
* **Bank** - always tied to an offer
    * the wall crossing consent form needs a subject line 
    * should not be able accept a consent once the offer is public
## See also

**Wall crossing** is closely related to the [insider-list](/analysis/insider-list) in that wall-crossing is a record of the controlled entries to the insider list.  However the wall-crossing list is a subset of the insider list.

# API

* POST /offer/{id}/wallcross

    Only if deal status is Pre-Launch or Building

* PATCH	/offer/{id}/wallcross/{wid}

    { 
        "id": "<wid>",
        "subject":  "<markdown>",
        "consent_to_cross": "<markdown>",
        "invite": ["{wiid#1}", "{wiid#2}", ...],
    }

* GET /offer/{id}/wallcross/{wid}/invite

    [
        {
            "id": "<wiid>", 
            "user_id": "<userid>",
            "invite_sent_time": "0001-01-01T00:00:00.000Z",
            "invite_response_time": "0001-01-01T00:00:00.000Z",
            "inside_display_time": "0001-01-01T00:00:00.000Z",
            "inside_confirm_time": "0001-01-01T00:00:00.000Z"
            "status": "(Accepted|Declined|Invite Sent|Displayed|Confirmed)"
        },
        { 
            ...
        }
    ]

* POST /offer/{id}/wallcross/{wiid}/invite

        {
            "id": "<wiid>", 
            "user_id": "<userid>",
            "invite_sent_time": "0001-01-01T00:00:00.000Z",
            "invite_response_time": "0001-01-01T00:00:00.000Z",
            "inside_display_time": "0001-01-01T00:00:00.000Z",
            "inside_confirm_time": "0001-01-01T00:00:00.000Z"
            "status": "(Accepted|Declined|Invite Sent|Displayed|Confirmed)"
        },

* PUT /offer/{id}/wallcross/{id}/invite/{id}/revoke

Iff state == 'Invite Sent'

* PUT /offer/{id}/wallcross/{id}/invite/{id}/resend

Iff (state == 'Invite Sent') or (state == 'Declined')

* PUT	/offer/{id}/wallcross/{id}/confirm

No payload/response

