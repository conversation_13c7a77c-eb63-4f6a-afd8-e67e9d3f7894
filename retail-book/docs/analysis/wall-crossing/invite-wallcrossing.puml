{% plantuml %}
@startuml
actor "Bank User" as bu
participant Bank as bank
participant Intermediary as im
actor "Gate-geeper\n(consenting)" as gk1
actor "Gate-geeper\n(declining)" as gk2

bu --> bank: Trigger wall-crossing event
bank --> im: Receive event
im --> gk1: Notify gate-keeper
im --> gk2: Notify gate-keeper
gk2 --> im: Decline
im --> bank: Decline
gk1 --> im: Consent
im --> bank: Consent 
bank --> bu: Notify consent 
bank --> im: Send offer-outline (storage)
im --> im: Grant access to consentors
im --> gk1: Notify consentors
gk1 --> im: Acknolwedge inside
im --> bank: Acknolwedge inside
@enduml
{% endplantuml %}