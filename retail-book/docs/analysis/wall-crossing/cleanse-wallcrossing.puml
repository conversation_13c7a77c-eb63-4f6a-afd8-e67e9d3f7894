{% plantuml %}
@startuml
actor "Bank User" as bu
participant Bank as bank
participant Intermediary as im
actor "Gate-geeper\n(consenting)" as gk1
actor "Gate-geeper\n(declining)" as gk2

bu --> bank: Open offer
bank --> bu: Cleanse insiders
bank --> im: Send offer (to remaining)
bank --> im: Open offer
im --> gk1: Cleanse insiders
im --> gk1: Notify offer
im --> gk2: Notify offer
@enduml
{% endplantuml %}