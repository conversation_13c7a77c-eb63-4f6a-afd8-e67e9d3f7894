---
layout: page
title: Features
parent: Analysis
nav_order: 1
has_children: false
permalink: /analysis/features/
---

# Participants
## Intermediaries
* Intermediaries with (op?) permissions can upload default settlement instructions
* Details
  * Name
  * Address
  * Logo
  * ... potentialy more


# Users
## User Onboarding
* Admin user (RB) can invite new users to the system (which emails them a verification link)
* Admin user (RB) can grant roles to those users
* A user can reset their password
* Admin user (RB) can assign "gatekeeper" attribute to a user.

## User Preferences
* User can edit their own preferences (such as email notification when new offers, or offers have materially changed)

# Offer Workflow

## Pre-creation

* User with profile (offer_creator) can create an offer with at least, a name & type

## Building
* User without read access to the offer cannot see it
* Creator of the offer can view it.
* Creator of the offer can assign other users read/write permissions within the same participant
* User with write access can update the main details
* User with write access can upload documentation
* User with write access can change state

## Pre Registration
* User without read access cannot see the offer
* User with write access can upload documentation
* User with write access can invite intermedaries to view the offer (to gatekeeps only)
* User with write access can update the main details

* Intermedaries gatekeepers with read access are emailed if preferences allow (when invited)
* Intermedaries with read access can elect to be emailed when material changes to the deal happen 
* Intermedaries with read access can view the offer
* Intermedaries with read access can view documentation attached to the deal
* Intermedaries gatekeepers can add access to other intermedaries users 

* User with write access can change state

## Registration Open
* User with write access can invite intermedaries to the offer
* Intermedaries can sign up to the offer 
* User with write access can upload a signed agreement contract
* Intermedaries with read access, can download documentation
* Intermedaries can view their own contracts
* Intermedaries can invite users in their own system to
* User with write access can change state

## Accepting Orders
* User with write access can invite intermedaries to the offer
* Intermedaries who have signed up - can enter orders (aggregate / detail)
* Intermedaries who haven't signed up - can enter unagreed orders (aggregate / detail)
* Intermedaries can sign up to the offer
* User with write access can upload a signed agreement contract ( system agrees any un-agreed  )
* User with write access can view the whole order book
* Intermedaries can see their own orders only
* User & Intermedary can view order history
* User with write access can change state

## Closed
* Intermedaries *might* be able to put a late order in?
* Intermedaries who have signed up - can enter orders detail for validation
* Intermedaries who's detailed orders do not match the aggregated orders, is flagged (perhaps email?) to offer coordinator
* User with write access can reject invalid aggregation/detail orders
* User with write access can accept late/invalid aggregation/detail amendments.
* User with write access can edit order book
* User with write access can allocate to the orders (MVP) (via spreedsheet upload)
* User can change state

## Allocated
* User can change state
* User with write access needs to get a report of per intermediary settlement instructions. (orders + settlement instructions (copied from intermediary preferences but....))

## InstructionSent
* User can change state

## Settled
* User with write access changes state after confirmation of movement of settle and stock.
