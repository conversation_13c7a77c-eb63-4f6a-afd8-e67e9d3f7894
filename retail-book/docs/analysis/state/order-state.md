---
layout: page
title: Order State
parent: State
grand_parent: Analysis
nav_order: 1
has_children: false
permalink: /analysis/state/order-state
---


# Order State 

* **Unconfirmed** the entry state for detail orders (i.e orders that will create an order book) by the intermediary
* **Pending** the entry state for summary orders.   Indicates an order that will need approval before presenting to the issuer.  Indicates that the order is either late or has been entered before the intermediary has accepted the offer terms.
* **Accepted** an order that has been accepted by the bank
* **Allocated**  an order where the bank has allocated a number of shares 
* **Settled** an order where the allocated shares have been settled with the intermediary

For the purposes of this document 'Unconfirmed' and 'Pending' orders are considered: **Unacknowledged** and 'Rejected', 'Accepted', 'Allocated' and 'Settled' as **Acknowledged**.

## State Diagram 

![order-state](order-state.svg)

## Order State Notes

The **Current Order** for an intermediary is used to calculate the intermediaries contribution to the offer raise.  In general the **Current Order** is the last amendment in the Order History.

However, in the presence of orders that are either reject or net yet approved then the **Current Order** depends on the actor (i.e. intermediary or bank) and the context within which orders are being viewed.
## Intermediary Order State Processing

As an intermediary there is an expectation that any new amendment that is **Unacknowledged** (see above) is optimistically assumed to be **Accepted** in totals or views.  This is to give positive feedback to the user to reassure them that the amendment has in-fact been made.

Should an **Unacknowledged** order be rejected then the **Current Order** is the previously acknowledged order in the history, or **Previous Order**.  Should no **Previous Order** exist the **Current Order** is the empty or zero order.

### Order entry

Should always show the **Current Order**.

### Order grid for an offer

* **Current Order** should always show in this grid
* Expanding the **Current Order** shows the true history
* Totals or summaries should reflect the **Current Order** only 

### Order grid across all offers

* **Current Order** should always show in this grid
* Expanding the **Current Order** shows the true history
* Totals or summaries should reflect the **Current Order** only 

### Bank Order State Processing

As a bank there is a requirement to know the amount of accepted orders for reporting to issuers.  The bank should also be shown what the shareholding, non-shareholding total is including the unacknowledged orders - this is because they may or may not choose to pass this information to the issuer depending on the circumstances.  

### Order grid for an offer

* **Current Order** should always show in this grid - this order can  be rejected/accepted should the offer state allow it (unless it is the empty/zero order)
* Expanding the **Current Order** shows the true history
* Totals or summaries should reflect shareholder and non-shareholder totals of:
    * the **Current Order** - which includes unacknowledged orders
    * the **Previous Order** if the **Current Order** is **Unacknowledged**

### Order grid across all offers

* **Current Order** should always show in this grid - this order can  be rejected/accepted should the offer state allow it (unless it is the empty/zero order)
* Expanding the **Current Order** shows the true history
* Totals or summaries should reflect shareholder and non-shareholder totals of:
    * the **Current Order** - which includes unacknowledged orders
    * the **Previous Order** if the **Current Order** is **Unacknowledged**
