{% plantuml %}
@startuml
scale 350 width

state "Bulding on Hold" as BuildingOnHold
state "Pre-Launch" as PreLaunch
state "Pre-Launch on Hold" as PreLaunchOnHold
state "Applications Open" as ApplicationsOpen
state "Applications Open on Hold" as ApplicationsOpenOnHold
state "Applications Closed" as ApplicationsClosed
state "Instructions Sent" as InstructionsSent

[*] --> Building : Create Offer
Building --> PreLaunch : Pre-Launch
Building --> BuildingOnHold : Hold
BuildingOnHold --> Building: Continue

PreLaunch --> ApplicationsOpen : OpenApplications
PreLaunch --> Building : Back
PreLaunch --> PreLaunchOnHold : Hold
PreLaunchOnHold --> PreLaunch : Continue

ApplicationsOpen --> ApplicationsClosed : Close
ApplicationsOpen --> PreLaunch : Back
ApplicationsOpen --> ApplicationsOpenOnHold : Hold
ApplicationsOpenOnHold --> ApplicationsOpen : Continue

ApplicationsClosed --> Allocated : Allocate
ApplicationsClosed --> ApplicationsOpen : Back

Allocated --> InstructionsSent : Instruct
Allocated --> Closed : Back

InstructionsSent --> Settled : Settle
InstructionsSent --> Allocated : Back

Settled --> InstructionsSent : Back
Settled --> [*]

BuildingOnHold --> Abandoned : Abandon
PreLaunchOnHold --> Abandoned : Abandon
ApplicationsOpenOnHold --> Abandoned : Abandon
Abandoned --> [*]
@enduml
{% endplantuml %}
