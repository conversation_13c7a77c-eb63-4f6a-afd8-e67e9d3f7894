---
layout: page
title: Offer State
parent: State
grand_parent: Analysis
nav_order: 1
has_children: false
permalink: /analysis/state/offer-state
---

# Offer State 

## Normal process

* **Building** - Where skeleton offer details are constructed, an offer in building state is only visible to the offer co-ordinator.  Sorts of information entered are issuer, raise type, raise amount, follow-on (equity only), currency, min/max order amounts.
* **Pre-Launch** - Some draft details are visible to intermediaries, however the offer is fully read-only. To enter this state the offer must at least have Key Info, Allocation, Settlement Info. Other offer resources can be added by the co-ordinator, such as factsheets, videos, logos, etc. The co-ordinator invites intermediaries to apply.
Pre-launch is also the stage where intermediaries can elect to sign up to the offer and receive their intermediaries agreement (or Distributor Agreement). No orders can be entered. The intermediary agrees to certain disclaimers relating to the offer. The following documents can be uploaded (+=mandatory, -=optional): 
    * IPOs & Bond issues: Prospectus (+), Timeline (+), Terms and Conditions (+), Supplementary prospectus (-)
    * Follow-ons: Retail RNS (+), Presentation (-)
* **Applications Open**  - There must be at least one participating intermediary and the offer must be announced.  At this point the offer is public and any insiders are cleansed.  Intermediaries are able to enter orders.  All orders will be binding at the time of offer closure.
* **Applications Closed** - This is either triggered by the co-ordinator or by the passing of the offer closure date/time.  30mins (configurable) prior to closure an email is sent to intermediaries informing them of the impending closure (and the need to finalise their orders).  Whilst in the closed state intermediaries who were allowed to enter aggregated offer details will be required to submit their final orders.  Those final orders should match the total of the pre-close amount.  At this point the co-ordinator will ascertain the per-intermediary allocation amounts.
* **Allocating** - This is when an allocation is drafted but not yet notified to the intermediaries. Orders cannot be entered anymore.
* **Allocated** - Once allocated all intermediaries have been informed of their allocation from the raise.
* **InstructionsSent** - Details are sent to the settlement provider to inform them of the required settlement per counterparty.  
* **Settled** - At this point the stock and cash have moved between the co-ordinator and the intermediary and the intermediary has been informed that their commission has been paid (where applicable).

# Exceptional Process

* **'Back'** - inevitably mistakes will occur which will require the rewind of states.  When travelling back through states (and moving forwards again) notifications that have previously been sent should not be resent. 
* **&lt;Xxxx&gt;OnHold**  - Special states to allow an offer to be held.   This is to allow offers that are pulled by the issuer to remain as they are until later when they can be continued.  Once an offer is closed it can not be placed on hold.
* **Abandoned** - An offer which has no chance of being continued.  

# State Constraints
## Legend

* N/A = Not available/accessible
* None = No restrictions
* Bank = Offer co-ordinator
* Retail = Retail intermediariy
* +C = Create
* +R = Read
* +U = Update
* +D = Delete (inactivate)

|__State__          |__Offer Details__  |__Orders__         |__Order Approval__|__Documents__      |__Contracts__      |__User Access__        |__Back__   |__Next State__          |__Note__                                        |
|-------------------|-------------------|-------------------|------------------|-------------------|-------------------|-----------------------|-----------|------------------------|------------------------------------------------|
|Building           |Bank+CRUD          |N/A                |N/A               |Bank+CRUD          |TBC                |Bank+CRUD              |N/A        |None                    |                                                |
|Pre-Launch         |Bank+CRUD, Retail+R|N/A                |N/A               |Bank+CRUD, Retail+R|Bank+CRUD, Retail+R|Bank+CRUD, Retail+CRUD |None       |See pre-launch above    |                                                |
|Applications Open  |Bank+CRUD, Retail+R|Bank+R, Retail+CRUD|Bank+CR           |Bank+CRUD, Retail+R|Bank+CRUD, Retail+R|Bank+CRUD, Retail+CRUD |No orders  |Warn if 0 intermediaries|Orders are auto-approved with a signed contract |
|Applications Closed|Bank+CRUD, Retail+R|Bank+R, Retail+CRUD|Bank+CR           |Bank+R, Retail+R   |Bank+CRUD, Retail+R|Bank+CRUD, Retail+CRUD |None       |None                    |No orders are auto-approved, close is manual    |
|Allocated          |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+R            |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+CRUD, Retail+CRUD |None       |More than one order     |Allocation is via a notification in Day 0       |
|Instructions Sent  |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+R            |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+CRUD, Retail+CRUD |None       |None                    |Instructions sent is via a notification in Day 0|
|Settled            |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+R            |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+CRUD, Retail+CRUD |None       |None                    |Settled is via a notification in Day 0          |
|...OnHold          |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+R            |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+CRUD, Retail+CRUD |None       |None                    |**No** amendments to avoid creating an invalid state|
|Abandoned          |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+R            |Bank+R, Retail+R   |Bank+R, Retail+R   |Bank+CRUD, Retail+CRUD |None       |N/A                     |                                                |


# State Diagram 
![offer-state.svg](offer-state.svg)
