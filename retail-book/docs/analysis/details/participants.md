---
layout: page
title: Participants
parent: Details
grand_parent: Analysis
nav_order: 1
has_children: false
permalink: /analysis/details/participants
---

# Participants

## Overview
From the [glossary](../glossary.md) a participant is a member of the network, within the context of a deal they act as either a retail-intermediary, or a bank. A Participant user, is a user is part of that entity
who then can be added into a deal.

## User Onboarding

User on-boarding can be performed by a user who has the role "participant on-boarding". The user added, must have an email in the set of acceptable domains (in the case the retail intermediary).

The data entered when onboarding:

| Name       | Type   | Description             |
|------------|--------|-------------------------|
| First Name | string | first name of the user  |
| Last Name  | string | last name of the user   |
| email | string | email of the user|
| Type | enum | admin or user |

The flow of participant onboarding is shown below:

![participant-user-on-boarding.svg](participant-user-on-boarding.svg)

