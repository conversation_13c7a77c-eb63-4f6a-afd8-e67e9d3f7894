---
layout: page
title: Insider List
parent: Analysis
nav_order: 1
has_children: false
permalink: /analysis/insider-list
---

# Insider List 

## Definition

The insider list is a list of the times that *individual persons* (not companies or groups of people) were made aware of inside information.   In the context of **RetailBook** the insider list can be constructed from a number of sources.  Note that where no inside information is (or was) present then the insider list does not have meaning.

The insider list is one of the first thing that a regulator will ask for when investigating into any potential market abuse and is an important feature of the offer creation process.

Being an insider comes with legal restrictions about what the insider can and can't do once they're deemed inside.  Crucially an insider should not transact in the affected securities that are issued by the company on which the inside information originates from.  Hence insider lists are really only relevant to offers where a security is related to the inside information (i.e. follow-ons or mergers with secondary raises etc.) and not intial offerings (i.e. IPOs or Retail Bonds)


## Sources

* **Wall-crossings** - the names of people invited to collaborate on a offer prior to the offer being made public (see wall crossing [defined](./wall-crossing/description.md) and [life-cycle](./wall-crossing/life-cycle.md))
* **Team access** - within RB the creator of the offer is clearly inside (by definition) and any other RB team members invited to the offer are also classed as insiders
* **Others** - other people can arbitrarily be added to the insider list.  Note that this can occur at some point after the actual fact (i.e. when the **RetailBook** are made aware of it happening).  Reasons why this might occur are: 
    * a person from another trusted insitution that is not a participant (such as a legal adviser);
    * someone who witnessed the information but is not an RB user, this person could still be an RB employee but could also be any other person from outside the firm.
    
The insider list is triggered by a single bank-side user marking that the offer is inside information with an **effective_start** date (any) and an unset or future **effective_end** date.  The list can be updated to reflect new information on the register but in general items should not be changed or deleted in the list.  

**NB** In practice though, to keep the system flexible it should be editable but not deletable.

A simple logical model for an insider register (i.e. those parts of the list excluding the team access part) could look like this (although in practice the data need not be arranged this way for data integrity reasons):

![insider-register.png](../assets/insider-register.png)

## Timings

An offer is considered to contain inside information as soon as a bank user adds an insider register to the offer (and sets the **effective_start**) if the **effective_end** date of the register is either not set or in the past.

The time at which someone is made inside is the time the information is transmitted.  Whether that be verbally, electronically or in person.  Note that the insider need not acknowledge the receipt of the inside information, they are still classed inside regardless.  However it is generally considered good practice to request acknowledgement of the inside information.

## Bank-side first

There is no requirement for a firm that disseminates inside information to track the information beyond the people that it knows about.  For instance, **RetailBook** does not need to report to regulators what it knows about how the intermediary shared inside information, so only knowledge of the gatekeepers that consented to be wall-crossed is required by **RetailBook**.

However, there is equally a requirement for the intermediary to keep track of  who was brought inside and when.  Although this is not a primary concern of **RetailBook** (and you would hope the intermediaries have existing solutions for dealing with this problem) and this feature would be a future requirement.

## Cleanse

When information is made public all the insiders are released from their legal obligations.  This routinely happens when the offer is 'Applications Open' since once **RetailBook** is accepting orders the information must be public.

However, should the deal not launch the offer may be put on hold in such cases 

