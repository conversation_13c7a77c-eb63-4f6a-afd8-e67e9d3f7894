{% plantuml %}
@startuml
entity "Offer" as offer {
   ...
}

entity "Insider Register" as ir {
   created_by: text
   effective_start: datetime
   effective_end: datetime
}

entity "Wall-Crossing" as wc {
  consent_text : text
  offer_outline : text
}

entity "Wall-Crossing Event" as wci {
   name: text
   email: text
   consent_response: acceptDecline
   consent_received: datetime
   crossed: datetime
   acknowledged: datetime
   cleansed: datetime
}

entity "Insider" as i {
   name: text
   email: text
   crossed: datetime
   acknowledged: datetime
   cleansed: datetime
}

offer ||--o| ir
ir ||--|| wc
ir --o{ i
wc --o{ wci

@enduml
{% endplantuml %}