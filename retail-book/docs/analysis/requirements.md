---
layout: page
title: Requirements
parent: Analysis
nav_order: 1
has_children: false
permalink: /analysis/requirements/
---

# Functional

|__ID__|__Author__|__Requirement__|__MuSCoW__|__Justification__|__Notes__|__Entered Date__|
|NF01|knights|Data should be encrypted in transit and at rest|Must have|This data is commercially sensitive and potentially inside information||25/04/2022
|NF02|knights|Deal data should be hidden from all parties that are not authorised to see it by the deal owner|Must have|This data is commercially sensitive and potentially inside information||25/04/2022
|NF03|knights|Deal data should not be directly modifiable by anyone other than the network operator|Should have|Modifications to data should only be via API  ||25/04/2022
|NF08 |knights|Every deal has a separate permissions model controlled by the deal owner|Must have|Different parties will be invited or wall-crossed depending on the circumstances.||25/04/2022
|NF06|knights|The cost of running the network, to the network operator, should not exceed <insert money here>|Should have|Costs for running the network should be understood through all the variously changing dimensions.|$100k p.a. seems like a good starting point.  Scale to zero is not a requirement in itself but a nice to have.  This is a weak requirement because software/service costs  will be passed on to some extent.  However, the lower the operating costs the bigger the profit!|25/04/2022
|NF05|knights|The deal owner should be able to verify:|Should have|Take-up of the service will likely depend upon the level of trust banks have of the privacy.||25/04/2022
||a) who has seen the data over any time period||||
||b) what transactions have occurred during any time period||||
|NF09|knights|The deal runner may choose the location (i.e. cloud or on premises) where their deal runner instance runs|Should have|This will aid adoption and not restrict firms that are not 'cloud ready'.||25/04/2022
|NF07 |knights|The network operator should be protected from failure of any of the network participants|Should have|Loss of a single party  (or its data) should not threaten the whole network.||25/04/2022
|NF04|knights|The network operator should have the ability to resolve any data or network issue|Must have|When thing's go wrong there must be a way to fix it up|Note that when considered with requirement #2 this ability may come with the need for the deal owner to provide an audited 'break glass' mechanism or supervised access.|25/04/2022
|NF10|knights|The retail intermediary or retail intermediary platform must be able to build an automation interface to the solution/service that is agnostic of the deal runner|Must have|The success will be dependent on the level of integration/automation we can achieve with the intermediary.||27/04/2022
|NF11|knights|A retail intermediary may or may not charge commission on a deal|Must have|Those orders must be distinguished from the non-commission orders to allow for the proper cash-payment to the intermediary||8/8/2022

# Non-Functional

|__ID__|__Author__|__Requirement__|__MuSCoW__|__Justification__|__Notes__|__Entered Date__|
|F01|knights|The network operator is responsible for on-boarding/off-boarding nodes/participants to/from the network|Must have|The network operator must have total control since this is a paid for service|25/04/22
|F02|knights|A closed deal where shares are allocated to retail intermediaries, is a billable event with fees due to the network operator.  A closed deal that allocates no shares is not billable.|Must have|25/04/22
|F03|knights|A deal that is not announced by the deal owner is considered inside information and the information should be given reasonable protection to avoid accidental/malicious  disclosure|Must have|The success of the network will partially depend on us being able to communicate how the network is safe from accidental/malicious disclosure of inside information||25/04/22