---
layout: page
title: Azure Container Registry
parent: Azure
nav_order: 1
has_children: false
permalink: /azure/acr/
---
 
# Azure Container Registry

## Provision ACR

First, provision the azure container registry on the Azure portal. This can be done by either:
- Searching for `Azure Container Registries`
- Running a terraform script in the build folder of this source code.
- Using the `az` cli tool.

Notes:
- The resource group must match the cluster, in this case, **RG-Development**.
- Location should also match the rest of the cluster, **UK South**.
- Only the **premium SKU** provides private access.
- Due to the privacy - we may want to onboard both non-production and production registries as premium.
- Attempt to follow the naming convention - `rbclusterdev` and `rbclusterprod`.

![acr.png](../assets/acr.png)

## CI/CD Container Registry Access

Github Actions requires a dedicated service principal to the Azure instance; with rights to push images to the registry.

You may want to see [this article](https://docs.microsoft.com/en-us/azure/container-instances/container-instances-github-action) for more information.
 
1. Get the resource group ID for RG-Development:
   1. `$ az group show --name RG-Development --query id --output tsv`
2. Create a new service principal in this resource group:
   1. `az ad sp create-for-rbac --scope {output of step 1.i} --role Contributor --sdk-auth`
   2. This will result in a JSON structure. Save this for later.
3. Get the ID of the newly provisioned ACR instance.
   1. `az acr show --name {name of the registry} --query id --output tsv`
   2. Again, save this for later.
4. Update the principal with push & pull rights for ACR.
   1. `az role assignment create --assignee {output of 2.1: clientId field} --scope {output of 3.1} --role AcrPush`
   2. Replace the two outputs above with previous results.

## Update Github Secrets

** Please note: you require administrator rights in the repository to do this. **

1. Navigate to the settings -> secrets -> actions -> repository secrets area. Or [click here](https://github.com/RETAIL-BOOK-LIMITED/retail-book/settings/secrets/actions).
2. We follow a naming convention for secrets in our builds. `DEV_*` naming is for non-production areas and `PROD_` is for production areas.
3. Create a `[ENV]_AZURE_CREDENTIALS` repository secret (or [click here](https://github.com/RETAIL-BOOK-LIMITED/retail-book/settings/secrets/actions/new)):
   1. Insert the output of step 2.1 in the [CI/CD container registry section](#CI/CD Container Registry Access).
4. Create a `[ENV]_REGISTRY_USERNAME` repository secret (or [click here](https://github.com/RETAIL-BOOK-LIMITED/retail-book/settings/secrets/actions/new)):
   1. Insert the `clientId` value of the output of step 2.1 in the [CI/CD container registry section](#CI/CD Container Registry Access).
5. Create a `[ENV]_REGISTRY_PASSWORD` repository secret (or [click here](https://github.com/RETAIL-BOOK-LIMITED/retail-book/settings/secrets/actions/new)):
    1. Insert the `clientSecret` value of the output of step 2.1 in the [CI/CD container registry section](#CI/CD Container Registry Access).
6. Create a `[ENV]_AZURE_REGISTRY_LOGIN_SERVER` repository secret (or [click here](https://github.com/RETAIL-BOOK-LIMITED/retail-book/settings/secrets/actions/new)):
   1. You can find this on the azure portal - on the page describing your ACR instance (as part of the metadata).

The document specifies a `resource_group` secret. We have ommitted this, unless required later in the future.