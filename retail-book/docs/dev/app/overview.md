---
layout: page
title: Application Overview
nav_order: 2
parent: Application
grand_parent: Dev
has_children: false
permalink: /dev/app/overview
---

# Overview

## Application processes

| Name | Purpose |
| ApiGateway | provide the API end point into the application itself |
| CommandHandler | does all the 'writing' in the application, accepts commands and generated events |
| viewserver | provides read-only views over the data in the applicatino |
| interapp gateway | lists to the event stream, and routes specific events out into the wider network |

## Diagram

![app-overview.drawio.png](app-overview.drawio.png)
