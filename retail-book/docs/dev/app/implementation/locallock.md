---
layout: page
title: Local Locking
nav_order: 1
parent: Implementation Overview
has_children: false
permalink: /dev/app/implementation/locallock
---

# Local Locking

## Distributed Locking

In order to impersonate running in a pod environment, do the following:

Find the cluster in question:
```bash
kubectl config view -o jsonpath='{"Cluster name\tServer\n"}{range .clusters[*]}{.name}{"\t"}{.cluster.server}{"\n"}{end}'
export CLUSTER_NAME="retailbook-staging"
```

Set the API Server URL:

```bash
APISERVER=$(kubectl config view -o jsonpath="{.clusters[?(@.name==\"$CLUSTER_NAME\")].cluster.server}")
```

Create a token file before running the main class (**change the namespace accordingly**):
```bash
kubectl get secrets -o jsonpath="{.items[?(@.metadata.annotations['kubernetes\.io/service-account\.name']=='default')].data.token}" -n participant-marimoplc |base64 --decode >> src/commandhandler/var/run/secrets/kubernetes.io/serviceaccount/token
```

And run the main class with the following env variables (at the time of writing - refer to config):
```bash
APP_CLUSTER_HOST=xxx.privatelink.uksouth.azmk8s.io
APP_CLUSTER_PORT=443
APP_CLUSTER_TOKEN_FILE=xxx
APP_TEST_MODE=true
```

## Etcd Locking

This should be the default mode (it is at the time of writing). In local environment, you can either port forward
etcd and disable it completely using the config.

Alter the following in the `commandhandler` service:

```yaml
clusterConfig:
  etcd:
    enabled: true
    endpoints:
      - host: etcd
        port: 2379
```

