---
layout: page
title: Release
nav_order: 2
parent: <PERSON>
has_children: false
permalink: /dev/release
---

# Create a release version

The release steps for releasing the UI and the backend are the same, the only difference is the repo you committed to.

1. Once you have merged your pull request to the staging branch check an "action" is created:
  ![staging_action.PNG](./images/staging_action.PNG)
2. When it is finishes click on the build task to reveal some post build actions:
  <img src="./images/action_actions.PNG" width="500px" height="200px">
3. Click on "SemVer Tag" and then the link to create a new version:
  ![SemVer.PNG](./images/SemVer.PNG)
4. Name the release appropriately (i.e. the version number)
5. Click "Generate release notes"
6. Ensure "Set as the latest release is ticked"
7. Click "Publish release"
  <img src="./images/create_tag.PNG" width="100%" height="500px">
8. Check that an action for your tag is created and wait for it to finish:
  ![tag_action.PNG](./images/tag_action.PNG)

# Push a release version

1. Open up the admin tool
   - ```cd C:\Users\<USER>\source\retail-book\src\admin\cmd```
   - ```go run cli.go```
2. "Dev Ops"
3. "Release"
4. Select the environment, component, enter the tag name to release, then press enter.
   - This action requires direct push access to the retail-book-infra-tooling repo, so be an admin or ask an admin to add you to the bypass rule

# View release status in ArgoCd

1. Run "k9s" in cmder and ensure you are connected to the correct cluster (e.g. retailbook-staging)
   - To connect to the correct cluster follow the guide https://github.com/RETAIL-BOOK-LIMITED/retail-book-infra/blob/staging/docs/connect-to-aks.md
     - Generally you probably already have staging configured, so you'll only need the last line (so could be an alias) e.g.
     - ```ssh -L 443:retailbookstaging-d8c1df36.a6ebd97b-ec00-4f6f-b142-4bdfaa512e08.privatelink.uksouth.azmk8s.io:443 <EMAIL>```
2. Obtain the rolling password for argocd:
   - ```:secret``` + enter
   - ```/argocd-initial-admin-secret``` + enter
   - ```x```
3. Log in to the appropriate argocd page with username admin e.g. https://argocd.staging.retailbook.com/
4. Wait for all of the applications to sync

<img src="./images/synced.PNG" width="250px" height="400px">