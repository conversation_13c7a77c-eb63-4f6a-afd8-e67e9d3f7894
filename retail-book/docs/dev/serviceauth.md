---
layout: page
title: Service Identification
nav_order: 2
parent: <PERSON>
has_children: false
permalink: /dev/serviceauth
---

# Service To Service Authentication

One of the early requirements that were discovered is to have a reliable and safe way to perform service to service
identification (i.e. how do we identify the incoming asynchronous request originates from the service that claims to 
be the source?).

There are a few caveats:
- The messages are sent a NATS broker, which supports replaying and transactionality in our implementation. Having a token with a short expiry could be a poor solution.
- Although tokens can be hand crafted and transmitted as new participants enter the network - this could be a configuration nightmare and a security issue due to static tokens being leaked.
- It appears there needs to be a 'central' authority of trust that can easily revoke the 'identities'.


## Kubernetes

Kubernetes, out of the box, supports service account tokens. Any new namespace that you create, i.e:

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: test-participant-1
```

Will be mounted with the default service account. You can create custom service accounts too. The default, for the purpose of this article, is fine.

The service account is mounted as a secret in the following directory:

```
/var/run/secrets/kubernetes.io/serviceaccount/
```

Which contains the following files:

- `ca.crt`: The CA used to trust API queries, including the HTTPS JWKS endpoint.
- `namespace`: The current namespace we are in.
- `token`: An auto-rotated JWT token, signed by one of the keys presented by the JWKS (see below).

Without going into too much detail; the JWT token is split into three fragments (head, body, signature). This is irrelevant
for development purposes as most libraries abstract this logic away - but it's important for this article.

Taking an example token from the secrets path `/var/run/secrets/kubernetes.io/serviceaccount/`:

```
eyJhbGciOiJSUzI1NiIsImtpZCI6IkMzWUV5Y2dxNWNxMlBUUlZGaHZkb3RNOWpDMjA4akJJVHZZY2xDeE5GOHcifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vUtzP-6D-NgmayNT4s_RlUZqEjEGZJTgVNiAigR2bpxOxnNLgD626jJY6YEwrzyaIlD_OLjG8kAX_6tYVtPOrMw1_AA8qK6yX8Wnpj-hp4GWiCqlSFvBxcKsrn3xqb_RrkL79GwrfSlSDkC_RcS8cjuJ0tXjSrC6zLJXFJKrffGQRRA2Mv-JthLDp67Wbzqms2KExWilv28IptFuggm0w4wKxUdydpHvwaz1zWr5R9UIAQnD0s7uKIqwvR_p4jurCeSOrrfXqh4f8RvO7Qc1GEkwFskcNQn7SqVAq72dnS6Bz3MRgu7LzLSQcDEGLj2qBNoyXNaEzVAxK8Ej3UlLSWXW-Nfn928r-4CxxQm2_iSAnpSc4B50P_RwtPteHfoblaa1a4aabtoWyzulhtTGYraydWBvQHbhVomXPN0NL0pOr80g09lfvcuhRIO2N2HQE-3GGq91R4oKyuL73nDMmkyKh1ua7LtSezsRzZQPLNh8GvkjYTdGpKxkKCjg3Yx4fuKFR1k4dgfuFUlHLWhjlJucBelY0j5ucoBegFg7GDy4o_LNmoJlsWrRDBRtRdabFLyxAHHWcAavnuOi7duaJF-b5KHMmb57sbHw5cjrDzvoesY3LtTg9gCf0s8sVIfXWNlhY-1-CHeLXBa1Is9NtmQeCZpz2wCgmFn2mDouB4M
```

Cutting this token by the delimiter (.) and taking the middle fragment gives us the body. The body is a BASE64 encoded JSON document.

```
BODY=$(cut -d '.' -f 2 ${SA}/token)
echo $BODY | base64 -d | jq
```

Which gives us:

```json
{                                                                         
  "aud": [                                                                
    "https://retailbook-k8s-arm-dns-4efbc2a6.hcp.uksouth.azmk8s.io",      
    "\"retailbook-k8s-arm-dns-4efbc2a6.hcp.uksouth.azmk8s.io\""           
  ],                                                                      
  "exp": 1697114614,                                                      
  "iat": **********,                                                      
  "iss": "https://retailbook-k8s-arm-dns-4efbc2a6.hcp.uksouth.azmk8s.io", 
  "kubernetes.io": {                                                      
    "namespace": "test-participant-1",                                    
    "pod": {                                                              
      "name": "ubuntu",                                                   
      "uid": "********-1f27-4806-8b74-78df193c49ac"                       
    },                                                                    
    "serviceaccount": {                                                   
      "name": "default",                                                  
      "uid": "f4b7df3e-608f-44a4-a0c7-5528eb028689"                       
    },                                                                    
    "warnafter": **********                                               
  },                                                                      
  "nbf": **********,                                                      
  "sub": "system:serviceaccount:test-participant-1:default"               
}                                                                         
```

Therefore, the receiving side of this token can easily verify the identity via the namespace claim (or other custom claims with further configuration).
The expiry of the token (`exp` claim) is the epoch time after which the token should not be trusted. We may choose to ignore this at the receiver (or configure tokens with infinite expiry due to caveats mentioned in the intro).

However, the body in itself is a simple JSON document and cannot be trusted therefore during parsing the signature must be verified.

JWKS is an endpoint that presents public keys of the signers (for this exact purpose). To find out where the JWKS URI is, send a authenticated request to `https://kubernetes.default.svc/.well-known/openid-configuration`.

Since the OpenID + JWKS can technically be spoofed as well, the endpoints sit behind HTTPS. The root CA cert for the cluster is used as trust. This is mounted in the secrets alongside the token as `ca.crt`. The endpoint is also 
authenticated - so the bearer token (again, the `token` file) must be used. Example:

```
curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" -X GET ${API}/.well-known/openid-configuration | jq
```

Which returns:

```json
{
  "issuer": "https://retailbook-k8s-arm-dns-4efbc2a6.hcp.uksouth.azmk8s.io",
  "jwks_uri": "https://retailbook-k8s-arm-dns-4efbc2a6.hcp.uksouth.azmk8s.io:443/openid/v1/jwks",
  "response_types_supported": [
    "id_token"
  ],
  "subject_types_supported": [
    "public"
  ],
  "id_token_signing_alg_values_supported": [
    "RS256"
  ]
}
```

A request sent to the JWKS URI from the payload above:

```
curl -k -H "Authorization: Bearer ${TOKEN}" https://retailbook-k8s-arm-dns-4efbc2a6.hcp.uksouth.azmk8s.io:443/openid/v1/jwks
```

Returns:

```json
{
  "keys": [
    {
      "use": "sig",
      "kty": "RSA",
      "kid": "C3YEycgq5cq2PTRVFhvdotM9jC208jBITvYclCxNF8w",
      "alg": "RS256",
      "n": "vWCUdKtsfOxmt_VOzXASH7i-nK1KFQll6YJtQudhkHVLxsdRDRmeFj25hJx22rHNTJiM0iyURMhw6rbuQaadbA4OzNG3ZG5zbbt_6ulSrI9MFYrdt_8GM0xdgqCuvNMYfxN12pZBi30Dtirc9-K93KbYyGIe2IwhcmlcRYJzqxHqyM7RWICKPLgFSx5LodtUI9_Vu19asLqd7ZuBbdqkypXp3wXJNmCWx8yR9db5_4z-AbFo6wKGKP6acHBAcB-mIKVMIIvIRJ5VFUHDXdsjC0EMY2MgpeiJKrBa4hijEAOOzEw9eJUqkwcdx86wUgWz1zLR2xiKRIXarC2KG6EVhqdL-TLmrNlCdPI1lwdTcrljOzY7G2Z88PTf0n3FDc6AIdlJEBhO-VzXk0Fo0n-1zjVZsaFaj44PIQyZMwlSxcHZ6yqd7Gof0Y4hXyoEK34QualvlNB3gipAxbmLkZ047D0VNuK9SXgmXOKmbjQS4xeuTHtLow881CfS9xUWzXI3SDZ4spxlbH2846lC6szSq-uG5-sGLS5xCXDWEovK2v1u1yradZnJ03YCRugWvAJ4wTKsys0B_8JKydko9MsEkkHIHCBwutHg8lePslh-ZzBxxGXRdfyrXPmM3djHVZ7Hs-7ZMveev8I99FltDN1VxANXhsE4faXlJvhagUvM4bc",
      "e": "AQAB"
    }
  ]
}
```

Which shows a single key used for signing. This key can then verify the signature of the requestor's token - proving its identity.

Again, I stress, this is going into detail. Most of these operations are abstracted away.
