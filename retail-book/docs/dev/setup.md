---
layout: page
title: Environment Setup
nav_order: 2
parent: <PERSON>
has_children: false
permalink: /dev/env
---

# Developer environment setup

It is recommended that Dorset developers use AVDs of type "Standard D8ads v5".
These VMs fully support nested virtualisation and are equipped with 8 vcpus and 32 GiB memory, all of which is required to run RedHat Openshift and Docker locally.

To configure the AVD to run these tools please follow these steps (which involve a **lot** of rebooting):
1. Enabling the required Windows features<br>
   Run the following 2 commands from an **admin-level** PowerShell console
   ```
   Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
   Enable-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform
   ```
   then reboot the AVD.

2. Installing WSL2 for an integrated Linux experience<br>
   Run the following command from an **admin-level** PowerShell console
   ```
   wsl --install
   ```
   then reboot the AVD.<br>
   Then run the following command from an **admin-level** PowerShell console
   ```
   wsl --update
   ```
   then reboot the AVD again.<br>
   Then run the following commands from an **admin-level** PowerShell console
   ```
   wsl --set-default-version 2
   wsl --list --online
   wsl --install -d Ubuntu-20.04
   ```
   Running
   ```
   wsl --list --verbose
   ```
   from any console should confirm that Ubuntu-20.04 is installed and will run under WSL v2

3. Installing Docker Desktop<br>
   You can use Docker Desktop as RetailBook is, as of this writing, still a classed as a small business by Docker, so there should be no licensing costs involved.<br>
   Binaries, installation and licensing details can be found at https://www.docker.com/products/docker-desktop/

4. (Optional) Installing Red Hat OpenShift Local<br>
   Install Red Hat OpenShift Local by following the instructions at https://console.redhat.com/openshift/create/local 


## Development tools

- Log in to [Office365](https://www.office.com/) with your username in the format "retailbook\your.username" and install the teams app. 
- [Go](https://go.dev/dl/)
  - The version of Go we are currently using is specified in go.mod files in the "retail-book" repo. For example src/admin/go.mod "go 1.21". Minor versions of Go are backwards compatible so just get the latest version.
- [Intellij](https://www.jetbrains.com/idea/download/#section=windows)
  - Install the Go plugin
- [VsCode](https://code.visualstudio.com/download)
- [NodeJs](https://nodejs.org/en/download)
  - Say yes when offered to install chocolatey, it will save you doing the step below
- [Chocolatey](https://docs.chocolatey.org/en-us/choco/setup) (if not already installed as part of NodeJs install)
- [Az CLI](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli)
- [Git For Windows](https://gitforwindows.org/)
- [Cmder](https://cmder.app/)
  - Cmder runs everything much faster than bash for Windows so please use this going forward
- K9S - "choco install k9s --trace"
- Make - "choco install make"
- [Kubectl](https://kubernetes.io/docs/tasks/tools/#kubectl)
  - if you need a specific (or older) client you can run "choco install kubernetes-cli --version=<version> --allow-downgrade"

## Source Control

- Login to [GitHub](https://github.com/) and create an account as your retailbook email address (e.g. <EMAIL>).
  - Your username should include your name like "jamescamden-rb"
- Once logged in an admin of the [RETAIL-BOOK-LIMITED organisation](https://github.com/RETAIL-BOOK-LIMITED) will need to add you as a member, send them your username & email.
- Once you have access clone the retail-book and retail-book-ui code repos (there are others but these are the main code repos)
    - Generate SSH rsa key without passphrase
        - ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
    - Add to GitHub in Profile -> Settings -> SSH and GPG keys
    - Test public key using
        - ssh -T **************
    - Clone using SSH not HTTPS, e.g.
        - cd C:\Users\<USER>\Source
        - <NAME_EMAIL>:RETAIL-BOOK-LIMITED/retail-book.git retail-book

## Build retail-book

- (In bash) cd to the root directory of your retail-book checkout and run:
  - "make tidy" (to download dependencies)
  - "make test"

- This can be done on a module by module basis by cd-ing into each source directory and running "go mod tidy"
  - go mod is kind of like maven for Java in that there is a top level "go.work" file and each module also has a "go.mod" file (similar to pom.xml files)

## Build retail-book-ui

- Ask another member of the team for the contents of their .env.local file and place it into your retail-book-ui checkout
  - One of the settings in this file "NEXT_PUBLIC_RETAILBOOK_API_URL" controls which backend the UI will send requests to, so you can point it to a local environment or qa etc.
- (In bash) cd to the root directory of your retail-book-ui checkout and run:
  - "npm install"
  - "npm run dev"
- Run "az login"
- Navigate to http://localhost:3000/ in your browser

## Connect to the kubernetes cluster

- Add your public key to the repo retail-book-infra in layers/infrastructure/staging/inputs.hcl
  - This key can be the same one that you use for git.
    - An easy way to find this is to use git gui: "git gui" -> "Help" -> "Show SSH Key" (remember to remove the domain from the end).
  - Example: https://github.com/RETAIL-BOOK-LIMITED/retail-book-infra/commit/dcb8633a89cba7b957baf31df5a966c43e5a520e
- Make sure you have the correct Azure subscription
  - List is at https://portal.azure.com/#view/Microsoft_Azure_Billing/SubscriptionsBlade
  - Run "az account set --subscription RetailBook-Dev"
- Follow the instructions [here](https://github.com/RETAIL-BOOK-LIMITED/retail-book-infra/blob/staging/docs/connect-to-aks.md) to get connected to the kubernetes cluster
- run "k9s" in cmder and you should now see a load of entries