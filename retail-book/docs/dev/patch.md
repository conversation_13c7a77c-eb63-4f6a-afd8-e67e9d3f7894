---
layout: page
title: Patch
nav_order: 3
parent: <PERSON>
has_children: false
permalink: /dev/patch
---

# Patching a release version

If you urgently need to release some changes to preprod/prod but the main branch has unreleasable changes on it you may want to perform a "patch".
This basically just means building a new version, but it will only be the source code at the time of the production release plus your changes. 

1. Identify the version running in the environment you want to patch. To get this you can describe any pod from any participant in k9s, e.g. Image: retailbookprod.azurecr.io/interappgateway:v1.1.0
2. Create two new branches names after the version:
    - git checkout tags/v1.1.0 -b hotfix/v1.1.0-merge
    - git checkout tags/v1.1.0 -b hotfix/v1.1.0
3. Apply and push your changes to the non-merge branch.
4. Raise a pull request to go from the non-merge branch to the merge branch.
   ![PR.PNG](./images/PR.PNG)
5. Review all the changes in the pull request and seek necessary approvals etc.
6. Merge the two branches - this will trigger the standard tag-on-pr.yaml git action.
7. When creating the tag as normal, if you are patching an old version then you may want to untick "Set as the latest release". For example if staging is on 1.3.0 and prod is on 1.2.0 and you are creating 1.2.1 as a patch, then don't tick it.
   ![Tick.PNG](./images/Tick.PNG)
8. Monitor the git action as normal and then use the CLI tool to release your new version - good luck!
