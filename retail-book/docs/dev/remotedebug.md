---
layout: page
title: Remote Debugging
nav_order: 2
parent: <PERSON>
has_children: false
permalink: /dev/remote
---

# Remote Debugging a process

1. k9s
2. :namespace
3. Go to the namespace and press enter
4. Set up port forwarding (shift + f) for both nats and postgres
5. :deploy
6. Navigate to the process you want to debug (e.g. command handler)
7. Press enter, then press s on the pod to open a rsh shell
8. env to get all the environment config for this deployment, copy it, then exit
9. Go back up to the deployment and press s.
10. Set the number of replicas to 0
11. :pods
12. Wait until the process (e,g, command handler) has gone
13. Run/debug in Intellij with the following env variables pasted in
    - APP_ASSETS_DIR=../common/assets;APP_ASSETS_PREFIX=../common;APP_NATS_HOST=localhost;APP_NATS_PORT=4222;APP_PORT=8080;APP_POSTGRES_DATABASE=retailbook;APP_POSTGRES_HOST=localhost;APP_POSTGRES_PORT=5432;APP_TEST_MODE=true
    - For command handler add APP_POSTGRES_WRITER_USERNAME=writer, APP_SYSTEM_ID from the config copied earlier, and APP_POSTGRES_WRITER_PASSWORD from the postgresql process
    - For viewserver add APP_POSTGRES_READER_USERNAME=reader, and APP_SYSTEM_ID and APP_POSTGRES_READER_PASSWORD from the config copied earlier