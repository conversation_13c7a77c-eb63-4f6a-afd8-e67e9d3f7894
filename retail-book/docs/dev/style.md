---
layout: page
title: Style Guide
nav_order: 1
parent: <PERSON>
has_children: false
permalink: /dev/style
---

# Go - Project Style Guide

## Styling

### Formatting
- Let the machine take care of formatting. Run `gofmt` prior to pushing code.
- Avoid unnecessary parenthesis. Don't use them in control structures.
- Reduce nesting. Inverse conditionals where possible to shorten.

### Comments
- Use `/* */` blocks comments only for package commentary, disabling blocks of code or for large logic comments (within bodies of expressions).
- Comment, where applicable, all public functions. Start the comment with the name of the function. Use C++ style `//` line comments.

### File Naming
- File names should be snake case. Use `file_encoding.go` over `fileEncoding.go`.
- Singular case, not plural. `encoding.go` over `encodings.go`.

### Package Naming
- The name of the package should be the same as the source directory `src/common/pkgs/encoding` becomes the `encoding` package.
- Avoid shadowing package names with variables names. Don't assign `encoding := SomeFunction()` when `encoding` is a package available in the scope.
- Package names should be named uniquely to avoid renaming at import site, be **lower** case, singular case, short, and informative.

### Function Naming
- Function names should be MixedCaps - with an exception for test functions which may contain underscores like `TestingFunction_OneTwoThree`.
- Keep function names short. Packages that export functions sometimes give enough contextual information. For e.g. standard library `once.Do()` instead of `once.DoOnce()`.
- Getters should be called after the variable name. I.e. exporting variable `type` should be `obj.Type()` not `obj.GetType()`.

### Variable Naming
- Use camelcase.
- Prefix unexported globals with `_`. For example `const _defaultHost = "commandhandler.svc.cluster.local"`
- Avoid naked hard-to-read parameters (`doSomething(true, false)`) by creating custom readable types (for e.g. `type Status int`) in their place.

### Receiver Naming
- Use short, non-generic names. Do not use `this`.
- Usually, you can use the first letter of the struct you are receiving. For example `(s *Something) Function()`.

### Long Lines
- Avoid long lines requiring horizontal scroll.
- Soft limit of `120` characters.

### Grouping & Ordering

- Group declarations where possible. For instance:

```go
import (
	"a"
	"b"
)
```

```go
const (
	Add = 1
	Remove = 2
)
```

- Imports should be grouped in the following order: 
  - standard library first.
  - everything else after.
- Exported functions should appear **first**.
- Functions should roughly be ordered by their call order.
- Functions should be grouped by receiver, for example:

```go
type myStruct struct { ... }

func newMyStruct() *myStruct {}

func (m *myStruct) MethodOne() {}

func (m *myStruct) MethodOne() {}
```

### Scoping
- Reduce scope of variables where possible. For example:

```go
if err := DoSomething(); err != nil {
	// handle
}
```

### Structs
- Omit zero values when initializing structs.
- Use field names when initializing structs. `Struct{"ParamOne", "ParamTwo"}` should be avoided.
- Do not use `new()` for structs.

### Maps
- Use `make` for empty maps over literals: `map[T]X{}`
- Use literals `map[T]X{ key: val }` over `make` for non-empty structs.

## Guidelines

### Testing
- Use table driven tests where possible.

### Init()
- Avoid `init()` where possible.

### Pointers to Interfaces
- Pointers to interfaces should almost never be used.

### Receivers 
- Stay consistent. If you use value receivers over pointer receivers (and vice-versa) - be consistent throughout. [See more here on pointers vs values](https://golang.org/doc/effective_go.html#pointers_vs_values).

### Defer
- Use defer as much as possible for cleanup. There is a very tiny, almost negligible overhead to this.

### Do not panic
- Do not panic, including `Fatal` in production code.
- Prefer returning errors over fatally logging.

### Errors
- For API errors, use the `apierrors` package.
- Always handle errors, even in deferred functions.
- Reuse the `err` variable if `err != nil` check has been done in the same scope.

## Optimization

### Capacity 
- Where possible, specify the capacity hint when **making maps**. This, unlike slices, will not perform pre-emptive allocation.
- Where possible, specify the capacity when **making slices**. This will perform a pre-emptive allocation.

### `strconv` over `fmt`
- When converting primitive types to string, use `strconv` over `fmt`.