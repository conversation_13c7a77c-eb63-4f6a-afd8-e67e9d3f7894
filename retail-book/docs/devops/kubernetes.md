---
layout: page
title: Kubernetes
parent: DevOps
nav_order: 5
has_children: false
permalink: /devops/kubernetes
---

# Kubernetes Control

kubectl can be used to connect to, query, and update kubernetes. If you are connecting to multiple environments, e.g. staging and preprod, you need to provide a context

You can view available config using

```
$ kubectl config view

apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: DATA+OMITTED
    server: https://retailbookpreprod-xxxxxxxx.xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.privatelink.uksouth.azmk8s.io:443
  name: retailbook-preprod
- cluster:
    certificate-authority-data: DATA+OMITTED
    server: https://retailbookstaging-xxxxxxxx.xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.privatelink.uksouth.azmk8s.io:443
  name: retailbook-staging
contexts:
- context:
    cluster: retailbook-preprod
    user: clusterUser_retailbook-preprod_retailbook-preprod
  name: retailbook-preprod
- context:
    cluster: retailbook-staging
    user: clusterUser_retailbook-staging_retailbook-staging
  name: retailbook-staging
current-context: retailbook-preprod
kind: Config
preferences: {}
users:
- name: clusterUser_retailbook-preprod_retailbook-preprod
  user:
    client-certificate-data: REDACTED
    client-key-data: REDACTED
    token: REDACTED
- name: clusterUser_retailbook-staging_retailbook-staging
  user:
    client-certificate-data: REDACTED
    client-key-data: REDACTED
    token: REDACTED
```

and then select the context, e.g. 

```
kubectl get pods --context retailbook-staging
```

## Logs

Run this command to see the latest logs, e.g. for the pod `loki-0`

```kubectl logs loki-0 --previous --namespace loki```

## Killing Processes

If a process gets stuck in Terminating state, you can force kill it using, e.g. 

```
kubectl delete pod loki-0 --namespace loki --context retailbook-staging --force [--grace-period=N]
kubectl delete pvc storage-loki-0 --namespace loki --context retailbook-staging --force [--grace-period=N]
```

If it is still stuck, you can edit to remove the finalizers, e.g. if it's waiting to be finalized in Argo CD

```
kubectl edit pvc storage-loki-0 --namespace loki --context retailbook-staging
```

and edit text file to remove the finalizers:

```
finalizers:
  -  kubernetes.io/pv-protection
```

then re-run the delete command


## Disk Access

You can get command-line access to the file system by:

```
kubectl debug node/aks-default-13814278-vmss00000g -it --image=ubuntu
```

On K9S, typing `:pvc` will give you the volume it is mounted on, e.g. `storage-loki-0` on volume `pvc-49c1dad3-7544-42d0-9547-88dbd08f84ed`

On K9S, going to the loki namespace, you can find the node of the `loki-0` pod, e.g. `aks-default-13814278-vmss00000g`

Then you can inspect the mounted volume by

```
kubectl debug node/aks-default-13814278-vmss00000g -it --image=ubuntu
df -h | grep pvc-49c1dad3-7544-42d0-9547-88dbd08f84ed
```


## Increase Database Size

Reference: https://learn.microsoft.com/en-us/azure/azure-arc/data/resize-persistent-volume-claim

Each participant defines and creates a mounted volume for postgres data, configured in `common-values.yaml` as `postgresql.primary.persistence.size`

If you want to update this size, argocd will not recreate the persistent volume when you change the value and the sync will fail

Steps required:

1. Update the new size in common-values.yaml and commit/push/merge
2. Allow Argo CD to sync/refresh (it will fail because statefulset cannot be updated)
3. Scale the replicas of the stateful set to 0
4. Patch the size of the persistent volume claim of the postgresql database
5. Delete the stateful set
6. Sync the application in Argo CD

Worked example for Cats PLC participant in staging

```
λ kubectl get sts --namespace participant-catsplc --context retailbook-staging
NAME         READY   AGE
etcd         1/1     63d
nats         1/1     141d
postgresql   1/1     141d

λ kubectl scale statefulsets postgresql --namespace participant-catsplc --context retailbook-staging --replicas=0

λ kubectl get pvc --namespace participant-catsplc --context retailbook-staging
NAME                 STATUS   VOLUME                                     CAPACITY   ACCESS MODES   STORAGECLASS   AGE
data-postgresql-0    Bound    pvc-2cb370c6-eecc-45a7-9f11-f2e030c71e40   8Gi        RWO            default        141d
nats-js-pvc-nats-0   Bound    pvc-8e3f6bfd-33ae-4604-b502-431522fa8a9f   10Gi       RWO            default        141d

λ kubectl patch pvc data-postgresql-0 --namespace participant-catsplc --context retailbook-staging --type merge --patch "{\"spec\":{\"resources\":{\"requests\":{\"storage\":\"16Gi\"}}}}"

λ kubectl delete statefulset postgresql --namespace participant-catsplc --context retailbook-staging
```