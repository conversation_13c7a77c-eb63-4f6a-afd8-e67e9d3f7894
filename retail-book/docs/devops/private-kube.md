---
layout: page
title: Private Kubernetes
nav_order: 1
parent: <PERSON><PERSON><PERSON>
has_children: false
permalink: /devops/pkube
---

# Private Kubernetes

Our Kubernetes is private, so even though we're on the same vlan, we can't get at it normally with kubectl.

Examples on how to admin the box:


## Uninstalling a helm chart

```bash
$ az aks command invoke --resource-group rg-rb-aks-uksouth-qa --name aks-dev \
       --command "helm uninstall global"
```

## Deploying a helm chart

```bash
$ az account set --subscription 56cec302-2315-4e6b-aa95-3848cdebd88a
$ az aks get-credentials --resource-group rg-rb-aks-uksouth-qa --name aks-dev
$ az aks command invoke --resource-group rg-rb-aks-uksouth-qa --name aks-dev \
       --command "helm install global  ./global-0.0.1.tgz" \
       --file global-0.0.1.tgz
```

```bash
$  az aks command invoke --resource-group rg-rb-aks-uksouth-qa --name aks-dev --command "helm install -f bank_env.yaml bank participant-0.0.1.tgz" --file participant-0.0.1.tgz --file qa/bank_env.yaml
```