---
layout: page
title: <PERSON>2<PERSON> Login Page
parent: DevOps
nav_order: 2
has_children: false
permalink: /devops/b2c-login-page
---

# B2C Login Page

In order to brand our B2C login page we use custom templates. These work by providing a magic <div/> which the B2C engine uses to inject its html into our template which is returned and displayed.
It has been necessary to write custom javascript on this template (using raw js) in order to get the functionality we needed. This is mainly because the 'user flow' templates that are provided by default in B2C have different behaviour to the 'generic one' that is, Microsoft have customised beahviour just for the
standard templates.

## Location

All the files required for the templates are stored in the repository:

https://github.com/RETAIL-BOOK-LIMITED/retail-book-b2c-templates

## Delivery

The templates need to be readable by B2C which requires a public available CORS enabled secure http server. If https isn't enabled it fails, and the same for CORS. The easiest method is to use a Azure Storage account for this as it supports public access, TLS and CORS.
We use the rbb2ctemplates storage account:

https://portal.azure.com/#@retailbook.com/resource/subscriptions/56cec302-2315-4e6b-aa95-3848cdebd88a/resourceGroups/rb-pub/providers/Microsoft.Storage/storageAccounts/rbb2ctemplates/overview

This is kept separate as its publically accessible.

## CORS

CORS is setup per the guide:

https://learn.microsoft.com/en-us/rest/api/storageservices/cross-origin-resource-sharing--cors--support-for-the-azure-storage-services

## New Envs
The login page template has 1 per environment. This because all links need to be full links, and not relative (the host isn't our website.) Adding a new env requires us created a <env>signup.html page, renaming any ui.<oldenv> links to ui.<newenv> and uploading into the blob.
