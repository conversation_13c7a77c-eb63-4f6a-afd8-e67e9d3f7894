---
layout: page
title: Helm
parent: DevOps
nav_order: 6
has_children: false
permalink: /devops/helm
---

# Helm

In retail-book-infra-tooling, kubernetes process configuration is built up using Helm charts. Dependencies are configured in `Chart.yaml` and override values are by default configured in `values.yaml`

## Installation

As administrator:

```
choco install kubernetes-helm
```

## Execution

To view the generated yaml locally before deployment, cd to the relevant directory, download the dependencies configured in Chart.yaml, then generate the template

```
cd Source\retail-book-infra-tooling\staging\internal\loki
helm dependency build ## only required the first time, or if dependencies change
helm template .
```

If you need to pass in other variables to the helm charts, you can specify a comma-separated list of files, e.g. 

```
cd Source\retail-book-infra-tooling\staging\retailbook\participant
helm dependency build
helm template . --values .\common-values.yaml,..\global\values.yaml,.\participants\catsplc.yaml
```