---
layout: page
title: Container Registry
parent: DevOps
nav_order: 4
has_children: false
permalink: /devops/containers
---

# Azure Container Registry

This is a brief overview of what is done in terraform to onboard a container registry. See the Azure section to see how this links to github.

## azurerm_container_registry

The container registry follows a naming pattern (need to be unique across all of azure).
- Production environments as `rbclusterprod`.
- Non-production environments as `rbclusterdev`.

The location and the resource_group_name must follow the **RG-Development** resource group metadata.

The SKU is also supplied depending on the environment. For production we use the `Premium` SKU while for non production we use `Basic`.
We may choose to change this and use `Premium` for both as it is the only SKU that provides private access. To be explored.

## azuread_application

This is the first step of the `az ad sp create-for-rbac ...` command. It creates an app registration.

It is important to create this application in the correct local tenant (i.e. owner). The tenant ID for our tenancy is `6add0a26-d7dd-4271-8acf-b52c16ad9ea4`.

## azuread_service_principal

To create the service principal, only the app registration ID needs to be supplied (from the section above).

## azuread_service_principal_password

Azure auto-generates the password for the service principal (so there is no need to generate a random string within terraform).
The service principal ID from the previous step is supplied.

## azurerm_role_assignment

Service principal now exists, but requires the correct contribution rights to be able to push to the azure container registry.

The scope is therefore: `azurerm_container_registry.acr.id`, the definition of the role `Contributor` and the principal ID `azuread_service_principal.main.id`.
