---
layout: page
title: B2C Initial Creation
parent: DevOps
nav_order: 3
has_children: false
permalink: /devops/b2c
---

# B2C Setup

## Custom Claims
### User Attributes
#### Add
   
| Name | Type | Description |
| --- | --- | --- |
| SystemId | String | The name of the system this user will connect to |  
 | Superuser | boolean | Is this user a super user |

## User flow Create

* Click New User Flow (sign-in)
  * Email signup (only)
  * MFA Email
  * Returned Claims
    * Set to Email Address, Display Name, Superuser and SystemId
  * Click Create

* Edit Properties on this flow
  * Enable Javascript on sign-in page == true
  * Set Self-Service password reset == true
  * Set forced password reset == true

* Page Layout
  * Set https://rbb2ctemplates.blob.core.windows.net/templates/<env>signup.html as custom page for (Sign-in, error, update expired, forgot and change)


## App Registrations

### Backend API
 * Create (backend-api)
   * Who can use this API (Accounts in any identity provider or organizational directory)
   * Click Register

 * Add Application ID URI  
    * Set URI to "/api"           
    * Add Scope
      * Name: access
      * "Access API"
      * Allows the app to access the pai
                 
### Create GUI App Registration
* Create (gui)
  * Add Redirect URI
    * https://ui.<env>.retailbook.com/api/auth/callback/azure-ad-b2c
    * https://ui.<env>.retailbook.com/api/auth/callback
			
  * API Permissions
    * Add -> APIs my organization users -> backend-api -> access -> Add Permission
      * click on the row; and click on Grant Admin Consent
			
  * Create a client secret:
    * call it whatver, save the value
                              
### Create UserService App registration
* create a secret
* API Permissions
* Microsoft Graph -> App Perms -> User.ReadWrite.All -> Add
  * Grant Admin consent