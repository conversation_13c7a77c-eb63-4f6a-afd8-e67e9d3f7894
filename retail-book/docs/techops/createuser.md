---
layout: page
title: Create User
nav_order: 1
parent: <PERSON><PERSON><PERSON>
has_children: false
permalink: /techops/createuser
---

# Create a new user at a participant

1. Open k9s
2. Set up port-forwarding for NATS for the participant you are interested in, e.g.
   - :ns
   - select participant-catsplc
   - scroll to nats-0 and shift-f
3. Open up the admin tool
    - ```cd C:\Users\<USER>\source\retail-book\src\admin\cmd```
    - ```go run cli.go```
4. "Tech Ops"
5. "Create User"
6. Select your name and magic email address, e.g.
    - <PERSON>
    - <EMAIL>
    - Manager
    - Gatekeeper Y if you want to receive emails, otherwise can be toggled in the UI later
7. The admin tool doesn't confirm it's been successful, but email is sent to your retailbook.com address
8. Stop port-forwarding NATS
   - scroll to nats-0 and f and then ctrl-d
9. Log into the UI at localhost:3000, select "Forgot your password" and take it from there

