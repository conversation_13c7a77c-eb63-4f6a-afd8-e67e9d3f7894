---
layout: page
title: NATS
nav_order: 1
parent: Support
has_children: false
permalink: /support/nats
---

# NATS CLI

For interacting with NATS directly you will need the natscli client: https://github.com/nats-io/natscli

For windows running this command should be fine: "go install github.com/nats-io/natscli/nats@latest"

# Remove an event (e.g. poison message)

Ideally you should never have to do this and this decision should not be taken lightly

Please make sure you have considered the below:
- Can the issue be resolved by a code fix and do we have time to do it?
- Skipping messages in the interapp gateway could cause state to become inconsistent between different participants
- A particular event/command may produce other events/commands or a chain of other events/commands when processed, so skipping a message could cause an action to be partially actioned which may be difficult to undo

Once you are sure you want to proceed:

1. Identify from the application logs which message is causing the issue and take note of its correlation_id and type
   ![nats_log.PNG](./images/nats_log.PNG)
2. Set up your ssh forwarding as normal
2. Port forward nats for the impacted participant
   - k9s
   - :ns
   - navigate to the relevant participant and press enter
   - navigate to nats and press shift + f (then press enter alot to accept the default ports)
  ![nats_k9s.PNG](./images/nats_k9s.PNG)
3. Leaving your k9s terminal open, open another terminal and test your nats connection
   - "nats stream ls"
  ![nats_ls.PNG](./images/nats_ls.PNG)
4. Find the message you want to remove and take note of its sequence number, bear in mind it is not necessarily the last message published to the event stream - you will need to search for your specific message
   - "nats stream view events `--`since=1h"
      - if you know the message type you can also include the subject like "nats stream view events `--`since=1h `--`subject=event.orderSummaryRejected"
      - to view what subjects are available on the 'events' stream you can do "nats stream subjects events"
  ![nats_view.PNG](./images/nats_view.PNG)
5. "nats stream rmm events ${id}"
   - e.g. nats stream rmm events 6646

There is also a "nats stream purge" command to clear an entire stream, but this should really never be used - not even in qa.