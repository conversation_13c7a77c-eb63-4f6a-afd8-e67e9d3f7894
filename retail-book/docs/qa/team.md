---
layout: page
title: Team
nav_order: 2
parent: <PERSON><PERSON>
has_children: false
permalink: /qa/team
---

# Team

### Add team member dialog

* GIVEN i'm a manager
* WHEN I click "add team member"
* THEN the add team member dialog is shown

### Add team meber dialog radio buttons

* GIVEN I've the 'add team member dialog up'
  * AND Editor is selected
* WHEN I select Manager
* THEN Manager is selected, and Editor is not selected


* GIVEN I've the 'add team member dialog up'
    * AND Manager is selected
* WHEN I select Editor
* THEN Editor is selected, and Manager is not selected


### Add manager

* GIVEN i've the "add team member dialog up"
  * AND i've filled out the name, email fields
  * AND i've set role to be manager
  * AND gatekeeper to false
* WHEN I click Invite
* THEN The user is created in B2C
  * AND The user is emailed an invitation
  * AND that user is permissioned as a manager

### Add Gatekeeper 

* GIVEN i've the "add team member dialog up"
    * AND i've filled out the name, email fields
    * AND i've set role to be manager
    * AND gatekeeper to true
* WHEN I click Invite
* THEN The user is created in B2C
    * AND The user is emailed an invitation
    * AND the user is permissioned as a manager
    * AND the user is setup to be a gatekeeper

### Add Editor

* GIVEN i've the "add team member dialog up"
    * AND i've filled out the name, email fields
    * AND i've set role to be editor
    * AND gatekeeper to false
* WHEN I click Invite
* THEN The user is created in B2C
    * AND The user is emailed an invitation
    * AND that user is permissioned as an editor

### Update user
#### Manager -> Editor

* GIVEN A user has been created whos a manager
  * AND the edit team member dialog is up
* WHEN I select editor
  * AND click Save
* THEN the user is revoked manager permission

#### Editor -> Manager 

* GIVEN A user has been created who's an editor
    * AND the edit team member dialog is up
* WHEN I select manager
    * AND click Save
* THEN the user is granted manager permission

##### Gatekeeper off -> on

* GIVEN A user has been created who isn't a gatekeeper
    * AND the edit team member dialog is up
* WHEN I select gatekeeper
    * AND click Save
* THEN the user becomes a gatekeeper

##### Gatekeeper on -> off

* GIVEN A user has been created who is a gatekeeper
    * AND the edit team member dialog is up
* WHEN I select gatekeeper
    * AND click Save
* THEN the user is no longer a gatekeeper

### Resend Invite

* GIVEN A user has been created
* WHEN Resend invite is selected
* THEN the user is re-sent an invite to login.

### Delete

* GIVEN a user has been created
* WHEN I select delete
* THEN the user is set to inactive
  * AND the user isn't shown in the manager team screen
