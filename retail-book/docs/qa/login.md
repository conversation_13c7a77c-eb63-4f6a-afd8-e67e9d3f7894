---
layout: page
title: Login
nav_order: 2
parent: <PERSON><PERSON>
has_children: false
permalink: /qa/login
---


# Login QA Test cases

### Invalid Email Address

* GIVEN I put in an invalid email address 
  * AND leave password empty 
* WHEN I click sign in
* THEN an error message is displayed (Please enter a valid email address)

### Invalid Password

* GIVEN I put in an invalid password
  * AND add a valid email address  
* WHEN I click sign in
* THEN an error message is displayed (Your password is incorrect)  

### Empty Password

* GIVEN I put in an empty password
  * AND a valid email address
* WHEN I click sign in
* THEN an error message is displayed ("Please enter your password")

### Incorrect email address

* GIVEN I put in an incorrect email address
* WHEN I click sign in
* THEN An error message is displayed (We can't seem to find your account)

### Log in

* GIVEN I have entered a correct email, and password
* WHEN I click sign in
* THEN I'm redirected to the offer page

### Reset Password

* WHEN I click "Forgot your password"
* THEN I'm redirected to the forgot your password flow.

### Reset - Password - Email verification

* GIVEN i've clicked, forgot your password
  * AND entered a valid email address
* WHEN I click send verification code
    * THEN I'm emailed a code

### Reset - Password - Email verification, password reset

* GIVEN I have a verification code ( as per "Reset - Password - Email verification" )
  * AND I have entered the code in the box
* WHEN I click 'Verify Code'
  * THEN I get "E-mail address verified. You can now continue"

### Reset - Password - Email verification, send again

* GIVEN I have already tried to send a verification code ones
* WHEN I click "send code again"
* THEN I'm emailed a new code

### Reset - Password - Email verification, and Reset

* GIVEN I have a verified my email
  * AND entered a new password
* WHEN I click continue
  * THEN i'm redirected to the home page.

  
 






