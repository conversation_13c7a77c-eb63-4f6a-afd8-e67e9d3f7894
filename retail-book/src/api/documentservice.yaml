topicPrefix: "documentservice"

components:
  schemas:
    container:
      type: object
      description: The container request (all required information for the creation to occur).
      required:
        - system_id
        - entity_id
      properties:
        system_id:
          type: string
          description: The system ID (i.e. the owner) of the container (must match the authentication material)
        entity_id:
          type: string
          description: The ID of the entity to create the document container for (offer, etc?)
        restricted_to:
          type: string
          description: Applicable only for specific containers. Who (i.e. what system ID) is the restricted assignee for this container?

    permit:
      type: object
      description: The permit request (all required information to create or revoke a permit).
      required:
        - container
        - target_system_id
        - role
      properties:
        container:
          type: object
          schema:
            "$ref": "#/components/schemas/container"
        target_system_id:
          type: string
          description: The system ID that receives/is revoked this permit.
        role:
          type: string
          description: An enum, 'read' or 'write' describing the type of role to permit.

createGlobalContainer:
  parameter: "container"
  description: Creates a new (multiple recipient global access) azure container within Azure blob storage.

createSpecificContainer:
  parameter: "container"
  description: Creates a new (multiple recipient specific access) azure container within Azure blob storage.

createPermit:
  parameter: "permit"
  description: Creates a new permit (i.e. assigns a role to a service principal of choice) for an existing container (global or specific).

revokePermit:
  parameter: "permit"
  description: Revokes a permit from a container.