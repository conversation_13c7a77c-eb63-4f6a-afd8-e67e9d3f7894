openapi: 3.0.3
info:
  title: Event Handler
  description: Schema for all the event views; these are rest endpoints to accept an event (and then do something with it.)
  version: 1.0.0

components:
  schemas:
    header:
      type: object
      required:
        - timestamp
      properties:
        timestamp:
          type: string
        origin:
          type: string
        target:
          type: string

    id-event:
      type: object
      required:
        - header
        - id
      properties:
        header:
          $ref: '#/components/schemas/header'
        id:
          type: string

    ack:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    nack:
      type: object
      required:
        - message
      properties:
        message:
          type: string

    eventType:
      type: string
      description: "Different types of event types"
      enum:
        - offerCreated
        - offerRemoved
        - offerTimestampRoleUpdated
        - offerInserted
        - offerAmended
        - offerStatusUpdated
        - offerInvited
        - offerInviteDrafted
        - offerNotified
        - organisationAmended
        - organisationDeleted
        - permCreated
        - permGranted
        - permRevoked
        - documentCreated
        - documentInserted
        - documentAmended
        - documentDeleted
        - entityEmailed
        - orderBookCreated
        - orderBookInserted
        - orderBookAppended
        - orderBookFinalized
        - allocationBookCreated
        - allocationBookAppended
        - allocationBookFinalized
        - allocationBookInserted
        - userAdded
        - userAmended
        - userActivated
        - userInvited
        - userCreationFailed
        - notificationCreated
        - notificationRead
        - notificationUnread
        - notificationDeleted
        - orderSummaryCreated
        - orderSummaryInserted
        - orderSummaryPatched
        - orderSummaryAccepted
        - orderSummaryRejected
        - orderSummaryConfirmed
        - allocationSummaryCreated
        - offerAllocationCreated
        - offerAllocationCleared
        - offerAllocationNotified
        - offerTermsAccepted
        - offerTermsInserted
        - taskCreated
        - taskUpdated
        - wallCrossInfoCreated
        - wallCrossInfoUpdated
        - wallCrossInviteCreated
        - wallCrossInviteInserted
        - wallCrossInviteResponded
        - wallCrossInviteGatekeeperVoided
        - offerInsiderCreated
        - offerInsiderAmended
        - offerCleansed
        - orderMetricsUpdated
        - allocationMetricsUpdated
        - allocationMetricsPublished
        - settlementInstructionCreated
        - settlementInstructionUpdated
        - settlementInstructionDeleted
        - settlementBookCreated
        - settlementObligationCreated
        - settlementObligationSettled


    offerRemoved:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          description: The name of the offer, this will be sent on notifications and be used

    offerTimestampRoleUpdated:
      type: object
      required:
        - userID
      properties:
        userID:
          type: string
          description: The userID of the user whos role has been assigned

    offerCreated:
      type: object
      required:
        - id
        - name
        - type
      properties:
        id:
          type: string
        name:
          type: string
          description: The name of the offer, this will be sent on notifications and be used
        type:
          type: string
          description: The type of the offer
        issued_by:
          type: string
        shareholders_only:
          type: boolean
        inside_information:
          type: boolean
        created_date:
          type: string
          format: "date-time"
        registration_date:
          type: string
          format: "date-time"
        open_date:
          type: string
          format: "date-time"
        close_date:
          type: string
          format: "date-time"
        status:
          type: string
        min_order_amount:
          type: number
        description:
          type: string
        details:
          type: string
        allocation_principles:
          type: string
        settlement_details:
          type: string
        currency:
          type: string
        isin:
          type: string
        sedol:
          type: string
        ticker:
          type: string
        crest_id:
          type: string
        raise_amount:
          type: number
        offer_price:
          type: number
        settlement_date:
          type: string
          format: "date-time"

    offerInserted:
      type: object
      required:
        - id
        - name
        - type
        - is_launched
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        external_id:
          "$ref": "#/components/schemas/identifier"

        name:
          type: string
          description: The name of the offer, this will be sent on notifications and be used
        type:
          type: string
          description: The type of the offer
        issued_by:
          type: string
        shareholders_only:
          type: boolean
        inside_information:
          type: boolean
        registration_date:
          type: string
          format: "date-time"
        created_date:
          type: string
          format: "date-time"
        open_date:
          type: string
          format: "date-time"
        close_date:
          type: string
          format: "date-time"
        status:
          type: string
        min_order_amount:
          type: number
        description:
          type: string
        details:
          type: string
        allocation_principles:
          type: string
        settlement_details:
          type: string
        currency:
          type: string
        isin:
          type: string
        sedol:
          type: string
        ticker:
          type: string
        crest_id:
          type: string
        raise_amount:
          type: number
        offer_price:
          type: number
        documents:
          type: array
          items:
            "$ref": "#/components/schemas/documentCreated"
        wallcrossinfo_id:
          "$ref": "#/components/schemas/identifier"
        is_launched:
          type: boolean
        price_range_low:
          type: number
        price_range_high:
          type: number
        settlement_date:
          type: string
          format: "date-time"

    offerAmended:
      type: object
      required:
        - id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        name:
          type: string
          description: The name of the offer, this will be sent on notifications and be used
        type:
          type: string
          description: The type of the offer
        issued_by:
          type: string
        shareholders_only:
          type: boolean
        inside_information:
          type: boolean
        issuer_logo:
          type: string
        registration_date:
          type: string
          format: "date-time"
        open_date:
          type: string
          format: "date-time"
        close_date:
          type: string
          format: "date-time"
        min_order_amount:
          type: number
        description:
          type: string
        details:
          type: string
        allocation_principles:
          type: string
        settlement_details:
          type: string
        currency:
          type: string
        isin:
          type: string
        sedol:
          type: string
        ticker:
          type: string
        crest_id:
          type: string
        raise_amount:
          type: number
        status:
          type: string
        offer_price:
          type: number
        cleansing_time:
          type: string
          format: "date-time"
        price_range_low:
          type: number
        price_range_high:
          type: number
        security_name:
          type: string
        settlement_date:
          type: string
          format: "date-time"

    offerStatusUpdated:
      type: object
      required:
        - id
        - old_state
        - new_state
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
          description: The ID of the offer that was updated
        old_state:
          type: string
          description: The old status of the offer
        new_state:
          type: string
          description: New status of the offer

    documentInserted:
      type: object
      required:
        - id
        - offer_id

      properties:
        id:
          type: string

        offer_id:
          $ref: "#/components/schemas/identifier"

        title:
          type: string
          description: Title of the document

        filename:
          type: string
          description: Filename of the document

        type:
          type: string
          description: "The document type, blank if this isn't changing"

        mime_type:
          type: string
          description: "The mime type"

        size:
          type: number
          description: "The size of the file"

        status:
          type: string
          description: "The document status"

    documentCreated:
      type: object
      required:
        - id
        - offer_id

      properties:
        id:
          type: string

        offer_id:
          $ref: "#/components/schemas/identifier"

        title:
          type: string
          description: Title of the document

        filename:
          type: string
          description: Filename of the document

        type:
          type: string
          description: "The document type, blank if this isn't changing"

        mime_type:
          type: string
          description: "The mime type"

        size:
          type: number
          description: "The size of the file"

        status:
          type: string
          description: "The document status"

    documentDeleted:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          type: string

        offer_id:
          $ref: "#/components/schemas/identifier"

    documentAmended:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          type: string

        offer_id:
          $ref: "#/components/schemas/identifier"

        title:
          type: string
          description: Title of the document
        filename:
          type: string
          description: Filename of the document

        type:
          type: string
          description: "The document type, blank if this isn't changing"

        mime_type:
          type: string
          description: "The mime type"

        size:
          type: number
          description: "The size of the file"

        status:
          type: string
          description: "The document status"

    permCreated:
      type: object
      required:
        - policy_name
        - resource
        - action
      properties:
        policy_name:
          type: string
        resource:
          type: string
        action:
          type: string

    permGranted:
      type: object
      required:
        - role_name
        - scope
        - user
      properties:
        role_name:
          type: string
        scope:
          type: string
        user:
          type: string

    permRevoked:
      type: object
      required:
        - role_name
        - scope
        - user
      properties:
        role_name:
          type: string
        scope:
          type: string
        user:
          type: string

    offerInviteDrafted:
      type: object
      required:
        - id
        - intermediaries
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        intermediaries:
          type: array
          items: {
            type: string
          }

    offerInvited:
      type: object
      required:
        - id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer:
          "$ref": "#/components/schemas/offerInserted"

    detailedOrder:
      type: object
      required:
        - client_order_ref
        - notional_value
      properties:
        client_order_ref:
          type: string
        notional_value:
          type: number
        existing_holding:
          type: number
        tax_wrapper:
          type: boolean

    detailedAllocation:
      type: object
      required:
        - client_order_ref
        - existing_holding
        - commission_due
        - tax_wrapper
        - notional_value
        - order_quantity
        - num_applications
      properties:
        client_order_ref:
          type: string
        notional_value:
          type: number
        existing_holding:
          type: number
        commission_due:
          type: boolean
        tax_wrapper:
          type: boolean
        allocation:
          type: number
        order_quantity:
          type: number
        num_applications:
          type: number

    entityEmailed:
      type: object
      required:
        - subject
        - to
        - template_type
        - msg
      properties:
        subject:
          type: string
          description: the subject of the email
        to:
          type: string
          description: the recipient of the email
        cc:
          type: string
          description: the carbon copy recipients of the email
        bcc:
          type: string
          description: the blind carbon copy recipients of the email
        template_type:
          type: string
          description: the name of the template to use
        msg:
          type: array
          description: the data to feed into the supplied template
          items:
            $ref: '#/components/schemas/emailContent'

    emailContent:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
        value:
          type: object

    taskCreated:
      type: object
      required:
        - id
        - status
      properties:
        id:
          type: string
        status:
          type: string

    taskUpdated:
      type: object
      required:
        - id
        - status
      properties:
        id:
          type: string
        status:
          type: string
        message:
          type: string

    wallCrossInfoCreated:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          type: string
        offer_id:
          type: string

    wallCrossInfoUpdated:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          type: string
        offer_id:
          type: string

    wallCrossInviteCreated:
      type: object
      required:
        - id
        - info_id
        - time_sent
        - status
        - wallcross_container_id
        - intermediary_system_id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        info_id:
          "$ref": "#/components/schemas/identifier"
        subject:
          type: string
        consent_to_cross:
          type: string
        time_sent:
          type: string
          format: "date-time"
        status:
          type: string
        wallcross_container_id:
          type: string
        offer_outline_document_id:
          type: string
        intermediary_system_id:
          type: string

    wallCrossInviteInserted:
      type: object
      required:
        - id
        - infoId
      properties:
        id:
          type: string
        infoId:
          type: string

    wallCrossInviteResponded:
      type: object
      required:
        - id
        - time_responded
        - user_name
        - user_email
        - status
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        time_responded:
          type: string
          format: "date-time"
        user_name:
          type: string
        user_email:
          type: string
        user_id:
          type: string
        status:
          type: string

    wallCrossInviteGatekeeperVoided:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          type: string
        offer_id:
          type: string

    offerCleansed:
      type: object
      required:
        - id
        - cleansed_time
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        cleansed_time:
          type: string
          format: "date-time"

    orderBookInserted:
      type: object
      required:
        - offer_id
        - order_book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        order_book_id:
          $ref: '#/components/schemas/identifier'

    orderBookCreated:
      type: object
      required:
        - offer_id
        - order_book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        order_book_id:
          $ref: '#/components/schemas/identifier'

    allocationBookCreated:
      type: object
      required:
        - offer_id
        - allocation_book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        allocation_book_id:
          $ref: '#/components/schemas/identifier'

    allocationBookAppended:
      type: object
      required:
        - offer_id
        - allocation_book_id
        - allocations
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        allocation_book_id:
          $ref: '#/components/schemas/identifier'
        allocations:
          type: array
          items:
            '$ref': '#/components/schemas/detailedAllocation'

    allocationBookInserted:
      type: object
      required:
        - offer_id
        - allocation_book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        allocation_book_id:
          $ref: '#/components/schemas/identifier'

    orderBookAppended:
      type: object
      description: Following an initial order book advertisement; we now append data.
      required:
        - offer_id
        - order_book_id
        - orders
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        order_book_id:
          $ref: '#/components/schemas/identifier'
        orders:
          type: array
          items:
            '$ref': '#/components/schemas/detailedOrder'

    orderBookFinalized:
      type: object
      description: Following an initialization of an order book + multiple appends; the book is ready to go into an active and valid state.
      required:
        - offer_id
        - order_book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        order_book_id:
          $ref: '#/components/schemas/identifier'

    allocationBookFinalized:
      type: object
      description: Following an initialization of an allocation book + multiple appends.
      required:
        - offer_id
        - allocation_book_id
        - shareholding_total
        - non_shareholding_total
        - offer_allocation_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        allocation_book_id:
          $ref: '#/components/schemas/identifier'
        offer_allocation_id:
          $ref: '#/components/schemas/identifier'
        shareholding_total:
          "$ref": "#/components/schemas/allocationTotal"
        non_shareholding_total:
          "$ref": "#/components/schemas/allocationTotal"
        intermediary:
          type: string

    settlementInstructionCreated:
      type: object
      required:
        - id
        - timestamp
        - intermediary
        - settlement_reference
      properties:
        id:
          type: string
        timestamp:
          type: string
          format: "date-time"
        intermediary:
          type: string
        settlement_reference:
          type: string

    settlementBookCreated:
      type: object
      required:
        - id
        - offer_id
        - created
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          "$ref": "#/components/schemas/identifier"
        created:
          type: string
          format: "date-time"

    settlementObligationCreated:
      type: object
      required:
        - id
        - settlement_book_id
        - offer_id
        - created
        - delivery_type
        - counterparty
        - settlement_reference
        - security_isin
        - security_name
        - settlement_date
        - quantity
        - open_quantity
        - cash_amount
        - open_cash_amount
        - price
        - currency
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        settlement_book_id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          "$ref": "#/components/schemas/identifier"
        created:
          type: string
          format: "date-time"
        delivery_type:
          type: string
        counterparty:
          type: string
        settlement_reference:
          type: string
        security_isin:
          type: string
        security_name:
          type: string
        settlement_date:
          type: string
          format: "date-time"
        quantity:
          type: number
        open_quantity:
          type: number
        cash_amount:
          type: number
        open_cash_amount:
          type: number
        price:
          type: number
        currency:
          type: string

    settlementObligationSettled:
      type: object
      required:
        - id
        - open_quantity
        - open_cash_amount
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        open_quantity:
          type: number
        open_cash_amount:
          type: number

    settlementInstructionUpdated:
      type: object
      required:
        - id
        - timestamp
        - intermediary
        - settlement_reference
      properties:
        id:
          type: string
        timestamp:
          type: string
          format: "date-time"
        intermediary:
          type: string
        settlement_reference:
          type: string

    settlementInstructionDeleted:
      type: object
      required:
        - id
        - timestamp
      properties:
        id:
          type: string
        timestamp:
          type: string
          format: "date-time"

    userAdded:
      type: object
      required:
        - id
        - email
      properties:
        id:
          type: string
        email:
          type: string
        name:
          type: string
        role:
          type: string
          enum:
            - manager
            - editor
        gatekeeper:
          type: boolean
        status:
          type: string

    userActivated:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    userCreationFailed:
      type: object
      required:
        - id
        - message
      properties:
        id:
          type: string
        message:
          type: string

    userInvited:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    userAmended:
      type: object
      required:
        - id
      properties:
        email:
          type: string
        name:
          type: string
        status:
          type: string
        id:
          type: string
        ext_id:
          type: string
        role:
          type: string
          enum:
            - manager
            - editor
        gatekeeper:
          type: boolean

    identifier:
      type: object
      required:
        - id
        - id_type
      properties:
        id:
          type: string
          description: The ID of the resource that we are identifying.
        id_type:
          type: string
          description: Defines the location of the resource that this identifier relates to (for example offers are local to the bank but remote to the intermediary).
          x-go-custom-tag: validate:"min=3,oneof=remote local"
        system_id:
          type: string
          description: Optional identifier that describes the system if the id relates to a remote resource. Otherwise empty.

    notificationCreated:
      type: object
      required:
        - id
        - user_id
        - status
        - created
        - title
        - message
        - type
        - action_label
        - category
      properties:
        id:
          type: string
        user_id:
          type: string
        status:
          type: string
        created:
          type: string
          format: "date-time"
        title:
          type: string
        message:
          type: string
        type:
          type: string
        action_label:
          type: string
        action_path:
          type: string
        category:
          type: string
        target_id:
          type: string

    notificationRead:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    notificationUnread:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    notificationDeleted:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    offerNotified:
      type: object
      required:
        - id
        - notificationType
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        notificationType:
          type: string
        message:
          type: string
        subject:
          type: string


    summaryTotal:
      type: object
      required:
        - applications
        - notional_value

      properties:
        applications:
          type: number
        notional_value:
          type: number
        existing_holding:
          type: number

    allocationTotal:
      type: object
      required:
        - applications
        - allocation_quantity
        - notional_value
        - order_quantity
        - num_orders
        - allocated_orders
        - unallocated_orders
        - allocation_value
      properties:
        applications:
          type: number
        notional_value:
          type: number
        order_quantity:
          type: number
        num_orders:
          type: number
        allocated_orders:
          type: number
        unallocated_orders:
          type: number
        allocation_value:
          type: number
        allocation_quantity:
          type: number

    orderSummaryInserted:
      type: object
      required:
        - id
        - offer_id
        - offer_name
        - external_id
        - order_type
        - status
        - totals
        - created
        - entered_by
        - entered_by_email
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_name:
          type: string
        external_id:
          "$ref": "#/components/schemas/identifier"
        order_book_id:
          "$ref": "#/components/schemas/identifier"
        status:
          type: string
        order_type:
          type: string
        entered_by:
          type: string
        entered_by_email:
          type: string
        created:
          type: string
          format: date-time
        totals:
          "$ref": "#/components/schemas/summaryTotal"
        shareholder:
          "$ref": "#/components/schemas/summaryTotal"
        non-shareholder:
          "$ref": "#/components/schemas/summaryTotal"
        previous_id:
          "$ref": "#/components/schemas/identifier"

    orderSummaryCreated:
      type: object
      required:
        - id
        - offer_id
        - offer_name
        - order_type
        - status
        - totals
        - created
        - entered_by
        - entered_by_email
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_name:
          type: string
        order_book_id:
          "$ref": "#/components/schemas/identifier"
        status:
          type: string
        order_type:
          type: string
        created:
          type: string
          format: date-time
        entered_by:
          type: string
        entered_by_email:
          type: string
        totals:
          "$ref": "#/components/schemas/summaryTotal"
        shareholder:
          "$ref": "#/components/schemas/summaryTotal"
        non-shareholder:
          "$ref": "#/components/schemas/summaryTotal"

    allocationSummaryCreated:
      type: object
      required:
        - id
        - offer_allocation_id
        - status
        - totals
        - created
        - entered_by
        - shareholder
        - non_shareholder
        - external_id
        - external_system_id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        allocation_book_id:
          "$ref": "#/components/schemas/identifier"
        offer_allocation_id:
          "$ref": "#/components/schemas/identifier"
        status:
          type: string
        created:
          type: string
          format: date-time
        entered_by:
          type: string
        totals:
          "$ref": "#/components/schemas/allocationTotal"
        shareholder:
          "$ref": "#/components/schemas/allocationTotal"
        non_shareholder:
          "$ref": "#/components/schemas/allocationTotal"
        external_id:
          type: string
        external_system_id:
          type: string

    offerAllocationCreated:
      type: object
      required:
        - id
        - date_created
        - entered_by
        - shareholdings
        - non_shareholdings
        - totals
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        date_created:
          type: string
          format: date-time
        entered_by:
          type: string
        shareholdings:
          "$ref": "#/components/schemas/allocationTotal"
        non_shareholdings:
          "$ref": "#/components/schemas/allocationTotal"
        totals:
          "$ref": "#/components/schemas/allocationTotal"

    offerAllocationCleared:
      type: object
      required:
        - offer_id
        - offer_allocation_id
      properties:
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_allocation_id:
          "$ref": "#/components/schemas/identifier"

    offerAllocationNotified:
      type: object
      required:
        - offer_id
        - offer_allocation_id
      properties:
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_allocation_id:
          "$ref": "#/components/schemas/identifier"

    orderSummaryPatched:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          type: string
        status:
          type: string

    orderSummaryAccepted:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          "$ref": "#/components/schemas/identifier"
        status:
          type: string
        previous_id:
          "$ref": "#/components/schemas/identifier"

    orderSummaryRejected:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          "$ref": "#/components/schemas/identifier"
        status:
          type: string

    orderSummaryConfirmed:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          type: string
        status:
          type: string

    offerTermsAccepted:
      type: object
      required:
        - offer_id
        - accepted_time
      properties:
        offer_id:
          "$ref": "#/components/schemas/identifier"
        accepted_time:
          type: string
          format: date-time
        receive_commission:
          type: boolean
        retail_offer_notice_id:
          type: string
        accepted_by:
          type: string
        accepted_by_email:
          type: string

    offerTermsInserted:
      type: object
      required:
        - offer_id
        - system_id
      properties:
        offer_id:
          "$ref": "#/components/schemas/identifier"
        system_id:
          type: string
        accepted_time:
          type: string
          format: date-time
        receive_commission:
          type: boolean
        retail_offer_notice_id:
          type: string
        accepted_by:
          type: string
        accepted_by_email:
          type: string

    organisationAmended:
      type: object
      properties:
        name:
          type: string
          description: The full legal name of the organisation

    organisationDeleted:
      type: object

    offerInsiderCreated:
      type: object
      required:
        - event
        - status
        - offer_id
        - user_name
        - user_email
      properties:
        offer_id:
          type: string
        user_name:
          type: string
        user_email:
          type: string
        event:
          type: string
        status:
          type: string
        inside_time:
          type: string
          format: date-time

    offerInsiderAmended:
      type: object
      required:
        - offer_id
        - user_name
        - user_email
      properties:
        offer_id:
          type: string
        user_name:
          type: string
        user_email:
          type: string
        inside_time:
          type: string
          format: date-time

    orderMetricsUpdated:
      type: object
      required:
        - id
        - timestamp
        - offer_id
        - offer_name
        - offer_type
        - offer_currency
        - intermediary
        - latest
        - applications
        - notional_value
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        timestamp:
          type: string
          format: date-time
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_name:
          type: string
        offer_type:
          type: string
        offer_currency:
          type: string
        intermediary:
          type: string
        latest:
          type: boolean
        applications:
          type: number
        notional_value:
          type: number

    allocationMetricsUpdated:
      type: object
      required:
        - id
        - timestamp
        - offer_id
        - offer_name
        - offer_type
        - offer_currency
        - intermediary
        - latest
        - shareholder_allocated_value
        - shareholder_allocated_qty
        - nonshareholder_allocated_value
        - nonshareholder_allocated_qty
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        timestamp:
          type: string
          format: date-time
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_name:
          type: string
        offer_type:
          type: string
        offer_currency:
          type: string
        intermediary:
          type: string
        latest:
          type: boolean
        shareholder_allocated_value:
          type: number
        shareholder_allocated_qty:
          type: number
        nonshareholder_allocated_value:
          type: number
        nonshareholder_allocated_qty:
          type: number

    allocationMetricsPublished:
      type: object
      required:
        - timestamp
        - offer_id
        - offer_name
        - offer_type
        - offer_currency
        - intermediary
        - latest
        - shareholder_allocated_value
        - shareholder_allocated_qty
        - nonshareholder_allocated_value
        - nonshareholder_allocated_qty
      properties:
        timestamp:
          type: string
          format: date-time
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_name:
          type: string
        offer_type:
          type: string
        offer_currency:
          type: string
        intermediary:
          type: string
        latest:
          type: boolean
        shareholder_allocated_value:
          type: number
        shareholder_allocated_qty:
          type: number
        nonshareholder_allocated_value:
          type: number
        nonshareholder_allocated_qty:
          type: number

paths:
  /dummy:
    get:
      operationId: "dummyGet"
      responses:
        '200':
          description: "Dummy"

