topicPrefix: "userservice"

components:
  schemas:
    user:
      type: object
      required:
        - username
        - display_name
      properties:
        display_name:
          type: string
        username:
          type: string

    userid:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    userSwitch:
      type: object
      required:
        - id
        - systemId
      properties:
        id:
          type: string
        systemId:
          type: string

    addUserResponse:
      type: object
      required:
        - id
        - password
      properties:
        id:
          type: string
          description: ID is the object ID in azure of the created user.
        password:
          type: string
          description: Password is the one time password (before being forced to change upon first login).

createUser:
  parameter: "user"

enableSuperUser:
  parameter: "userid"

disableSuperUser:
  parameter: "userid"

deleteUser:
  parameter: "userid"

switchSystem:
  parameter: "userSwitch"

listUsers: {}
