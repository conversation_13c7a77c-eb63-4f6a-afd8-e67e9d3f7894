# TODO => Move indexes from Cache to Record

Cache:
  - name: Offer
    index:
      - name: "offer-name-index"
        fields:
          - name
      - name: "offer-allocation-index"
        fields:
          - allocation
      - name: "offer-open_date-index"
        fields:
          - timeline.open_date
      - name: "offer-close_date-index"
        fields:
          - timeline.close_date
      - name: "offer-created-index"
        fields:
          - audit.date_created
    query:
      - name: ByName
        parameters:
          - name: name
      - name: ByAllocation
        parameters:
          - name: allocation

  - name: Order
    index:
      - name: "order-client_order_ref-orderbook_id"
        fields:
          - client_order_ref
          - orderbook_id
      - name: "order-orderbook_id"
        fields:
          - orderbook_id
          - client_order_ref
    query:
      - name: ByClientOrderRefOrderBookId
        parameters:
          - name: client_order_ref
          - name: orderbook_id
      - name: ByOrderBookId
        parameters:
          - name: orderbook_id
        sorting:
          - orderbook_id
          - client_order_ref

  - name: PrivateOfferDetails
    index:
      - name: "private-offer-details-offer-id-index"
        fields:
          - offer_id
    query:
      - name: ByOfferId
        parameters:
          - name: offer_id

  - name: EventAudit
    index:
      - name: "event-audit-index-correlation_id"
        fields:
          - correlation_id
      - name: "event-audit-by-offer-id"
        fields:
          - offer_id
      - name: "event-audit-by-document-id"
        fields:
          - document_id
    query:
      - name: ByOfferId
        parameters:
          - name: offer_id
      - name: ByDocumentId
        parameters:
          - name: document_id
      - name: ByCorrelationId
        parameters:
          - name: correlation_id

  - name: Organisation

  - name: Account

  - name: Allocation
    index:
      - name: "allocation-index-intermediary-allocationbook_id"
        fields:
          - intermediary
          - allocationbook_id
      - name: "allocation-index-allocationbook_id-client_order_ref"
        fields:
          - allocationbook_id
          - client_order_ref
      - name: "allocation-index-allocationbook_id-intermediary"
        fields:
          - allocationbook_id
          - intermediary
    query:
      - name: ByIntermediaryAllocationBookId
        parameters:
          - name: intermediary
          - name: allocationbook_id
      - name: ByAllocationBookId
        parameters:
          - name: allocationbook_id

  - name: AllocationBook

  - name: Address

  - name: Notification
    index:
      - name: "notification-index-user_id"
        fields:
          - user_id
      - name: "notification-index-status"
        fields:
          - status
      - name: "notification-index-created"
        fields:
          - created
      - name: "notification-index-title"
        fields:
          - title

    query:
      - name: ByUserStatus
        parameters:
          - name: user_id
          - name: status

      - name: ByUserAll
        parameters:
          - name: user_id

  - name: OrderBook

  - name: Task

  - name: AllocationSummary
    index:
      - name: "allocation-summary-index-offerallocation_id"
        fields:
          - offerallocation_id
    query:
      - name: ByOfferAllocationId
        parameters:
          - name: offerallocation_id

  - name: OfferAllocation

  - name: OrderSummary
    index:
      - name: "order-summary-index-status"
        fields:
          - status
      - name: "order-summary-index-date"
        fields:
          - date_created
      - name: "order-summary-index-next-offer_id"
        fields:
          - offer_id
          - next_id
      - name: "order-summary-index-offer-name"
        fields:
          - offer_name
      - name: "order-summary-index-previous_good_id-offer_id"
        fields:
          - previous_good_id
          - offer_id
      - name: "order-summary-orderbook_id"
        fields:
          - order_book_id
    query:
      - name: ByStatus
        parameters:
          - name: status
      - name: ByPreviousGoodIdOfferId
        parameters:
          - name: previous_good_id
          - name: offer_id
      - name: ByOrderBookId
        parameters:
          - name: order_book_id

  - name: User
    index:
      - name: "user-index-ext_id"
        fields:
          - ext_id
      - name: "user-index-gatekeeper"
        fields:
          - gatekeeper
      - name: "user-index-email"
        fields:
          - email
      - name: "user-index-status"
        fields:
          - status
      - name: "user-index-name"
        fields:
          - name
      - name: "user-index-role"
        fields:
          - role
      - name: "user-index-status-gatekeeper"
        fields:
          - status
          - gatekeeper
    query:
      - name: ByEmail
        parameters:
          - name: email
      - name: ByExtId
        parameters:
          - name: ext_id
      - name: ByGatekeeper
        parameters:
          - name: gatekeeper
      - name: ByStatusGatekeeper
        parameters:
          - name: status
          - name: gatekeeper

  - name: Permission
    query:
      - name: ByRule
        parameters:
          - name: ptype
          - name: v0
          - name: v1
            optional: true
          - name: v2
            optional: true
          - name: v3
            optional: true
          - name: v4
            optional: true
          - name: v5
            optional: true
      - name: ByPTypeUserScope
        parameters:
          - name: ptype
            value: "g"
          - name: user
            field: v0
          - name: scope
            field: v2
      - name: ByPTypeToV1
        parameters:
          - name: ptype
          - name: v0
          - name: v1
      - name: ByPTypeToV2
        parameters:
          - name: ptype
          - name: v0
          - name: v1
          - name: v2
      - name: ByPTypeToV3
        parameters:
          - name: ptype
          - name: v0
          - name: v1
          - name: v2
          - name: v3
      - name: ByPTypeToV4
        parameters:
          - name: ptype
          - name: v0
          - name: v1
          - name: v2
          - name: v3
          - name: v4
      - name: ByPType
        parameters:
          - name: ptype
      - name: ByV0
        parameters:
          - name: v0
      - name: ByV1
        parameters:
          - name: v1
      - name: ByV2
        parameters:
          - name: v2
      - name: ByV3
        parameters:
          - name: v3
      - name: ByV4
        parameters:
          - name: v4
      - name: ByV5
        parameters:
          - name: v5

  - name: WallCrossInfo
  - name: WallCrossInvite

  - name: WallCrossInviteGatekeeper
    index:
      - name: "gatekeeper-index-wallcrossinvite_id-user_id"
        fields:
          - wallcrossinvite_id
          - user_id
    query:
      - name: ByInviteIdAndUser
        parameters:
          - name: wallcrossinvite_id
          - name: user_id

  - name: OfferInsider
    index:
      - name: "offerinsider-index-offer_id"
        fields:
          - offer_id
      - name: "offerinsider-index-offer_id-user_email"
        fields:
          - offer_id
          - user_email
    query:
      - name: ByOfferId
        parameters:
          - name: offer_id
      - name: ByOfferIdAndUserEmail
        parameters:
          - name: offer_id
          - name: user_email

  - name: IntermediaryOrderMapping
    index:
      - name: "intordermapping-index-order_mapping_id"
        fields:
          - order_mapping_id
    query:
      - name: ByOrderMappingId
        parameters:
          - name: order_mapping_id

  - name: SummaryTotal
    index:
      - name: "summarytotal-index-order_summary_id-aggregation"
        fields:
          - ordersummary_id
          - aggregation
    query:
      - name: ByOrderSummaryIdAndAggregation
        parameters:
          - name: ordersummary_id
          - name: aggregation

  - name: OrderMetrics
    index:
      - name: "ordermetrics-index-offer_id-timestamp"
        fields:
          - offer_id
          - timestamp
    query:
      - name: ByOfferIdAndOrderSourceAndLatest
        parameters:
          - name: offer_id
          - name: order_source
          - name: latest

  - name: AllocationMetrics
    index:
      - name: "allocationmetrics-index-timestamp"
        fields:
          - timestamp
      - name: "allocationmetrics-index-offer_id-intermediary-latest"
        fields:
          - offer_id
          - intermediary
          - latest
    query:
      - name: ByOfferIdAndIntermediaryAndLatest
        parameters:
          - name: offer_id
          - name: intermediary
          - name: latest
      - name: ByOfferIdAndLatest
        parameters:
          - name: offer_id
          - name: latest

  - name: SettlementInstruction
    index:
      - name: "settlementinstruction-index-intermediary"
        fields:
          - intermediary
    query:
      - name: ByIntermediary
        parameters:
          - name: intermediary

  - name: SettlementBook
    index:
      - name: "settlementbook-index-offer_id"
        fields:
          - offer_id
    query:
      - name: ByOfferId
        parameters:
          - name: offer_id

  - name: SettlementObligation
    index:
      - name: "settlementobligation-index-settlement_book_id"
        fields:
          - settlement_book_id
      - name: "settlementobligation-index-created"
        fields:
          - created
      - name: "settlementobligation-index-updated"
        fields:
          - updated
      - name: "settlementobligation-index-settlement_date"
        fields:
          - settlement_date
    query:
      - name: BySettlementBookId
        parameters:
          - name: settlement_book_id

Enum:
  - OfferType:
      - IPO
      - Retail Bond
      - Follow On
  - UserStatus:
      - Active
      - Inactive
      - Pending
      - Created
      - Invited
      - Failure
  - OrderType:
      - Itemised
      - Summary
  - InviteStatus:
      - Draft
      - Active
  - OrderStatus:
      - Pending
      - ReplacePending
      - Unconfirmed
      - Accepted
      - Rejected
      - Replaced
      - Discarded
      - Deleted
      - Errored
      - Unknown
  - OrderFilter:
      - Pending
      - ReplacePending
      - ReplacePendingOrRejected
      - PositiveAcknowledged
  - DocumentType:
      - "prospectus"
      - "timeline"
      - "terms-and-conditions"
      - "master-agreement"
      - "retail-RNS"
      - "brand-guidelines"
      - "factsheet"
      - "final-terms-for-cash-offer"
      - "information-booklet"
      - "ITF"
      - "KID"
      - "KIID"
      - "offer-launch-RNS"
      - "pathfinder"
      - "RNS"
      - "supplementary-prospectus"
      - "valuation-notice"
      - "video"
      - "custom"
      - "logo"
      - "web-gateway-disclaimer"
      - "retail-offer-notice"
      - "tile-image"
  - DocumentStatus:
      - pending
      - ready
      - deleted
  - WallCrossStatus:
      - Pending
      - Accepted
      - Rejected
      - NoResponse
  - NotificationType:
      - info
      - warning
      - success
      - error
  - NotificationCategory:
      - Offer
      - OfferOrder
      - OfferDocument
      - OfferAllocation
      - WallCrossing
  - UserNotificationStatus:
      - Read
      - Unread
  - AggregationType:
      - total
      - shareholder
      - nonshareholder
  - AllocationStatus:
      - unnotified
      - notified
      - invalid
  - AccountType:
      - settlement
      - commission
  - TaskStatus:
      - running
      - success
      - failed
  - OfferInsiderEvent:
      - WallCrossing
      - TeamAccess
      - Manual
  - OfferInsiderStatus:
      - Accepted
      - Declined
      - Automatic
  - DeliveryType:
      - FOP
      - DVP

Record:

  - EventAudit:
      users:
        read:
          - writer
          - reader
          - auditor
        write:
          - auditor

      cacheable: true
      fields:
        tstamp: time.Time
        event_type: string
        source: string
        target: string
        correlation_id: string
        offer_id: { join: Offer, documentField: string }
        document_id: { join: Document, documentField: string }
        data: string

  - IntermediaryOrderMapping:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        offer_id: { join: Offer }
        intermediary: string
        order_mapping_id: string

  - Audit:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        offer_id: { join: Offer }
        date_created: time.Time
        date_updated: time.Time

  - Timeline:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        offer_id: { join: Offer }
        registration_date: time.Time
        open_date: time.Time
        close_date: time.Time
        cleansing_date: time.Time
        settlement_date: time.Time

  - Instrument:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        offer_id: { join: Offer }
        isin: string
        sedol: string
        ticker: string
        name: string

  - Settlement:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        offer_id: { join: Offer }
        crest_id: string

  - Permission:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true
      fields:
        ptype: string
        v0: string
        v1: string
        v2: string
        v3: string
        v4: string
        v5: string

  - OrderSummary:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true
      external: true

      fields:
        offer_id: { join: Offer }
        offer_name: string # In order to display the offer name, and be able to sort on it - we need this copied here.
        #  if our underlying DB changes; we might be able to remove this.
        entered_by: string
        entered_by_email: string
        date_created: time
        date_updated: time
        status: { enum: OrderStatus }
        order_type: { enum: OrderType }
        order_book_id: string
        totals:
          record: SummaryTotal
          join_fields:
            - aggregation: "total"
        shareholdings:
          record: SummaryTotal
          join_fields:
            - aggregation: "shareholder"
        non_shareholdings:
          record: SummaryTotal
          join_fields:
            - aggregation: "nonshareholder"
        allocationsummary_id: { join: AllocationSummary }
        next_id: string
        previous_id: string
        previous_good_id: string

  - OfferAllocation:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      external: true
      fields:
        entered_by: string
        date_created: time
        totals:
          record: OfferAllocationTotal
          join_fields:
            - aggregation: "total"
        shareholdings:
          record: OfferAllocationTotal
          join_fields:
            - aggregation: "shareholder"
        non_shareholdings:
          record: OfferAllocationTotal
          join_fields:
            - aggregation: "nonshareholder"
        status: { enum: AllocationStatus, joinField: true }

  - AllocationSummary:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      external: true
      fields:
        entered_by: string
        date_created: time
        totals:
          record: AllocationSummaryTotal
          join_fields:
            - aggregation: "total"
        shareholdings:
          record: AllocationSummaryTotal
          join_fields:
            - aggregation: "shareholder"
        non_shareholdings:
          record: AllocationSummaryTotal
          join_fields:
            - aggregation: "nonshareholder"
        allocationbook_id: { join: AllocationBook }
        offerallocation_id: { join: OfferAllocation }

  - AllocationBook:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      external: true
      fields:
        offer_id: { join: Offer }
        created: time.Time
        offer_price_at_creation: number

  - SummaryTotal:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        ordersummary_id: { join: OrderSummary }
        aggregation: { enum: AggregationType, joinField: true }
        applications: number
        notional_value: number
        existing_holding: number

  - OfferAllocationTotal:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        offerallocation_id: { join: OfferAllocation }
        aggregation: { enum: AggregationType, joinField: true }
        applications: number
        notional_value: number
        order_quantity: number
        num_orders: number
        allocated_orders: number
        unallocated_orders: number
        allocation_value: number
        allocation_quantity: number

  - AllocationSummaryTotal:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        allocationsummary_id: { join: AllocationSummary }
        aggregation: { enum: AggregationType, joinField: true }
        applications: number
        notional_value: number
        order_quantity: number
        num_orders: number
        allocated_orders: number
        unallocated_orders: number
        allocation_value: number
        allocation_quantity: number

  - Terms:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        privateofferdetails_id: { join: PrivateOfferDetails }
        invite_id: { join: Invite }
        accepted: boolean
        accepted_time: time.Time
        accepted_by: string
        accepted_by_email: string
        receive_commission: boolean
        retail_offer_notice_id: string

  - Invite:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        offer_id: { join: Offer }
        status: { enum: InviteStatus }
        intermediary_system_id: string
        intermediary_display_name: string
        master_agreement_signed: boolean
        time_of_invite: time.Time
        time_of_sending: time.Time
        terms: { record: Terms }

  - Document:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      fields:
        blob_id: string
        offer_id: { join: Offer, documentField: string }
        filename: string
        title: string
        document_type: { enum: DocumentType }
        size: number
        mime_type: string
        status: { enum: DocumentStatus }

  - OrderBook:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true
      external: true
      fields:
        offer_id: { join: Offer, documentField: string }
        timestamp: time.Time
        errored: boolean

  - Order:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true
      fields:
        orderbook_id: { join: OrderBook }
        client_order_ref: string
        notional_value: number
        existing_holding: number
        tax_wrapper: boolean
        validation_error: string
        validation_failed: boolean

  - Allocation:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true
      fields:
        allocationbook_id: { join: AllocationBook }
        client_order_ref: string
        notional_value: number
        existing_holding: number
        commission_due: boolean
        tax_wrapper: boolean
        applications: number
        allocation: number
        intermediary: string
        order_quantity: number
        validation_error: string
        validation_failed: boolean

  - User:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true
      fields:
        email: string
        name: string
        last_active: time.Time
        gatekeeper: boolean
        ext_id: string
        status: { enum: UserStatus }
        last_failure: string
        role: string

  - Task:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      fields:
        status: { enum: TaskStatus }
        created_by: string
        created_time: time.Time
        update_time: time.Time
        message: string
        response: string

  - Account:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      fields:
        account_type: { enum: AccountType }
        name: string
        bank_name: string
        swift_bic: string
        account_number: string
        sort_code: string

  - Address:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      fields:
        address1: string
        address2: string
        address3: string
        address4: string
        address_city: string
        address_postal_code: string
        address_country: string

  - Organisation:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      fields:
        name: string
        crest_account_name: string
        crest_participant_id: string
        account_id: { join: Account }
        address_id: { join: Address }
        settlement_reference: string

  - Notification:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true
      fields:
        user_id: string
        created: time.Time
        title: string
        message: string
        notification_type: { enum: NotificationType }
        action_label: string
        action_path: string
        status: { enum: UserNotificationStatus }
        category: { enum: NotificationCategory }
        target_id: string

  - WallCrossInvite:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      external: true
      fields:
        wallcrossinfo_id: { join: WallCrossInfo }
        intermediary_system_id: string
        intermediary_display_name: string
        bank_system_id: string
        gatekeepers: { recordArray: WallCrossInviteGatekeeper }
        invite_sent_time: time.Time
        status: { enum: WallCrossStatus }

  - WallCrossInviteGatekeeper:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      fields:
        wallcrossinvite_id: { join: WallCrossInvite }
        user_id: string
        user_name: string
        user_email: string
        status: { enum: WallCrossStatus }
        response_time: time.Time

  - WallCrossInfo:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      external: true
      fields:
        wallcross_container_id: string
        bank_system_id: string
        subject: string
        consent_to_cross: string
        invites: { recordArray: WallCrossInvite }
        offer_id: { join: Offer }
        offer_outline_document_id: string

  - OfferInsider:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      external: true
      fields:
        offer_id: { join: Offer }
        user_id: string
        user_name: string
        user_email: string
        event: { enum: OfferInsiderEvent }
        status: { enum: OfferInsiderStatus }
        response_time: time.Time
        inside_time: time.Time
        cleansed_time: time.Time

  - Offer:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      cacheable: true
      external: true
      fields:
        name: string
        offer_type: { enum: OfferType }
        audit: { record: Audit }
        timeline: { record: Timeline }
        description: string
        details: string
        allocation_principles: string
        settlement_details: string
        currency: string
        raise_amount: number
        issued_by: string
        status:
          refEnum:
            typeName: "OfferStatusState"
            package: model
            import: "retailbook.com/rb/common/pkgs/model"
        shareholder_only: boolean
        inside_information: boolean
        market_sector: string
        min_order_amount: number
        offer_price: number
        order_summary_mapping: { recordArray: IntermediaryOrderMapping }
        settlement: { record: Settlement }
        instrument: { record: Instrument }
        document: { recordArray: Document }
        invites: { recordArray: Invite }
        wallcross_info: { record: WallCrossInfo }
        allocation: { join: OfferAllocation }
        is_launched: boolean
        price_range_low: number
        price_range_high: number
        settlement_book: { join: SettlementBook }
        permission_timestamps: string

  - PrivateOfferDetails:
      users:
        read:
          - writer
          - reader
        write:
          - writer

      cacheable: true

      fields:
        offer_id: { join: "Offer", documentField: string }
        terms: { record: Terms }

  - OrderMetrics:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        timestamp: time.Time
        offer_id: string #Participants may want to view offer metrics but may not actually have been invited to participate in that offer, so can't join to offer here.
        offer_source: string
        offer_name: string
        offer_type: { enum: OfferType }
        offer_currency: string
        order_summary_id: string #The bank's local id. Can be null, useful for tracing.
        order_source: string
        applications: number
        notional_value: number
        latest: boolean

  - AllocationMetrics:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        timestamp: time.Time
        offer_id: string #Participants may want to view offer metrics but may not actually have been invited to participate in that offer, so can't join to offer here.
        offer_source: string
        offer_name: string
        offer_type: { enum: OfferType }
        offer_currency: string
        offer_allocation_id: string #The bank's local id. Can be null, useful for tracing.
        allocation_book_id: string #The bank's local id. Can be null, useful for tracing.
        intermediary: string
        sh_allocated_value: number
        sh_allocated_qty: number
        nh_allocated_value: number
        nh_allocated_qty: number
        latest: boolean

  - SettlementInstruction:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        intermediary: string
        settlement_reference: string
        timestamp: time.Time

  - SettlementBook:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        offer_id: { join: Offer }
        created: time.Time
        settlement_date_at_creation: time.Time
        isin_at_creation: string
        security_name_at_creation: string

  - SettlementObligation:
      users:
        read:
          - writer
          - reader
        write:
          - writer
      fields:
        settlement_book_id: { join: SettlementBook }
        created: time.Time
        updated: time.Time
        delivery_type: { enum: DeliveryType }
        counterparty: string
        settlement_reference: string
        security_isin: string
        security_name: string
        settlement_date: time.Time
        actual_settlement_date: time.Time
        quantity: number
        open_quantity: number
        cash_amount: number
        open_cash_amount: number
        price: number
        currency: string
