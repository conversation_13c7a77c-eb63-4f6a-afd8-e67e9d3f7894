#
# A set of different configs for Docly that can give us a single-source of truth OpenAPI spec
#
# npx @redocly/cli bundle external@latest --output rb-external-api
# npx @redocly/cli build-docs rb-external-api.yaml --output rb-external-api.html
#
apis:
  internal@latest:
    root: ./api_gateway.yaml
  external@latest:
    root: ./api_gateway.yaml
    decorators:
      filter-out: 
        property: x-audience
        value: [private]
  bank@latest:
    root: ./api_gateway.yaml
    decorators: 
      filter-out: 
        property: x-audience
        value: [intermediary, private]
  intermediary@latest:
    root: ./api_gateway.yaml
    decorators:
      remove-x-internal: on
      filter-out: 
        property: x-audience
        value: [bank, private]


