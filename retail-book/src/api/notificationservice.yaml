topicPrefix: "notificationservice"

components:
  schemas:
    emailMetadata:
      type: object
      description: Sending metadata for mail.
      required:
        - to
        - subject
      properties:
        to:
          type: array
          description: List of recipients of this e-mail.
          items:
            type: string
        cc:
          type: array
          description: Optional list of carbon copy recipients of this e-mail.
          items:
            type: string
        bcc:
          type: array
          description: Optional list of blind carbon copy recipients of this e-mail.
          items:
            type: string
        subject:
          type: string
          description: The subject line of the e-mail.

    templatedEmail:
      type: object
      description: A templated email as well as metadata required for sending.
      required:
        - metadata
        - template
        - templateData
      properties:
        template:
          type: string
          description: The name (not ID) of the template to use.
        templateData:
          type: array
          items:
            type: object
            "$ref": "#/components/schemas/templateData"
        metadata:
          type: object
          schema:
            "$ref": "#/components/schemas/emailMetadata"

    templateData:
      type: object
      description: Template data (a single key-value pair) to use when injecting into Provider templates.
      required:
        - key
        - value
      properties:
        key:
          type: string
          description: The name of the field to replace.
        value:
          type: object
          description: The value to insert in place.

sendTemplatedEmail:
  parameter: "templatedEmail"
  description: Sends an email using the underlying SMTP platform provider.