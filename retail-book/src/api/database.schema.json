{"$schema": "https://json-schema.org/draft/2019-09/schema", "$id": "https://www.retailbook.com/schemas/database", "type": "object", "properties": {"Cache": {"type": "array", "items": {"type": "object", "additionalProperties": false, "required": ["name"], "properties": {"name": {"type": "string"}, "record": {"type": "string"}, "index": {"type": "array", "items": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}}, "required": ["name", "fields"]}}, "query": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/$defs/QueryParam"}}, "sorting": {"type": "array", "items": "string"}}}}}}}, "Enum": {"type": "array", "items": {"type": "object", "patternProperties": {"^.*$": {"$ref": "#/$defs/EnumDef"}}}}, "Record": {"type": "array", "items": {"type": "object", "patternProperties": {"^.*$": {"$ref": "#/$defs/RecordDef"}}}}}, "required": ["<PERSON><PERSON>"], "$defs": {"EnumDef": {"type": "array", "items": {"type": "string"}}, "QueryParam": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}, "field": {"type": "string"}, "use_equals": {"type": "boolean"}, "optional": {"type": "boolean"}}, "required": ["name"]}, "RecordDef": {"type": "object", "properties": {"users": {"type": "object", "properties": {"read": {"type": "array", "items": "string"}, "write": {"type": "array", "items": "string"}}}, "fields": {"type": "object", "patternProperties": {"^.*$": {"anyOf": [{"type": "string"}, {"type": "object", "required": "join", "properties": {"join": {"type": "string"}, "documentField": {"type": "string"}}}, {"type": "object", "required": "record", "properties": {"record": {"type": "string"}}}, {"type": "object", "required": "recordArray", "properties": {"recordArray": {"type": "string"}}}, {"type": "object", "required": "enum", "properties": {"enum": {"type": "string"}, "joinField": {"type": "boolean"}}}, {"type": "object", "properties": {"required": ["refEnum"], "refEnum": {"type": "object", "properties": {"typeName": {"type": "string"}, "package": {"type": "string"}, "import": {"type": "string"}}, "required": ["typeName", "package", "import"]}}}]}}}}}}}