openapi: 3.0.3
info:
  title: View Server
  description: The view server
  version: 1.0.0
tags:
  - name: offers
    description: All offer related activity

components:
  schemas:
    viewType:
      type: string
      description: "Different types of view types to expose via API"
      enum:
        - getOffer
        - getOfferUIState
        - getAllOffers
        - getOfferHistory
        - getIntermediaries
        - getAllUsers
        - getRoleScopedUsers
        - getUser
        - getUserGlobalPerms
        - getUserLocalPerms
        - getDocumentMeta
        - getDocuments
        - getNotification
        - getNotifications
        - getWallCrossInvites
        - getInvites
        - getInvite
        - getOrderSummaryView
        - getAllOrderSummaryView
        - getSpecificOrderSummaryView
        - getOrderBook
        - getOrderSummaryHistory
        - getOfferAllocation
        - getOfferWallCrossInfo
        - getOfferWallCrossInvites
        - getWallCrossInvite
        - getAllocationBook
        - getAllocationBookAggregated
        - getTerms
        - getOrganisation
        - getTask
        - getOfferInsiders
        - getOfferOrderMetrics
        - getOfferDailyOrderMetrics
        - getMonthlyOfferMetrics
        - getOfferLatestAllocationMetrics
        - getAllSettlementInstructions
        - getSettlementInstruction
        - getOfferSettlementObligations
        - getAllSettlementObligations
        - getOfferSettlementBook

    getDocumentMeta:
      type: object
      description: "Retrieve meta information for a document"
      required:
        - offer_id
        - document_id
      properties:
        offer_id:
          type: string
        document_id:
          type: string
        document_id_type:
          type: string

    getDocuments:
      type: object
      description: "Retrieve meta information for all documents"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getOfferUIState:
      type: object
      description: "Retrieve UI state"
      required:
        - id
      properties:
        id:
          type: string

    getOffer:
      type: object
      description: "Retrieve a single offer by ID"
      required:
        - id
      properties:
        id:
          type: string

    getAllOffers:
      type: object
      description: "Retrieve all offers"

    getOfferHistory:
      type: object
      description: "Retrieve offer history"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getIntermediaries:
      type: object
      description: "Retrieve a list of all intermediaries"

    getAllUsers:
      type: object

    getRoleScopedUsers:
      type: object
      description: "Get all users with specific role and scope"
      required:
        - role
        - scope
      properties:
        role:
          type: string
        scope:
          type: string

    getUser:
      type: object
      description: "Get information about a user"
      required:
        - uid
      properties:
        uid:
          type: string

    getOrganisation:
      type: object
      description: "Get details for the current organisation"

    getTask:
      type: object
      description: "Get details for given task id"
      required:
        - task_id
      properties:
        task_id:
          type: string

    getUserGlobalPerms:
      type: object
      description: "Gets all perms granted to a particular user in global scope"
      required:
        - uid
      properties:
        uid:
          type: string

    getUserLocalPerms:
      type: object
      description: "Gets all perms granted to a particular user for a particular scope"
      required:
        - scope
        - uid
      properties:
        uid:
          type: string
        scope:
          type: string

    getNotification:
      type: object
      description: "Get a single notification"
      required:
        - id
        - uid
      properties:
        uid:
          type: string
        id:
          type: string

    getNotifications:
      type: object
      description: "Get notifications for a user"
      required:
        - uid
      properties:
        uid:
          type: string

    getWallCrossInvites:
      type: object
      description: "Get wall cross invites for a user"
      required:
        - uid
      properties:
        uid:
          type: string

    getInvites:
      type: object
      description: "Get invites for a particular offer"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getInvite:
      type: object
      description: "Get a particular invite"
      required:
        - id
        - offer_id
      properties:
        id:
          type: string
        offer_id:
          type: string

    getAllocationBook:
      type: object
      description: "Retrieves an allocation book by ID"
      required:
        - offer_id
        - allocation_book_id
      properties:
        allocation_book_id:
          type: string
          description: The offer ID the summaries should be linked to (mainly for permission granting).
        offer_id:
          type: string
          description: The offer ID the summaries should be linked to (mainly for permission granting).

    getAllocationBookAggregated:
      type: object
      description: "Retrieves an allocation book by ID, aggregated by intermediary"
      required:
          - offer_id
          - allocation_book_id
      properties:
        allocation_book_id:
            type: string
            description: The offer ID the summaries should be linked to (mainly for permission granting).
        offer_id:
            type: string
            description: The offer ID the summaries should be linked to (mainly for permission granting).

    getOfferAllocation:
      type: object
      description: "Get the latest offer allocation"
      required:
        - offer_id
      properties:
        offer_id:
          type: string
          description: The offer ID the summaries should be linked to (mainly for permission granting).

    getOrderSummaryView:
      type: object
      description: "Get order summary view"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getAllOrderSummaryView:
      type: object
      description: "Get all orders summaries"

    getSpecificOrderSummaryView:
      type: object
      description: "Get specific order summary view"
      required:
        - offer_id
        - id
      properties:
        offer_id:
          type: string
        id:
          type: string

    getOrderBook:
      type: object
      description: "Get a specific order book (itemized type)"
      required:
        - offer_id
        - order_summary_id
      properties:
        offer_id:
          type: string
        order_summary_id:
          type: string

    getOrderSummaryHistory:
      type: object
      description: "Get order summary history"
      required:
        - offer_id
      properties:
        offer_id:
          type: string
        id:
          type: string
        system_id:
          type: string

    getOfferWallCrossInfo:
      type: object
      description: "Get wall cross info for the offer"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getOfferWallCrossInvites:
      type: object
      description: "Get all invites for a offer wall cross"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getWallCrossInvite:
      type: object
      description: "Get specific invite"
      required:
        - id
      properties:
        id:
          type: string

    getOfferInsiders:
      type: object
      description: "Get insiders on the offer"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getTerms:
      type: object
      description: "Get terms for a specific offer"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getOfferOrderMetrics:
      type: object
      description: "Gets a list of snapshots of the order book grouped by intermediary, returns a snapshot for every addition/amend to the order book"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getOfferDailyOrderMetrics:
      type: object
      description: "Gets a list of snapshots of the order book grouped by intermediary, returns a bucketed snapshot for every day in which the order book was added to/amended"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getOfferLatestAllocationMetrics:
      type: object
      description: "Gets a the latest snapshot of the allocation metrics grouped by intermediary."
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getMonthlyOfferMetrics:
      type: object
      description: "Gets a list of snapshots of offer metrics (across offers) grouped by intermediary and currency, returns a bucketed snapshot for every month in which there exists offers and in between"

    getAllSettlementInstructions:
      type: object
      description: "Retrieve all settlement instructions"

    getSettlementInstruction:
      type: object
      description: "Retrieve all settlement instructions"
      required:
        - id
      properties:
        id:
          type: string

    getOfferSettlementBook:
      type: object
      description: "Retrieve settlement book for the offer"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getOfferSettlementObligations:
      type: object
      description: "Retrieve all settlement obligations for an offer"
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    getAllSettlementObligations:
      type: object
      description: "Retrieve all settlement obligations for the search criteria, paginated"

    # Model objects



paths:
  /dummy:
    get:
      summary: Dummy
      responses:
        '200':
          description: Ok
