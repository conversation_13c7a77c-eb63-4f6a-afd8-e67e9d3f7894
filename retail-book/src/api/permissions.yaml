#
# This file defines the possible resources, actions, permissions in the system
#  it also defines what permissions a rest<PERSON><PERSON>ler, cmdHandler or viewHandler needs.
#

resources:
  - offer
  - offer_details
  - offer_terms
  - offer_insider
  - order
  - document
  - user
  - invite
  - permission
  - email
  - order_summary
  - allocation_summary
  - offer_allocation
  - wallcross
  - wallcross_invite
  - order_book
  - allocation_book
  - notification
  - organisation
  - account
  - address
  - task
  - metrics
  - settlement_instruction
  - settlement

actions:
  - insert
  - create
  - read
  - write
  - delete
  - accept
  - reject
  - confirm
  - resend
  - revoke
  - grant
  - respond

actionSets:
  default:
    - create
    - read
    - write
    - delete
  immutable:
    - create
    - read
    - delete


# Defines which actions are allowed for which resources, the values can either be individual actions
#  or a set of action (or a combination of both.) The action list is first checked before the actionSet list.
#  So if there's a duplicate in each list, the set will never be found.
#
#  This generates a list of possible permissions of form "resource:action"
resourceActions:
  offer: [ default, insert, grant, revoke ]
  offer_details: [ write, read ]
  offer_terms: [ write, read ]
  offer_insider: [ default ]
  order: [ default, accept, reject ]
  order_book: [ default, insert ]
  allocation_book: [ default, insert ]
  order_summary: [ default, accept, reject, confirm, insert ]
  allocation_summary: [ default, insert ]
  offer_allocation: [ default, insert ]
  document: [ default, insert ]
  user: [ default ]
  invite: [ default ]
  permission: [ create, grant, revoke, read ]
  email: [ create ]
  wallcross: [ ]
  wallcross_invite: [ default, respond, insert ]
  notification: [ default ]
  organisation: [ default, read, write ]
  task: [ default, read, write ]
  account: [ default, read, write ]
  address: [ default, read, write ]
  metrics: [ default, insert ]
  settlement_instruction: [ default ]
  settlement: [ default ]

# Defines which scopes are applicable for certain permissions
#  we have two scopes, local or global. If it's a local scope an identifier needs to be passed
#  if its global it'll always be "*"
#
# In general, we're assigning local scopes to the offer, and then using global scopes on all offer related resources
#  so we don't have to grant permissions to all users each time a resource is created - we however retain the flexibility
#  to permit that in future here.
#
scopes:
  offer:
    global: [ create, insert ]
    local: [ read, write, delete, grant, revoke ]
  offer_details:
    local: [ write, read ]
  offer_terms:
    global: [ write, read ]
  offer_insider:
    global: [ read ]
    local: [ write ]
  order:
    global: [ default, accept, reject ]
  order_book:
    global: [ default, insert ]
  allocation_book:
    global: [ default, insert ]
  order_summary:
    global: [ default, accept, reject, confirm, insert ]
  allocation_summary:
    global: [ default, insert ]
  offer_allocation:
    global: [ default, insert ]
  document:
    global: [ default, insert ]
  user:
    global: [ default ]
  invite:
    global: [ default ]
  permission:
    global: [ create, grant, revoke, read ]
  email:
    global: [ create ]
  wallcross:
    global: [ ]
  wallcross_invite:
    global: [ create, insert ]
    local: [ read, respond ]
  notification:
    global: [ default ]
  organisation:
    global: [ default ]
  task:
    global: [ default ]
  account:
    global: [ default ]
  address:
    global: [ default ]
  metrics:
    global: [ default, insert ]
  settlement_instruction:
    global: [ default ]
  settlement:
    global: [ default ]

#
# Policies, these are the predefined roles the user can take;
#

policies:
  # These are global policies
  manager: [ "offer:create",
             "order:create","order:read","order:write","order:accept","order:reject","order:delete",
             "order_book:create","order_book:read","order_book:write","order_book:delete",
             "order_summary:create","order_summary:read","order_summary:delete","order_summary:write","order_summary:accept","order_summary:reject","order_summary:confirm","order_summary:insert",
             "allocation_book:create","allocation_book:read","allocation_book:delete","allocation_book:write",
             "allocation_summary:create","allocation_summary:read","allocation_summary:delete","allocation_summary:write",
             "offer_allocation:create","offer_allocation:read","offer_allocation:delete","offer_allocation:write",
             "document:create","document:read","document:write","document:delete",
             "user:create","user:read","user:write","user:delete",
             "organisation:read","organisation:write",
             "task:read","task:write",
             "account:read","account:write",
             "address:read","address:write",
             "metrics:read",
             "settlement:read","settlement:create","settlement:write",
             "settlement_instruction:read","settlement_instruction:create","settlement_instruction:write","settlement_instruction:delete",
             "wallcross_invite:create",
             "notification:read","notification:write","notification:delete",
             "invite:create","invite:read","invite:write",
             "permission:grant","permission:revoke",
             "offer_terms:read","offer_terms:write" ]

  editor: [ "offer:create",
            "order:create","order:read","order:write","order:accept","order:reject","order:delete",
            "order_book:create","order_book:read","order_book:write","order_book:delete",
            "order_summary:create","order_summary:read","order_summary:delete","order_summary:write","order_summary:accept","order_summary:reject","order_summary:confirm","order_summary:insert",
            "allocation_book:create","allocation_book:read","allocation_book:delete","allocation_book:write",
            "allocation_summary:create","allocation_summary:read","allocation_summary:delete","allocation_summary:write",
            "offer_allocation:create","offer_allocation:read","offer_allocation:delete","offer_allocation:write",
            "document:create","document:read","document:write","document:delete",
            "wallcross_invite:create",
            "user:read",
            "organisation:read",
            "task:read", "task:write",
            "account:read",
            "address:read",
            "metrics:read",
            "settlement:read","settlement:create","settlement:write",
            "settlement_instruction:read",
            "notification:read","notification:write","notification:delete",
            "invite:create","invite:read","invite:write",
            "offer_terms:read" ]

  # These are local policies related to offer access
  offer_owner: [ "offer:read","offer:write","offer:delete","offer_details:write","offer_details:read","offer:grant","offer:revoke","offer_insider:create","offer_insider:write" ]
  offer_collaborator: [ "offer:read","offer:write","offer_details:write","offer_details:read" ]

  offer_retail_gatekeeper: [ "offer:read","offer:write","offer:grant","offer:revoke" ]
  offer_retail_collaborator: [ "offer:read","offer:write" ]
  offer_retail_readonly: [ "offer:read" ]

  # local policies related to wallcrossing
  wallcross_retail_gatekeeper: [ "wallcross_invite:read", "wallcross_invite:respond" ]

#
# Defines the permissions for rest handlers, commands, or views
#   For rest handlers use the operationId to define the permission
#   For command handlers use the name of command (e.g. createOffer)
#   For view handlers use the name of command (e.g. getOffer)
#
# If the permission starts with inherit: then permissions are taken from the other path
#  for example foo: ["inherit:commands/insertOffer"] will use the same parameters as insertOffer
#
# Each value is treated as a permission, to provide an alternate option combine two or more permissions
#  in the string e.g "offer:write|offer:read", if the set of permissions given is
#   ["offer:insert","user:read|user:write"] this changes to "offer:insert" && ("user:read" || "user:write")
#

permissions:
  rest:
    newOffer: [ "inherit:commands/createOffer" ]
    removeOffer: ["inherit:commands/removeOffer"]
    deleteOfferDocumentById: [ "inherit:commands/deleteDocument" ]
    getOfferDocumentById: [ "offer:write|offer:read","document:read" ]
    getOfferDocumentMetaById: [ "inherit:rest/getOfferDocumentById" ]
    createOfferInsider: [ "inherit:commands/createOfferInsider" ]

    uploadDocument: [ "offer_details:write","document:create" ]
    uploadWallCrossDocument: [ "inherit:rest/uploadDocument" ]
    getWallCrossDocument: [ "wallcross_invite:read" ]
    getOfferWallCrossDocument: [ "inherit:rest/getOfferDocumentById" ] #If you have access to the offer then it is no longer inside information

    grantRoleToUserOnOffer: [ "offer:write","offer:grant" ]
    revokeRoleToUserOnOffer: [ "offer:write","offer:revoke" ]
    getFullOrderBook: [ "inherit:views/getOffer", "order:read|order:write","order_summary:read|order_summary:write" ]
    getOrderAudit: [ "inherit:rest/getFullOrderBook" ]
    getFullOrderAudit: [ "inherit:rest/getFullOrderBook" ]
    getSpecificFullOrderBook: [ "inherit:rest/getFullOrderBook" ]

  commands:
    # Offers
    removeOffer: [ "offer:delete" ]
    insertOffer: [ "offer:insert" ]
    createOffer: [ "offer:create" ]
    amendOffer: [ "offer:write","offer_details:write" ]
    updateOfferStatus: [ "offer:write","offer_details:write" ]
    createOfferInsider: [ "offer:write","offer_insider:create" ]
    amendOfferInsider: [ "offer:write","offer_insider:write" ]
    updateOfferRoleTimestamp: [ "offer:write" ]

    # Organisation
    amendOrganisation: [ "organisation:write" ]
    deleteOrganisation: [ "organisation:write" ]

    # Perms
    createPerm: [ "permission:create" ]
    grantPerm: [ "permission:grant" ]
    revokePerm: [ "permission:revoke" ]
    readPerm: [ "permission:read" ]

    # Documents
    insertDocument: [ "offer_details:write","document:insert" ]
    createDocument: [ "offer_details:write","document:create" ]
    amendDocument: [ "offer_details:write","document:write" ]
    deleteDocument: [ "offer_details:write","document:delete" ]

    # Tasks
    createTask: [ "task:write" ]
    updateTaskStatus: [ "task:write" ]
    updateTaskMessage: [ "task:write" ]

    # Emails
    createEmail: [ "email:create" ]
    createEmailForOfferUsers: [ "email:create" ]

    # Order Summary
    insertOrderSummary: [ "offer:write", "order_summary:insert" ]
    createOrderSummary: [ "offer:write", "order_summary:create" ]
    patchOrderSummary: [ "offer:write", "order_summary:write" ]
    deleteOrderSummary: [ "offer:write", "order_summary:delete" ]
    acceptOrderSummary: [ "offer_details:write", "order_summary:accept" ]
    rejectOrderSummary: [ "offer_details:write", "order_summary:reject" ]
    confirmOrderSummary: [ "offer:write", "order_summary:confirm" ]

    # Allocation Summary
    createAllocationSummary: [ "offer_details:write", "allocation_summary:create" ]

    # Order Book
    insertOrderBook: [ "offer:write", "order_book:insert" ]
    createOrderBook: [ "offer:write", "order_book:create" ]
    appendOrderBook: [ "offer:write", "order_book:write" ]
    insertOrderBookOrders: [ "offer:write", "order_book:insert" ]
    finalizeOrderBook: [ "offer:write", "order_book:write" ]

    # Allocation Book
    createAllocationBook: [ "offer_details:write", "allocation_book:create" ]
    appendAllocationBook: [ "offer_details:write", "allocation_book:write" ]
    finalizeAllocationBook: [ "offer_details:write", "allocation_book:write" ]
    insertAllocationBook: [ "offer:write", "allocation_book:insert" ]
    insertAllocationBookOrders: [ "offer:write", "allocation_book:insert" ]

    # Allocation
    clearOfferAllocation: [ "offer_details:write", "offer_allocation:delete" ]
    notifyOfferAllocation: [ "offer_details:write", "offer_allocation:write" ]

    # Users
    createUser: [ "user:create" ]
    amendUser: [ "user:write" ]
    deleteUser: [ "user:delete" ]
    inviteUser: [ "inherit:commands/createUser" ]
    failUserCreation: [ "inherit:commands/createUser" ]
    activateUser: [ "inherit:commands/amendUser" ]
    userActive: [ "inherit:commands/amendUser" ]

    # Notification
    createNotification: [ "user:read", "notification:create" ]
    readNotification: [ "user:read", "notification:write" ]
    unreadNotification: [ "user:read","notification:write" ]
    readAllNotifications: [ "user:read", "notification:write" ]
    deleteNotification: [ "user:read", "notification:delete" ]
    createNotificationForOfferUsers: [ "user:read", "notification:create" ]

    # Invites
    createOfferInvite: [ "offer_details:write", "invite:create" ]

    # Terms
    acceptOfferTerms: [ "offer:write","offer_terms:write" ]
    insertOfferTerms: [ "inherit:commands/acceptOfferTerms" ]

    # Offer Notification
    createOfferNotification: [ "offer_details:write" ]

    # Wallcrossing - if you have access to alter the offer details then you have access alter to the wall crossing details, but invites require explicit perms
    updateWallCrossInfo: [ "offer_details:write" ]
    deleteWallCrossInfo: [ "offer_details:write" ]
    createWallCrossInvite: [ "offer_details:write","wallcross_invite:create" ]
    insertWallCrossInvite: [ "wallcross_invite:insert" ]
    insertWallCrossInviteResponse: [ "inherit:commands/insertWallCrossInvite" ]
    insertWallCrossInviteGatekeeper: [ "inherit:commands/insertWallCrossInvite" ]
    respondToWallCrossInvite: [ "wallcross_invite:respond" ]
    voidWallCrossGatekeeperInvite: [ "wallcross_invite:write" ]
    cleanseOffer: [ "offer_details:write" ]

    # Metrics/Market Information
    updateOrderMetrics: [ "metrics:create" ]
    updateAllocationMetrics: [ "metrics:create" ]
    insertAllocationMetrics: [ "metrics:insert" ]
    publishAllocationMetrics: [ "metrics:insert" ]

    # Settlement Instruction
    createSettlementInstruction: [ "settlement_instruction:create" ]
    updateSettlementInstruction: [ "settlement_instruction:write" ]
    deleteSettlementInstruction: [ "settlement_instruction:delete" ]
    deleteNamedSettlementInstruction: [ "settlement_instruction:delete" ]
    createSettlementBook: [ "settlement:create" ]

    # Settlement Obligation
    settleObligation: [ "settlement:write" ]

  views:
    # Offers
    getOffer: [ "offer:read|offer:write" ]
    getOfferUIState: [ "inherit:views/getOffer" ]
    getAllOffers: [ "inherit:views/getOffer" ]
    getOfferHistory: [ "inherit:views/getOffer" ]


    # Static
    getIntermediaries: [ ] # No permissions (other than being an authenticated user

    # Users
    getUser: [ "user:read|user:write" ]
    getAllUsers: [ "inherit:views/getUser" ]
    getRoleScopedUsers: [ "inherit:views/getUser" ]

    # Organisation
    getOrganisation: [ "organisation:read|organisation:write" ]

    # Task
    getTask: [ "task:read|task:write" ]

    # Documents
    getDocuments: [ "offer:read|offer:write","document:read|document:write" ]
    getDocumentMeta: [ "inherit:views/getDocuments" ]

    # Notifications
    getNotification: [ "user:read|user:write","notification:read|notification:write" ]
    getNotifications: [ "inherit:views/getNotification" ]

    # Invites
    getInvite: [ "inherit:views/getOffer", "invite:read|invite:write" ]
    getInvites: [ "inherit:views/getInvite" ]

    # Orders
    getOrderSummaryView: [ "inherit:views/getOffer", "order_summary:read|order_summary:write" ]
    getSpecificOrderSummaryView: [ "inherit:views/getOrderSummaryView" ]
    getOrderSummaryHistory: [ "inherit:views/getOrderSummaryView" ]
    getOrderBook: [ "inherit:views/getOffer", "order:read|order:write" ]
    getAllOrderSummaryView: [ "inherit:views/getOrderSummaryView" ]

    # Allocations
    getOfferAllocation: [ "inherit:views/getOffer", "offer_allocation:read|offer_allocation:write" ]
    getAllocationBookAggregated: [ "inherit:views/getOffer","offer_allocation:read|offer_allocation:write" ]

    # Wall crossing - if you have access to the offer then you have access to the wall crossing, but invites require explicit perms
    getOfferWallCrossInfo: [ "inherit:views/getOffer" ]
    getOfferWallCrossInvites: [ "inherit:views/getOfferWallCrossInfo" ]
    getWallCrossInvite: [ "wallcross_invite:read" ]
    getAllWallCrossInvites: [ "inherit:views/getWallCrossInvite" ]

    # Terms
    getTerms: [ "inherit:views/getOffer","offer_terms:read|offer_terms:write" ]

    # Metrics
    getOfferOrderMetrics: [ "inherit:views/getOffer" ]
    getOfferDailyOrderMetrics: [ "inherit:views/getOffer" ]
    getOfferLatestAllocationMetrics: [ "inherit:views/getOffer" ]
    getMonthlyOfferMetrics: [ "metrics:read" ]

    # Settlement book
    getOfferSettlementBook: [ "inherit:views/getOffer" ]

    # Settlement Instructions
    getAllSettlementInstructions: [ "settlement_instruction:read" ]
    getSettlementInstruction: [ "inherit:views/getAllSettlementInstructions" ]

    # Settlement Obligations
    getOfferSettlementObligations: [ "inherit:views/getOffer" ] #if you can view the offer then you can view the settlement obligations, view of all settlement obligations is separated
    getAllSettlementObligations: [ "settlement:read" ] #if you can view the offer then you can view the settlement obligations, view of all settlement obligations is separated
