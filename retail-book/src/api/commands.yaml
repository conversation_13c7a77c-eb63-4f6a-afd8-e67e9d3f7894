openapi: 3.0.3
info:
  title: Command Handler
  description: Command Handlers definitions
  version: 1.0.0

components:
  schemas:
    commandType:
      type: string
      description: "Different types of command types. Used for NATS & RBAC purposes"
      enum:
        - createOffer
        - removeOffer
        - amendOffer
        - insertOffer
        - updateOfferStatus
        - grantPerm
        - revokePerm
        - readPerms
        - insertDocument
        - createDocument
        - amendDocument
        - deleteDocument
        - createEmail
        - createOrderSummary
        - patchOrderSummary
        - deleteOrderSummary
        - acceptOrderSummary
        - rejectOrderSummary
        - confirmOrderSummary
        - insertOrderSummary
        - createOrderBook
        - createAllocationBook
        - appendOrderBook
        - appendAllocationBook
        - insertOrderBook
        - insertAllocationBook
        - insertOrderBookOrders
        - insertAllocationBookOrders
        - finalizeOrderBook
        - finalizeAllocationBook
        - notifyOfferAllocation
        - clearOfferAllocation
        - createUser
        - amendUser
        - deleteUser
        - failUserCreation
        - inviteUser
        - activateUser
        - userActive
        - createNotification
        - createNotificationForOfferUsers
        - createEmailForOfferUsers
        - readNotification
        - unreadNotification
        - readAllNotifications
        - deleteNotification
        - createOfferInvite
        - publishOfferInvite
        - createOfferNotification
        - updateWallCrossInfo
        - deleteWallCrossInfo
        - createWallCrossInvite
        - respondToWallCrossInvite
        - voidWallCrossGatekeeperInvite
        - insertWallCrossInviteResponse
        - insertWallCrossInvite
        - insertWallCrossInviteGatekeeper
        - createOfferInsider
        - amendOfferInsider
        - cleanseOffer
        - acceptOfferTerms
        - insertOfferTerms
        - amendOrganisation
        - deleteOrganisation
        - createTask
        - updateTaskStatus
        - updateTaskMessage
        - updateOrderMetrics
        - updateAllocationMetrics
        - insertAllocationMetrics
        - publishAllocationMetrics
        - createSettlementInstruction
        - updateSettlementInstruction
        - deleteSettlementInstruction
        - deleteNamedSettlementInstruction
        - createSettlementBook
        - settleObligation
        - updateOfferRoleTimestamp

    resource:
      type: string
      description: "Used for RBAC purposes. Type of resource command is being applied to. Note this is not the scope. 
                    The scope defines what subset of objects of this resource type the permission are applied on. 
                    For instance, a write operation on offer 'A' will have a scope of 'A' and resource type of 'offer'."
      enum:
        - permission
        - offer
        - invitation
        - order
        - document
        - user
        - order_summary
        - email
        - wallcrossing

    ack:
      type: object

      required:
        - id
        - ids
      properties:
        id:
          type: string
        ids:
          type: array
          items:
            type: string

    nack:
      type: object
      required:
        - message
      properties:
        message:
          type: string

    createOffer:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
          description: The name of the offer, this will be sent on notifications and be used
          x-go-custom-tag: validate:"required,min=3,max=128"
        type:
          type: string
          description: The type of the offer
          x-go-custom-tag: validate:"oneof=IPO 'Follow On' 'Retail Bond'"
        inside_time:
          type: string
          format: "date-time"
        issued_by:
          type: string
        shareholders_only:
          type: boolean
        inside_information:
          type: boolean
        registration_date:
          type: string
          format: "date-time"
        open_date:
          type: string
          format: "date-time"
        close_date:
          type: string
          format: "date-time"
        min_order_amount:
          type: number
        description:
          type: string
        details:
          type: string
        allocation_principles:
          type: string
        settlement_details:
          type: string
        currency:
          type: string
        isin:
          type: string
        sedol:
          type: string
        ticker:
          type: string
        crest_id:
          type: string
        raise_amount:
          type: number
        offer_price:
          type: number
        settlement_date:
          type: string
          format: "date-time"

    removeOffer:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    insertOffer:
      type: object
      required:
        - name
        - type
        - external_id
        - is_launched

      properties:
        external_id:
          "$ref": "#/components/schemas/identifier"
        name:
          type: string
          description: The name of the offer, this will be sent on notifications and be used
          x-go-custom-tag: validate:"required,min=3,max=128"
        type:
          type: string
          description: The type of the offer
          x-go-custom-tag: validate:"oneof=IPO 'Follow On' 'Retail Bond'"
        issued_by:
          type: string
        shareholders_only:
          type: boolean
        inside_information:
          type: boolean
        registration_date:
          type: string
          format: "date-time"
        created_date:
          type: string
          format: "date-time"
        open_date:
          type: string
          format: "date-time"
        close_date:
          type: string
          format: "date-time"
        min_order_amount:
          type: number
        description:
          type: string
        details:
          type: string
        allocation_principles:
          type: string
        settlement_details:
          type: string
        currency:
          type: string
        isin:
          type: string
        sedol:
          type: string
        ticker:
          type: string
        crest_id:
          type: string
        raise_amount:
          type: number
        offer_price:
          type: number
        status:
          type: string
        document:
          type: array
          items:
            "$ref": "#/components/schemas/offerDocument"
        wallcrossinfo_id:
          "$ref": "#/components/schemas/identifier"
        is_launched:
          type: boolean
        price_range_low:
          type: number
        price_range_high:
          type: number
        settlement_date:
          type: string
          format: "date-time"

    acceptOfferTerms:
      type: object
      required:
        - offer_id
      properties:
        offer_id:
          type: string
        receive_commission:
          type: boolean

    insertOfferTerms:
      type: object
      required:
        - offer_id
        - system_id
        - accepted_time
      properties:
        offer_id:
          "$ref": "#/components/schemas/identifier"
        system_id:
          type: string
        accepted_time:
          type: string
          format: date-time
        receive_commission:
          type: boolean
        retail_offer_notice_id:
          type: string
        accepted_by:
          type: string
        accepted_by_email:
          type: string

    amendOffer:
      type: object
      required:
        - id
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        name:
          type: string
          description: The name of the offer, this will be sent on notifications and be used
        type:
          type: string
          description: The type of the offer
        issued_by:
          type: string
        shareholders_only:
          type: boolean
        inside_information:
          type: boolean
        registration_date:
          type: string
          format: "date-time"
        open_date:
          type: string
          format: "date-time"
        close_date:
          type: string
          format: "date-time"
        min_order_amount:
          type: number
        description:
          type: string
        details:
          type: string
        allocation_principles:
          type: string
        settlement_details:
          type: string
        currency:
          type: string
        isin:
          type: string
        sedol:
          type: string
        ticker:
          type: string
        crest_id:
          type: string
        raise_amount:
          type: number
        offer_price:
          type: number
        cleansing_time:
          type: string
          format: "date-time"
        price_range_low:
          type: number
        price_range_high:
          type: number
        security_name:
          type: string
        settlement_date:
          type: string
          format: "date-time"

    updateOfferStatus:
      type: object
      required:
        - offer_id
      properties:
        offer_id:
          "$ref": "#/components/schemas/identifier"
        event:
          type: string
          description: The event that should trigger a state move - provided its legal
        status:
          type: string
          description: The status we're updating to.
        set_open_time:
          type: boolean
          description: The open time should be set by this state change
        set_close_time:
          type: boolean
          description: The close time should be set by this state change
        cleansing_time:
          type: string
          format: date-time

    insertDocument:
      type: object
      required:
        - offer_id
        - id

      properties:
        id:
          type: string
          description: Unique ID of the document
        offer_id:
          $ref: "#/components/schemas/identifier"
        title:
          type: string
          description: Title of the document
        filename:
          type: string
          description: Filename of the document
        type:
          type: string
          description: "The document type, blank if this isn't changing"

        mime_type:
          type: string
          description: "The mime type"

        size:
          type: number
          description: "The size of the file"

    createTask:
      type: object
      required:
        - created_by
        - created
      properties:
        created_by:
          type: string
          description: The user that started the task
        created:
          type: string
          format: date-time

    updateTaskMessage:
      type: object
      required:
        - id
        - message
      properties:
        id:
          type: string
        message:
          type: string
          
    updateTaskStatus:
      type: object
      required:
        - id
        - status
      properties:
        id:
          type: string
        status:
          type: string
        message:
          type: string
        response:
          type: string

    amendOrganisation:
      type: object
      properties:
        name:
          type: string
          description: The full legal name of the organisation
        crest_account_name:
          type: string
          description: The name of the crest account
        crest_participant_id:
          type: string
          description: The crest participant id
        account_name:
          type: string
          description: "The name of the commission/settlement account"
        bank_name:
          type: string
          description: "The name of the bank for the commission/settlement account"
        swift_bic:
          type: string
          description: "The BIC code of the bank for the commission/settlement account"
        account_number:
          type: string
          description: "The account number of the bank account for the commission/settlement account"
        sort_code:
          type: string
          description: "The sort code of the bank account for the commission/settlement account"
        address1:
          type: string
          description: "The address of the organisation"
        address2:
          type: string
          description: "The address of the organisation"
        address3:
          type: string
          description: "The address of the organisation"
        address4:
          type: string
          description: "The address of the organisation"
        address_city:
          type: string
          description: "The address of the organisation"
        address_postal_code:
          type: string
          description: "The address of the organisation"
        address_country:
          type: string
          description: "The address of the organisation"
        settlement_reference:
          type: string
          description: "The settlement reference of the organisation"

    deleteOrganisation:
      type: object

    createDocument:
      type: object
      required:
        - offer_id
        - id

      properties:
        id:
          type: string
          description: Unique ID of the document
        offer_id:
          $ref: "#/components/schemas/identifier"
        title:
          type: string
          description: Title of the document
        filename:
          type: string
          description: Filename of the document
        type:
          type: string
          description: "The document type, blank if this isn't changing"

        mime_type:
          type: string
          description: "The mime type"

        size:
          type: number
          description: "The size of the file"

    amendDocument:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          type: string
          description: Unique ID of the document
        offer_id:
          $ref: "#/components/schemas/identifier"
        title:
          type: string
          description: Title of the document
        filename:
          type: string
          description: Filename of the document

        type:
          type: string
          description: "The document type, blank if this isn't changing"

        mime_type:
          type: string
          description: "The mime type"

        size:
          type: number
          description: "The size of the file"

    deleteDocument:
      type: object
      required:
        - id
        - offer_id
      properties:
        id:
          type: string
          description: Unique ID of the document
        offer_id:
          $ref: "#/components/schemas/identifier"

    grantPerm:
      type: object
      required:
        - user
        - role
        - scope
      properties:
        user:
          type: string
          description: The user identity to apply the existing role onto.
          x-go-custom-tag: validate:"excludesall=0x2C"
        role:
          description: The exact case sensitive name of the role to apply onto the user identity.
          type: string
          x-go-custom-tag: validate:"excludesall=0x2C"
        scope:
          description: The scope of the permission/role being granted. The role specifies the generic resource, the scope specifies what subset of the resource it is applied to.
          type: string
          x-go-custom-tag: validate:"excludesall=0x2C"

    revokePerm:
      type: object
      required:
        - user
        - role
        - scope
      properties:
        user:
          type: string
          description: The user identity to apply the existing role onto.
          x-go-custom-tag: validate:"excludesall=0x2C"
        role:
          description: The exact case sensitive name of the role to apply onto the user identity.
          type: string
          x-go-custom-tag: validate:"excludesall=0x2C"
        scope:
          description: The scope of the permission/role being granted. The role specifies the generic resource, the scope specifies what subset of the resource it is applied to.
          type: string
          x-go-custom-tag: validate:"excludesall=0x2C"

    readPerms:
      type: object
      description: Reads all the permissions from the local storage (i.e. the memory) of a particular service.

    createEmail:
      type: object
      required:
        - subject
        - to
        - template_type
        - msg
      properties:
        subject:
          type: string
          description: the subject of the email
        to:
          type: string
          description: the recipient of the email
        cc:
          type: string
          description: the carbon copy recipients of the email
        bcc:
          type: string
          description: the blind carbon copy recipients of the email
        template_type:
          type: string
          description: the name of the template to use (must match the registrar in the notification service)
        msg:
          type: array
          description: the data to feed into the supplied template
          items:
            $ref: '#/components/schemas/EmailContent'

    createEmailForOfferUsers:
      type: object
      description: Creates an email for all team members of a specified offer
      required:
        - offer_id
        - subject
        - template_type
        - msg
      properties:
        offer_id:
          $ref: "#/components/schemas/identifier"
        subject:
          type: string
          description: the subject of the email
        cc:
          type: string
          description: the carbon copy recipients of the email
        bcc:
          type: string
          description: the blind carbon copy recipients of the email
        template_type:
          type: string
          description: the name of the template to use (must match the registrar in the notification service)
        msg:
          type: array
          description: the data to feed into the supplied template
          items:
            $ref: '#/components/schemas/EmailContent'

    EmailContent:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
        value:
          type: object

    OrderAggregate:
      type: object
      required:
        - notional_value
        - applications
      properties:
        notional_value:
          type: number
          description: The notional value of all the orders in this aggregate
        applications:
          type: number
          description: The number of applications in this aggregate
        existing_holding:
          type: number
          description: The existing holding in this aggregate - only specified for shareholding aggregations

    createOrderSummary:
      type: object
      required:
        - offer_id
        - confirmed
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        confirmed:
          type: boolean
        shareholder:
          $ref: "#/components/schemas/OrderAggregate"
        non-shareholder:
          $ref: "#/components/schemas/OrderAggregate"

    insertOrderSummary:
      type: object
      required:
        - id
        - offer_id
        - offer_name
        - order_type
        - status
        - created
        - entered_by
        - entered_by_email
        - totals
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_name:
          type: string
        order_book_id:
          "$ref": "#/components/schemas/identifier"
        order_type:
          type: string
        status:
          type: string
        created:
          type: string
          format: date-time
        entered_by:
          type: string
        entered_by_email:
          type: string
        totals:
          $ref: "#/components/schemas/OrderAggregate"
        shareholder:
          $ref: "#/components/schemas/OrderAggregate"
        non-shareholder:
          $ref: "#/components/schemas/OrderAggregate"

    patchOrderSummary:
      type: object
      required:
        - offer_id
        - order_summary_id
      properties:
        offer_id:
          type: string
          description: The offer ID, local to the current system.
        order_summary_id:
          type: string
          description: The order summary ID, local to the current system.
        new_status:
          type: string
          description: A valid order status string

    deleteOrderSummary:
      type: object
      description: Deletes the specified order summary on the offer. Different behaviour for linked and dangling orders.
      required:
        - offer_id
        - id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        id:
          $ref: '#/components/schemas/identifier'

    clearOrderSummaries:
      type: object
      description: Clears all pending (for e.g. unconfirmed) order summaries (i.e. sets them as deleted) on the offer.
      required:
        - offer_id
      properties:
        offer_id:
          type: string
          description: The id of the offer

    createOrderBook:
      type: object
      description: Creates an empty order book in the system as well as the wrapping order summary.
      required:
        - offer_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'

        # Book ID only present if we are referring to an external book ID that exists already. Won't be on the intermediary.
        book_id:
          $ref: '#/components/schemas/identifier'
          x-go-custom-tag: validate:"omitempty"

    createAllocationBook:
      type: object
      description: Creates an empty allocation book in the system as well as the wrapping allocation summary.
      required:
        - offer_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        offer_price:
          type: number

    insertOrderBook:
      type: object
      description: Creates an empty order book in the system as well as the wrapping order summary.
      required:
        - offer_id
        - book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        book_id:
          $ref: '#/components/schemas/identifier'

    insertAllocationBook:
      type: object
      description:  Creates an empty allocation book in the system as well as the wrapping allocation summary.
      required:
        - offer_id
        - book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        book_id:
          $ref: '#/components/schemas/identifier'

    appendOrderBook:
      type: object
      description: Appends data to an existing order book.
      required:
        - offer_id
        - order_book_id
        - orders
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        order_book_id:
          $ref: '#/components/schemas/identifier'
        orders:
          type: array
          description: The orders that are to be appended to existing detailed orders.
          x-go-custom-tag: validate:"required,min=1,max=6000"
          items:
            $ref: '#/components/schemas/detailedOrder'

    appendAllocationBook:
      type: object
      description: Appends data to an existing allocation book.
      required:
        - offer_id
        - allocation_book_id
        - allocations
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        allocation_book_id:
          $ref: '#/components/schemas/identifier'
        allocations:
          type: array
          description: The allocations that are to be appended to existing detailed allocations.
          x-go-custom-tag: validate:"required,min=1,max=6000"
          items:
            $ref: '#/components/schemas/detailedAllocation'

    insertOrderBookOrders:
      type: object
      description: Appends data to an existing order book.
      required:
        - offer_id
        - order_book_id
        - orders
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        order_book_id:
          $ref: '#/components/schemas/identifier'
        orders:
          type: array
          description: The orders that are to be appended to existing detailed orders.
          x-go-custom-tag: validate:"required,min=1,max=1000"
          items:
            $ref: '#/components/schemas/detailedOrder'

    insertAllocationBookOrders:
      type: object
      description: Appends data to an existing allocation book.
      required:
        - offer_id
        - allocation_book_id
        - allocations
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        allocation_book_id:
          $ref: '#/components/schemas/identifier'
        allocations:
          type: array
          description: The allocations that are to be appended to existing detailed allocations.
          x-go-custom-tag: validate:"required,min=1,max=6000"
          items:
            $ref: '#/components/schemas/detailedAllocation'

    finalizeOrderBook:
      type: object
      description: Finalizes the order book (and therefore the wrapping order summary structure).
      timeout: 300
      required:
        - offer_id
        - order_book_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        order_book_id:
          $ref: '#/components/schemas/identifier'

    finalizeAllocationBook:
      type: object
      description: Finalizes the allocation book (and therefore the wrapping allocation summary structure).
      timeout: 300
      required:
        - offer_id
        - allocation_book_id
        - entered_by
        - ignore_row_validation
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        offer_allocation_id:
          $ref: '#/components/schemas/identifier'
        allocation_book_id:
          $ref: '#/components/schemas/identifier'
        entered_by:
          type: string
        ignore_row_validation:
          type: boolean

    notifyOfferAllocation:
      type: object
      description: Notifies all eligible intermediaries (invited to the offer) with their part of the allocation book.
      required:
        - offer_id
        - offer_allocation_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        offer_allocation_id:
          $ref: '#/components/schemas/identifier'

    clearOfferAllocation:
      type: object
      description: Clears (i.e. invalidates & unlinks) the specified offer allocation from the offer.
      required:
        - offer_id
        - offer_allocation_id
      properties:
        offer_id:
          $ref: '#/components/schemas/identifier'
        offer_allocation_id:
          $ref: '#/components/schemas/identifier'

    detailedOrder:
      type: object
      description: A detailed order - i.e. a normalized and parsed version of a row in an order book spreadsheet

      properties:
        client_order_ref:
          type: string
          description: External client order reference
          x-go-custom-tag: validate:"omitempty,min=1,max=100"
        existing_holding:
          type: number
          description: Existing shareholding
          x-go-custom-tag: validate:"required,min=0"
        tax_wrapper:
          type: boolean
          description: Is this an order originating from tax-wrapped funds?
        notional_value:
          type: number
          description: The notional value of this order

    detailedAllocation:
      type: object
      description: A detailed allocation - i.e. a normalized and parsed version of a row in an allocation spreadsheet
      required:
        - client_order_ref
        - existing_holding
        - commission_due
        - tax_wrapper
        - notional_value
        - intermediary
        - num_applications
        - order_quantity
      properties:
        intermediary:
          type: string
          description: The name of the intermediary for this allocation (this and the client order ref makes a unique composite key).
          x-go-custom-tag: validate:"omitempty,min=1,max=30"
        client_order_ref:
          type: string
          description: External client order reference
          x-go-custom-tag: validate:"omitempty,min=1,max=100"
        existing_holding:
          type: number
          description: Existing shareholding
          x-go-custom-tag: validate:"required,min=0"
        commission_due:
          type: boolean
          description: Does this order pay commission to the intermediary?
        tax_wrapper:
          type: boolean
          description: Is this an order originating from tax-wrapped funds?
        notional_value:
          type: number
          description: The notional value of this order
        allocation:
          type: number
          description: The allocated amount
        num_applications:
          type: number
          description: The number of applications made in this order (detailed line = 1, summary = potentially many more).
        order_quantity:
          type: number
          description: The number of shares in this order (order value / offer price truncated).

    deleteUser:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    createUser:
      type: object
      required:
        - email
        - role
        - gatekeeper
      properties:
        email:
          type: string
        name:
          type: string
        role:
          type: string
        gate_keeper:
          type: boolean

    amendUser:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        ext_id:
          type: string
        email:
          type: string
        name:
          type: string
        role:
          type: string
          enum:
            - manager
            - editor
        gatekeeper:
          type: boolean

    inviteUser:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    activateUser:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    userActive:
      type: object
      required:
        - id
      properties:
        id:
          type: string


    failUserCreation:
      type: object
      required:
        - id
        - message
      properties:
        id:
          type: string
        message:
          type: string


    identifier:
      type: object
      required:
        - id
        - id_type
      properties:
        id:
          type: string
          description: The ID of the resource that we are identifying.
        id_type:
          type: string
          description: Defines the location of the resource that this identifier relates to (for example offers are local to the bank but remote to the intermediary).
          x-go-custom-tag: validate:"min=3,oneof=remote local"
        system_id:
          type: string
          description: Optional identifier that describes the system if the id relates to a remote resource. Otherwise empty.

    offerDocument:
      type: object
      properties:
        id:
          type: string

        offer_id:
          type: string

        title:
          type: string
          description: Title of the document

        filename:
          type: string
          description: Filename of the document

        type:
          type: string
          description: "The document type, blank if this isn't changing"

        mime_type:
          type: string
          description: "The mime type"

        size:
          type: number
          description: "The size of the file"

        status:
          type: string
          description: "The document status"

    createNotification:
      type: object
      required:
        - user_id
        - title
        - message
        - type
        - action_label
        - category
      properties:
        user_id:
          type: string
        title:
          type: string
        message:
          type: string
        type:
          type: string
        action_label:
          type: string
        category:
          type: string
        target_id:
          type: string

    createNotificationForOfferUsers:
      type: object
      description: Creates a notification for all team members of a specified offer
      required:
        - offer_id
        - title
        - message
        - type
        - category
      properties:
        offer_id:
          $ref: "#/components/schemas/identifier"
        title:
          type: string
        message:
          type: string
        type:
          type: string
        category:
          type: string

    readNotification:
      type: object
      required:
        - id
        - user_id
      properties:
        id:
          type: string
        user_id:
          type: string

    unreadNotification:
      type: object
      required:
        - id
        - user_id
      properties:
        id:
          type: string
        user_id:
          type: string

    readAllNotifications:
      type: object
      required:
        - user_id
      properties:
        user_id:
          type: string

    deleteNotification:
      type: object
      required:
        - id
        - user_id
      properties:
        id:
          type: string
        user_id:
          type: string

    intermediary:
      type: object
      required:
        - system_id
        - display_name
      properties:
        system_id:
          type: string
        display_name:
          type: string
        master_agreement_signed:
          type: boolean

    publishOfferInvite:
      type: object
      required:
        - offer_id
        - invite_ids
      properties:
        offer_id:
          type: string
        invite_ids:
          type: array
          items:
            type: string

    createOfferInvite:
      type: object
      required:
        - offer_id
        - intermediaries
      properties:
        offer_id:
          type: string
        intermediaries:
          type: array
          items:
            "$ref": "#/components/schemas/intermediary"

    createOfferNotification:
      type: object
      required:
        - id
        - notificationType
        - intermediaries
      properties:
        id:
          type: string
        intermediaries:
          type: array
          items:
            "$ref": "#/components/schemas/intermediary"
        subject:
          type: string
        message:
          type: string
        notificationType:
          type: string

    updateWallCrossInfo:
      type: object
      required:
        - offer_id
      properties:
        offer_id:
          type: string
        subject:
          type: string
        consent_to_cross:
          type: string
        offer_outline_document_id:
          type: string

    insertWallCrossInvite:
      type: object
      required:
        - external_id
        - info_id
        - wallcross_container_id
        - time_sent
        - status
      properties:
        external_id:
          "$ref": "#/components/schemas/identifier"
        info_id:
          "$ref": "#/components/schemas/identifier"
        wallcross_container_id:
          type: string
        subject:
          type: string
        consent_to_cross:
          type: string
        offer_outline_document_id:
          type: string
        time_sent:
          type: string
          format: "date-time"
        status:
          type: string

    deleteWallCrossInfo:
      type: object
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    createWallCrossInvite:
      type: object
      required:
        - offer_id
        - intermediaries
      properties:
        offer_id:
          type: string
        intermediaries:
          type: array
          items:
            "$ref": "#/components/schemas/intermediary"

    insertWallCrossInviteGatekeeper:
      type: object
      required:
        - user_id
        - invite_id
      properties:
        user_id:
          type: string
        invite_id:
          type: string

    createOfferInsider:
      type: object
      required:
        - event
        - status
      properties:
        event:
          type: string
        status:
          type: string
        offer_id:
          type: string
        user_id:
          type: string
        user_name:
          type: string
        user_email:
          type: string
        wallcross_invite_id:
          type: string
        inside_time:
          type: string
          format: "date-time"
        response_time:
          type: string
          format: "date-time"

    updateOfferRoleTimestamp:
          type: object
          required:
            - offer_id
            - user_id
          properties:
            offer_id:
              type: string
            user_id:
              type: string


    amendOfferInsider:
      type: object
      required:
        - offer_id
        - insider_id
      properties:
        offer_id:
          type: string
        insider_id:
          type: string
        inside_time:
          type: string
          format: "date-time"

    respondToWallCrossInvite:
      type: object
      required:
        - id
        - status
      properties:
        id:
          type: string
        status:
          type: string

    voidWallCrossGatekeeperInvite:
      type: object
      required:
        - ids
        - offer_id
      properties:
        ids:
          type: array
          items:
            type: string
        offer_id:
          type: string

    insertWallCrossInviteResponse:
      type: object
      required:
        - invite_id
        - intermediary
        - user_name
        - user_email
        - status
        - time_responded
      properties:
        invite_id:
          "$ref": "#/components/schemas/identifier"
        intermediary:
          type: string
        time_responded:
          type: string
          format: "date-time"
        user_name:
          type: string
        user_email:
          type: string
        status:
          type: string

    cleanseOffer:
      type: object
      required:
        - id
        - cleansed_time
      properties:
        id:
          "$ref": "#/components/schemas/identifier"
        cleansed_time:
          type: string
          format: "date-time"

    confirmOrderSummary:
      type: object
      required:
        - offer_id
        - id
      properties:
        offer_id:
          type: string
        id:
          type: string

    rejectOrderSummary:
      type: object
      required:
        - offer_id
        - id
      properties:
        offer_id:
          "$ref": "#/components/schemas/identifier"
        id:
          "$ref": "#/components/schemas/identifier"

    acceptOrderSummary:
      type: object
      required:
        - offer_id
        - id
      properties:
        offer_id:
          $ref: "#/components/schemas/identifier"
        id:
          $ref: "#/components/schemas/identifier"

    updateOrderMetrics:
      type: object
      required:
        - offer_id
        - order_summary_id
      properties:
        offer_id:
          type: string
        order_summary_id:
          type: string

    updateAllocationMetrics:
      type: object
      required:
        - offer_id
        - offer_allocation_id
        - allocation_book_id
        - intermediary
        - shareholder_allocated_value
        - shareholder_allocated_qty
        - nonshareholder_allocated_value
        - nonshareholder_allocated_qty
      properties:
        offer_id:
          type: string
        offer_allocation_id:
          type: string
        allocation_book_id:
          type: string
        intermediary:
          type: string
        shareholder_allocated_value:
          type: number
        shareholder_allocated_qty:
          type: number
        nonshareholder_allocated_value:
          type: number
        nonshareholder_allocated_qty:
          type: number

    insertAllocationMetrics:
      type: object
      required:
        - timestamp
        - offer_id
        - offer_name
        - offer_type
        - offer_currency
        - intermediary
        - latest
        - shareholder_allocated_value
        - shareholder_allocated_qty
        - nonshareholder_allocated_value
        - nonshareholder_allocated_qty
      properties:
        timestamp:
          type: string
          format: date-time
        offer_id:
          "$ref": "#/components/schemas/identifier"
        offer_name:
          type: string
        offer_type:
          type: string
        offer_currency:
          type: string
        intermediary:
          type: string
        latest:
          type: boolean
        shareholder_allocated_value:
          type: number
        shareholder_allocated_qty:
          type: number
        nonshareholder_allocated_value:
          type: number
        nonshareholder_allocated_qty:
          type: number

    publishAllocationMetrics:
      type: object
      required:
        - offer_id
        - intermediary
      properties:
        offer_id:
          type: string
        intermediary:
          type: string
        timestamp:
          type: string
          format: "date-time"

    createSettlementInstruction:
      type: object
      required:
        - intermediary
        - settlement_reference
      properties:
        intermediary:
          type: string
        settlement_reference:
          type: string

    updateSettlementInstruction:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        settlement_reference:
          type: string

    deleteSettlementInstruction:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    deleteNamedSettlementInstruction:
      type: object
      required:
        - name
      properties:
        name:
          type: string

    createSettlementBook:
      type: object
      required:
        - offer_id
      properties:
        offer_id:
          type: string

    settleObligation:
      type: object
      required:
        - id
        - fully_settle
      properties:
        id:
          type: string
        fully_settle:
          type: boolean
        quantity:
          type: number

paths:
  /offer:
    post:
      tags:
        - offers
      summary: Invokes the creation of a new offer
      description: Creates a new offer by transforming the request into a command
      operationId: newOffer
      responses:
        '200':
          description: ACK - Successful request; to be processed for creation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ack'
        '400':
          description: NACK - Invalid offer data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/nack'
      security:
        - auth:
            - 'offer:write'
      requestBody:
        $ref: '#/components/schemas/createOffer'
