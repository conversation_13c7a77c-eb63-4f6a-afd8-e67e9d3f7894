<PERSON><PERSON><PERSON>,Version,LicenseFile
"capnproto.org/go/capnp/v3","v3.1.0-alpha.1","LICENSE"
"cel.dev/expr","v0.19.1","LICENSE"
"cloud.google.com/go","v0.110.7","LICENSE"
"cloud.google.com/go/accessapproval","v1.7.1","LICENSE"
"cloud.google.com/go/accesscontextmanager","v1.8.1","LICENSE"
"cloud.google.com/go/aiplatform","v1.48.0","LICENSE"
"cloud.google.com/go/analytics","v0.21.3","LICENSE"
"cloud.google.com/go/apigateway","v1.6.1","LICENSE"
"cloud.google.com/go/apigeeconnect","v1.6.1","LICENSE"
"cloud.google.com/go/apigeeregistry","v0.7.1","LICENSE"
"cloud.google.com/go/appengine","v1.8.1","LICENSE"
"cloud.google.com/go/area120","v0.8.1","LICENSE"
"cloud.google.com/go/artifactregistry","v1.14.1","LICENSE"
"cloud.google.com/go/asset","v1.14.1","LICENSE"
"cloud.google.com/go/assuredworkloads","v1.11.1","LICENSE"
"cloud.google.com/go/automl","v1.13.1","LICENSE"
"cloud.google.com/go/baremetalsolution","v1.1.1","LICENSE"
"cloud.google.com/go/batch","v1.3.1","LICENSE"
"cloud.google.com/go/beyondcorp","v1.0.0","LICENSE"
"cloud.google.com/go/bigquery","v1.53.0","LICENSE"
"cloud.google.com/go/billing","v1.16.0","LICENSE"
"cloud.google.com/go/binaryauthorization","v1.6.1","LICENSE"
"cloud.google.com/go/certificatemanager","v1.7.1","LICENSE"
"cloud.google.com/go/channel","v1.16.0","LICENSE"
"cloud.google.com/go/cloudbuild","v1.13.0","LICENSE"
"cloud.google.com/go/clouddms","v1.6.1","LICENSE"
"cloud.google.com/go/cloudtasks","v1.12.1","LICENSE"
"cloud.google.com/go/compute","v1.23.0","LICENSE"
"cloud.google.com/go/compute/metadata","v0.6.0","LICENSE"
"cloud.google.com/go/contactcenterinsights","v1.10.0","LICENSE"
"cloud.google.com/go/container","v1.24.0","LICENSE"
"cloud.google.com/go/containeranalysis","v0.10.1","LICENSE"
"cloud.google.com/go/datacatalog","v1.16.0","LICENSE"
"cloud.google.com/go/dataflow","v0.9.1","LICENSE"
"cloud.google.com/go/dataform","v0.8.1","LICENSE"
"cloud.google.com/go/datafusion","v1.7.1","LICENSE"
"cloud.google.com/go/datalabeling","v0.8.1","LICENSE"
"cloud.google.com/go/dataplex","v1.9.0","LICENSE"
"cloud.google.com/go/dataproc/v2","v2.0.1","LICENSE"
"cloud.google.com/go/dataqna","v0.8.1","LICENSE"
"cloud.google.com/go/datastore","v1.13.0","LICENSE"
"cloud.google.com/go/datastream","v1.10.0","LICENSE"
"cloud.google.com/go/deploy","v1.13.0","LICENSE"
"cloud.google.com/go/dialogflow","v1.40.0","LICENSE"
"cloud.google.com/go/dlp","v1.10.1","LICENSE"
"cloud.google.com/go/documentai","v1.22.0","LICENSE"
"cloud.google.com/go/domains","v0.9.1","LICENSE"
"cloud.google.com/go/edgecontainer","v1.1.1","LICENSE"
"cloud.google.com/go/errorreporting","v0.3.0","LICENSE"
"cloud.google.com/go/essentialcontacts","v1.6.2","LICENSE"
"cloud.google.com/go/eventarc","v1.13.0","LICENSE"
"cloud.google.com/go/filestore","v1.7.1","LICENSE"
"cloud.google.com/go/firestore","v1.12.0","LICENSE"
"cloud.google.com/go/functions","v1.15.1","LICENSE"
"cloud.google.com/go/gkebackup","v1.3.0","LICENSE"
"cloud.google.com/go/gkeconnect","v0.8.1","LICENSE"
"cloud.google.com/go/gkehub","v0.14.1","LICENSE"
"cloud.google.com/go/gkemulticloud","v1.0.0","LICENSE"
"cloud.google.com/go/gsuiteaddons","v1.6.1","LICENSE"
"cloud.google.com/go/iam","v1.1.1","LICENSE"
"cloud.google.com/go/iap","v1.8.1","LICENSE"
"cloud.google.com/go/ids","v1.4.1","LICENSE"
"cloud.google.com/go/iot","v1.7.1","LICENSE"
"cloud.google.com/go/kms","v1.15.0","LICENSE"
"cloud.google.com/go/language","v1.10.1","LICENSE"
"cloud.google.com/go/lifesciences","v0.9.1","LICENSE"
"cloud.google.com/go/logging","v1.7.0","LICENSE"
"cloud.google.com/go/longrunning","v0.5.1","LICENSE"
"cloud.google.com/go/managedidentities","v1.6.1","LICENSE"
"cloud.google.com/go/maps","v1.4.0","LICENSE"
"cloud.google.com/go/mediatranslation","v0.8.1","LICENSE"
"cloud.google.com/go/memcache","v1.10.1","LICENSE"
"cloud.google.com/go/metastore","v1.12.0","LICENSE"
"cloud.google.com/go/monitoring","v1.15.1","LICENSE"
"cloud.google.com/go/networkconnectivity","v1.12.1","LICENSE"
"cloud.google.com/go/networkmanagement","v1.8.0","LICENSE"
"cloud.google.com/go/networksecurity","v0.9.1","LICENSE"
"cloud.google.com/go/notebooks","v1.9.1","LICENSE"
"cloud.google.com/go/optimization","v1.4.1","LICENSE"
"cloud.google.com/go/orchestration","v1.8.1","LICENSE"
"cloud.google.com/go/orgpolicy","v1.11.1","LICENSE"
"cloud.google.com/go/osconfig","v1.12.1","LICENSE"
"cloud.google.com/go/oslogin","v1.10.1","LICENSE"
"cloud.google.com/go/phishingprotection","v0.8.1","LICENSE"
"cloud.google.com/go/policytroubleshooter","v1.8.0","LICENSE"
"cloud.google.com/go/privatecatalog","v0.9.1","LICENSE"
"cloud.google.com/go/pubsub","v1.33.0","LICENSE"
"cloud.google.com/go/pubsublite","v1.8.1","LICENSE"
"cloud.google.com/go/recaptchaenterprise/v2","v2.7.2","LICENSE"
"cloud.google.com/go/recommendationengine","v0.8.1","LICENSE"
"cloud.google.com/go/recommender","v1.10.1","LICENSE"
"cloud.google.com/go/redis","v1.13.1","LICENSE"
"cloud.google.com/go/resourcemanager","v1.9.1","LICENSE"
"cloud.google.com/go/resourcesettings","v1.6.1","LICENSE"
"cloud.google.com/go/retail","v1.14.1","LICENSE"
"cloud.google.com/go/run","v1.2.0","LICENSE"
"cloud.google.com/go/scheduler","v1.10.1","LICENSE"
"cloud.google.com/go/secretmanager","v1.11.1","LICENSE"
"cloud.google.com/go/security","v1.15.1","LICENSE"
"cloud.google.com/go/securitycenter","v1.23.0","LICENSE"
"cloud.google.com/go/servicedirectory","v1.11.0","LICENSE"
"cloud.google.com/go/shell","v1.7.1","LICENSE"
"cloud.google.com/go/spanner","v1.47.0","LICENSE"
"cloud.google.com/go/speech","v1.19.0","LICENSE"
"cloud.google.com/go/storagetransfer","v1.10.0","LICENSE"
"cloud.google.com/go/talent","v1.6.2","LICENSE"
"cloud.google.com/go/texttospeech","v1.7.1","LICENSE"
"cloud.google.com/go/tpu","v1.6.1","LICENSE"
"cloud.google.com/go/trace","v1.10.1","LICENSE"
"cloud.google.com/go/translate","v1.8.2","LICENSE"
"cloud.google.com/go/video","v1.19.0","LICENSE"
"cloud.google.com/go/videointelligence","v1.11.1","LICENSE"
"cloud.google.com/go/vision/v2","v2.7.2","LICENSE"
"cloud.google.com/go/vmmigration","v1.7.1","LICENSE"
"cloud.google.com/go/vmwareengine","v1.0.0","LICENSE"
"cloud.google.com/go/vpcaccess","v1.7.1","LICENSE"
"cloud.google.com/go/webrisk","v1.9.1","LICENSE"
"cloud.google.com/go/websecurityscanner","v1.6.1","LICENSE"
"cloud.google.com/go/workflows","v1.11.1","LICENSE"
"github.com/Azure/azure-sdk-for-go/sdk/azcore","v1.18.0","README.md"
"github.com/Azure/azure-sdk-for-go/sdk/azidentity","v1.8.2","README.md"
"github.com/Azure/azure-sdk-for-go/sdk/azidentity/cache","v0.3.2","README.md"
"github.com/Azure/azure-sdk-for-go/sdk/internal","v1.11.0","README.md"
"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/authorization/armauthorization/v2","v2.2.0","README.md"
"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/storage/armstorage","v1.6.0","README.md"
"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob","v1.6.0","README.md"
"github.com/AzureAD/microsoft-authentication-extensions-for-go/cache","v0.1.1","LICENSE"
"github.com/AzureAD/microsoft-authentication-library-for-go","v1.4.2","LICENSE"
"github.com/BurntSushi/toml","v1.4.0","README.md"
"github.com/GoogleCloudPlatform/opentelemetry-operations-go/detectors/gcp","v1.25.0","LICENSE"
"github.com/Knetic/govaluate","v3.0.1-0.20171022003610-9aa49832a739+incompatible","LICENSE"
"github.com/acarl005/stripansi","v0.0.0-20180116102854-5a71ef0e047d","LICENSE"
"github.com/agnivade/levenshtein","v1.2.1","README.md"
"github.com/alecthomas/kingpin/v2","v2.4.0","README.md"
"github.com/alecthomas/units","v0.0.0-20211218093645-b94a6e3cc137","README.md"
"github.com/antithesishq/antithesis-sdk-go","v0.4.3-default-no-op","LICENSE"
"github.com/arbovm/levenshtein","v0.0.0-20160628152529-48b4e1c0c4d0","LICENSE"
"github.com/armon/go-socks5","v0.0.0-20160902184237-e75332964ef5","LICENSE"
"github.com/avelino/slugify","v0.0.0-20180501145920-855f152bd774","LICENSE"
"github.com/beorn7/perks","v1.0.1","LICENSE"
"github.com/bits-and-blooms/bitset","v1.22.0","LICENSE"
"github.com/bmatcuk/doublestar/v4","v4.8.1","LICENSE"
"github.com/bojanz/currency","v1.3.1","LICENSE"
"github.com/caarlos0/env/v11","v11.3.1","LICENSE.md"
"github.com/casbin/casbin/v2","v2.104.0","LICENSE"
"github.com/casbin/govaluate","v1.3.0","LICENSE"
"github.com/cespare/xxhash/v2","v2.3.0","README.md"
"github.com/cncf/xds/go","v0.0.0-20241223141626-cff3c89139a3","LICENSE"
"github.com/cockroachdb/apd/v3","v3.2.1","LICENSE"
"github.com/colega/zeropool","v0.0.0-20230505084239-6fb4a4f75381","LICENSE"
"github.com/coreos/go-semver","v0.3.1","LICENSE"
"github.com/coreos/go-systemd/v22","v22.5.0","LICENSE"
"github.com/cpuguy83/go-md2man/v2","v2.0.6","LICENSE.md"
"github.com/creack/pty","v1.1.9","LICENSE"
"github.com/d5/tengo/v2","v2.17.0","LICENSE"
"github.com/davecgh/go-spew","v1.1.2-0.20180830191138-d8f796af33cc","LICENSE"
"github.com/decred/dcrd/crypto/blake256","v1.1.0","LICENSE"
"github.com/decred/dcrd/dcrec/secp256k1/v4","v4.4.0","LICENSE"
"github.com/dgryski/go-rendezvous","v0.0.0-20200823014737-9f7001d12a5f","LICENSE"
"github.com/dgryski/trifles","v0.0.0-20230903005119-f50d829f2e54","LICENSE"
"github.com/dustin/go-humanize","v1.0.0","LICENSE"
"github.com/emicklei/go-restful/v3","v3.11.0","LICENSE"
"github.com/envoyproxy/go-control-plane","v0.13.4","LICENSE"
"github.com/envoyproxy/go-control-plane/envoy","v1.32.4","LICENSE"
"github.com/envoyproxy/go-control-plane/ratelimit","v0.1.0","LICENSE"
"github.com/envoyproxy/protoc-gen-validate","v1.2.1","LICENSE"
"github.com/evanphx/json-patch","v5.6.0+incompatible","LICENSE"
"github.com/fatih/camelcase","v1.0.0","LICENSE.md"
"github.com/fergusstrange/embedded-postgres","v1.30.0","LICENSE"
"github.com/frankban/quicktest","v1.14.6","LICENSE"
"github.com/fsnotify/fsnotify","v1.9.0","LICENSE"
"github.com/fxamacker/cbor/v2","v2.8.0","LICENSE"
"github.com/gabriel-vasile/mimetype","v1.4.8","LICENSE"
"github.com/gdamore/encoding","v1.0.0","LICENSE"
"github.com/gdamore/tcell/v2","v2.5.3","LICENSE"
"github.com/go-errors/errors","v1.0.1","LICENSE.MIT"
"github.com/go-logr/logr","v1.4.2","LICENSE"
"github.com/go-logr/stdr","v1.2.2","LICENSE"
"github.com/go-openapi/jsonpointer","v0.21.0","LICENSE"
"github.com/go-openapi/jsonreference","v0.20.2","LICENSE"
"github.com/go-openapi/swag","v0.23.0","LICENSE"
"github.com/go-playground/assert/v2","v2.2.0","LICENSE"
"github.com/go-playground/locales","v0.14.1","LICENSE"
"github.com/go-playground/universal-translator","v0.18.1","LICENSE"
"github.com/go-playground/validator/v10","v10.26.0","LICENSE"
"github.com/go-redis/redis","v6.15.9+incompatible","LICENSE"
"github.com/go-task/slim-sprig","v0.0.0-20210107165309-348f09dbbbc0","README.md"
"github.com/go-task/slim-sprig/v3","v3.0.0","README.md"
"github.com/go-viper/mapstructure/v2","v2.2.1","LICENSE"
"github.com/gobuffalo/here","v0.6.7","LICENSE"
"github.com/goccy/go-json","v0.10.5","LICENSE"
"github.com/gocomply/xsd2go","v0.1.9","LICENSE"
"github.com/godbus/dbus/v5","v5.0.4","LICENSE"
"github.com/gofrs/flock","v0.8.1","LICENSE"
"github.com/gogo/protobuf","v1.3.2","LICENSE"
"github.com/golang-jwt/jwt","v3.2.2+incompatible","LICENSE"
"github.com/golang-jwt/jwt/v4","v4.5.2","LICENSE"
"github.com/golang-jwt/jwt/v5","v5.2.2","LICENSE"
"github.com/golang/glog","v1.2.4","LICENSE"
"github.com/golang/mock","v1.6.0","LICENSE"
"github.com/golang/protobuf","v1.5.4","LICENSE"
"github.com/google/btree","v1.0.1","LICENSE"
"github.com/google/gnostic","v0.6.9","LICENSE"
"github.com/google/gnostic-models","v0.6.8","LICENSE"
"github.com/google/go-cmp","v0.7.0","LICENSE"
"github.com/google/go-tpm","v0.9.3","LICENSE"
"github.com/google/go-tpm-tools","v0.3.13-0.20230620182252-4639ecce2aba","LICENSE"
"github.com/google/gofuzz","v1.2.0","LICENSE"
"github.com/google/pprof","v0.0.0-20241210010833-40e02aabc2ad","LICENSE"
"github.com/google/shlex","v0.0.0-20191202100458-e7afc7fbc510","README"
"github.com/google/uuid","v1.6.0","LICENSE"
"github.com/gookit/color","v1.5.2","LICENSE"
"github.com/gorilla/mux","v1.8.1","LICENSE"
"github.com/gorilla/websocket","v1.5.0","LICENSE"
"github.com/gregjones/httpcache","v0.0.0-20190611155906-901d90724c79","README.md"
"github.com/grpc-ecosystem/go-grpc-prometheus","v1.2.0","LICENSE"
"github.com/grpc-ecosystem/grpc-gateway","v1.16.0","README.md"
"github.com/hashicorp/hcl","v1.0.0","LICENSE"
"github.com/hpcloud/tail","v1.0.0","README.md"
"github.com/iancoleman/strcase","v0.3.0","LICENSE"
"github.com/imdario/mergo","v0.3.13","LICENSE"
"github.com/inconshreveable/mousetrap","v1.0.1","LICENSE"
"github.com/josharian/intern","v1.0.0","license.md"
"github.com/jpillora/backoff","v1.0.0","LICENSE"
"github.com/json-iterator/go","v1.1.12","LICENSE"
"github.com/julienschmidt/httprouter","v1.3.0","LICENSE"
"github.com/keybase/go-keychain","v0.0.0-20231219164618-57a3676c3af6","LICENSE"
"github.com/kisielk/errcheck","v1.5.0","LICENSE"
"github.com/kisielk/gotool","v1.0.0","LICENSE"
"github.com/klauspost/compress","v1.18.0","LICENSE"
"github.com/kr/pretty","v0.3.1","License"
"github.com/kr/pty","v1.1.1","License"
"github.com/kr/text","v0.2.0","License"
"github.com/kylelemons/godebug","v1.1.0","LICENSE"
"github.com/leodido/go-urn","v1.4.0","LICENSE"
"github.com/lestrrat-go/blackmagic","v1.0.2","LICENSE"
"github.com/lestrrat-go/httpcc","v1.0.1","LICENSE"
"github.com/lestrrat-go/httprc","v1.0.6","LICENSE"
"github.com/lestrrat-go/iter","v1.0.2","LICENSE"
"github.com/lestrrat-go/jwx/v2","v2.1.4","LICENSE"
"github.com/lestrrat-go/option","v1.0.1","LICENSE"
"github.com/lib/pq","v1.10.9","LICENSE.md"
"github.com/liggitt/tabwriter","v0.0.0-20181228230101-89fcab3d43de","LICENSE"
"github.com/lucasb-eyer/go-colorful","v1.2.0","LICENSE"
"github.com/magiconair/properties","v1.8.9","LICENSE.md"
"github.com/mailru/easyjson","v0.7.7","LICENSE"
"github.com/markbates/pkger","v0.17.1","LICENSE"
"github.com/mattn/go-colorable","v0.1.14","LICENSE"
"github.com/mattn/go-isatty","v0.0.20","LICENSE"
"github.com/mattn/go-runewidth","v0.0.13","LICENSE"
"github.com/matttproud/golang_protobuf_extensions","v1.0.4","LICENSE"
"github.com/minio/highwayhash","v1.0.3","LICENSE"
"github.com/mitchellh/mapstructure","v1.5.0","LICENSE"
"github.com/moby/spdystream","v0.5.0","LICENSE"
"github.com/modern-go/concurrent","v0.0.0-20180306012644-bacd9c7ef1dd","LICENSE"
"github.com/modern-go/reflect2","v1.0.2","LICENSE"
"github.com/mohae/deepcopy","v0.0.0-20170929034955-c48cc78d4826","LICENSE"
"github.com/monochromegane/go-gitignore","v0.0.0-20200626010858-205db1a8cc00","LICENSE"
"github.com/montanaflynn/stats","v0.7.0","LICENSE"
"github.com/munnerz/goautoneg","v0.0.0-20191010083416-a7dc8b61c822","LICENSE"
"github.com/mwitkow/go-conntrack","v0.0.0-20190716064945-2f068394615f","LICENSE"
"github.com/mxk/go-flowrate","v0.0.0-20140419014527-cca7078d478f","LICENSE"
"github.com/nats-io/jwt/v2","v2.7.3","LICENSE"
"github.com/nats-io/nats-server/v2","v2.11.0","LICENSE"
"github.com/nats-io/nats.go","v1.41.0","LICENSE"
"github.com/nats-io/nkeys","v0.4.10","LICENSE"
"github.com/nats-io/nuid","v1.0.1","LICENSE"
"github.com/nxadm/tail","v1.4.8","LICENSE"
"github.com/onsi/ginkgo","v1.16.5","LICENSE"
"github.com/onsi/ginkgo/v2","v2.23.3","LICENSE"
"github.com/onsi/gomega","v1.37.0","LICENSE"
"github.com/pelletier/go-toml","v1.9.5","LICENSE"
"github.com/pelletier/go-toml/v2","v2.2.4","LICENSE"
"github.com/peterbourgon/diskv","v2.0.1+incompatible","LICENSE"
"github.com/philhofer/fwd","v1.1.2","LICENSE.md"
"github.com/pkg/browser","v0.0.0-20240102092130-5ac0b6a4141c","LICENSE"
"github.com/pkg/diff","v0.0.0-20210226163009-20ebb0f2a09e","LICENSE"
"github.com/pkg/errors","v0.9.1","LICENSE"
"github.com/planetscale/vtprotobuf","v0.6.1-0.20240319094008-0393e58bdf10","LICENSE"
"github.com/pmezard/go-difflib","v1.0.1-0.20181226105442-5d4384ee4fb2","LICENSE"
"github.com/prometheus/client_golang","v1.21.1","LICENSE"
"github.com/prometheus/client_model","v0.6.1","LICENSE"
"github.com/prometheus/common","v0.63.0","LICENSE"
"github.com/prometheus/procfs","v0.16.0","LICENSE"
"github.com/redis/go-redis/v9","v9.7.0","LICENSE"
"github.com/richardlehane/mscfb","v1.0.4","README.md"
"github.com/richardlehane/msoleps","v1.0.4","README.md"
"github.com/rivo/tview","v0.0.0-20230130130022-4a1b7a76c01c","README.md"
"github.com/rivo/uniseg","v0.4.2","README.md"
"github.com/rogpeppe/go-internal","v1.14.1","LICENSE"
"github.com/rs/cors","v1.11.1","LICENSE"
"github.com/rs/xid","v1.6.0","LICENSE"
"github.com/rs/zerolog","v1.34.0","LICENSE"
"github.com/russross/blackfriday/v2","v2.1.0","README.md"
"github.com/sagikazarmark/locafero","v0.9.0","LICENSE"
"github.com/segmentio/asm","v1.2.0","LICENSE"
"github.com/sendgrid/rest","v2.6.9+incompatible","LICENSE"
"github.com/sendgrid/sendgrid-go","v3.16.0+incompatible","LICENSE"
"github.com/shopspring/decimal","v1.4.0","LICENSE"
"github.com/sourcegraph/conc","v0.3.0","LICENSE"
"github.com/spaolacci/murmur3","v1.1.0","LICENSE"
"github.com/spf13/afero","v1.14.0","README.md"
"github.com/spf13/cast","v1.7.1","LICENSE"
"github.com/spf13/cobra","v1.6.1","README.md"
"github.com/spf13/jwalterweatherman","v1.1.0","LICENSE"
"github.com/spf13/pflag","v1.0.6","LICENSE"
"github.com/spf13/viper","v1.20.1","LICENSE"
"github.com/stretchr/objx","v0.5.2","LICENSE"
"github.com/stretchr/testify","v1.10.0","LICENSE"
"github.com/subosito/gotenv","v1.6.0","LICENSE"
"github.com/thedatashed/xlsxreader","v1.2.8","LICENSE"
"github.com/tinylib/msgp","v1.1.9","LICENSE"
"github.com/tj/assert","v0.0.3","LICENSE"
"github.com/urfave/cli","v1.22.15","LICENSE"
"github.com/urfave/cli/v2","v2.27.6","LICENSE"
"github.com/werf/kubedog","v0.9.10","LICENSE"
"github.com/werf/lockgate","v0.1.1","LICENSE"
"github.com/werf/logboek","v0.5.4","LICENSE"
"github.com/x448/float16","v0.8.4","LICENSE"
"github.com/xhit/go-str2duration/v2","v2.1.0","LICENSE"
"github.com/xi2/xz","v0.0.0-20171230120015-48954b6210f8","LICENSE"
"github.com/xlab/treeprint","v1.1.0","LICENSE"
"github.com/xo/terminfo","v0.0.0-20210125001918-ca9a967f8778","LICENSE"
"github.com/xrash/smetrics","v0.0.0-20240521201337-686a1a2994c1","LICENSE"
"github.com/xuri/efp","v0.0.0-20250227110027-3491fafc2b79","LICENSE"
"github.com/xuri/excelize/v2","v2.9.0","LICENSE"
"github.com/xuri/nfp","v0.0.0-20250226145837-86d5fc24b2ba","LICENSE"
"github.com/yuin/goldmark","v1.4.13","LICENSE"
"go.etcd.io/etcd/api/v3","v3.5.21","LICENSE"
"go.etcd.io/etcd/client/pkg/v3","v3.5.21","LICENSE"
"go.etcd.io/etcd/client/v3","v3.5.21","LICENSE"
"go.opentelemetry.io/auto/sdk","v1.1.0","LICENSE"
"go.opentelemetry.io/contrib/detectors/gcp","v1.34.0","LICENSE"
"go.opentelemetry.io/otel","v1.34.0","LICENSE"
"go.opentelemetry.io/otel/metric","v1.34.0","LICENSE"
"go.opentelemetry.io/otel/sdk","v1.34.0","LICENSE"
"go.opentelemetry.io/otel/sdk/metric","v1.34.0","LICENSE"
"go.opentelemetry.io/otel/trace","v1.34.0","LICENSE"
"go.starlark.net","v0.0.0-20200306205701-8dd3e2ee1dd5","LICENSE"
"go.uber.org/atomic","v1.11.0","README.md"
"go.uber.org/automaxprocs","v1.6.0","LICENSE"
"go.uber.org/goleak","v1.3.0","LICENSE"
"go.uber.org/multierr","v1.11.0","README.md"
"go.uber.org/zap","v1.27.0","LICENSE"
"golang.org/x/crypto","v0.37.0","LICENSE"
"golang.org/x/exp","v0.0.0-20240604190554-fc45aab8b7f8","LICENSE"
"golang.org/x/image","v0.18.0","LICENSE"
"golang.org/x/mod","v0.24.0","LICENSE"
"golang.org/x/net","v0.38.0","LICENSE"
"golang.org/x/oauth2","v0.29.0","LICENSE"
"golang.org/x/sync","v0.13.0","LICENSE"
"golang.org/x/sys","v0.32.0","LICENSE"
"golang.org/x/telemetry","v0.0.0-20240521205824-bda55230c457","LICENSE"
"golang.org/x/term","v0.31.0","LICENSE"
"golang.org/x/text","v0.24.0","LICENSE"
"golang.org/x/time","v0.11.0","LICENSE"
"golang.org/x/tools","v0.30.0","LICENSE"
"golang.org/x/xerrors","v0.0.0-20200804184101-5ec99f83aff1","LICENSE"
"google.golang.org/appengine","v1.6.7","LICENSE"
"google.golang.org/genproto","v0.0.0-20230822172742-b8732ec3820d","LICENSE"
"google.golang.org/genproto/googleapis/api","v0.0.0-20250404141209-ee84b53bf3d0","LICENSE"
"google.golang.org/genproto/googleapis/rpc","v0.0.0-20250407143221-ac9807e6c755","LICENSE"
"google.golang.org/grpc","v1.71.1","LICENSE"
"google.golang.org/protobuf","v1.36.6","LICENSE"
"gopkg.in/check.v1","v1.0.0-20201130134442-10cb98267c6c","LICENSE"
"gopkg.in/errgo.v2","v2.1.0","LICENSE"
"gopkg.in/evanphx/json-patch.v4","v4.12.0","LICENSE"
"gopkg.in/fsnotify.v1","v1.4.7","LICENSE"
"gopkg.in/inf.v0","v0.9.1","LICENSE"
"gopkg.in/ini.v1","v1.67.0","LICENSE"
"gopkg.in/natefinch/lumberjack.v2","v2.2.1","LICENSE"
"gopkg.in/tomb.v1","v1.0.0-20141024135613-dd632973f1e7","LICENSE"
"gopkg.in/yaml.v2","v2.4.0","LICENSE"
"gopkg.in/yaml.v3","v3.0.1","LICENSE"
"k8s.io/api","v0.32.3","LICENSE"
"k8s.io/apimachinery","v0.32.3","LICENSE"
"k8s.io/cli-runtime","v0.26.2","LICENSE"
"k8s.io/client-go","v0.32.3","LICENSE"
"k8s.io/klog/v2","v2.130.1","LICENSE"
"k8s.io/kube-openapi","v0.0.0-20241105132330-32ad38e42d3f","LICENSE"
"k8s.io/utils","v0.0.0-20250321185631-1f6e0b77f77e","LICENSE"
"sigs.k8s.io/json","v0.0.0-20241014173422-cfa47c3a1cc8","LICENSE"
"sigs.k8s.io/kustomize/api","v0.12.1","LICENSE"
"sigs.k8s.io/kustomize/kyaml","v0.13.9","LICENSE"
"sigs.k8s.io/randfill","v0.0.0-20250304075658-069ef1bbf016","LICENSE"
"sigs.k8s.io/structured-merge-diff/v4","v4.6.0","LICENSE"
"sigs.k8s.io/yaml","v1.4.0","LICENSE"
