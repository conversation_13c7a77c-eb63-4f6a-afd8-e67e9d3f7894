package common

import (
	"io"
	"os/exec"
)

func Log(outp io.Writer, mesg string) {
	_, err := io.WriteString(outp, mesg)
	if err != nil {
		panic(err)
	}
	_, err = io.WriteString(outp, "\n")
	if err != nil {
		panic(err)
	}
}

func Exec(outp io.Writer, name string, args ...string) error {
	cmd := exec.Command(name, args...)

	cmd.Stdout = outp
	cmd.Stderr = outp

	if err := cmd.Run(); err != nil {
		Log(outp, err.Error())
		return err
	}

	return nil
}
