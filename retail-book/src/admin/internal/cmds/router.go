package cmds

type Command string

type CommandRouter interface {
	// Execute executes the Command with any arguments.
	Execute(cmd Command, args any)

	// OnSelectedHandler returns a function which can be used as a selected
	// handler in tview which executes the command with args.
	OnSelectedHandler(cmd Command, args any) func()

	// RegisterCommand registers an arbitrary handling function for a given Command.
	RegisterCommand(cmd Command, cb func(any))
}

type commandRouter struct {
	handlers map[Command]func(any)
}

func NewCommandRouter() CommandRouter {
	return &commandRouter{
		handlers: make(map[Command]func(any)),
	}
}

func (c *commandRouter) Execute(cmd Command, args any) {
	c.handlers[cmd](args)
}

func (c *commandRouter) OnSelectedHandler(cmd Command, args any) func() {
	return func() {
		c.Execute(cmd, args)
	}
}

func (c *commandRouter) RegisterCommand(cmd Command, cb func(any)) {
	c.handlers[cmd] = cb
}

// Supported commands
const (
	ShowScreen Command = "show_screen"
	Back       Command = "back"
)

// ShowScreenArgs - Command arguments for the ShowScreen command.
type ShowScreenArgs struct {
	Screen string
}
