package screens

import (
	"github.com/rivo/tview"
	"retailbook.com/rb/admin/internal/cmds"
)

func MainMenu(router cmds.CommandRouter) tview.Primitive {

	list := tview.NewList().
		AddItem("Tech Ops", "Technology operations functions", '1', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{"techops"})).
		AddItem("Dev Ops", "Development operation functions", '2', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{"devops"}))

	// TODO => More menu commands
	/*.
	AddItem("Commands", "Command related functions", '2', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{"commands"})).
	AddItem("Events", "Event related functions", '3', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{"commands"}))


	*/

	list.SetBorder(true)
	return list
}
