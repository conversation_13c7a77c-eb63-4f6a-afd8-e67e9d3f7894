package screens

import (
	"github.com/rivo/tview"
	"retailbook.com/rb/admin/internal/cmds"
	"retailbook.com/rb/admin/internal/devops"
)

func devOps(router cmds.CommandRouter) tview.Primitive {
	list := tview.NewList().
		AddItem("Release", "Release a new version", '1', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{Screen: "release"}))

	list.SetTitle("Dev Ops").SetBorder(true)
	return list
}

func release(app *tview.Application) tview.Primitive {
	type formData struct {
		Env       string
		Component string
		Version   string
	}

	flex := tview.NewFlex().SetDirection(tview.FlexRow)

	flex.SetBorder(true)
	flex.SetTitle("Dev Ops / Release")

	commandOutput := tview.NewTextView()
	commandOutput.SetTitle("Output")
	commandOutput.SetBorder(true)

	commandOutput.SetChangedFunc(func() {
		app.Draw()
	})

	data := &formData{}

	form := tview.NewForm().
		AddDropDown("Env", []string{"staging", "preprod", "prod"}, 0, func(v string, _ int) { data.Env = v }).
		AddDropDown("Component", []string{"UI", "Server"}, 0, func(v string, _ int) { data.Component = v }).
		AddInputField("Version", "", 40, nil, func(v string) { data.Version = v }).
		AddButton("Enter", func() {
			commandOutput.Clear()
			go func() {
				devops.ReleaseVersion(data.Env, data.Component, data.Version, commandOutput)
			}()
		})

	flex.SetInputCapture(FormTabControl(commandOutput, form, app, 0))

	flex.AddItem(form, 11, 0, true)
	flex.AddItem(commandOutput, 0, 1, false)

	return flex
}
