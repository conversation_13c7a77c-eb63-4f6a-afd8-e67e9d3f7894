package screens

import (
	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
	"retailbook.com/rb/admin/internal/cmds"
	"strings"
)

// ScreenManager manages the screens in the application.
// We arrange each screen in a "path" and use that for navigation backwards.
//
// For e.g. /techops/createuser
// If we go "back" from 'createuser' we then get to 'techops', then back to the root ('/').
type ScreenManager struct {
	app           *tview.Application
	currentScreen string
	router        cmds.CommandRouter
}

func NewScreenManager(app *tview.Application, router cmds.CommandRouter) *ScreenManager {
	sm := &ScreenManager{app: app, currentScreen: "/", router: router}

	router.RegisterCommand(cmds.ShowScreen, sm.showScreen)
	router.RegisterCommand(cmds.Back, sm.back)

	sm.rebuild()

	return sm
}

func (s *ScreenManager) showScreen(args any) {
	ssa := args.(*cmds.ShowScreenArgs)

	if strings.HasPrefix(ssa.Screen, "/") {
		s.currentScreen = ssa.Screen
	} else {
		trimmed := strings.TrimSuffix(s.currentScreen, "/")

		paths := strings.Split(trimmed, "/")

		paths = append(paths, ssa.Screen)
		s.currentScreen = strings.Join(paths, "/")
	}

	s.rebuild()
}

func (s *ScreenManager) back(any) {
	if s.currentScreen == "/" {
		return
	}

	s.currentScreen = s.currentScreen[:strings.LastIndex(s.currentScreen, "/")]
	if s.currentScreen == "" {
		s.currentScreen = "/"
	}
	s.rebuild()
}

func (s *ScreenManager) rebuild() {
	frame := tview.NewFrame(s.buildScreen()).SetBorders(0, 0, 0, 0, 0, 0)
	frame.AddText("v0.2", true, tview.AlignLeft, tcell.ColorWhite)
	frame.AddText("RetailBook", true, tview.AlignRight, tcell.ColorBlue)

	frame.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			s.router.Execute(cmds.Back, nil)
			return nil
		}
		return event
	})

	s.app.SetRoot(frame, true)
}

func (s *ScreenManager) buildScreen() tview.Primitive {
	switch s.currentScreen {
	case "/":
		{
			return MainMenu(s.router)
		}
	case "/techops":
		{
			return techOps(s.router)
		}
	case "/techops/create_user":
		{
			return createUser(s.app)
		}
	case "/techops/create_participant":
		{
			return createParticipant(s.app)
		}
	case "/techops/audit_events":
		{
			return auditEvents(s.app)
		}
	case "/techops/grant_perm":
		{
			return grantPermToUser(s.app)
		}
	case "/devops":
		return devOps(s.router)
	case "/devops/release":
		return release(s.app)
	}

	panic("unknown screen" + s.currentScreen)
}
