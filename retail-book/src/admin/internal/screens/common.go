package screens

import (
	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

func FormTabControl(commandOutput tview.Primitive, form *tview.Form, app *tview.Application, buttonIdx int) func(event *tcell.EventKey) *tcell.EventKey {
	return func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyTab {
			if commandOutput.HasFocus() {
				form.SetFocus(0)
				app.SetFocus(form)
				return nil
			} else {
				_, bid := form.GetFocusedItemIndex()
				if bid == buttonIdx {
					app.SetFocus(commandOutput)
					return nil
				}
			}
		} else if event.Key() == tcell.KeyBacktab {
			if commandOutput.HasFocus() {
				app.SetFocus(form)
			} else {
				id, _ := form.GetFocusedItemIndex()
				if id == 0 {
					app.SetFocus(commandOutput)
					return nil
				}
			}
		}

		return event
	}
}
