package screens

import (
	"github.com/iancoleman/strcase"
	"github.com/rivo/tview"
	"retailbook.com/rb/admin/internal/cmds"
	"retailbook.com/rb/admin/internal/techops"
	"strings"
)

func techOps(router cmds.CommandRouter) tview.Primitive {

	list := tview.NewList().
		AddItem("Add Participant", "Creates a new participant in the system", '1', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{Screen: "create_participant"})).
		AddItem("Create User", "Creates a new user for a participant", '2', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{Screen: "create_user"})).
		AddItem("Audit Events", "Views events for a given participant", '3', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{Screen: "audit_events"})).
		AddItem("Grant Perms", "Grant Perms", '3', router.OnSelectedHandler(cmds.ShowScreen, &cmds.ShowScreenArgs{Screen: "grant_perm"}))

	/* TODO => Implement this: AddItem("Toggle Superuser", "Updates a particular user to be a super user (or not)", '4', nil) */

	list.SetTitle("Tech Ops").SetBorder(true)

	return list

}

func createParticipant(app *tview.Application) tview.Primitive {
	type formData struct {
		Env         string
		SystemId    string
		DisplayName string
		Role        string
		Domains     string
	}

	flex := tview.NewFlex().SetDirection(tview.FlexRow)

	flex.SetBorder(true)
	flex.SetTitle("Tech Ops / Create Participant")

	commandOutput := tview.NewTextView()
	commandOutput.SetTitle("Output")
	commandOutput.SetBorder(true)

	commandOutput.SetChangedFunc(func() {
		app.Draw()
	})

	data := &formData{}

	form := tview.NewForm().
		AddDropDown("Env", []string{"staging", "preprod", "prod"}, 0, func(v string, _ int) { data.Env = v }).
		AddInputField("System Id", "", 40, nil, func(v string) { data.SystemId = v }).
		AddInputField("Display Name", "", 40, nil, func(v string) { data.DisplayName = v }).
		AddDropDown("Type", []string{"bank", "intermediary"}, 0, func(v string, _ int) { data.Role = v }).
		AddInputField("Domains", "", 40, nil, func(v string) { data.Domains = v }).
		AddButton("Enter", func() {
			commandOutput.Clear()
			go func() {
				techops.CreateParticipant(data.Env, data.SystemId, data.DisplayName, data.Role, data.Domains, commandOutput)
			}()
		})

	flex.SetInputCapture(FormTabControl(commandOutput, form, app, 0))

	flex.AddItem(form, 13, 0, true)
	flex.AddItem(commandOutput, 0, 1, false)

	return flex
}

func createUser(app *tview.Application) tview.Primitive {
	type formData struct {
		Name       string
		Email      string
		Role       string
		Gatekeeper bool
	}

	data := &formData{}

	flex := tview.NewFlex().SetDirection(tview.FlexRow)

	flex.SetBorder(true)
	flex.SetTitle("Tech Ops / Create User")

	commandOutput := tview.NewTextView()
	commandOutput.SetTitle("Output")
	commandOutput.SetBorder(true)

	form := tview.NewForm().
		AddInputField("Name", "", 40, nil, func(v string) { data.Name = v }).
		AddInputField("Email", "", 40, nil, func(v string) { data.Email = v }).
		AddDropDown("Role", []string{"Manager", "Editor"}, 0, func(v string, _ int) { data.Role = v }).
		AddCheckbox("Gatekeeper", false, func(v bool) { data.Gatekeeper = v }).
		AddButton("Enter", func() {
			go func() {
				techops.CreateUser(data.Name, data.Email, data.Role, data.Gatekeeper, commandOutput)
			}()
		})

	flex.AddItem(form, 12, 0, true)
	flex.AddItem(commandOutput, 0, 1, false)

	flex.SetInputCapture(FormTabControl(commandOutput, form, app, 0))

	return flex
}

func grantPermToUser(app *tview.Application) tview.Primitive {
	type formData struct {
		Role  string
		Scope string
		User  string
	}

	data := &formData{}

	flex := tview.NewFlex().SetDirection(tview.FlexRow)

	flex.SetBorder(true)
	flex.SetTitle("Tech Ops / Create User")

	commandOutput := tview.NewTextView()
	commandOutput.SetTitle("Output")
	commandOutput.SetBorder(true)

	form := tview.NewForm().
		AddDropDown("Role", []string{"offer_retail_collaborator", "offer_retail_gatekeeper"}, 0, func(v string, _ int) { data.Role = v }).
		AddInputField("Scope", "", 40, nil, func(v string) { data.Scope = v }).
		AddInputField("User", "", 40, nil, func(v string) { data.User = v }).
		AddButton("Enter", func() {
			go func() {
				techops.GrantPermToUser(data.Role, data.Scope, data.User, commandOutput)
			}()
		})

	flex.AddItem(form, 12, 0, true)
	flex.AddItem(commandOutput, 0, 1, false)

	flex.SetInputCapture(FormTabControl(commandOutput, form, app, 0))

	return flex
}

func auditEvents(app *tview.Application) tview.Primitive {
	type formData struct {
		// CorrelationKey is the field key that we search for.
		CorrelationKey string
		// CorrelationValue is the corresponding correlation value.
		CorrelationValue string
		// AdminPassword is the password to the couch database (that has been port forwarded).
		AdminPassword string
	}

	data := &formData{}

	flex := tview.NewFlex().SetDirection(tview.FlexRow)
	flex.SetBorder(true)
	flex.SetTitle("Tech Ops / Audit Events")

	commandOutput := tview.NewTextView()
	commandOutput.SetTitle("Output")
	commandOutput.SetBorder(true)
	commandOutput.SetDynamicColors(true)

	setCorrelationField := func(option string, _ int) {
		data.CorrelationKey = strcase.ToSnake(strings.ToLower(option))
	}

	setCorrelationValue := func(v string) {
		data.CorrelationValue = v
	}

	setAdminPassword := func(v string) {
		data.AdminPassword = v
	}

	form := tview.NewForm().
		AddDropDown("Correlation Field", []string{"Document ID", "Offer ID"}, 0, setCorrelationField).
		AddInputField("Correlation Value", "", 100, nil, setCorrelationValue).
		AddPasswordField("Database Admin Password", "", 50, '*', setAdminPassword).
		AddButton("Enter", func() {
			go func() {
				techops.FindAudit(data.CorrelationKey, data.CorrelationValue, data.AdminPassword, commandOutput)
			}()
		})

	instructions := tview.NewTextView().
		SetWrap(true).
		SetWordWrap(true).
		SetText("Please ensure you are port forwarding couchdb first!")

	flex.AddItem(instructions, 1, 0, false)
	flex.AddItem(form, 10, 0, true)
	flex.AddItem(commandOutput, 0, 1, false)

	flex.SetInputCapture(FormTabControl(commandOutput, form, app, 0))

	return flex
}
