package techops

import (
	"context"
	"encoding/json"
	"io"
	"retailbook.com/rb/common/pkgs/broker"
	"retailbook.com/rb/common/pkgs/commands"
	"retailbook.com/rb/common/pkgs/data"
	"retailbook.com/rb/common/pkgs/perm"
	"retailbook.com/rb/common/pkgs/server/middleware/correlation"
)

func GrantPermToUser(role string, scope string, user string, writer io.Writer) {

	cu := commands.GrantPerm{
		Role:  role,
		Scope: scope,
		User:  user,
	}

	writer.Write([]byte("Sending command:\n"))

	cmdBytes, _ := json.Marshal(cu)
	writer.Write(cmdBytes)
	writer.Write([]byte("\n"))

	natsConn, err := broker.BuildNatsConnection("localhost", "4222", "nats")

	defer natsConn.Connection.Close()

	if err != nil {
		writer.Write([]byte(err.Error()))
		return
	}

	api := commands.NewApiService(natsConn)

	ctx := correlation.WithValue(context.TODO(), data.RandomGuid())

	res, err := api.GrantPermRequest(ctx, perm.Superuser, &cu)
	if err != nil {
		writer.Write([]byte("Error\n"))
		writer.Write([]byte(err.Error()))
		return
	}

	writer.Write(res)

	writer.Write([]byte("Writing somthing to hopefully fool windows security threat scanner\n"))

	return
}
