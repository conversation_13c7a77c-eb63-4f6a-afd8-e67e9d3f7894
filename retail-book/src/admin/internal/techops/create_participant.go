package techops

import (
	"encoding/json"
	"errors"
	"fmt"
	"gopkg.in/yaml.v3"
	"io"
	"os"
	"os/exec"
	"path"
	"retailbook.com/rb/admin/internal/common"
	"slices"
	"strings"
)

const infraRepo = "**************:RETAIL-BOOK-LIMITED/retail-book-infra.git"
const infraRepoName = "retail-book-infra"
const infraToolingRepo = "**************:RETAIL-BOOK-LIMITED/retail-book-infra-tooling.git"
const infraToolingRepoName = "retail-book-infra-tooling"

var subscriptions = map[string]string{
	"staging": "RetailBook-Dev",
	"preprod": "RetailBook-PreProd",
	"prod":    "RetailBook-Prod",
}

func CreateParticipant(env string, systemId string, displayName string, roleType string, domains string, writer io.Writer) {

	err := createInfra(infraRepo, infraRepoName, env, systemId, displayName, writer)
	if err != nil {
		common.Log(writer, err.Error())
		common.Log(writer, "Create infra failed")
		return
	}

	err = createTooling(infraToolingRepo, infraToolingRepoName, env, systemId, displayName, roleType, strings.Split(domains, ","), writer)
	if err != nil {
		common.Log(writer, err.Error())
		common.Log(writer, "Create infra tooling failed")
		return
	}

	io.WriteString(writer, "\n\nFin\n\n")
}

func createInfra(repo string, repoName string, env string, systemId string, displayName string, writer io.Writer) error {
	io.WriteString(writer, "Creating infra\n")

	common.Log(writer, "Creating temporary directory for git-hub repo "+infraRepoName)
	infraDir, err := os.MkdirTemp("", "*")

	if err != nil {
		common.Log(writer, err.Error())
		common.Log(writer, "Command failed")
		return err
	}
	defer os.RemoveAll(infraDir)

	oldDir, _ := os.Getwd()
	defer os.Chdir(oldDir)

	// Clone the repo into this dir
	os.Chdir(infraDir)

	io.WriteString(writer, "Cloning repo "+repoName+" into "+infraDir+"\n")

	err = common.Exec(writer, "git", "clone", repo, "--depth=1")
	if err != nil {
		common.Log(writer, "Clone failed")
		return err
	}

	os.Chdir(path.Join(infraDir, repoName))

	participantDir := path.Join(infraDir, repoName, "layers", "participant", systemId, env)

	err = os.MkdirAll(participantDir, 0755)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	err = createInputsHCL(participantDir, systemId, displayName, writer)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	err = createTerragruntFile(participantDir, writer)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	io.WriteString(writer, "Running terragrunt\n")

	os.Chdir(participantDir)
	io.WriteString(writer, "cd "+participantDir+"\n")
	io.WriteString(writer, "terragrunt apply > ./terragrunt.log\n")

	cmd := exec.Command("terragrunt", "apply", "-auto-approve", "-no-color")

	cmd.Stdout = writer
	cmd.Stderr = writer

	if err = cmd.Run(); err != nil {
		io.WriteString(writer, err.Error())
	}

	err = addDiffCommitPush(writer, repoName, "main", fmt.Sprintf("Configuration for participant %s/%s", env, systemId))

	if err != nil {
		return err
	}

	return nil

}

func createTooling(repo string, repoName string, env string, systemId string, displayName string, role string, domains []string, writer io.Writer) error {
	io.WriteString(writer, "\n\n\nCreating infra tooling\n")

	common.Log(writer, "Creating temporary directory for git-hub repo "+infraToolingRepoName)
	infraToolingDir, err := os.MkdirTemp("", "*")

	if err != nil {
		common.Log(writer, err.Error())
		common.Log(writer, "Command failed")
		return err
	}
	defer os.RemoveAll(infraToolingDir)

	oldDir, _ := os.Getwd()
	defer os.Chdir(oldDir)

	// Clone the repo into this dir
	os.Chdir(infraToolingDir)

	io.WriteString(writer, "Cloning repo "+repoName+" into "+infraToolingDir+"\n")

	err = common.Exec(writer, "git", "clone", repo, "--depth=1")
	if err != nil {
		common.Log(writer, "Clone failed")
		return err
	}

	os.Chdir(path.Join(infraToolingDir, repoName))

	baseDir := path.Join(infraToolingDir, repoName, env, "retailbook")

	err = updateParticipantCreator(baseDir, systemId, writer)
	if err != nil {
		common.Log(writer, "Update participant creator failed")
		return err
	}

	clientId, principalId, resourceId, err := getResourceInfo(env, systemId, writer)
	if err != nil {
		common.Log(writer, "Get resource info failed")
		return err
	}

	err = writeParticipant(path.Join(baseDir, "participant", "participants"), systemId, clientId, resourceId, role, domains, writer)
	if err != nil {
		common.Log(writer, "Write participant failed")
		return err
	}

	err = updateCentralData(path.Join(baseDir, "global", "services", "centraldata"), systemId, displayName, principalId, role, writer)
	if err != nil {
		common.Log(writer, "Update central data failed")
		return err
	}

	err = addDiffCommitPush(writer, repoName, "main", "Adding participant "+systemId)

	if err != nil {
		common.Log(writer, "Push git repo failed")
		return err
	}

	return nil
}

func updateCentralData(basePath string, systemId string, displayName string, principalId string, role string, writer io.Writer) error {
	io.WriteString(writer, "Updating Central Data...\n")

	data, err := os.ReadFile(path.Join(basePath, "values.yaml"))
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	var centralData = map[string]interface{}{}
	err = yaml.Unmarshal(data, &centralData)
	if err != nil {
		return err
	}

	participants, ok := centralData["participants"].([]interface{})
	if !ok {
		return errors.New("cannot unmarshal centraldata participants list")
	}

	participantData := map[string]interface{}{
		"displayName": displayName,
		"systemId":    systemId,
		"principalId": principalId,
		"roles":       []string{role},
	}
	var replaced = false
	for idx, participantInf := range participants {
		participant, ok := participantInf.(map[string]interface{})
		if !ok {
			return errors.New("can't unmarshal participant")
		} else {
			if participant["systemId"] == systemId {
				io.WriteString(writer, "Replacing definition of participant "+systemId+"\n")
				participants[idx] = participantData
				centralData["participants"] = participants
				replaced = true
				break
			}
		}
	}
	if !replaced {
		io.WriteString(writer, "Appending definition of participant "+systemId)
		centralData["participants"] = append(participants, participantData)
	}

	newCentralData, err := yaml.Marshal(centralData)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	err = os.WriteFile(path.Join(basePath, "values.yaml"), newCentralData, 0755)

	if err != nil {

		io.WriteString(writer, err.Error())
		return err
	}
	return nil
}

func getResourceInfo(env string, systemId string, writer io.Writer) (string, string, string, error) {
	io.WriteString(writer, "Getting information for identity\n")

	cmd := exec.Command("az", "identity", "show", "--name", fmt.Sprintf("%s_api_identity", systemId), "--resource-group", fmt.Sprintf("retailbook-participant-%s-%s", env, systemId), "--subscription", subscriptions[env])

	outp, err := cmd.CombinedOutput()
	if err != nil {
		io.WriteString(writer, err.Error())
		return "", "", "", err
	}

	mp := make(map[string]any)
	err = json.Unmarshal(outp, &mp)
	if err != nil {
		io.WriteString(writer, err.Error())
		return "", "", "", err
	}

	return mp["clientId"].(string), mp["principalId"].(string), mp["id"].(string), nil

}

func writeParticipant(basePath string, systemId string, clientId string, resourceId string, role string, domains []string, writer io.Writer) error {
	builder := new(strings.Builder)

	builder.WriteString("commandhandler:\n")
	builder.WriteString("  domain: [")
	builder.WriteString("\"" + strings.Join(domains, "\", \"") + "\"")
	builder.WriteString("]\n\n")

	//global:
	//  azureIdentity:
	//    clientID: e9fb7095-0ac0-439e-a419-fbc27350c7a3
	//    resourceID: /subscriptions/8f06ffc4-1cf1-4aaf-844a-a1567c3e31c7/resourcegroups/retailbook-participant-staging-solarplc/providers/Microsoft.ManagedIdentity/userAssignedIdentities/solarplc_api_identity
	//  role: bank

	builder.WriteString("global:\n  azureIdentity:\n")
	builder.WriteString(fmt.Sprintf("    clientID: %s\n", clientId))
	builder.WriteString(fmt.Sprintf("    resourceID: %s\n", resourceId))
	builder.WriteString(fmt.Sprintf("  role: %s\n", role))

	err := os.WriteFile(path.Join(basePath, fmt.Sprintf("%s.yaml", systemId)), []byte(builder.String()), 0755)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	return nil
}

type creatorParticipants struct {
	Participants []string `yaml:"participants"`
}

func updateParticipantCreator(dir string, systemId string, writer io.Writer) error {
	io.WriteString(writer, "Updating participant-creator...\n")

	data, err := os.ReadFile(path.Join(dir, "participant-creator", "values.yaml"))
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	var participants creatorParticipants
	err = yaml.Unmarshal(data, &participants)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}

	if !slices.Contains(participants.Participants, systemId) {
		io.WriteString(writer, "Adding "+systemId+" to participant-creator list\n")
		participants.Participants = append(participants.Participants, systemId)

		new_data, err := yaml.Marshal(participants)
		if err != nil {
			io.WriteString(writer, err.Error())
			return err
		}

		os.WriteFile(path.Join(dir, "participant-creator", "values.yaml"), []byte("#The value must be Kubernetes Compatible (only lower case and dashes)\n"+string(new_data)), 0755)
	} else {
		io.WriteString(writer, "No changes to participant-creator list, skipping update\n")
	}

	return nil
}

func createInputsHCL(participantDir string, systemId string, displayName string, writer io.Writer) error {
	io.WriteString(writer, "Writing inputs.hcl\n")

	fp, err := os.OpenFile(path.Join(participantDir, "inputs.hcl"), os.O_CREATE|os.O_RDWR, 0755)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}
	defer fp.Close()

	fp.WriteString(`inputs = {
    participant = {
		id           = "` + systemId + `"
        display_name = "` + displayName + `"
    }
}`)
	return nil
}

func createTerragruntFile(participantDir string, writer io.Writer) error {
	io.WriteString(writer, "Writing terragrunt.hcl\n")

	fp, err := os.OpenFile(path.Join(participantDir, "terragrunt.hcl"), os.O_CREATE|os.O_RDWR, 0755)
	if err != nil {
		io.WriteString(writer, err.Error())
		return err
	}
	defer fp.Close()

	data := `include {
  path = find_in_parent_folders()
}

include "root" {
  path           = find_in_parent_folders()
  merge_strategy = "deep"
}

include "module" {
  path           = find_in_parent_folders("module.hcl")
  merge_strategy = "deep"
}

include "inputs" {
  path           = "inputs.hcl"
  merge_strategy = "deep"
}

include "inputs" {
  path           = find_in_parent_folders("inputs.hcl")
  merge_strategy = "deep"
}

dependency "infrastructure" {
  config_path = "../../../infrastructure/${basename(get_original_terragrunt_dir())}"
}
`
	fp.WriteString(data)
	return nil
}

func addDiffCommitPush(writer io.Writer, repoName string, defaultBranch string, commitMsg string) error {

	err := common.Exec(writer, "git", "add", ".")
	if err != nil {
		common.Log(writer, "Add failed")
		return err
	}

	err = common.Exec(writer, "git", "diff")
	if err != nil {
		common.Log(writer, "Diff failed")
		return err
	}

	err = common.Exec(writer, "git", "diff-index", "--quiet", "HEAD")
	if err == nil {
		common.Log(writer, "No changes to files in repository "+repoName)
		return nil
	}

	err = common.Exec(writer, "git", "commit", "-am", commitMsg)
	if err != nil {
		common.Log(writer, "Commit failed")
		return err
	}

	err = common.Exec(writer, "git", "push", "origin", defaultBranch)
	if err != nil {
		common.Log(writer, "Push failed")
		return err
	}

	return nil
}
