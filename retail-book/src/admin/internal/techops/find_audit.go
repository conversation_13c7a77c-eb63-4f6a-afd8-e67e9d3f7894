package techops

import (
	"context"
	"fmt"
	"io"
	"math"
	"retailbook.com/rb/cache/pkgs/postgresdb"
	"retailbook.com/rb/cache/pkgs/postgresdbcache"
	"retailbook.com/rb/common/pkgs/database"
	"sort"
)

func FindAudit(key, val, adminPw string, writer io.Writer) {
	// TODO: Would be nice if we would use kubectl (or equivalent) to do the port-forwarding + secret retrieval ourselves here.

	// Ensure we have a working postgres connection.
	io.WriteString(writer, "\n")
	postgressConfig := make(map[string]any, 0)
	postgressConfig["host"] = "localhost"
	postgressConfig["port"] = 5432
	postgressConfig["user"] = "postgres"
	postgressConfig["password"] = adminPw
	postgressConfig["database"] = "retailbook"

	ctx := context.Background()

	client, err := postgresdbcache.NewDbClient(postgressConfig)
	if err != nil {
		err = fmt.Errorf("failed to create database connection: %e", err)
		io.WriteString(writer, err.Error())
		return
	}

	err = client.Ping()
	if err != nil {
		err = fmt.Errorf("failed to ping the database: %e", err)
		io.WriteString(writer, err.Error())
		return
	}

	txnManager := postgresdb.PGTransactionManager{Db: client}
	ctx, err = txnManager.BeginTransaction(ctx, false)
	if err != nil {
		err = fmt.Errorf("failed to begin transaction: %e", err)
		io.WriteString(writer, err.Error())
		return
	}
	txn := txnManager.GetTransaction(ctx)
	defer txn.Rollback()

	cache := database.NewPostgresEventAuditCache(client, txnManager)

	audit := make([]database.EventAudit, 0)
	if key == "offer_id" {
		audit, _, err = cache.ByOfferId().GetAll(ctx, val, math.MaxInt64, "")
	} else if key == "document_id" {
		audit, _, err = cache.ByDocumentId().GetAll(ctx, val, math.MaxInt64, "")
	} else {
		io.WriteString(writer, fmt.Sprintf("%s is not a valid correlation key", key))
		return
	}
	if err != nil {
		err = fmt.Errorf("failed to query the database: %e", err)
		io.WriteString(writer, err.Error())
		return
	}

	sort.Slice(audit, func(i, j int) bool {
		return audit[i].Tstamp().Compare(audit[j].Tstamp()) > 0
	})

	for _, a := range audit {
		//encoded := a.Data()
		//compressed, err := base64.StdEncoding.DecodeString(encoded)
		//if err != nil {
		//	err = fmt.Errorf("failed to decode the database: %e", err)
		//	io.WriteString(writer, err.Error())
		//	io.WriteString(writer, "\n")
		//	io.WriteString(writer, encoded)
		//	io.WriteString(writer, "\n")
		//	return
		//}

		//reader, err := gzip.NewReader(bytes.NewReader(compressed))
		//if err != nil {
		//	io.WriteString(writer, err.Error())
		//	return
		//}
		//decompressed, err := io.ReadAll(reader)
		//if err != nil {
		//	io.WriteString(writer, err.Error())
		//	return
		//}

		event := []byte("[red]" + a.EventType() + "[white]   ")
		writer.Write(event)
		io.WriteString(writer, " - ")
		io.WriteString(writer, a.Data())
		io.WriteString(writer, "\n")
	}
}
