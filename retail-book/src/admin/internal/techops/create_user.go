package techops

import (
	"context"
	"encoding/json"
	"io"
	"retailbook.com/rb/common/pkgs/broker"
	"retailbook.com/rb/common/pkgs/commands"
	"retailbook.com/rb/common/pkgs/data"
	"retailbook.com/rb/common/pkgs/optional"
	"retailbook.com/rb/common/pkgs/perm"
	"retailbook.com/rb/common/pkgs/server/middleware/correlation"
	"strings"
)

func CreateUser(name string, email string, role string, gatekeeper bool, writer io.Writer) {

	cu := commands.CreateUser{
		Name:       optional.With(name),
		Email:      email,
		Role:       strings.ToLower(role),
		GateKeeper: optional.With(gatekeeper),
	}

	writer.Write([]byte("Sending command:\n"))

	cmdBytes, _ := json.Marshal(cu)
	writer.Write(cmdBytes)
	writer.Write([]byte("\n"))

	natsConn, err := broker.BuildNatsConnection("localhost", "4222", "nats")

	defer natsConn.Connection.Close()

	if err != nil {
		writer.Write([]byte(err.Error()))
		return
	}

	api := commands.NewApiService(natsConn)

	ctx := correlation.WithValue(context.TODO(), data.RandomGuid())

	res, err := api.CreateUserRequest(ctx, perm.Superuser, &cu)
	if err != nil {
		writer.Write([]byte("Error\n"))
		writer.Write([]byte(err.Error()))
		return
	}

	writer.Write(res)

	return
}
