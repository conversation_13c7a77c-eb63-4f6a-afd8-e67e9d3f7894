package devops

import (
	"fmt"
	"io"
	"os"
	"path"
	"retailbook.com/rb/admin/internal/common"
)

const repo = "**************:RETAIL-BOOK-LIMITED/retail-book-infra-tooling.git"
const repoName = "retail-book-infra-tooling"

func ReleaseVersion(env, component, release string, writer io.Writer) {
	common.Log(writer, "Creating temporary directory for git-hub repo")
	dir, err := os.MkdirTemp("", "*")

	if err != nil {
		common.Log(writer, err.Error())
		common.Log(writer, "Command failed")
		return
	}
	defer os.RemoveAll(dir)

	// Clone the repo into this dir
	oldDir, _ := os.Getwd()
	defer os.Chdir(oldDir)

	os.Chdir(dir)

	err = common.Exec(writer, "git", "clone", repo, "--depth=1")
	if err != nil {
		common.Log(writer, "Clone failed")
		return
	}
	os.Chdir(path.Join(dir, repoName))

	if component == "UI" {
		// Only a single file to update
		err = common.Exec(writer, "sed", "-i", fmt.Sprintf("s/uiTag: .*$/uiTag: %s/", release), path.Join(env, "retailbook", "global", "values.yaml"))
		if err != nil {
			common.Log(writer, "Sub failed")
			return
		}
	} else {
		// Need to update a couple of files
		err = common.Exec(writer, "sed", "-i", fmt.Sprintf("s/tag: .*$/tag: %s/", release), path.Join(env, "retailbook", "global", "values.yaml"))
		if err != nil {
			common.Log(writer, "Sub failed")
			return
		}

		err = common.Exec(writer, "sed", "-i", fmt.Sprintf("s/tag: .*$/tag: %s/", release), path.Join(env, "retailbook", "participant", "common-values.yaml"))
		if err != nil {
			common.Log(writer, "Sub failed")
			return
		}
	}

	err = common.Exec(writer, "git", "diff")
	if err != nil {
		common.Log(writer, "Diff failed")
		return
	}

	err = common.Exec(writer, "git", "commit", "-am", fmt.Sprintf("Releasing %s/%s version: %s", env, component, release))
	if err != nil {
		common.Log(writer, "Commit failed")
		return
	}

	err = common.Exec(writer, "git", "push", "origin", "main")
	if err != nil {
		common.Log(writer, "Push failed")
		return
	}

	common.Log(writer, "c'est fin")
}
