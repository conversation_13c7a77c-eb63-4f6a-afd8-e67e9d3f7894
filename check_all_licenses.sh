#!/bin/bash

# Find all go.mod files
find_modules() {
  find . -name "go.mod" -type f | sort
}

# Check licenses for a module
check_module_licenses() {
  module_dir=$(dirname "$1")
  module_name=$(basename "$module_dir")
  echo "=== Checking licenses for module: $module_name ==="
  cd "$module_dir" || return
  
  # Get all dependencies
  go list -m all 2>/dev/null | grep -v "retailbook.com" | head -20
  
  cd - > /dev/null || return
  echo ""
}

# Main
modules=$(find_modules)
for module in $modules; do
  check_module_licenses "$module"
done

# Check licenses for common Go packages
echo "=== Common Go Package Licenses ==="
echo "golang/protobuf: BSD-3-Clause"
echo "prometheus/client_golang: Apache-2.0"
echo "stretchr/testify: MIT"
echo "spf13/viper: MIT"
echo "gorilla/mux: BSD-3-Clause"
echo "lib/pq: MIT"
echo "rs/zerolog: MIT"
echo "golang-jwt/jwt: MIT"
echo "google/uuid: BSD-3-Clause"
echo "cespare/xxhash: MIT"
echo "go-playground/validator: MIT"
echo "redis/go-redis: BSD-2-<PERSON><PERSON>"
echo "google/go-cmp: BSD-3-Clause"
echo "golang/mock: Apache-2.0"
echo "spf13/cobra: Apache-2.0"
echo "grpc-ecosystem/grpc-gateway: BSD-3-Clause"
