#!/bin/bash

# Function to check license for a GitHub repository
check_license() {
  repo=$1
  echo "Checking license for $repo"
  curl -s "https://api.github.com/repos/$repo/license" | grep -E '"spdx_id"|"name"' | head -2
  echo ""
}

# Check licenses for some key dependencies
check_license "golang/protobuf"
check_license "prometheus/client_golang"
check_license "stretchr/testify"
check_license "spf13/viper"
check_license "gorilla/mux"
check_license "go-redis/redis"
check_license "lib/pq"
check_license "rs/zerolog"
check_license "golang-jwt/jwt"
check_license "google/uuid"
check_license "cespare/xxhash"
check_license "go-playground/validator"
