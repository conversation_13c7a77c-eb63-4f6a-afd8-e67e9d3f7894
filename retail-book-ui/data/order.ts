import { faker } from "@faker-js/faker";
import Decimal from "decimal.js";
import { Zero } from "helpers";
import {
  EOfferType,
  TOrder,
  TOrderLineItem,
  EOrderType,
  TOffer,
  EOrderStatus,
  IOrderBase,
  TOrderHistory,
} from "types";

export const aggregateLineItems = (
  lineItems: TOrderLineItem[]
): {
  non_shareholding?: IOrderBase;
  shareholding?: IOrderBase;
} => {
  const nonShareholderLineItems = lineItems.filter(
    (item) => !item.existing_holding
  );
  const nonShareholderValue = nonShareholderLineItems.reduce(
    (acc, lineItem) => Decimal.add(acc, lineItem.notional_value),
    Zero
  );
  const nonShareholderApplications = new Decimal(
    nonShareholderLineItems.length
  );

  const shareholderLineItems = lineItems.filter(
    (item) => !!item.existing_holding
  );
  const shareholderValue = shareholderLineItems.reduce(
    (acc, lineItem) => Decimal.add(acc, lineItem.notional_value),
    Zero
  );
  const shareholderApplications = new Decimal(shareholderLineItems.length);
  const shareholderExistingHolding = shareholderLineItems.reduce(
    (acc, lineItem) => Decimal.add(acc, lineItem.existing_holding ?? 0),
    Zero
  );

  return {
    non_shareholding: {
      notional_value: nonShareholderValue,
      applications: nonShareholderApplications,
    },
    shareholding: {
      notional_value: shareholderValue,
      applications: shareholderApplications,
      existing_holding: shareholderExistingHolding,
    },
  };
};

export const generateOrderLineItems = (
  offer: TOffer,
  count = 10
): TOrderLineItem[] =>
  Array(count)
    .fill(1)
    .map(() => ({
      client_order_ref: faker.datatype.uuid(),
      notional_value: new Decimal(faker.datatype.number(50000)),
      commission_due: faker.datatype.boolean(),
      tax_wrapper: faker.datatype.boolean(),
      existing_holding: offer.shareholders_only
        ? new Decimal(faker.datatype.number({ min: 1, max: 1000 }))
        : offer.type !== EOfferType.IPO
        ? faker.datatype.boolean()
          ? new Decimal(faker.datatype.number(100))
          : Zero
        : Zero,
    }));

export const createOrderHistoryEntry = (
  newOrder: TOrder,
  oldOrder?: TOrder
): TOrderHistory => {
  let applicationsChange = Zero;
  let existingHoldingChange = Zero;
  let valueChange = Zero;

  if (oldOrder) {
    applicationsChange =
      newOrder.totals.applications?.minus?.(
        oldOrder?.totals.applications ?? Zero
      ) ?? Zero;
    existingHoldingChange =
      newOrder.totals.existing_holding?.minus?.(
        oldOrder?.totals.existing_holding ?? Zero
      ) ?? Zero;
    valueChange =
      newOrder.totals.notional_value?.minus?.(
        oldOrder?.totals.notional_value ?? Zero
      ) ?? Zero;
  }

  return {
    delta: {
      applications: applicationsChange,
      notional_value: valueChange,
      existing_holding: existingHoldingChange,
    },
    order: newOrder,
  };
};

export const generateOrder = (
  offer: TOffer,
  orderType?: EOrderType
): TOrder => {
  orderType =
    orderType ??
    faker.helpers.arrayElement([EOrderType.AGGREGATE, EOrderType.DETAILED]);

  let order: TOrder = {
    id: faker.datatype.uuid(),
    order_type: orderType,
    status: faker.helpers.arrayElement([
      EOrderStatus.PENDING,
      EOrderStatus.ACCEPTED,
    ]),
    offer_id: faker.datatype.uuid(),
    order_book_id: undefined,
    offer_name: "",
    order_date: faker.date.past(1).toISOString(),
    update_date: faker.date.recent(3).toISOString(),
    totals: {},
    intermediary: faker.company.name(),
  };

  if (orderType === EOrderType.DETAILED) {
    const lineItems = generateOrderLineItems(offer);
    order = { ...order, ...aggregateLineItems(lineItems), lineItems };
  } else {
    if (!offer.shareholders_only) {
      order.non_shareholding = {
        applications: new Decimal(faker.datatype.number(300)),
        notional_value: new Decimal(faker.datatype.number(500000)),
      };
    }

    if (offer.type !== EOfferType.IPO) {
      order.shareholding = {
        existing_holding: new Decimal(faker.datatype.number(1000)),
        applications: new Decimal(faker.datatype.number(300)),
        notional_value: new Decimal(faker.datatype.number(500000)),
      };
    }
  }

  order.totals = {
    applications: Decimal.add(
      order.shareholding?.applications ?? Zero,
      order.non_shareholding?.applications ?? Zero
    ),
    existing_holding: Decimal.add(
      order.shareholding?.existing_holding ?? Zero,
      order.non_shareholding?.existing_holding ?? Zero
    ),
    notional_value: Decimal.add(
      order.shareholding?.notional_value ?? Zero,
      order.non_shareholding?.notional_value ?? Zero
    ),
  };

  return order;
};
