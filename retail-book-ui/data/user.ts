import { faker } from '@faker-js/faker';
import { EUserRole } from 'types';

export const generateUser = (role?: EUserRole) => {
  const firstName = faker.name.firstName();
  const lastName = faker.name.lastName();

  return {
    id: faker.datatype.uuid(),
    username: faker.internet.userName(firstName, lastName),
    email: faker.internet.email(firstName, lastName),
    firstName,
    lastName,
    role:
      role ??
      faker.helpers.arrayElement([EUserRole.INTERMEDIARY, EUserRole.ISSUER]),
  };
};
