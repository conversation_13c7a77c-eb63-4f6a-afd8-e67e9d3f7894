import { faker } from "@faker-js/faker";
import { addDays, addHours, subDays, subHours } from "date-fns";
import Decimal from "decimal.js";
import {
  EContractStatus,
  EOfferStatus,
  EOfferType,
  TOffer,
  TOfferSummary,
} from "types";

// Why the hell is this still here?

export const generateOffer = (
  type?: EOfferType,
  status?: EOfferStatus
): TOffer => {
  type =
    type ??
    faker.helpers.arrayElement([
      EOfferType.FOLLOW_ON,
      EOfferType.RETAIL_BOND,
      EOfferType.IPO,
    ]);

  const shareholders_only =
    type === EOfferType.IPO ? false : faker.datatype.boolean();

  status =
    status ??
    faker.helpers.arrayElement([
      EOfferStatus.PRE_LAUNCH,
      EOfferStatus.APPLICATIONS_OPEN,
      EOfferStatus.APPLICATIONS_CLOSED,
    ]);

  let registrationDate, openDate, closeDate;

  const now = new Date();

  switch (status) {
    case EOfferStatus.BUILDING: {
      registrationDate = addDays(now, 7);
      openDate = addDays(registrationDate, 7);
      closeDate =
        type === EOfferType.IPO ? addDays(openDate, 14) : addHours(openDate, 6);
      break;
    }
    case EOfferStatus.PRE_LAUNCH: {
      registrationDate = subDays(now, 7);
      openDate = addDays(now, 7);
      closeDate =
        type === EOfferType.IPO ? addDays(openDate, 14) : addHours(openDate, 6);
      break;
    }
    case EOfferStatus.APPLICATIONS_OPEN: {
      registrationDate = subDays(now, 7);
      openDate =
        type === EOfferType.IPO
          ? subDays(now, 3)
          : subHours(registrationDate, 3);
      closeDate = type === EOfferType.IPO ? addDays(now, 4) : addHours(now, 3);
      break;
    }
    case EOfferStatus.APPLICATIONS_CLOSED:
    default: {
      closeDate = faker.date.past();
      openDate =
        type === EOfferType.IPO
          ? subDays(closeDate, 14)
          : subHours(closeDate, 6);
      registrationDate = subDays(openDate, 7);
      break;
    }
  }

  const dateUpdated = subHours(now, 3);
  const dateCreated = subDays(registrationDate, 30);

  const offer_price = new Decimal(faker.datatype.number(10000));
  const min_order_amount =
    offer_price === undefined
      ? new Decimal(faker.datatype.number(1000000))
      : offer_price.mul(
          new Decimal(faker.datatype.number({ min: 1, max: 10 }))
        );

  const raise_amount = new Decimal(faker.datatype.number(100000));
  const issued_by = faker.company.name();
  const name = `${issued_by} - ${type}`;

  return {
    id: faker.datatype.uuid(),
    dateCreated: dateCreated.toISOString(),
    dateUpdated: dateUpdated.toISOString(),
    name,
    issued_by,
    type,
    shareholders_only,
    inside_information: false,
    issuer_logo: faker.datatype.boolean()
      ? `/placeholder-logos/offer-logo-${faker.datatype.number({
          min: 1,
          max: 5,
        })}.png`
      : undefined,
    registration_date: registrationDate.toISOString(),
    open_date: openDate.toISOString(),
    close_date: closeDate.toISOString(),
    status,
    offer_price,
    min_order_amount,
    raise_amount,
    description: faker.lorem.paragraph(),
    details: `<p>${faker.lorem.paragraph()}</p><p>${faker.lorem.paragraph()}</p><p>${faker.lorem.paragraph()}</p>`,
    allocation_principles: `<ul><li>${faker.lorem.paragraph()}</li><li>${faker.lorem.paragraph()}</li><li>${faker.lorem.paragraph()}</li></ul>`,
    settlement_details: `<ul><li>${faker.lorem.paragraph()}</li><li>${faker.lorem.paragraph()}</li><li>${faker.lorem.paragraph()}</li></ul>`,
    slug: faker.helpers.slugify(name).toLowerCase(),
    currency: faker.finance.currencyCode(),
    isin: faker.random.alphaNumeric(12, { casing: "upper" }),
    sedol: faker.random.alphaNumeric(7, { casing: "upper" }),
    crest_id: faker.finance.pin(3),
    ticker: issued_by.slice(0, 3).toUpperCase(),    
    documents: Array(5)
      .fill(1)
      .map(() => ({
        id: faker.datatype.uuid(),
        title: faker.commerce.productName(),
        file_name: faker.system.commonFileName(),
        type: faker.system.commonFileExt(),
        size: faker.datatype.number({ min: 1, max: 10, precision: 0.01 }),
        url: faker.system.filePath(),
        mime_type: faker.system.mimeType(),
      })),
    contracts: Array(2)
      .fill(1)
      .map(() => ({
        id: faker.datatype.uuid(),
        title: faker.commerce.productName(),
        file_name: faker.system.commonFileName(),
        type: faker.system.commonFileExt(),
        size: faker.datatype.number({ min: 1, max: 10, precision: 0.01 }),
        status: faker.helpers.arrayElement([
          EContractStatus.SIGNED,
          EContractStatus.UNSIGNED,
        ]),
        url: faker.system.filePath(),
        mime_type: faker.system.mimeType(),
      })),
      is_launched: false,
      settlement_date: closeDate.toISOString()
  };
};

export const getOfferSummary = (offer: TOffer): TOfferSummary => {
  const {
    id,
    name,
    type,
    status,
    close_date,
    open_date,
    registration_date,
    issuer_logo,
    issued_by,
    currency,
  } = offer;

  return {
    id,
    name,
    type,
    status,
    close_date,
    open_date,
    registration_date,
    issuer_logo,
    issued_by,
    currency,
    // totals,
  };
};

const generateValidationIPO = (): TOffer => {
  const offer = generateOffer(EOfferType.IPO, EOfferStatus.APPLICATIONS_OPEN);
  return {
    ...offer,
    issued_by: "Profex",
    name: "Profex IPO",
    shareholders_only: false,
    inside_information: false,
    description:
      "Profex Limited announced on 14th September the publication of a Pathfinder in relation to an initial public offering onto The AIM of the London Stock Exchange. The Company is seeking to raise £60m primary and TBC secondary by means of a placing and intermediaries offer of Ordinary Shares.",
    details:
      "Profex Limited announced on 14th September the publication of a Pathfinder in relation to an initial public offering onto The AIM of the London Stock Exchange. The Company is seeking to raise £60m primary and TBC secondary by means of a placing and intermediaries offer of Ordinary Shares.",
    allocation_principles:
      "<ul><li>Allocations to Intermediaries will be determined solely by the Company (following consultation with Keefe, Bruyette & Woods (acting through Stifel Nicolaus Europe Limited)).</li><li>Each Intermediary will be required to apply the allocation policy to each of its underlying applications from Retail Investors.</li><li>Along with the allocation policy, each Intermediary will be advised by Profex of the aggregate number of Shares allocated to it, and the amount payable for those Shares at the Offer Price.</li></ul>",
    settlement_details:
      "<ul><li>The Intermediary will make payment for the Ordinary Shares allocated to it, by means of the CREST system, against delivery of the shares.</li><li>Trade date and announced Result of the Issue is expected to be 24 November.</li><li>It is expected that Admission will become effective and unconditional dealings will commence on 29 November. There will be no conditional dealing.</li></ul>",
    slug: "profex-ipo",
    currency: "GBP",
    ticker: "PRFX",
    documents: [
      {
        id: "document-1",
        title: "Prospectus",
        file_name: "Pathfinder for Intermediaries.pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-2",
        title: "Timeline",
        file_name: "Timeline.pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-3",
        title: "Terms and Conditions",
        file_name: "Terms and Conditions.pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-4",
        title: "Intermediaries Booklet",
        file_name: "Intermediary Agreement (16 September 2021).pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-4",
        title: "Pathfinder",
        file_name: "Pathfinder for Intermediaries.pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-5",
        title: "Valuation Notice",
        file_name: "Valuation_Notice_final.pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-6",
        title: "Factsheet",
        file_name: "Intermediaries_Factsheet.pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-7",
        title: "Logo (png)",
        file_name: "PH_logo.png",
        type: "png",
        size: 2,
        url: "#",
        mime_type: "image/png",
      },
    ],
    contracts: [
      {
        id: "contract-1",
        title: "Intermediaries Booklet",
        file_name: "REX Intermediary Agreement.pdf",
        type: "pdf",
        size: 2,
        status: EContractStatus.UNSIGNED,
        url: "#",
        mime_type: "application/pdf",
      },
    ],
  };
};

const generateValidationFollowOn = (): TOffer => {
  const offer = generateOffer(
    EOfferType.FOLLOW_ON,
    EOfferStatus.APPLICATIONS_OPEN
  );

  return {
    ...offer,
    issued_by: "Aspire",
    name: "Aspire Follow On ABB",
    shareholders_only: false,
    inside_information: false,
    description:
      "Aspire Group plc , the world's leading bottle manufacturer, announced on 10 August 2022 its intention to undertake an equity placing and separate retail offer  to raise, in aggregate, gross proceeds of c. £100m. The proceeds of the Capital Raise will be used to fund the acquisition and associated costs of Renegade GmbH, one of the leading global manufacturers of sustainable bottle tops for the soft drinks industry.",
    details:
      '<p>Aspire Group plc (LSE: COA), the world\'s leading bottle manufacturer ("Aspire" or the "Company" announced on 10 August 2022 its intention to undertake an equity placing (the “Placing”) and separate retail offer (the “REX Retail Offer”) to raise, in aggregate, gross proceeds of c. £100m (together, the “Capital Raise”). The proceeds of the Capital Raise will be used to fund the acquisition and associated costs of Renegade GmbH (“Renegade”), one of the leading global manufacturers of sustainable bottle tops for the soft drinks industry.</p><p>While the Placing has been structured as a non-pre-emptive offer within the Company\'s existing authorities from shareholders for non-pre-emptive offers so as to minimise cost, time to completion and use of management time, the Company values its retail shareholder base and believes that it is appropriate to provide its retail shareholders in the United Kingdom the opportunity to participate in the REX Retail Offer. The REX Retail Offer announcement can be found on REX.</p><p>The net proceeds of the Placing will be used to fund the total cash consideration of c.€115m (c.$117m) in connection with the acquisition of Renegade.</p><p>Applications can be made through Intermediaries from an Individual Savings Account ("ISA"), a Junior Individual Savings Account ("Junior ISA") or a Self-Invested Personal Pension ("SIPP").</p>',
    allocation_principles:
      "<ul><li>Allocations to Intermediaries will be determined solely by the Company (following consultation with the Joint Bookrunners).</li><li>The allocation policy will be confirmed to Intermediaries following the Results Announcement.</li><li>Each Intermediary will be required to apply the allocation policy to each of its underlying applications from Retail Investors.</li><li>Along with the allocation policy, each Intermediary will be advised by Profex of the aggregate number of Shares allocated to it, and the amount payable for those Shares at the Offer Price.</li></ul>",
    settlement_details:
      "<ul><li>The Intermediary will make payment for the Ordinary Shares allocated to it, by means of the CREST system, against delivery of the shares.</li><li>Trade date and announced Result of the Issue is expected to be 11 November 2022.</li><li>It is expected that Admission and Settlement will be on 15 November 2022.</li></ul>",
    slug: "aspire-follow-on-abb",
    ticker: "ASP",
    documents: [
      {
        id: "document-1a",
        title: "Retail RNS",
        file_name: "Pathfinder for Intermediaries.pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
      {
        id: "document-2a",
        title: "Intermediaries Booklet",
        file_name: "Intermediary Agreement (16 September 2021).pdf",
        type: "pdf",
        size: 2,
        url: "#",
        mime_type: "application/pdf",
      },
    ],
    contracts: [
      {
        id: "contract-1a",
        title: "Intermediaries Booklet",
        file_name: "REX Intermediary Agreement.pdf",
        type: "pdf",
        size: 2,
        status: EContractStatus.SIGNED,
        url: "#",
        mime_type: "application/pdf",
      },
    ],
  };
};

export const generateOffers = () => {
  return [
    generateValidationIPO(),
    generateValidationFollowOn(),
    generateOffer(EOfferType.FOLLOW_ON, EOfferStatus.PRE_LAUNCH),
    generateOffer(EOfferType.FOLLOW_ON, EOfferStatus.APPLICATIONS_OPEN),
    generateOffer(EOfferType.FOLLOW_ON, EOfferStatus.APPLICATIONS_CLOSED),
    generateOffer(EOfferType.RETAIL_BOND, EOfferStatus.PRE_LAUNCH),
    generateOffer(EOfferType.RETAIL_BOND, EOfferStatus.APPLICATIONS_OPEN),
    generateOffer(EOfferType.RETAIL_BOND, EOfferStatus.APPLICATIONS_CLOSED),
    generateOffer(EOfferType.IPO, EOfferStatus.PRE_LAUNCH),
    generateOffer(EOfferType.IPO, EOfferStatus.APPLICATIONS_OPEN),
    generateOffer(EOfferType.IPO, EOfferStatus.APPLICATIONS_CLOSED),
  ];
};
