{"client": "Thunder Client", "collectionName": "retailbook", "dateExported": "2022-11-04T21:30:25.616Z", "version": "1.1", "folders": [{"_id": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Offers", "containerId": "", "created": "2022-11-04T21:03:28.812Z", "sortNum": 10000}, {"_id": "f0bc294e-9178-45f0-8e29-4c154c552156", "name": "Orders", "containerId": "", "created": "2022-11-04T21:19:58.100Z", "sortNum": 20000}, {"_id": "ba753acc-31d1-4cad-b7c7-1de4f44c1ec8", "name": "Static", "containerId": "", "created": "2022-11-04T21:20:06.789Z", "sortNum": 30000}, {"_id": "d9e58bef-69d8-491f-ba7f-32b3f67b28b3", "name": "User", "containerId": "", "created": "2022-11-04T21:20:12.114Z", "sortNum": 40000}], "requests": [{"_id": "02542a09-c0c5-42ff-8f44-c5e2a76fa009", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Creates a new offer", "url": "http://20.90.244.195/offer", "method": "POST", "sortNum": 5000, "created": "2022-11-04T21:04:23.882Z", "modified": "2022-11-04T21:19:37.812Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"name\": \"string\",\n  \"coordinator_id\": \"string\",\n  \"offer_runner_id\": \"string\",\n  \"currency\": \"string\",\n  \"short_code\": \"string\",\n  \"raise_amount\": 0,\n  \"offer_type\": \"string\",\n  \"raise_type\": \"string\",\n  \"shareholder_only\": false,\n  \"inside_information\": false,\n  \"issued_by\": \"string\",\n  \"market_sector\": \"string\"\n}", "form": []}, "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "60739abf-f9ef-4ab5-91a6-0717ac2b1dfb", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Gets all the offers", "url": "http://20.90.244.195/offer", "method": "GET", "sortNum": 10000, "created": "2022-11-04T20:37:15.278Z", "modified": "2022-11-04T21:03:30.825Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "f986480f-226b-49ab-a074-91c045c314f7", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Updates an offer", "url": "http://20.90.244.195/offer/{id}", "method": "POST", "sortNum": 40000, "created": "2022-11-04T21:10:09.194Z", "modified": "2022-11-04T21:11:43.305Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "98eee581-1c21-40aa-8e1a-6d5f25efdc98", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Gets a single offer", "url": "http://20.90.244.195/offer/{id}", "method": "GET", "sortNum": 60000, "created": "2022-11-04T21:11:32.010Z", "modified": "2022-11-04T21:12:04.292Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "b4b0c00a-b018-4620-b5e7-97f1732b8c8f", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Creates a new document for the offer", "url": "http://20.90.244.195/offer/{id}/document", "method": "POST", "sortNum": 70000, "created": "2022-11-04T21:13:12.016Z", "modified": "2022-11-04T21:14:01.062Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "0cc75eca-18e5-4eaf-9f4f-3331d0a2386d", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Gets summary information about all the documents for the offer", "url": "http://20.90.244.195/offer/{id}/document", "method": "GET", "sortNum": 80000, "created": "2022-11-04T21:14:03.368Z", "modified": "2022-11-04T21:14:13.052Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "5a5548f3-cf13-4fc0-969a-342ec7e3f254", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "f0bc294e-9178-45f0-8e29-4c154c552156", "name": "Creates an aggregation order consisting of a notional value and a number of underlying orders", "url": "http://20.90.244.195/offer/{offer_id}/order/aggregate", "method": "POST", "sortNum": 80000, "created": "2022-11-04T21:21:49.081Z", "modified": "2022-11-04T21:22:26.228Z", "headers": [], "params": [{"name": "offer_id", "value": "", "isPath": true}], "tests": []}, {"_id": "a4859595-a122-4c2a-84fb-0292039cf7e1", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Updates the specific document", "url": "http://20.90.244.195/offer/{id}/document/{did}", "method": "PUT", "sortNum": 90000, "created": "2022-11-04T21:14:20.662Z", "modified": "2022-11-04T21:14:45.756Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}, {"name": "did", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "e7a4343c-c7f1-4e96-9b68-3200902080c5", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "f0bc294e-9178-45f0-8e29-4c154c552156", "name": "Creates an order book order using cancel/replace semantics", "url": "http://20.90.244.195/offer/{offer_id}/order/book", "method": "POST", "sortNum": 90000, "created": "2022-11-04T21:22:37.170Z", "modified": "2022-11-04T21:23:05.586Z", "headers": [], "params": [{"name": "offer_id", "value": "", "isPath": true}], "tests": []}, {"_id": "e79194b9-6acb-499b-a1a9-d4a89e67e7f8", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Gets a specific document", "url": "http://20.90.244.195/offer/{id}/document/{did}", "method": "GET", "sortNum": 100000, "created": "2022-11-04T21:14:48.396Z", "modified": "2022-11-04T21:16:09.003Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}, {"name": "did", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "7b53fa2b-22a8-4be6-8c89-d79d930664b7", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "f0bc294e-9178-45f0-8e29-4c154c552156", "name": "Creates a detail order for a given offer ID by accepting a supported template sheet format", "url": "http://20.90.244.195/offer/{offer_id}/order/sheet", "method": "POST", "sortNum": 100000, "created": "2022-11-04T21:22:48.421Z", "modified": "2022-11-04T21:23:28.700Z", "headers": [], "params": [{"name": "offer_id", "value": "", "isPath": true}], "tests": []}, {"_id": "962f3773-c86f-4c19-a9ee-737e3400ad86", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "ba753acc-31d1-4cad-b7c7-1de4f44c1ec8", "name": "Returns a list of all intermediaries on the system", "url": "http://20.90.244.195/static/intermediaries", "method": "GET", "sortNum": 100000, "created": "2022-11-04T21:23:49.804Z", "modified": "2022-11-04T21:24:08.579Z", "headers": [], "params": [], "tests": []}, {"_id": "c37c6e57-6380-43f4-af05-b9987c79a9f1", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Updates the offer status if possible", "url": "http://20.90.244.195/offer/{id}/status", "method": "PUT", "sortNum": 110000, "created": "2022-11-04T21:16:24.838Z", "modified": "2022-11-04T21:17:14.910Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "body": {"type": "json", "raw": "{\n  \"event\": \"string\"\n}", "form": []}, "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "8ff08d99-70f8-4b83-bec9-4433bb7ea378", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "ba753acc-31d1-4cad-b7c7-1de4f44c1ec8", "name": "List of all roles applicable to a particular resource type", "url": "http://20.90.244.195/static/roles?resource_type", "method": "GET", "sortNum": 110000, "created": "2022-11-04T21:24:23.784Z", "modified": "2022-11-04T21:24:47.438Z", "headers": [], "params": [{"name": "resource_type", "value": "", "isPath": false}], "tests": []}, {"_id": "1277a6c1-eb86-4d91-8d05-45c0c1ac1af9", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "d9e58bef-69d8-491f-ba7f-32b3f67b28b3", "name": "Grant role to user", "url": "http://20.90.244.195/user/{user_id}/permissions/{scope}/{role}", "method": "PUT", "sortNum": 110000, "created": "2022-11-04T21:26:26.038Z", "modified": "2022-11-04T21:27:08.628Z", "headers": [], "params": [{"name": "user_id", "value": "", "isPath": true}, {"name": "scope", "value": "", "isPath": true}, {"name": "role", "value": "", "isPath": true}], "tests": []}, {"_id": "ff89cc45-a835-400f-b199-3e6f9b5323ef", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Returns the offer history", "url": "http://20.90.244.195/offer/{id}/history", "method": "GET", "sortNum": 120000, "created": "2022-11-04T21:17:58.160Z", "modified": "2022-11-04T21:18:40.662Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "8f408b3c-1c41-4c64-b65d-b890deedf04f", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "89569c98-65b7-45eb-8572-77a2670194fa", "name": "Creates a list of continuous order buckets for a given offer ID", "url": "http://20.90.244.195/offer/{id}/buckets", "method": "POST", "sortNum": 130000, "created": "2022-11-04T21:18:50.976Z", "modified": "2022-11-04T21:19:22.520Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "auth": {"type": "bearer", "bearer": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.X9fad6Vz4VU0Y6bs1VUEecaDeOxpRtdVS0KJrSCEBhA"}, "tests": []}, {"_id": "8021a946-64e7-4192-aa21-71c2c2e7ebbb", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "d9e58bef-69d8-491f-ba7f-32b3f67b28b3", "name": "Add a new user into the system", "url": "http://20.90.244.195/user", "method": "POST", "sortNum": 130000, "created": "2022-11-04T21:27:30.044Z", "modified": "2022-11-04T21:27:45.787Z", "headers": [], "params": [], "tests": []}, {"_id": "d4edeac9-6d9e-40ad-830d-60e05c498771", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "d9e58bef-69d8-491f-ba7f-32b3f67b28b3", "name": "Get list of all users in the system", "url": "http://20.90.244.195/user", "method": "GET", "sortNum": 140000, "created": "2022-11-04T21:28:05.182Z", "modified": "2022-11-04T21:28:14.362Z", "headers": [], "params": [], "tests": []}, {"_id": "132ee7f7-15d8-4991-9078-5d9a81cf55c3", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "d9e58bef-69d8-491f-ba7f-32b3f67b28b3", "name": "Update a user in the system", "url": "http://20.90.244.195/user/{id}", "method": "POST", "sortNum": 150000, "created": "2022-11-04T21:28:32.188Z", "modified": "2022-11-04T21:28:55.947Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "tests": []}, {"_id": "8c450076-2ff4-4eb1-aa9e-2a14f38d5653", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "d9e58bef-69d8-491f-ba7f-32b3f67b28b3", "name": "Get permissions granted to a user in global scope", "url": "http://20.90.244.195/user/{id}/permissions", "method": "GET", "sortNum": 160000, "created": "2022-11-04T21:29:03.327Z", "modified": "2022-11-04T21:29:27.280Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}], "tests": []}, {"_id": "225e1b9f-0529-4cfe-ab79-b84c3fecc8c2", "colId": "64a30753-8d49-4d89-94ab-0b05c2b67d30", "containerId": "d9e58bef-69d8-491f-ba7f-32b3f67b28b3", "name": "Get permissions granted to a user in a specific scope", "url": "http://20.90.244.195/user/{id}/permissions/{scope}", "method": "GET", "sortNum": 170000, "created": "2022-11-04T21:29:29.465Z", "modified": "2022-11-04T21:29:39.270Z", "headers": [], "params": [{"name": "id", "value": "", "isPath": true}, {"name": "scope", "value": "", "isPath": true}], "tests": []}]}