{"name": "retail-book-clearleft-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build:static": "next build && next export", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "start-storybook -p 6006 -s public", "build-storybook": "build-storybook -s public", "test:e2e": "playwright test"}, "dependencies": {"@headlessui/react": "1.7.13", "@hookform/resolvers": "^2.9.11", "@tanstack/react-query": "^4.14.1", "@tanstack/react-query-devtools": "^4.14.1", "axios": "^1.3.3", "classnames": "^2.3.2", "currency-symbol-map": "^5.1.0", "date-fns": "^2.29.3", "decimal.js": "^10.4.3", "easymde": "^2.18.0", "javascript-color-gradient": "^2.4.4", "jest": "^29.3.1", "next": "^12.3.1", "next-auth": "^4.15.0", "qs": "^6.11.0", "react": "18.2.0", "react-data-grid": "7.0.0-beta.29", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-feather": "^2.0.10", "react-hook-form": "^7.36.1", "react-markdown": "^8.0.4", "react-number-format": "^5.0.1", "react-simplemde-editor": "^5.2.0", "react-toastify": "^9.0.8", "react-tooltip": "^5.9.1", "react-vertical-timeline-component": "3.6.0", "recharts": "^2.9.3", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.19.3", "@faker-js/faker": "^7.5.0", "@hookform/devtools": "^4.2.2", "@playwright/test": "^1.27.1", "@storybook/addon-actions": "^6.5.12", "@storybook/addon-essentials": "^6.5.12", "@storybook/addon-interactions": "^6.5.12", "@storybook/addon-links": "^6.5.12", "@storybook/addon-postcss": "^2.0.0", "@storybook/builder-webpack5": "^6.5.12", "@storybook/manager-webpack5": "^6.5.12", "@storybook/react": "^6.5.12", "@storybook/testing-library": "^0.0.13", "@svgr/webpack": "^6.5.0", "@tailwindcss/forms": "^0.5.3", "@tomfreudenberg/next-auth-mock": "^0.5.6", "@types/javascript-color-gradient": "^2.4.2", "@types/node": "18.8.3", "@types/react": "18.0.21", "@types/react-dom": "18.0.6", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "autoprefixer": "^10.4.12", "babel-loader": "^9.1.2", "eslint": "^8.33.0", "eslint-config-next": "12.3.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-storybook": "^0.6.4", "postcss": "^8.4.17", "postcss-import": "^15.0.0", "prettier": "^2.7.1", "storybook-addon-next": "^1.6.9", "tailwindcss": "^3.1.8", "tsconfig-paths-webpack-plugin": "^4.0.0", "typescript": "^4.8.4"}}