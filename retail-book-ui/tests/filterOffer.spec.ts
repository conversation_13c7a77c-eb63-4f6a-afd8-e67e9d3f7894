import { test, expect, Page } from "@playwright/test";
test.use({
  storageState: "auth.json",
});

let page: Page;
test.beforeAll(async ({ browser }) => {
  page = await browser.newPage();
});

test("should be able to apply a filter by Pre-Launch", async () => {
  await page.goto("/");
  await page.getByRole("tab", { name: "All" }).click();
  await page.waitForSelector("role=button[name='7 Statuses']");
  await page.getByRole("button", { name: "7 Statuses" }).click();
  await page.waitForSelector("role=option[name='Pre-Launch']");
  await page.getByRole("option", { name: "Pre-Launch" }).click();
  await page.getByRole("button", { name: "6 Statuses" }).click();
  expect(
    await page.getByRole("link").filter({ hasText: "Pre-Launch" }).count()
  ).toEqual(0);
});

test("should be able to apply a filter by Building", async () => {
  await page.goto("/");
  await page.getByRole("tab", { name: "All" }).click();
  await page.waitForSelector("role=button[name='7 Statuses']");
  await page.getByRole("button", { name: "7 Statuses" }).click();
  await page.waitForSelector("role=option[name='Building']");
  await page.getByRole("option", { name: "Building" }).click();
  await page.getByRole("button", { name: "6 Statuses" }).click();
  expect(
    await page.getByRole("link").filter({ hasText: "Building" }).count()
  ).toEqual(0);
});

test("should be able to apply a filter by Applications Open", async () => {
  await page.goto("/");
  await page.getByRole("tab", { name: "All" }).click();
  await page.waitForSelector("role=button[name='7 Statuses']");
  await page.getByRole("button", { name: "7 Statuses" }).click();
  await page.waitForSelector("role=option[name='Applications Open']"); 
  await page.getByRole("option", { name: "Applications Open" }).click();
  await page.getByRole("button", { name: "6 Statuses" }).click();
  expect(
    await page
      .getByRole("link")
      .filter({ hasText: "Applications Open" })
      .count()
  ).toEqual(0);
});

test("should be able to apply a filter by Applications Closed", async () => {
  await page.goto("/");
  await page.getByRole("tab", { name: "All" }).click();
  await page.waitForSelector("role=button[name='7 Statuses']");
  await page.getByRole("button", { name: "7 Statuses" }).click();
  await page.waitForSelector("role=option[name='Applications Closed']");
  await page.getByRole("option", { name: "Applications Closed" }).click();
  await page.getByRole("button", { name: "6 Statuses" }).click();
  expect(
    await page
      .getByRole("link")
      .filter({ hasText: "Applications Closed" })
      .count()
  ).toEqual(0);
});


test("should be able to apply a filter by Instructions Sent", async () => {
  await page.goto("/");
  await page.getByRole("tab", { name: "All" }).click();
  await page.waitForSelector("role=button[name='7 Statuses']");
  await page.getByRole("button", { name: "7 Statuses" }).click();
  await page.waitForSelector("role=option[name='Instructions Sent']");
  await page.getByRole("option", { name: "Instructions Sent" }).click();
  await page.getByRole("button", { name: "6 Statuses" }).click();
  expect(
    await page
      .getByRole("link")
      .filter({ hasText: "Instructions Sent" })
      .count()
  ).toEqual(0);
});

test("should be able to apply a filter by Allocated", async () => {
  await page.goto("/");
  await page.getByRole("tab", { name: "All" }).click();
  await page.waitForSelector("role=button[name='7 Statuses']");
  await page.getByRole("button", { name: "7 Statuses" }).click();
  await page.waitForSelector("role=option[name='Allocated']");
  await page.getByRole("option", { name: "Allocated" }).click();
  await page.getByRole("button", { name: "6 Statuses" }).click();
  expect(
    await page.getByRole("link").filter({ hasText: "Allocated" }).count()
  ).toEqual(0);
});

test("should be able to apply a filter by Settled", async () => {
  //Test filter Building
  await page.goto("/");
  await page.getByRole("tab", { name: "All" }).click();

  await page.waitForSelector("role=button[name='7 Statuses']");
  await page.getByRole("button", { name: "7 Statuses" }).click();   
  await page.waitForSelector("role=option[name='Settled']");
  await page.getByRole("option", { name: "Settled" }).click();
  await page.getByRole("button", { name: "6 Statuses" }).click();
  expect(
    await page.getByRole("link").filter({ hasText: "Settled" }).count()
  ).toEqual(0);
});