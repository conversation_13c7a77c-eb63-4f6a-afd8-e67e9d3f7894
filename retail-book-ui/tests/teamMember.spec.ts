// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
// CHANGE THE CLICK
// CHANGE TO BANK!!
test.use({
  storageState: "auth-bank.json",
});

test("team member", async ({ page }) => {
  await page.goto("/team");

  //   // ADD TEAM MEMBER
  await page.getByRole("button", { name: "Add team member" }).click();
  await page.getByLabel("Name").click();
  await page.getByLabel("Name").fill("Test name");
  await page.getByLabel("Email").click();
  await page.getByLabel("Email").fill("Test email");
  await page.locator('legend:has-text("Wall crossing")').click();
  await page.getByLabel("EditorAble to view offers and add orders").check();
  await page
    .locator(
      'label:has-text("GatekeeperNotified of wall crossings and able to consent to be crossed")'
    )
    .click();
  await page.getByRole("button", { name: "Invite" }).click();

  // TEST ADD TEAM MEMBER
  await page.locator('a:has-text("2")').click();
  await expect(page.getByText("Test name")).toBeVisible();
  await expect(page.getByRole("cell", { name: "Test email" })).toBeVisible();

  // EDIT TEAM MEMBER
  await page
    .getByRole("row", {
      name: "Test name Test email Editor Open team member menu",
    })
    .getByRole("button", { name: "Open team member menu" })
    .click();

  await page.getByRole("link", { name: "Edit" }).click();
  await page.getByLabel("Name").click();
  await page.getByLabel("Name").fill("Test name 2");
  await page.getByLabel("Email").click();
  await page.getByLabel("Email").fill("Test email 2");
  await page
    .getByLabel(
      "ManagerIn addition to editor permissions, managers can add and remove team members"
    )
    .check();
  await page
    .getByLabel(
      "GatekeeperNotified of wall crossings and able to consent to be crossed"
    )
    .uncheck();
  await page.getByRole("button", { name: "Save" }).click();

  // TEST EDIT TEAM MEMBER: DOES NOT WORK UNTIL BACKEND INTEGRATION WITH AUTH WORKS

  // RESEND INVITE: DOES NOT WORK UNTIL BACKEND INTEGRATION WITH AUTH WORKS

  // DELETE TEAM MEMBER: ENDPOINT NOT IMPLEMENTED YET
});
