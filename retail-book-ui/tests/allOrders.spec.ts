// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-bank.json",
});

test("all orders", async ({ page }) => {
  // NAVIGATE TO ALL ORDERS
  await page.goto("/");
  await page.getByRole("link", { name: "Orders" }).click();

  // TEST ALL ORDERS
  // NOTE: THE FOLLOWING DATES WILL CHANGE IF A TEST ORDER IS CREATED
  await expect(
    page.getByRole("cell", { name: "06 Dec 2022 12:38" })
  ).toBeVisible();
  await expect(
    page.getByRole("cell", { name: "12 Dec 2022 05:42" })
  ).toBeVisible();
});
