// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-intermediary.json",
});

test("edit offer intermediary", async ({ page }) => {
  // EDIT OFFER NAVIGATION
  await page.goto("/");
  await page
    .getByRole("link", {
      name: "Profex IPO Ready for orders Applications Open IPO GBP Profex IPO - IPO 2 months to close Pre Launch 06 Nov 22 07:00 Open 15 Nov 22 08:06 Close 30 Jan 23 07:12",
    })
    .click();

  // TEST EDIT OFFER
  await expect(page.getByRole("heading", { name: "Profex IPO" })).toBeVisible();
  await expect(page.getByText("30 Jan 23")).toBeVisible();
  await expect(
    page.getByText(
      "Aspire Group plc , the world's leading bottle manufacturer, announced on 10 Augu"
    )
  ).toBeVisible();
});
