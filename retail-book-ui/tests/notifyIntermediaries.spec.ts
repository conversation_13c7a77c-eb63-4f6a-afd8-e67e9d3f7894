// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-bank.json",
});

test("notify intermediary", async ({ page }) => {
  // TEST NOTIFY INTERMEDIARIES
  await page.goto("/");
  await page
    .getByRole("link", {
      name: "Linked Offer Test2 Pre-Launch IPO GBP Linked Offer Test2 - IPO --- to open Pre Launch --- -- Open --- -- Close --- --",
    })
    .click();
  await page.getByRole("button", { name: "Notify users of changes" }).click();
  await page
    .getByRole("dialog", { name: "Notify of changes to this offer" })
    .locator("textarea")
    .click();
  await page
    .getByRole("dialog", { name: "Notify of changes to this offer" })
    .locator("textarea")
    .fill("Test message!");
  await page.getByRole("button", { name: "Send" }).click();

  // TEST NOTIFY INTERMEDIARIES
  await page.getByRole("button", { name: "View notifications" }).click();
  await expect(
    page.locator(
      'label:has-text("There are currently no unread notifications to show")'
    )
  ).not.toBeVisible();
});
