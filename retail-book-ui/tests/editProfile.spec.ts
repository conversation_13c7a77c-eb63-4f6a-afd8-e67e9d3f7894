// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-bank.json",
});

test("edit profile", async ({ page }) => {
  // EDIT PROFILE NAVIGATION
  await page.goto("/");
  await page.getByRole("button", { name: "Open user menu" }).click();
  await page.getByRole("link", { name: "Account" }).click();
  // THE FOLLOWING LINE MAY NOT BE NEEDED IF A TEST USER IS CREATED
  await page.getByRole("button", { name: "Save" }).click();
  await page.getByLabel("Username").click();
  await page.getByLabel("Username").fill("placeholder username");
  await page.getByLabel("Email Address").click();
  await page.getByLabel("Email Address").fill("placeholder email");

  // SAVE CHANGES
  await page.getByRole("button", { name: "Save" }).click();

  // TEST CHANGES
  await expect(page.getByPlaceholder("placeholder username")).toBeVisible();
  await expect(page.getByPlaceholder("placeholder email")).toBeVisible();

  // UNDO CHANGES
  await page.getByPlaceholder("placeholder username").click();
  await page
    .getByPlaceholder("placeholder username")
    .fill("NEW PLACEHOLDER USERNAME");
  await page.getByPlaceholder("placeholder email").click();
  await page
    .getByPlaceholder("placeholder email")
    .fill("NEW PLACEHOLDER EMAIL");

  // SAVE CHANGES
  await page.getByRole("button", { name: "Save" }).click();
});
