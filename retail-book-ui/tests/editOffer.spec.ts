// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-bank.json",
});

test("edit offer", async ({ page }) => {
  // EDIT OFFER
  await page.goto("/");
  await page
    .getByRole("link", {
      name: "Ready for orders Applications Open Follow On GBP Aspire Follow On - Follow On 2 months to close Pre Launch 09 Nov 22 07:00 Open 07 Nov 22 07:00 Close 10 Feb 23 07:00",
    })
    .click();

  await page.getByRole("tab", { name: "Overview" }).click();
  await page.getByLabel("Offer name").click();
  await page.getByLabel("Offer name").fill("Aspire Follow On edit");
  await page
    .getByRole("combobox", { name: "Type" })
    .selectOption("Retail Bond");
  await page.getByLabel("Price").click();
  await page.getByLabel("Price").fill("1.00");
  await page.getByLabel("Minimum Order").click();
  await page.getByLabel("Minimum Order").fill("27");
  await page.getByLabel("Raise Amount").click();
  await page.getByLabel("Raise Amount").fill("6000");
  await page.locator('input[name="open_date"]').fill("2022-11-09");
  await page.locator('input[name="close_date"]').fill("2023-02-12");

  // MDE:
  //   await page
  //     .locator(
  //       'div[role="application"]:has-text("||||xxxxxxxxxx Aspire Group plc , the world\'s leading bottle manufacturer, annou") textarea'
  //     )
  //     .fill(" edit1");
  //   await page
  //     .locator(
  //       'div[role="application"]:has-text("||||xxxxxxxxxx Aspire Group plc (LSE: COA), the world\'s leading bottle manufactu") textarea'
  //     )
  //     .fill(" edit2");
  //   await page
  //     .locator(
  //       'div[role="application"]:has-text("||||xxxxxxxxxx ​​ * Allocations to Intermediaries will be determined solely by t") textarea'
  //     )
  //     .fill("edit3");

  // SAVE CHANGES
  await page.getByRole("button", { name: "Save" }).click();

  // TEST CHANGES
  await expect(
    await page.getByRole("heading", { name: "Aspire Follow On edit" })
  ).toBeVisible();
  await expect(page.getByRole("combobox", { name: "Type" })).toHaveValue(
    "Retail Bond"
  );
  await expect(page.getByLabel("Price")).toHaveValue("1.00");
  await expect(page.getByLabel("Minimum Order")).toHaveValue("27");
  await expect(page.getByLabel("Raise Amount")).toHaveValue("6000");

  // MDE:
  //   await expect(
  //     page.locator(
  //       'span[role="presentation"]:has-text("edit1Aspire Group plc , the world\'s leading bottle manufacturer, announced on 10")'
  //     )
  //   ).toBeVisible();

  // UNDO CHANGES
  await page.getByLabel("Offer name").click();
  await page.getByLabel("Offer name").fill("Aspire Follow On");
  await page.getByRole("combobox", { name: "Type" }).selectOption("IPO");
  await page.getByLabel("Price").click();
  await page.getByLabel("Price").fill("2.00");
  await page.getByLabel("Minimum Order").click();
  await page.getByLabel("Minimum Order").fill("28");
  await page.getByLabel("Raise Amount").click();
  await page.getByLabel("Raise Amount").fill("6001");
  await page.locator('input[name="open_date"]').fill("2022-11-08");
  await page.locator('input[name="close_date"]').fill("2023-02-11");

  // MDE:
  //   await page
  //     .locator(
  //       'span[role="presentation"]:has-text("edit1Aspire Group plc , the world\'s leading bottle manufacturer, announced on 10")'
  //     )
  //     .fill("blah");

  //SAVE UNDONE CHANGES
  await page.getByRole("button", { name: "Save" }).click();
});
