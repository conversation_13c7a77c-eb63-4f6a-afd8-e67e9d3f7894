// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-intermediary.json",
});

test("update order", async ({ page }) => {
  // UPDATE ORDER
  await page.goto("/");
  await page
    .getByRole("link", {
      name: "Profex IPO View Order Applications Open IPO GBP Profex IPO - IPO Total Value £15.00 Total Applications 13 11 months to close Pre Launch 07 Nov 22 07:00 Open 28 Oct 22 08:06 Close 31 Jan 22 07:12",
    })
    .click();
  await page.getByRole("button", { name: "Update" }).click();
  await page.getByLabel("Applications").click();
  await page.getByLabel("Applications").fill("12");
  await page.locator('input[type="text"]').click();
  await page.locator('input[type="text"]').fill("13.00");
  await page.getByRole("button", { name: "Confirm Orders" }).click();
  await page.getByRole("button", { name: "Submit" }).click();

  // TEST CHANGES
  await expect(page.locator("dd", { hasText: "13" })).toBeVisible();
  await expect(page.locator("dd", { hasText: "£15.00" })).toBeVisible();

  // UNDO CHANGES
  await page.getByRole("button", { name: "Update" }).click();
  await page.getByLabel("Applications").click();
  await page.getByLabel("Applications").fill("13");
  await page.locator('input[type="text"]').click();
  await page.locator('input[type="text"]').fill("15.00");
  await page.getByRole("button", { name: "Confirm Orders" }).click();
  await page.getByRole("button", { name: "Submit" }).click();
});
