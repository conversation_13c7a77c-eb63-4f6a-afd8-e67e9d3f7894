// Note: These tests use the bank role.
// The following instructions will create a bank.json file that must be updated every 7 days.
// Run this command and login as a bank to save bank role:  npx playwright codegen --save-storage=auth.json localhost:3000
// Run this command to use bank role: `npx playwright codegen  --load-storage=auth-bank.json localhost:3000                                              `
// Run this command to preserve order: `npx playwright test --workers=1`

// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect, Page } from "@playwright/test";
test.use({
  storageState: "auth-bank.json",
});

let page: Page;
test.beforeAll(async ({ browser }) => {
  page = await browser.newPage();
});

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

test("test add edit delete document flow", async () => {
  // add document
  await page.goto("/");
  await page
    .getByRole("link", {
      name: "Profex IPO Ready for orders Applications Open IPO GBP Profex IPO - IPO 17 days to close Pre Launch 09 Nov 22 07:00 Open 29 Nov 22 07:06 Close 30 Dec 22 07:13",
    })
    .click();
  await page.getByRole("tab", { name: "Documents & Resources" }).click();
  await page.getByRole("button", { name: "Add document" }).click();
  await page.setInputFiles(
    'input[id="document-upload"]',
    "./tests/fixtures/test-rick.pdf"
  );
  await page.getByLabel("Name").click();
  await page.getByLabel("Name").fill("test-rick");
  await page.getByRole("button", { name: "Update document" }).click();
  await page.getByRole("button", { name: "Cancel" }).click();
  await expect(await page.getByText("test-rick")).toBeVisible();
  //editDocument;

  await page
    .getByRole("row", {
      name: "test-rick prospectus pdf 889467 Open team member menu",
    })
    .getByRole("button", { name: "Open team member menu" })
    .click();
  await page.getByRole("link", { name: "Edit" }).click();
  await page.getByLabel("Name").click();
  await page.getByLabel("Name").fill("test-rick-update");
  await page
    .getByRole("combobox", { name: "Select a type" })
    .selectOption("timeline");
  await page.getByRole("button", { name: "Update document" }).click();
  await expect(await page.getByText("test-rick-update")).toBeVisible();
  await page.getByRole("cell", {
    name: "timeline",
  });

  // delete document
  await page
    .getByRole("row", {
      name: "test-rick-update timeline pdf 889467 Open team member menu",
    })
    .getByRole("button", { name: "Open team member menu" })
    .click();
  await page.getByRole("link", { name: "Remove" }).click();
  page.getByRole("button", { name: "Remove document" }).click();
  await delay(3000);
  await expect(await page.getByText("test-rick-update")).not.toBeVisible();
});
