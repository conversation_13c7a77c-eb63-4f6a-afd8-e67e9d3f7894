// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-kelvin.json",
});

test("notify intermediary", async ({ page }) => {
  // NOTIFICATION HISTORY
  await page.goto("/account");
  await page.getByRole("tab", { name: "Notification Log" }).click();

  // TEST NOTIFICATION HISTORY
  // NOTE THE NEXT LINE WILL CHANGE IF A TEST NOTIFICATION IS CREATED
  await expect(
    page.getByRole("cell", { name: "test notification" })
  ).toBeVisible();
  await expect(page.getByRole("cell", { name: "test" })).toBeVisible();
  await expect(
    page.getByRole("cell", { name: "18 Nov 2022 03:57" })
  ).toBeVisible();
});
