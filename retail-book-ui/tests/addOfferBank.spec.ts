// eslint-disable-next-line @typescript-eslint/rule-name
import { test, expect } from "@playwright/test";
test.use({
  storageState: "auth-bank.json",
});

test("add offer bank", async ({ page }) => {
  // ADD OFFER NAVIGATION
  await page.goto("/");
  await page.getByRole("link", { name: "Add Offer" }).click();
  await page.getByLabel("Offer Name").fill("a-test-lil");
  // note: uncomment this part after delete offer endpoint is implemented
  // otherwise there will be an offer created each time this test is run
  //   await page.getByRole("button", { name: "Create" }).click();

  //   // TEST ADD OFFER
  //   await expect(page.getByRole("heading", { name: "a-test-lil" })).toBeVisible();
  //   await expect(page.getByText("Offer typeIPO")).toBeVisible();

  // CHECK THAT NEW OFFER IS ON OFFERS PAGE
  await page.goto("/");
  await page.getByRole("button", { name: "2 Statuses" }).click();
  await page.getByRole("option", { name: "Pre-Launch" }).click();
  await page.getByRole("option", { name: "Applications Open" }).click();
  // note: uncomment this part after delete offer endpoint is implemented
  // otherwise there will be an offer created each time this test is run
  //   await expect(
  //     page.getByRole("link", {
  //       name: "a-test-lil Building IPO GBP a-test-lil - IPO --- to registration Pre Launch --- -- Open --- -- Close --- --",
  //     })
  //   ).toBeVisible();
});
