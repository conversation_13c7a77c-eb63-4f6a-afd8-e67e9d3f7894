const defaultTheme = require("tailwindcss/defaultTheme");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./stories/**/*.{js,ts,jsx,tsx}",
  ],
  // Don't safeList these patterns for production or it will negatively impact the bundle size - required for foundation stories
  safelist: [
    {
      pattern: /text-/,
    },
    {
      pattern: /bg-/,
    },
  ],
  theme: {
    extend: {
      spacing: {
        "3xs": "var(--space-3xs)",
        "2xs": "var(--space-2xs)",
        xs: "var(--space-xs)",
        sm: "var(--space-s)",
        md: "var(--space-m)",
        lg: "var(--space-l)",
        xl: "var(--space-xl)",
        "2xl": "var(--space-2xl)",
        "3xl": "var(--space-3xl)",
        // 1 up pairs
        "3xs-2xs": "var(--space-3xs-2xs)",
        "2xs-xs": "var(--space-2xs-xs)",
        "xs-sm": "var(--space-xs-s)",
        "sm-md": "var(--space-s-m)",
        "md-lg": "var(--space-m-l)",
        "lg-xl": "var(--space-l-xl)",
        "xl-2xl": "var(--space-xl-2xl)",
        "2xl-3xl": "var(--space-2xl-3xl)",
        // Custom pairs
        "xs-md": "var(--space-xs-m)",
      },
      fontFamily: {
        sans: ["DM Sans", ...defaultTheme.fontFamily.sans],
        mono: ["DM Mono", ...defaultTheme.fontFamily.mono],
        brand: ["FF Mark", ...defaultTheme.fontFamily.sans],
      },
      lineHeight: {
        tight: "1.1",
        snug: "1.25",
        normal: "1.4",
        relaxed: "1.7",
        loose: "2",
      },
    },
    fontSize: {
      xs: "var(--step--2)",
      sm: "var(--step--1)",
      base: "var(--step-0)",
      lg: "var(--step-1)",
      xl: "var(--step-2)",
      "2xl": "var(--step-3)",
      "3xl": "var(--step-4)",
      "4xl": "var(--step-5)",
      "5xl": "var(--step-6)",
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      bold: 700,
    },
    colors: {
      transparent: "transparent",
      current: "currentColor",
      white: "#ffffff",
      black: "#000000",
      "duke-blue": {
        "step-2": "#9B99D4",
        "step-1": "#4F4DB4",
        "step-0": "#040194",
        "step--1": "#030168",
        "step--2": "#02003B",
      },
      bluetiful: {
        "step-3": "#DFE8FB",
        "step-2": "#ABC1F3",
        "step-1": "#6B92EB",
        "step-0": "#2C63E2",
        "step--1": "#1F459E",
        "step--2": "#12285A",
      },
      ducati: {
        "step-2": "#A2D8DA",
        "step-1": "#5CBBBE",
        "step-0": "#169EA2",
        "step--1": "#0F6F71",
        "step--2": "#093F41",
      },
      jade: {
        "step-2": "#99DDC5",
        "step-1": "#4DC399",
        "step-0": "#00A96D",
        "step--1": "#00764C",
        "step--2": "#00442C",
      },
      "vivid-orange": {
        "step-3": "#FFEFE5",
        "step-2": "#FFBF99",
        "step-1": "#FF904D",
        "step-0": "#FF6000",
        "step--1": "#B34300",
        "step--2": "#662600",
      },
      "red-orange": {
        "step-3": "#FFECEA",
        "step-2": "#FFB3AC",
        "step-1": "#FF7A6E",
        "step-0": "#FF4130",
        "step--1": "#B32E22",
        "step--2": "#661A13",
      },
      pink: {
        "step-3": "#FFE8EF",
        "step-2": "#FFA5C0",
        "step-1": "#FF6190",
        "step-0": "#FF1E61",
        "step--1": "#B31544",
        "step--2": "#660C27",
      },
      grey: {
        "step-5": "#F6F6F7",
        "step-4": "#EDECEE",
        "step-3": "#DADADE",
        "step-2": "#B5B5BC",
        "step-1": "#7E7E8A",
        "step-0": "#474658",
        "step--1": "#32313E",
        "step--2": "#1C1C23",
      },
      "utility-red": {
        "step-1": "#FBCCCC",
        "step-0": "#ED0000",
        "step--1": "#5F0000",
      },
      "utility-green": {
        "step-1": "#CCE8CC",
        "step-0": "#008B00",
        "step--1": "#003800",
      },
      "utility-yellow": {
        "step-1": "#FAE5CC",
        "step-0": "#E87D00",
        "step--1": "#5D3200",
      },
    },
  },
  plugins: [require("@tailwindcss/forms")],
};
