import getSymbolFromCurrency from "currency-symbol-map";
import Decimal from "decimal.js";
import { formatNumberString } from "helpers";

const currencyDigits = new Map<string, number>([
  ["AED", 2],
  ["AFN", 0],
  ["ALL", 0],
  ["AMD", 2],
  ["ANG", 2],
  ["AOA", 2],
  ["ARS", 2],
  ["AUD", 2],
  ["AWG", 2],
  ["AZN", 2],
  ["BAM", 2],
  ["BBD", 2],
  ["BDT", 2],
  ["BGN", 2],
  ["BHD", 3],
  ["BIF", 0],
  ["BMD", 2],
  ["BND", 2],
  ["BOB", 2],
  ["BOV", 2],
  ["BRL", 2],
  ["BSD", 2],
  ["BTN", 2],
  ["BWP", 2],
  ["BYN", 2],
  ["BZD", 2],
  ["CAD", 2],
  ["CDF", 2],
  ["CHE", 2],
  ["CHF", 2],
  ["CHW", 2],
  ["CLF", 4],
  ["CLP", 0],
  ["CNY", 2],
  ["COP", 2],
  ["COU", 2],
  ["CRC", 2],
  ["CUC", 2],
  ["CUP", 2],
  ["CVE", 2],
  ["CZK", 2],
  ["DJF", 0],
  ["DKK", 2],
  ["DOP", 2],
  ["DZD", 2],
  ["EGP", 2],
  ["ERN", 2],
  ["ETB", 2],
  ["EUR", 2],
  ["FJD", 2],
  ["FKP", 2],
  ["GBP", 2],
  ["GEL", 2],
  ["GHS", 2],
  ["GIP", 2],
  ["GMD", 2],
  ["GNF", 0],
  ["GTQ", 2],
  ["GYD", 2],
  ["HKD", 2],
  ["HNL", 2],
  ["HRK", 2],
  ["HTG", 2],
  ["HUF", 2],
  ["IDR", 2],
  ["ILS", 2],
  ["INR", 2],
  ["IQD", 0],
  ["IRR", 0],
  ["ISK", 0],
  ["JMD", 2],
  ["JOD", 3],
  ["JPY", 0],
  ["KES", 2],
  ["KGS", 2],
  ["KHR", 2],
  ["KMF", 0],
  ["KPW", 0],
  ["KRW", 0],
  ["KWD", 3],
  ["KYD", 2],
  ["KZT", 2],
  ["LAK", 0],
  ["LBP", 0],
  ["LKR", 2],
  ["LRD", 2],
  ["LSL", 2],
  ["LYD", 3],
  ["MAD", 2],
  ["MDL", 2],
  ["MGA", 0],
  ["MKD", 2],
  ["MMK", 0],
  ["MNT", 2],
  ["MOP", 2],
  ["MRU", 2],
  ["MUR", 2],
  ["MVR", 2],
  ["MWK", 2],
  ["MXN", 2],
  ["MXV", 2],
  ["MYR", 2],
  ["MZN", 2],
  ["NAD", 2],
  ["NGN", 2],
  ["NIO", 2],
  ["NOK", 2],
  ["NPR", 2],
  ["NZD", 2],
  ["OMR", 3],
  ["PAB", 2],
  ["PEN", 2],
  ["PGK", 2],
  ["PHP", 2],
  ["PKR", 2],
  ["PLN", 2],
  ["PYG", 0],
  ["QAR", 2],
  ["RON", 2],
  ["RSD", 0],
  ["RUB", 2],
  ["RWF", 0],
  ["SAR", 2],
  ["SBD", 2],
  ["SCR", 2],
  ["SDG", 2],
  ["SEK", 2],
  ["SGD", 2],
  ["SHP", 2],
  ["SLE", 2],
  ["SLL", 0],
  ["SOS", 0],
  ["SRD", 2],
  ["SSP", 2],
  ["STN", 2],
  ["SVC", 2],
  ["SYP", 0],
  ["SZL", 2],
  ["THB", 2],
  ["TJS", 2],
  ["TMT", 2],
  ["TND", 3],
  ["TOP", 2],
  ["TRY", 2],
  ["TTD", 2],
  ["TWD", 2],
  ["TZS", 2],
  ["UAH", 2],
  ["UGX", 0],
  ["USD", 2],
  ["USN", 2],
  ["UYI", 0],
  ["UYU", 2],
  ["UYW", 4],
  ["UZS", 2],
  ["VED", 2],
  ["VES", 2],
  ["VND", 0],
  ["VUV", 0],
  ["WST", 2],
  ["XAF", 0],
  ["XCD", 2],
  ["XOF", 0],
  ["XPF", 0],
  ["YER", 0],
  ["ZAR", 2],
  ["ZMW", 2],
  ["ZWL", 2],
]);

const getDecimalsForCurrency = (ccy: string) => {
  return currencyDigits.get(ccy);
};

const defaultLocale = "en-GB";

export const formatCurrency = (
  amount: Decimal,
  currency: string,
  dp?: number
) => {
  if (!amount) return "--";

  const ccyDp = getDecimalsForCurrency(currency) ?? 0;

  if (dp === undefined || dp === null) {
    dp = ccyDp;
  }
  let ccy = getSymbolFromCurrency(currency);
  ccy = ccy ? ccy : "";

  const currentDecimalPlaces = amount.dp();

  // If we've specified some decimals places (if 0, we always truncate)
  //  but standard currency is less than specified (i.e we ask for 4 d.p for a price, but standard ccy is 2d.p)
  //  and the number of decimal places is less than the ccy amount, but more than 0.
  //  we pad to the ccy number of places
  //
  //  i.e  10.1 is 10.10 if we ask for 4 d.p, and the ccy is GBP.
  //       10.00 is 10 no matter what
  //       10.12345678 is 10.1234 if we ask for 4.d.p
  if (
    dp !== 0 &&
    ccyDp <= dp &&
    currentDecimalPlaces > 0 &&
    currentDecimalPlaces < ccyDp
  ) {
    return ccy + formatNumberString(amount.toFixed(ccyDp));
  } else {
    return ccy + formatNumberString(amount.toDecimalPlaces(dp).toString());
  }
};
