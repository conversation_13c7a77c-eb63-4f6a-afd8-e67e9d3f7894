import { ComponentType } from "react";
import Decimal from "decimal.js";

//  Utility
export type ExtractProps<T> = T extends ComponentType<infer P> ? P : T;

export type Clearable<T> = { [K in keyof T]: T[K] | null };


export enum EOfferType {
  FOLLOW_ON = "Follow On",
  RETAIL_BOND = "Retail Bond",
  IPO = "IPO",
}

export enum EOfferStatus {
  BUILDING = "Building",
  PRE_LAUNCH = "Pre-Launch",
  APPLICATIONS_OPEN = "Applications Open",
  APPLICATIONS_CLOSED = "Applications Closed",
  ALLOCATING = "Allocating",
  ALLOCATED = "Allocated",
  INSTRUCTIONS_SENT = "Instructions Sent",
  SETTLED = "Settled",
  ABANDONED = "Abandoned",
  BUILDING_ON_HOLD = "BuildingOnHold",
  PRE_LAUNCH_ON_HOLD = "Pre-Launch On Hold",
  APPLICATIONS_OPEN_ON_HOLD = "Applications Open On Hold"
}

export enum EWallCrossStatus {
  PENDING = "Pending",
  ACCEPTED = "Accepted",
  REJECTED = "Rejected",
  NOT_RESPONDED = "NoResponse"
}

export enum EInsiderEvent {
  WALL_CROSSING = "WallCrossing",
  TEAM_ACCESS = "TeamAccess",
  MANUAL_ADD = "Manual"
}

export enum EInsiderStatus {
  ACCEPTED = "Accepted",
  DECLINED = "Declined",
  AUTO = "Automatic"
}

export enum EUserStatus {
  ACTIVE = "Active",
  INACTIVE = "Inactive",
  PENDING = "Pending",
  CREATED = "Created",
  INVITED = "Invited",
  FAILURE = "Failure"
}

export enum EContractStatus {
  SIGNED = "Signed",
  UNSIGNED = "Unsigned",
}

export enum EOrderType {
  AGGREGATE = "Summary",
  DETAILED = "Itemised",
}

export enum EOrderStatus {
  UNCONFIRMED = "Unconfirmed",
  PENDING = "Pending",
  ACCEPTED = "Accepted",
  REJECT = "Rejected",
  SUPERSEDED = "Superseded",
  DELETED = "Deleted",
  REPLACEPENDING = "ReplacePending",
  REPLACED = "Replaced"
}

export enum EOrderHistoryEvent {
  CREATED = "CREATED",
  UPDATED = "UPDATED",
}

export enum EAllocationStatus {
  UNNOTIFIED = "unnotified",
  NOTIFIED = "notified",
  INVALID = "invalid"
}

export enum EUserRole {
  INTERMEDIARY = "intermediary",
  ISSUER = "bank",
}

export enum ENotificationType {
  INFO = "info",
  SUCCESS = "success",
  FAILURE = "failure",
  WARNING = "warning",
}

export enum ENotificationStatus {
  READ = "Read",
  UNREAD = "Unread",
}

export enum ENotificationCategory {
  OFFER = "Offer",
  OFFER_ORDER = "OfferOrder",
  OFFER_DOCUMENT = "OfferDocument",
  OFFER_ALLOCATION = "OfferAllocation",
  WALL_CROSSING = "WallCrossing",
}

export enum EDocumentType {
  RETAIL_OFFER_NOTICE = "retail-offer-notice"
}

export interface Resource {
  id: string;
  title: string;
  file_name: string;
  type: string;
  mime_type: string;
  size: number;
  url: string;
}

export interface UploadResource extends Resource {
  file: File;
}

interface Contract extends Resource {
  status: EContractStatus;
}

export type UpdateUserNotificationStatusVariables = {
  userId: string;
  id: string;
  status: string;
}

export type UpdateAllUserNotificationsStatusVariables = {
  userId: string;
  status: string;
}

export type DownloadAllocationAnnotatedVariables = {
  offerId: string;
  allocationBookId: string;
}

export type DownloadAllocationAnnotatedByOfferAllocVariables = {
  offerId: string;
  offerAllocationId: string;
}

export type TOfferSummary = {
  id: string;
  name: string;
  type: EOfferType;
  status: EOfferStatus;
  close_date: string;
  open_date: string;
  registration_date: string;
  issuer_logo?: string;
  issued_by: string;
  currency: string;
  totals?: IOrderBase;
  offer_price?: Decimal;
  shareholders_only?: boolean;
  inside_information?: boolean;
  price_range_low?: Decimal;
  price_range_high?: Decimal;
};

export type TOfferStatus = {
  failures: string[];
  state: string;
  valid: boolean;
};

export type StatusEvent = {
  event: string;
  set_open_time?: boolean;
  set_close_time?: boolean;
  cleansing_time?: string;
};

export type StatusTransition = {
  event: string;
  future_state: string;
};

export type TOfferState = {
  state_name: string;
  state_description: string;
}

export type StatusReturnType = {
  state_name: string;
  transitions: StatusTransition[];
};

export type TOfferUIStateOerationOptions = {
  set_open_time?: boolean;
  set_close_time?: boolean;
  set_cleansing_time?: boolean;
};

export type TOfferUIStateOperation = {
  enabled: boolean;
  label: string;
  confirm_text: string;
  options?: TOfferUIStateOerationOptions;    
};

export type TOfferUIStateField = {
  field: string;
  editable: boolean;
  error?: string;
  warning?: string;
};

export type TOfferUIStates = {
  has_allocation: boolean;
  is_launched: boolean;
  has_settlements: boolean;
};

export type TOfferUIState = {
  operations: TOfferUIStateOperation[];
  fields: TOfferUIStateField[];
  states: TOfferUIStates;
};

export type TOfferHeadingFieldMessagePair = {
  field: string;
  isError: boolean;
  message?: string;
}

export type TOffer = {
  /** Shared Schema */
  name: string; // The name of deal. Must be unique within the banks database
  type: EOfferType; // (Follow-on, IPO, Retail Bond) The type of deal (immutable)
  status: EOfferStatus;  
  close_date: string; // The date the deal closes
  min_order_amount?: Decimal; // the minimum order amount
  shareholders_only: boolean; // True/False
  inside_information: boolean;
  currency: string; // The ISO3 currency code for the deal (default to GBP)
  issued_by: string; // The participant that is issuing the deal
  raise_amount?: Decimal; // The amount of equity available to retail investors
  id: string;
  issuer_logo?: string;
  dateCreated: string;
  dateUpdated: string;
  registration_date: string;
  open_date: string;
  description: string;
  details: string;
  allocation_principles: string;
  settlement_details: string;
  offer_price?: Decimal;
  slug: string;
  isin?: string;
  sedol?: string;
  crest_id?: string;
  ticker?: string;
  security_name?: string;
  // ID/Type of attached documents to the deal
  documents: Resource[];
  // A list of all signed contracts on the deal
  contracts?: Contract[];
  wall_cross_info_id?: string;
  is_launched: boolean;
  price_range_low?: Decimal;
  price_range_high?: Decimal;
  settlement_date: string;
};

export type TWallCrossing = {
  id: string;
  invite_id: string;
  subject: string;
  bank_system_id: string;
  invite_sent_time: string;
  invite_response_time?: string;
  status: EWallCrossStatus;
  consent_to_cross?: string;
  offer_outline_document_id?: string;
  offer_id?: string;
  is_launched: boolean;
};

export type TWallCrossInfo = {
  id: string;
  subject?: string;
  consent_to_cross?: string;
  offer_outline_document_id?: string;
}

export interface WallCrossInviteResponse extends Invite {
  id?: string;
  invite_sent_time?: string;
  status?: string;
  responses?: WallCrossInviteGatekeeperResponse[];
}

export interface WallCrossInviteGatekeeperResponse {
  id: string;
  email?: string;
  name?: string;
  status: string;
  response_time?: string;
}

export type IntermediaryResponse = Array<{
  system_id: string;
  display_name: string;
}>;

export type Invite = {
  intermediary_system_id: string;
  intermediary_display_name: string;
};

export type TDocumentType = {
  display_name: string;
  value: string;
};

export type TTerms = {
  accepted: boolean;
  accepted_time?: string;
  accepted_by?: string;
  accepted_by_email?: string;
  receive_commission?: boolean;
  retail_offer_notice_id?: string;
}

export interface InviteResponse extends Invite {
  id?: string;
  time_of_invite?: string;
  time_of_sending?: string;
  terms?: TTerms;
  evergreen?: boolean;
  status?: string;
}

// Order
export interface IOrderBase {
  applications?: Decimal;
  notional_value?: Decimal;
  existing_holding?: Decimal;
}

export type TOrderLineItem = {
  client_order_ref: string;
  notional_value: Decimal;
  commission_due: boolean;
  tax_wrapper: boolean;
  existing_holding?: Decimal;
};

export type TOrder = {
  id: string;
  order_type: EOrderType;
  order_date: string;
  status: EOrderStatus;
  offer_id: string;
  offer_name: string;
  order_book_id?: string;
  update_date: string;
  non_shareholding?: IOrderBase;
  shareholding?: IOrderBase;
  totals: IOrderBase;
  lineItems?: TOrderLineItem[];
  intermediary?: string;
  intermediary_system_id?:string;
  entered_by?: string;
};

export type TOrderBreakdownL2 = {
  total?: IOrderBase;
  shareholding?: IOrderBase;
  non_shareholding?: IOrderBase;
}

export type TOrderBreakdown = {
  accepted: TOrderBreakdownL2;
  pending: TOrderBreakdownL2;
}

export type TOrderTotals = {
  orders: TOrder[];
  breakdown: TOrderBreakdown;
  totals: IOrderBase;
};

export type TOfferMetrics = {
  data: TOfferMetricsSnapshot[];
  totals?: TOfferMetricsTotals;
};
export type TOfferMetricsSnapshot = {
  date: string;
  intermediaries: TIntermediaryOfferMetric[];
  number_of_offers?: Decimal;
};
export type TIntermediaryOfferMetric = {
  name: string;
  system_id: string;
  currency: string;
  order_value?: Decimal;
  applications?: Decimal;
  allocation_value?: Decimal
};
export type TOfferMetricsTotals = {
  offers: Decimal;
  currency: TIntermediaryOfferMetric[];
  intermediary_currency: TIntermediaryOfferMetric[];
};

export interface TAggregateOrder {
  non_shareholding?: IOrderBase;
  shareholding?: IOrderBase;
}

export type TOrderHistory = {
  delta: IOrderBase;
  order: TOrder;
};

// Allocation
export interface IAllocationBase {
  applications?: Decimal;
  notional_value?: Decimal;
  order_quantity?: Decimal;
  num_orders?: Decimal;
  allocated_orders?: Decimal;
  unallocated_orders?: Decimal;
  allocation_value?: Decimal;
  allocation_quantity?: Decimal;
}

export type TAllocation = {
  id: string;
  entered_by?: string;
  date_created: string;
  totals: IAllocationBase;
  shareholdings?: IAllocationBase;
  non_shareholdings?: IAllocationBase;
  allocation_book_id?: string;
  status: EAllocationStatus;
};

export type TDetailedAllocation = {
  client_order_ref?: string;
  existing_holding?: Decimal;
  notional_value?: Decimal;
  tax_wrapper?: boolean;
  commission_due?: boolean;
  intermediary?: string;
  validation_error?: string;
  qty?:Decimal;  
  allocation_value?: Decimal;
  shares_allocated?:Decimal;
  applications?:Decimal;
};

export type TAggregatedAllocation = {
  allocated_orders?: Decimal;
  alloc_quantity?: Decimal;
  alloc_value?: Decimal;
  applications?: Decimal;
  intermediary: string;
  intermediary_system_id: string;
  qty?: Decimal;
  value?: Decimal;
  number_of_orders?: Decimal;
};

// Insider
export type TNewInsider = {
  user_name: string;
  user_email: string;
  event: EInsiderEvent;
  status: EInsiderStatus;
  inside_time: string;
}

export type TInsider = {
  id: string;
  user_name: string;
  user_email: string;
  event: EInsiderEvent;
  status: EInsiderStatus;
  response_time?: string;
  inside_time?: string;
  cleansed_time?: string;
}

export type AmendOfferInsiderVariables = {
  id: string;
  insideTime: string;
}

// User
export type TUser = {
  role_timestamps: string;
  id: string;
  ext_id: string;
  name: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  organisational_role: EUserRole;
  gatekeeper: boolean;
  status: string;
  last_error: string;
};

export type TTask = {
  status: string;
  message: string;
  response: any;
}

export type TOrganisation = {
  system_id: string;
  system_role: string;
  name: string;
  crest_account_name: string;
  crest_participant_id: string;
  account_type: string;
  account_name: string;
  account_bank_name: string;
  account_swift_bic: string;
  account_number: string;
  account_sort_code: string;
  address1: string;
  address2: string;
  address3: string;
  address4: string;
  address_city: string;
  address_postal_code: string;
  address_country: string;
  settlement_reference: string;
}

export type TDetailedOrder = {
  id: string;
  client_order_ref?: string;
  notional_value?: Decimal;
  existing_holding?: Decimal;
  tax_wrapper?: boolean;
  validation_error?:string;
};

export type Action = {
  label: string;
  category?: string;
  target_id?: string;
  path?: string;
}

export type Notification = {
  id: string;
  user_id: string;
  title: string;
  notification_status: ENotificationStatus;
  notification_type: ENotificationType;
  message: string;
  created: string;
  action: Action;
};

export type TNewOffer = {
  name: string; 
  type: EOfferType;
  inside_information?: boolean
  inside_time?: string;
};

export type TSettlementInstruction = {
  id: string;
  timestamp: string;
  intermediary_system_id: string;
  intermediary_display_name: string;
  settlement_reference: string;
};

export type TSettlementObligation = {
  id: string;
  settlement_book_id: string;
  offer_id: string;
  created?: string;
  updated?: string;
  delivery_type?: string;
  counterparty_system_id?: string;
  counterparty_display_name?: string;
  settlement_reference?: string;
  security_isin?: string;
  security_name?: string;
  settlement_date?: string;
  actual_settlement_date?: string;
  quantity?: Decimal;
  open_quantity?: Decimal;
  cash_amount?: Decimal;
  open_cash_amount?: Decimal;
  price?: Decimal;
  currency?: string;
};

export type TSettlementBook = {
  id: string;
  offer_id: string;
  created?: string;
  settlement_date_at_creation?: string;
  isin_at_creation?: string;
  security_name_at_creation?: string;
};

// API

export type TPaginatedData<T> = {
  pagination: IPaginationResponse;
  data: T[] | null;
};

export interface IPaginationRequest {
  offset: number;
  limit: number;
}

export interface IPaginationResponse {
  count: number;
  offset: number;
  limit: number;
}

export interface TSendNotificationArgs {
  offerId: string,
  message: string,
  intermediarySystemIds: string[],
}

export interface ApiGetParams extends IPaginationRequest {
  sort?: string;
  filter?: string[];
}

export interface ApiError {
  error: string;
  message: string;
  detail?: {
    field_errors?: {
      [key: string]: string[];
    };
    message?: string;
  };
}

export type ApiMutationResponse = {
  id?: string;
  ids?: string[];
};

export type ApiItemisedUploadResponse = {
  order_book_id?: string;
  order_summary_id?: string;  
  totals?: IOrderBase;
};

export type ApiOrderUploadResponse = {
  message?: string
  field_errors?: {
    id?: string;
    summary_id?: string;
    root?: string;
  };
};

export type ApiAllocationUploadResponse = {
  field_errors?: {
    id?: string;
    offer_id?: string;  
    root?: string;
  };
  cause?: {
    message?: string;
  };
  message?: string;
};