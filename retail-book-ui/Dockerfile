# Accept an environment build argument, defaulting to staging (required for auth configuration)
ARG BUILD_ENVIRONMENT=staging

# Install dependencies only when needed
FROM node:16-alpine AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
#COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./

COPY package.json yarn.lock*  ./

RUN yarn
#RUN \
#  if [ -f yarn.lock ]; then yarn \ 
#  elif [ -f package-lock.json ]; then npm ci; \  
#  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
#  else echo "Lockfile not found." && exit 1; \
#  fi

# For some reason below parameter doesn't work; probably my issue though for momment
#  remove it
# --frozen-lockfile; \


# Rebuild the source code only when needed
FROM node:16-alpine AS builder
ARG BUILD_ENVIRONMENT
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Remove all env files.
RUN rm .env*

# Copy the right file in as local.
COPY ./.env.${BUILD_ENVIRONMENT} ./.env.local

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
# ENV NEXT_TELEMETRY_DISABLED 1

RUN yarn build

# If using npm comment out above and use below instead
# RUN npm run build

# Production image, copy all the files and run next
FROM node:16-alpine AS runner
ARG BUILD_ENVIRONMENT
WORKDIR /app

ENV NODE_ENV production
# Uncomment the following line in case you want to disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder /app/.env.local ./.env.local

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
#COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
#COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/next.config.js ./next.config.js
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules

USER nextjs

EXPOSE 3000

ENV PORT 3000

#CMD ["node", "server.js"]
CMD ["node_modules/.bin/next", "start"]
