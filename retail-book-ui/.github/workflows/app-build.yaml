name: Environment - Single app - Continuous Build Pipeline
run-name: ${{ inputs.target_environment }} - UI - Continuous Build Pipeline

on:
  workflow_call:
    inputs:
      # The environment to build the application to
      target_environment:
        required: true
        type: string
      # Where is the context directory?
      context_dir:
        required: false
        type: string
        default: .

permissions:
  id-token: write
  contents: read


jobs:
  build:
    environment: ${{ inputs.target_environment }}
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Log in to Azure using OIDC
        uses: azure/login@v1
        with:
          client-id: ${{ vars.CLIENT_ID }}
          tenant-id: ${{ vars.TENANT_ID }}
          subscription-id: ${{ vars.SUBSCRIPTION_ID }}

      - name: Login to Azure ACR
        run: az acr login -n ${{ vars.REGISTRY_NAME }}

      # Labels the image with an environment - and tags it as latest.
      - name: Build image - latest
        uses: docker/build-push-action@v3
        with:
          push: false
          context: ${{ inputs.context_dir }}
          file: ${{ inputs.context_dir }}/Dockerfile
          tags: ${{ vars.REGISTRY_NAME }}.azurecr.io/ui:latest-ci
          labels: ${{ inputs.labels }}
          build-args: |
            BUILD_ENVIRONMENT=${{ inputs.target_environment }}
