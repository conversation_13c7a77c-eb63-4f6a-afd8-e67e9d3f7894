name: Environment - Single app - Continuous Delivery Pipeline
run-name: ${{ inputs.target_environment }} - UI - Continuous Delivery Pipeline

on:
  workflow_call:
    inputs:
      # The environment to build the application to
      target_environment:
        required: true
        type: string
      # Does the build require a tag to build (true for prod, false for qa - most of the time).
      must_have_tag:
        required: false
        type: boolean
        default: true
      # What are the tags? Supply if must_have_tag is true.
      tag:
        required: false
        type: string
        default: latest
      # Image labels.
      release_name:
        required: true
        type: string
        default: dev
      # Where is the context directory?
      context_dir:
        required: false
        type: string
        default: .

permissions:
  id-token: write
  contents: read

jobs:
  build:
    environment: ${{ inputs.target_environment }}
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # Retrieve tag if the build expects one - will fail if the HEAD is not tagged.
      - name: Require tag ref
        if: ${{ inputs.must_have_tag && startsWith(github.ref, 'refs/tags/') != true }}
        uses: actions/github-script@v6
        with:
          script: |
            core.setFailed("Not a tag ref (${{ github.ref }})")

      - name: Log in to Azure using OIDC
        uses: azure/login@v1
        with:
          client-id: ${{ vars.CLIENT_ID }}
          tenant-id: ${{ vars.TENANT_ID }}
          subscription-id: ${{ vars.SUBSCRIPTION_ID }}

      - name: Login to Azure ACR
        run: az acr login -n ${{ vars.REGISTRY_NAME }}

      # Labels the image with an environment - and tags it as the tag of the release or the latest.
      - name: Build and push image
        if: ${{ !inputs.npm_build }}
        uses: docker/build-push-action@v3
        with:
          push: true
          context: ${{ inputs.context_dir }}
          file: ${{ inputs.context_dir }}/Dockerfile
          tags: ${{ vars.REGISTRY_NAME }}.azurecr.io/ui:${{ inputs.tag }}
          labels: ${{ inputs.labels }}
          build-args: |
            BUILD_ENVIRONMENT=${{ inputs.target_environment }}