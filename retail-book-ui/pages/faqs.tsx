import { Container } from "components/Layout/Container";
import { Prose } from "components/Basic/Prose/Prose";
import { NextPage } from "next";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import Image from "next/image";

const FAQs: NextPage = () => {
  return (
    <Container>
      <PageHeader
        crumbs={[{ name: "FAQs", href: "/faqs" }]}
        title="FAQs"
      />
      <Prose className="pb-lg-lg">
        <h3>Users and user roles</h3>
        <h5 className="mt-xs">How do I set up a new user or edit permissions for my organisation?</h5>
        <div className="pl-lg">
          <ul>
            <li>Go to the <Image height={25} width={25} className="inline-block" alt="account" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAATCAMAAACjpw26AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAECUExURQQBlAAAkgAAjgAAkAQAlAAAjxEOmkE/riMhoQMAkzo5q93d8OHg8ujo9XBvwgMBlAEAkwkIlNbV7oeGzAYFj0VDsPLy+TIwpwAAkSEdoerp9RkWnQAAigAAidDP62VjvQAAjQIAkwYFk83M6puZ1BUUlVpYufHx+SoopCkoo8zM6eTj9N/e8VpZuAUClSsopRMQmhUUmqWk2dva8NnZ79PT7NfW7t3c8MDA5Dg2qgcGk7e24UhIr0ZFsEdFsUdFsEREroWEy+vq9yYlojQxqevr98jH53x7yEA9reDf8g8MmQMAlLOy34qIzQMBk0RCrwMBkTc1qikmpAMBkgQBlQAAAEldW/0AAABWdFJOU/////////////////////////////////////////////////////////////////////////////////////////////////////////////////8ASk1s2QAAAAlwSFlzAAAOwwAADsMBx2+oZAAAAKtJREFUKFNtT70KwyAYzChIJyHQ4ehgiat0cnQQmoaOvv+z9PvTZOgZ9O48T7P0vzhtwAiDbdbouTkiymca7ZbSYx5YbLv7gLa2oSyNezoAv1kcZCuNz7ytTihB0+hwMUlYv1FCwx08K6wkvz6eUOROtQkopda9tpDs7WKjJf5JAPGtcS0hW4BvmDapvIugfX7iKBEmMc2OkitEWpoxTvCs3aKuWNQbSV17/wHbU3Hi7rhPkAAAAABJRU5ErkJggg=="/> icon in the top right hand corner of the page</li>
            <li>Select &ldquo;Team&rdquo;</li>
            <li>This page allows Managers to add or remove users or change users&apos; roles</li>
          </ul>
        </div>

        <h5>What are the user Roles?</h5>
        <div className="pl-lg">
          <ul>
            <li>There are two user roles:</li>
            <ul>
              <li>“Editors” can view offer information and submit orders.</li>
              <li>“Managers” can view offer information and submit orders. They can also accept Terms, add and remove users and edit user roles.</li>
            </ul>
          </ul>
        </div>

        <h5>What are &ldquo;Gatekeepers&rdquo;?</h5>
        <div className="pl-lg">
          <ul>
            <li>Managers can also be designated “Gatekeeper”.  Gatekeepers may receive wall-crossing invitations prior to a deal launching. These individuals should be authorised to accept Market Soundings on behalf of your firm. Each firm should have at least one Gatekeeper.</li>
          </ul>
        </div>

        <h5>Who can access an Offer?</h5>
        <div className="pl-lg">
          <ul>
            <li>You can check which Users have access to an offer in the Access tab.</li>
            <li>If your firm is invited to an offer prior to public launch, Gatekeepers will receive a wall-crossing invitation. Those Gatekeepers accepting the wall-crossing invitation will begin to receive inside information related to the offer.  Those Gatekeepers that decline the wall-crossing invitation will be excluded from the offer until (i) the offer it is made public; (ii) another Gatekeeper adds them to the offer; or (iii) they subsequently accept the wall-crossing invitation.  You should be aware of your obligations under the Market Abuse Regulation when handling offer information prior to the public launch of an Offer, which may be considered inside information.</li>
            <li>If your firm is invited to an Offer after it has been launched publicly, Managers will be able to view the Offer information and will be able to add other users.</li>
          </ul>
        </div>

        <h3>Orders</h3>

        <h5>How do I enter an order?</h5>
        <div className="pl-lg">
          <ul>
            <li>Go to the ‘Orders’ Tab and click ‘Add Order’.</li>
            <li>You can then enter order in 2 ways:</li>
            <ul>
              <li>Update order list – this allows you to upload a spreadsheet containing your individual orders as received from your clients. This section provides a link to download the relevant spreadsheet template which can be uploaded on the same tab in the portal once populated.</li>
              <li>Add order as summary – this allows you to submit a single aggregate order representing all orders received, you will still need to split this between Existing Shareholders and New Investors where appropriate.</li>
            </ul>
          </ul>
        </div>

        <h5>How do I add an order by spreadsheet upload?</h5>
        <div className="pl-lg">
          <ul>
            <li>Go to the “Order” tab and click “Add Order” or “Update” if you are updating an existing order</li>
            <li>In the pop-up, select “Update order list”</li>
            <li>Download the order spreadsheet template</li>
            <li>In the template, data fields are:</li>
            <ul>
              <li>your Client Order Reference - this should not include any client personal data but should allow you to individually identify the order and associate it with a client</li>
              <li>the Existing Holding of the applicant (by number of securities) - this will allow us to allocate to your client in accordance with the company’s allocation policy where pre-emption is being observed. If the order is on behalf of a new investor, this cell can be left blank or marked as “zero”</li>
              <li>the Order Value – this should be the application value in the specified deal currency.  All orders are by value rather than quantity</li>
              <li>Tax wrapper – this optional field will allow us to provide enriched data to our client. If the order is placed on behalf of an ISA, Junior ISA or SIPP account this field should be marked “Yes”.</li>
            </ul>
            <li>Once the template has been populated with one row per underlying investor order, the spreadsheet can be uploaded to the same pop-up within the portal, by dragging the file into the box indicated or by browsing for the file.  The order data will be automatically processed.</li>
            <li>You can then review a summary of the order before pressing “Send Orders”</li>
            <li>If you are updating an order, your spreadsheet should include all rows already submitted and the new rows reflecting the new orders.</li>
          </ul>
        </div>

        <h5>How do I add a Summary Order?</h5>
        <div className="pl-lg">
          <ul>
            <li>Go to the “Order” tab and click “Add Order” or “Update” if you are updating an existing order</li>
            <li>In the pop-up, select “Add order as summary”</li>
            <li>Update the relevant fields with your order details and click “Confirm Orders”. Orders should be submitted by value of order in the deal currency</li>
            <li>Each time you submit an order, it needs to include all orders including those already submitted.</li>
            <ul>
              <li>For example, in Order 1 your total amount was £150,000 made up of 3 applications, in Order 2 you want to submit 3 new orders of £3,000, £5,000 and £1,000. Your new total amount should be: £159,000 with 6 total applications. Do not delete Order 1, this will be replaced on when you submit your new order.</li>
            </ul>
            <li>It is important to split orders between Existing Shareholders and New Investors where appropriate. These orders may be treated differently under the Company’s allocation policy.</li>
            <li>For Existing Shareholders, you must submit an Existing Shareholding by number of shares held.</li>
          </ul>
        </div>

        <h5>Can I update my order?</h5>
        <div className="pl-lg">
          <ul>
            <li>You can update your order at any time, while applications are open.  Your order will become binding upon the Close of the Offer.</li>
          </ul>
        </div>

        <h5>Why is my order “Pending”?</h5>
        <div className="pl-lg">
          <ul>
            <li>An order may remain pending for two reasons:</li>
            <ul>
              <li>You have not yet accepted the Terms of the Offer - to do this, go to the ‘Terms’ tab review the Retail Offer Notice, select whether you accept the terms of the Retail Offer Notice in the event you have entered into a Master Intermediary Agreement (or whether you have entered into a standalone Intermediary Agreement for the particular Offer) and whether you wish to be paid the intermediary’s commission and click ”Accept Terms”, your order will then be “Accepted”</li>
              <li>Your order was placed after the offer closed. RetailBook will accept your order on behalf of the Company where possible but this will be decided on a case by case basis.</li>
            </ul>
          </ul>
        </div>

        <h3>Allocations</h3>

        <h5>How will I receive my Allocation following an Offer?</h5>
        <div className="pl-lg">
          <ul>
            <li>Once an Offer has been allocated, you will be able to view your allocations in the Allocations tab, this can be viewed in summary form in the platform.  If you have submitted your order by spreadsheet upload, you will be able to download detailed allocations on a line-by-line basis.</li>
          </ul>
        </div>

      </Prose>
      <div className="pb-xl"></div>
    </Container>
  );
};

export default FAQs;
