import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { getUserNotificationsQuery, updateUserNotificationStatusMutation } from "utils/queries";
// Components
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import { SortColumn } from "react-data-grid";
import { useState } from "react";
import { Column, StyledReactDataGrid } from "components/StyledReactDataGrid/StyledReactDataGrid";

import {Notification, ENotificationStatus, ENotificationCategory} from "types";
import { Filter, FilterOption } from "components/Bespoke/Filter/Filter";
import { Badge } from "components/Basic/Badge/Badge";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { Button } from "components/Basic/Button/Button";
import { Textarea } from "components/Basic/Textarea/Textarea";
import { format, parseISO } from "date-fns";
import {toActionPath} from "components/Bespoke/Notifications/Notifications";


// TODO => Translate the data.... or fix the formatting.

const columns: Column<Notification>[] = [
    {
        key: "created",
        name: "Date",
        width: '15%',
        sortable: true,
        resizable: true,
        formatter: ({ row }) => {
            return (format(parseISO(row.created), "dd/MM/yyyy HH:mm"))
        },
    },
    {
        key: "notification_status",
        name: "",
        sortable: false,
        resizable: false,
        width: '10%',
        formatter: ({ row }) => {
            return (row.notification_status === ENotificationStatus.UNREAD ? <Badge theme="pink">{row.notification_status}</Badge> : "")
        },
    },
    {
        key: "title",
        name: "Title",
        sortable: true,
        resizable: true,
        width: '70%',
    },
];

const allStatusFilterOptions = [
    {
        label: ENotificationStatus.READ,
        value: `status=${ENotificationStatus.READ}:eq`,
    },
    {
        label: ENotificationStatus.UNREAD,
        value: `status=${ENotificationStatus.UNREAD}:eq`,
    }
]

const pageSize = 10;
const defaultStatusOptions = [allStatusFilterOptions[1]]


const Notifications: NextPage = () => {
    const { data: session } = useSession();
    const qc = useQueryClient();

    const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [statusFilter, setStatusFilter] = useState<FilterOption[]>(defaultStatusOptions);
    const [isViewDialogOpen, setViewDialogOpen] = useState(false);
    const [selectedNotification, setSelectedNotification] = useState<Notification>();

    const params = useMemo(() => {
        const filter = statusFilter.map(x => x.value);
        const sort =
            sortColumns.length > 0
                ? `${sortColumns[0].columnKey
                }=${sortColumns[0].direction.toLocaleLowerCase()}`
                : undefined;

        return {
            limit: pageSize,
            offset: (currentPage - 1) * pageSize,
            sort,
            filter
        };
    }, [currentPage, statusFilter, sortColumns]);

    const { data: notifications } = useQuery(
        getUserNotificationsQuery(params, session?.user?.ext_id, session?.accessToken)
    );


    const updateStatusMutationOption = useMemo(
        () => updateUserNotificationStatusMutation(qc, session?.user?.ext_id, session?.accessToken),
        [qc, session?.user?.ext_id, session?.accessToken]
    );

    const updateStatusMutator = useMutation({
        ...updateStatusMutationOption
    });

    const setNotificationStatus = (id: string, status: string) => {
        const newState = status.toLocaleLowerCase()

        updateStatusMutator.mutate({ id: id, status: newState, userId: session?.user?.ext_id ?? "" })
        
    }

    const totalItems = useMemo(
        () => notifications?.pagination.count ?? 0,
        [notifications?.pagination.count]
    );

    const router = useRouter()

    const routeTo = (path: string | undefined) => {
        if (path) {
            router.push(path)
        }
    }

    const actionPath = useMemo(
        () => toActionPath(selectedNotification),
        [selectedNotification]
    )

    const actionLabel = useMemo(
        () => {
            if (selectedNotification?.action?.category) {
                switch (selectedNotification.action.category) {
                    case ENotificationCategory.OFFER:
                        return "View offer";
                    case ENotificationCategory.OFFER_ORDER:
                        return "View order";
                    case ENotificationCategory.OFFER_DOCUMENT:
                        return "View document";
                    case ENotificationCategory.OFFER_ALLOCATION:
                        return "View allocation";
                    case ENotificationCategory.WALL_CROSSING:
                        return "View wall-crossing";
                }
            } else if (selectedNotification?.action?.label) {
                // TODO deprecated, back-compatible
                if (selectedNotification.message && selectedNotification.message.toLowerCase().includes("wall-crossing"))
                    return "View wall-crossing";
                else if (selectedNotification.action.label.toLowerCase() != "view")
                    return "View " + selectedNotification.action.label.toLowerCase();
                else
                    return "View"
            } else {
                // default
                return "View"
            }
        },
        [selectedNotification]
    )

    return (
        <>
            <Container>
                <PageHeader
                    crumbs={[{ name: "Notifications", href: "/notifications" }]}
                    title="Notifications"
                />
            </Container>

            <Container>
                <Container>
                    <div className="flex flex-wrap justify-end mb-sm gap-sm">
                        <Filter
                            label="Status"
                            labelPlural="Statuses"
                            value={statusFilter}
                            onChange={setStatusFilter}
                            options={allStatusFilterOptions}
                            multiple
                        />
                    </div>
                </Container>
                <StyledReactDataGrid
                    rows={notifications?.data ?? []}
                    columns={columns}
                    contextMenuItems={[

                        {
                            label: "View",
                            onClick: (_e, row) => {
                                setSelectedNotification(row)
                                setViewDialogOpen(true)
                            }
                        },
                        {
                            label: "Mark as read",
                            onClick: (_e, row) => {
                                setNotificationStatus(row.id, ENotificationStatus.READ);
                            },
                        },
                        {
                            label: "Mark as unread",
                            onClick: (e, row) => {
                                e.preventDefault();
                                setNotificationStatus(row.id, ENotificationStatus.UNREAD);
                            },
                        },
                        {
                            label: "Delete",
                            onClick: (e, row) => {
                                e.preventDefault();
                                setNotificationStatus(row.id, "delete");
                            },
                        },
                    ]}
                    sortColumns={sortColumns}
                    onSortColumnsChange={setSortColumns}
                    paginationProps={{
                        onClick: (page) => setCurrentPage(page),
                        currentPage: currentPage,
                        totalItems: totalItems,
                        pageSize: pageSize,
                    }}
                />

            </Container>

            {/* View dialog */}
            <StyledDialog
                fullWidth
                open={isViewDialogOpen}
                onClose={() => {
                    setViewDialogOpen(false)

                    if (selectedNotification?.notification_status == ENotificationStatus.UNREAD) {
                        setNotificationStatus(selectedNotification.id, ENotificationStatus.READ)
                    }
                    setSelectedNotification(undefined)
                }}
                unmount={false}
                title={"Notification"}
                footer={
                    <div className="flex gap-xs">
                        <Button type="button" onClick={() => routeTo(actionPath)}>{actionLabel}</Button>
                    </div>
                }
            >
                <Container className="w-3/4 justify-center">
                    <Textarea label={"Message"} rows={2} disabled>{selectedNotification?.message}</Textarea>
                </Container>
            </StyledDialog>
        </>
    );
};

export default Notifications;
