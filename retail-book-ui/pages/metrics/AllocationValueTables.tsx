import React, { useMemo } from "react";
import { Container } from "components/Layout/Container";
import { StyledReactDataGrid, Column } from "components/StyledReactDataGrid/StyledReactDataGrid";
import { TOfferMetrics } from "types";
import Decimal from "decimal.js";
import { formatNumber } from "helpers";

interface CcyRow {
  id: string;
  ccy: string;
  allocation_value: Decimal;
};

const ccyColumns: Column<CcyRow>[] = [
  {
    key: "ccy",
    name: "Currency",
    sortable: true,
    resizable: true,
    comparator: (a, b) => a.ccy.localeCompare(b.ccy),
  },
  {
    key: "allocation_value",
    name: "Allocation Value",
    sortable: true,
    resizable: true,
    formatter: ({row}) => {
      return (<span>{formatNumber(row.allocation_value)}</span>)
    },
    comparator: (a, b) => a.allocation_value.toNumber() - b.allocation_value.toNumber(),
  }
];

interface IntCcyRow {
  id: string;
  intermediary: string;
  ccy: string;
  allocation_value: Decimal; 
  market_value: Decimal;  
};

const intCcyColumns: Column<IntCcyRow>[] = [
  {
    key: "intermediary",
    name: "Intermediary",
    sortable: true,
    resizable: true,
    comparator: (a, b) => a.intermediary.localeCompare(b.intermediary),
  },
  {
    key: "ccy",
    name: "Currency",
    sortable: true,
    resizable: true,
    comparator: (a, b) => a.ccy.localeCompare(b.ccy),
  },
  {
    key: "allocation_value",
    name: "Allocation Value",
    sortable: true,
    resizable: true,
    formatter: ({row}) => {
      return (<span>{formatNumber(row.allocation_value)}</span>)
    },
    comparator: (a, b) => a.allocation_value.toNumber() - b.allocation_value.toNumber(),
  },
  {
    key: "market_value",
    name: "Market Value",
    sortable: true,
    resizable: true,
    formatter: ({row}) => {
      return (<span>{formatNumber(row.market_value)}</span>)
    },
    comparator: (a, b) => a.market_value.toNumber() - b.market_value.toNumber(),
  },
  {
    key: "market_value_percent",
    name: "Market Share",
    sortable: true,
    resizable: true,
    formatter: ({row}) => {
      return (<span>{formatNumber(row.allocation_value.div(row.market_value).times(new Decimal(100)).round())}%</span>)
    },
    comparator: (a, b) => a.allocation_value.toNumber() - b.allocation_value.toNumber(),
  },
];

interface AllocationValueTablesProps {
  dataNoFilter: TOfferMetrics | undefined,
  data: TOfferMetrics | undefined,
}

export const AllocationValueTables = (props: AllocationValueTablesProps) => {

  const currencyTotalsMap = useMemo(() => { return getCurrencyTotals(props.dataNoFilter) }, [props.dataNoFilter]);

  const ccyRows: CcyRow[] = useMemo(() => { return props.data?.totals?.currency.map((metric) => {
    return {
      id: metric.currency,
      ccy: metric.currency,
      allocation_value: metric.allocation_value ?? new Decimal(0),
    }
  }) ?? [] }, [props.data])

  const intCcyRows: IntCcyRow[] = useMemo(() => { return props.data?.totals?.intermediary_currency.map((metric) => {
    return {
      id: metric.system_id+metric.currency,
      intermediary: metric.name,
      ccy: metric.currency,
      allocation_value: metric.allocation_value ?? new Decimal(0),
      market_value: currencyTotalsMap.get(metric.currency) ?? new Decimal(0),
    }
  }) ?? [] }, [props.data, currencyTotalsMap])

  return (
    <Container as="div" className="flex flex-wrap justify-center gap-md">
      <StyledReactDataGrid columns={ccyColumns} rows={ccyRows} />
      <StyledReactDataGrid columns={intCcyColumns} rows={intCcyRows}  />
    </Container>
  );
};

export default AllocationValueTables;

//This should be a precomputated map really but there aren't that many currencies in the world let alone in RB...
const getCurrencyTotals = (dataNoFilter: TOfferMetrics | undefined): Map<string, Decimal> => {
  const returnMap = new Map<string, Decimal>();
  if (!dataNoFilter || !dataNoFilter.totals) {
    return returnMap;
  }
  for (let i = 0; i < dataNoFilter.totals.currency.length; i++) {
      returnMap.set(dataNoFilter.totals.currency[i].currency, dataNoFilter.totals.currency[i].allocation_value ?? new Decimal(0));
  }
  return returnMap;
}