import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import { NextPage } from "next";
import { EOfferType } from "types";
import { useSession } from "next-auth/react";
import { getMonthlyOfferMetrics } from "utils/queries";
import { useQuery } from "@tanstack/react-query";
import React, { useState, useMemo } from "react";
import AllocationValueChart from "./AllocationValueChart";
import AllocationValueTables from "./AllocationValueTables";
import { Tab } from "@headlessui/react";
import { Input } from "components/Basic/Input/Input";
import { Filter, FilterOption } from "components/Bespoke/Filter/Filter";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { tOfferMetricsToIntermediarySet, tOfferMetricsToCurrencySet, getColourMap } from "components/Charts/Utils"

const getDefaultFromDateString = (): string => {
  const today = new Date();
  today.setMonth(today.getMonth() - 13);
  const todaySplit = today.toISOString().split("T");
  return todaySplit[0];
};

const getDefaultToDateString = (): string => {
  const today = new Date();
  today.setDate(today.getDate() + 1);
  const todaySplit = today.toISOString().split("T");
  return todaySplit[0];
};

const Metrics: NextPage = () => {
  const { data: session } = useSession();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [typeFilter, setTypeFilter] = useState<FilterOption<string>[]>([]);
  const [currencyFilter, setCurrencyFilter] = useState<FilterOption<string>[]>([]);
  const [intermediaryFilter, setIntermediaryFilter] = useState<FilterOption<string>[]>([]);
  const [fromDate, setFromDate] = useState<string>(getDefaultFromDateString);
  const [toDate, setToDate] = useState<string>(getDefaultToDateString);

  const fromDateFilter = "fromDate="+fromDate+":gte"
  const toDateFilter = "toDate="+toDate+"T23:59:59:lte"

  // Do a vanilla query to get all the possible values
  const { data: monthlyNoFilter } = useQuery({
    ...getMonthlyOfferMetrics({limit: 0, offset: 0, filter: [fromDateFilter, toDateFilter]}, session?.accessToken),
  });

  const possibleCurrencies = useMemo(() => { return tOfferMetricsToCurrencySet(monthlyNoFilter); }, [monthlyNoFilter]);
  const currencyFilterOptions: FilterOption<string>[] = useMemo(() => { return possibleCurrencies.map((ccy) => {
    return {label: ccy, value: "currency="+ccy+":eq"}
  })}, [possibleCurrencies])
  const possibleIntermediaries = useMemo(() => { return tOfferMetricsToIntermediarySet(monthlyNoFilter); }, [monthlyNoFilter]);
  const intermediaryFilterOptions: FilterOption<string>[] = useMemo(() => { return possibleIntermediaries.map((int) => {
    return {label: int.name, value: "intermediary="+int.system_id+":eq"}
  })}, [possibleIntermediaries])
  const intColorMap: Map<string, string> = useMemo(() => { return getColourMap(possibleIntermediaries.map(int => int.name)) }, [possibleIntermediaries]);

  // Now get the filtered data
  const params = {
    limit: 0, offset: 0,
    filter: [...currencyFilter.map(x => x.value), ...intermediaryFilter.map(x => x.value), ...typeFilter.map(x => x.value), fromDateFilter, toDateFilter],
  }
  const { data: monthly } = useQuery({
    ...getMonthlyOfferMetrics(params, session?.accessToken),
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const offerTypeOptions = useMemo(() => { return Object.entries(EOfferType).map(([_, v]) => ({label: v, value: `offer_type=${v}:eq`})); }, []);
  
  return (
    <>
      <Container>
        <PageHeader title="Metrics"/>
      </Container>
      <Tab.Group onChange={setSelectedIndex} selectedIndex={selectedIndex}>
        <Container>
          <Tab.List className="tab-list">
            <StyledTab key={0}>Value vs Offers</StyledTab>
          </Tab.List>
        </Container>
        <Tab.Panels className="tab-panel tab-panel--bordered">
          
          <Container className="flex flex-wrap justify-start">
            <Container className="flex flex-wrap justify-start">
              <Input
                  label="From Date"
                  type="date"
                  onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
                  onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
                  max="9999-12-31T23:59"
                  value={fromDate}
                  onChange={x => setFromDate(x.target.value)}
                />
                <Input
                  className="pl-sm"
                  label="To Date"
                  type="date"
                  onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
                  onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
                  max="9999-12-31T23:59"
                  value={toDate}
                  onChange={x => setToDate(x.target.value)}
                />
            </Container>
            <Container className="flex flex-wrap justify-end">
              <Filter
                className="pr-sm"
                label="Type"
                labelPlural="Types"
                value={typeFilter}
                onChange={setTypeFilter}
                options={offerTypeOptions}
                multiple
              />
              <Filter
                className="pr-sm"
                label="Currency"
                labelPlural="Currencies"
                value={currencyFilter}
                onChange={setCurrencyFilter}
                options={currencyFilterOptions}
                multiple
              />
              <Filter
                label="Intermediary"
                labelPlural="Intermediaries"
                value={intermediaryFilter}
                onChange={setIntermediaryFilter}
                options={intermediaryFilterOptions}
                multiple
              />
            </Container>
          </Container>
          <AllocationValueChart dataNoFilter={monthlyNoFilter} data={monthly} intColorMap={intColorMap} />
          <AllocationValueTables dataNoFilter={monthlyNoFilter} data={monthly} />
        </Tab.Panels>
      </Tab.Group>
    </>
  );
};

export default Metrics;
