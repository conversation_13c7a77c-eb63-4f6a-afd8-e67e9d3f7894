import React, { useMemo } from "react";
import { Prose } from "components/Basic/Prose/Prose";
import { Line, Bar } from 'recharts';
import { RbBarChart } from "components/Charts/RbBarChart";
import { getMonthYearFormat, tOfferMetricsToRechartsFormatAllocationValue, tOfferMetricsToIntermediaryCurrencySet } from "components/Charts/Utils"
import { TOfferMetrics } from "types";
import Decimal from "decimal.js";

interface AllocationValueChartProps {
  dataNoFilter: TOfferMetrics | undefined,
  data: TOfferMetrics | undefined,
  intColorMap: Map<string, string>,
}

export const AllocationValueChart = (props: AllocationValueChartProps) => {
  const monthlyMetrics = useMemo(() => { return tOfferMetricsToRechartsFormatAllocationValue(props.data, getMonthYearFormat); }, [props.data]);
  const monthlyIntCcys = useMemo(() => { return tOfferMetricsToIntermediaryCurrencySet(props.data); }, [props.data]);
  const monthlyBars = useMemo(() => { return monthlyIntCcys.map( (intCcy) => {
    return <Bar key={intCcy.intermediary + " " + intCcy.currency} dataKey={intCcy.intermediary + " " + intCcy.currency} name={intCcy.intermediary + " " + intCcy.currency} fill={props.intColorMap.get(intCcy.intermediary)} stackId={intCcy.currency} />
  })}, [props.intColorMap, monthlyIntCcys]);
  const monthlyOfferLine = (
    <Line key="offerline" yAxisId="right" type="monotone" dataKey="Offers" stroke="#ff7300" />
  )

  const subTitle = (
    <Prose className="pt-sm">
      {props.data?.totals?.offers && (<h4>{props.data?.totals?.offers + " " + pluralise(props.data?.totals?.offers, "Offer")}</h4>)}
    </Prose>
  )

  return (
    <RbBarChart subTitle={subTitle} data={monthlyMetrics} dataKey="date" bars={monthlyBars.concat([monthlyOfferLine])} rightYAxisStroke="#ff7300" barCategoryGap="70%" xAxisHeight={100} />
  );
};

const pluralise = (d: Decimal, word: string): string => {
  if (d.absoluteValue().equals(new Decimal(1))) {
    return word;
  }
  return word + "s";
}

export default AllocationValueChart;
