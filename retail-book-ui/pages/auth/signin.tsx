import { signIn, useSession } from "next-auth/react";
import { useEffect } from "react";
import { useRouter } from "next/router";

export default function SignIn() {
  const router = useRouter();
  const { status } = useSession();

  useEffect(() => {
    if (status === "unauthenticated") {
      console.info("No JWT");
      console.info(status);
      signIn("azure-ad-b2c");
    } else if (status === "authenticated") {
      router.push("/");
    }
  }, [status, router]);

  return <div></div>;
}
