import { Tab } from "@headlessui/react";
import { useQuery } from "@tanstack/react-query";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { getOffersQuery, getUserQuery } from "utils/queries";

// components
import { Button } from "components/Basic/Button/Button";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { OfferCard } from "components/Bespoke/OfferCard/OfferCard";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";

// types
import { Pagination } from "components/Basic/Pagination/Pagination";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Filter, FilterOption } from "components/Bespoke/Filter/Filter";
import { EOfferStatus, EOfferType, EUserRole } from "types";

const sortOptions = [
  { label: "Open (Ascending)", value: "timeline.open_date=asc,created_date=asc" },
  { label: "Open (Descending)", value: "timeline.open_date=desc,created_date=asc" },
  { label: "Close (Ascending)", value: "timeline.close_date=asc,created_date=asc" },
  { label: "Close (Descending)", value: "timeline.close_date=desc,created_date=asc" },
];

const allTypeFilterOptions = [
  {
    label: EOfferType.FOLLOW_ON,
    value: `offer_type=${EOfferType.FOLLOW_ON}:eq`,
  },
  {
    label: EOfferType.IPO,
    value: `offer_type=${EOfferType.IPO}:eq`,
  },
  {
    label: EOfferType.RETAIL_BOND,
    value: `offer_type=${EOfferType.RETAIL_BOND}:eq`,
  },
];

const allStatusFilterOptions = [
  {
    label: EOfferStatus.PRE_LAUNCH,
    value: `status=${EOfferStatus.PRE_LAUNCH}:eq`,
  },
  {
    label: EOfferStatus.BUILDING,
    value: `status=${EOfferStatus.BUILDING}:eq`,
  },
  {
    label: EOfferStatus.APPLICATIONS_OPEN,
    value: `status=${EOfferStatus.APPLICATIONS_OPEN}:eq`,
  },
  {
    label: EOfferStatus.APPLICATIONS_CLOSED,
    value: `status=${EOfferStatus.APPLICATIONS_CLOSED}:eq`,
  },
  {
    label: EOfferStatus.ALLOCATING,
    value: `status=${EOfferStatus.ALLOCATING}:eq`
  },
  {
    label: EOfferStatus.INSTRUCTIONS_SENT,
    value: `status=${EOfferStatus.INSTRUCTIONS_SENT}:eq`,
  },
  {
    label: EOfferStatus.ALLOCATED,
    value: `status=${EOfferStatus.ALLOCATED}:eq`,
  },
  {
    label: EOfferStatus.SETTLED,
    value: `status=${EOfferStatus.SETTLED}:eq`,
  },
  {
    label: EOfferStatus.BUILDING_ON_HOLD,
    value: `status=${EOfferStatus.BUILDING_ON_HOLD}:eq`,
  },
  {
    label: EOfferStatus.PRE_LAUNCH_ON_HOLD,
    value: `status=${EOfferStatus.PRE_LAUNCH_ON_HOLD}:eq`
  },
  {
    label: EOfferStatus.APPLICATIONS_OPEN_ON_HOLD,
    value: `status=${EOfferStatus.APPLICATIONS_OPEN_ON_HOLD}:eq`
  }
];

const activeStatusFilterOptions = [
  {
    label: EOfferStatus.PRE_LAUNCH,
    value: `status=${EOfferStatus.PRE_LAUNCH}:eq`,
  },
  {
    label: EOfferStatus.APPLICATIONS_OPEN,
    value: `status=${EOfferStatus.APPLICATIONS_OPEN}:eq`,
  },
  {
    label: EOfferStatus.APPLICATIONS_CLOSED,
    value: `status=${EOfferStatus.APPLICATIONS_CLOSED}:eq`,
  },
    {
    label: EOfferStatus.ALLOCATING,
    value: `status=${EOfferStatus.ALLOCATING}:eq`
  },
  {
    label: EOfferStatus.INSTRUCTIONS_SENT,
    value: `status=${EOfferStatus.INSTRUCTIONS_SENT}:eq`,
  },
  {
    label: EOfferStatus.ALLOCATED,
    value: `status=${EOfferStatus.ALLOCATED}:eq`,
  },
];

const closedStatusFilterOptions = [
  {
    label: EOfferStatus.APPLICATIONS_CLOSED,
    value: `status=${EOfferStatus.APPLICATIONS_CLOSED}:eq`,
  },
  {
    label: EOfferStatus.ALLOCATING,
    value: `status=${EOfferStatus.ALLOCATING}:eq`
  },
  {
    label: EOfferStatus.INSTRUCTIONS_SENT,
    value: `status=${EOfferStatus.INSTRUCTIONS_SENT}:eq`,
  },
  {
    label: EOfferStatus.ALLOCATED,
    value: `status=${EOfferStatus.ALLOCATED}:eq`,
  },
  {
    label: EOfferStatus.SETTLED,
    value: `status=${EOfferStatus.SETTLED}:eq`,
  },
];

const pageSize = 12;

const Offers: NextPage = () => {
  const { data: session } = useSession();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const statusFilterOptions = useMemo(() => {
    if (selectedIndex === 0) return activeStatusFilterOptions;
    if (selectedIndex === 1) return closedStatusFilterOptions;
    return allStatusFilterOptions;
  }, [selectedIndex]);
  const [currentPage, setCurrentPage] = useState(1);
  const [typeFilter, setTypeFilter] = useState<FilterOption[]>([]);
  const [statusFilter, setStatusFilter] = useState<FilterOption[]>([]);
  const [sortOrder, setSortOrder] = useState<FilterOption>(sortOptions[1]);

  useEffect(() => {
    if (selectedIndex === 2) return;
    setStatusFilter([]);
  }, [selectedIndex]);

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedIndex]);

  const params = useMemo(() => {
    let statuses = statusFilter.map((opt) => opt.value);

    if (statuses.length === 0) {
      if (selectedIndex === 0)
        statuses = activeStatusFilterOptions.map((opt) => opt.value);
      if (selectedIndex === 1)
        statuses = closedStatusFilterOptions.map((opt) => opt.value);
    }

    return {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      filter: [...typeFilter.map((opt) => opt.value), ...statuses],
      sort: sortOrder.value,
    };
  }, [selectedIndex, currentPage, typeFilter, statusFilter, sortOrder]);

  const { data: offers, isLoading } = useQuery({
    ...getOffersQuery(params, session?.accessToken),
  });

  const totalItems = useMemo(
    () => offers?.pagination.count ?? 0,
    [offers?.pagination.count]
  );

  const { data: user } = useQuery(
    getUserQuery(session?.user.ext_id, session?.accessToken)
  );

  return (
    <>
      <Container>
        <PageHeader
          title={`${
            user?.organisational_role === EUserRole.INTERMEDIARY
              ? "My Offers"
              : "Offers"
          }`}
        >
          {user?.organisational_role === EUserRole.ISSUER && (
            <Button size="lg" type="button" theme="special">
              <Link href="/offers/create">Add Offer</Link>
            </Button>
          )}
        </PageHeader>
      </Container>
      <Tab.Group onChange={setSelectedIndex} selectedIndex={selectedIndex}>
        <Container>
          <Tab.List className="tab-list">
            <StyledTab>Active</StyledTab>
            <StyledTab>Closed</StyledTab>
            <StyledTab>All</StyledTab>
          </Tab.List>
        </Container>
        <Tab.Panels className="tab-panel tab-panel--bordered bg-book">
          <Container>
            <div className="flex flex-wrap justify-end mb-sm gap-sm">
              <Filter
                label="Type"
                labelPlural="Types"
                value={typeFilter}
                onChange={setTypeFilter}
                options={allTypeFilterOptions}
                multiple
              />
              <Filter
                label="Status"
                labelPlural="Statuses"
                value={statusFilter}
                onChange={setStatusFilter}
                options={statusFilterOptions}
                multiple
              />
              <Filter
                label="Sort by"
                value={sortOrder}
                onChange={setSortOrder}
                options={sortOptions}
              />
            </div>
            {isLoading ? (
              <div className="flex items-center justify-center mt-sm">
                <Spinner size="lg" message="Loading offers..." />
              </div>
            ) : offers?.data && offers.data.length > 0 ? (
              <>
                <div className="grid grid-cols-1 gap-md md:grid-cols-2 lg:grid-cols-3 mb-md">
                  {offers.data.map((offer) => (
                    <OfferCard key={offer.id} offer={offer} />
                  ))}
                </div>
                <Pagination
                  onClick={(page) => setCurrentPage(page)}
                  currentPage={currentPage}
                  totalItems={totalItems}
                  pageSize={pageSize}
                />
              </>
            ) : (
              <EmptyAction message="No offers to show" />
            )}
          </Container>
        </Tab.Panels>
      </Tab.Group>
    </>
  );
};

export default Offers;
