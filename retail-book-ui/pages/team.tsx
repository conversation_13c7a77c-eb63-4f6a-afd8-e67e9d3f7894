import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { ApiError, ApiMutationResponse, TUser, EUserStatus, EUserRole } from "types";
import {
  createUserMutation,
  deleteUserMutation,
  getTeamMembersQuery,
  getUserQuery,
  resendInviteMutation,
  updateUserMutation,
} from "utils/queries";
import { ajvResolver } from "@hookform/resolvers/ajv";
import { Alert } from "components/Basic/Alert/Alert";
import { Badge } from "components/Basic/Badge/Badge";
import { Button } from "components/Basic/Button/Button";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import { Input } from "components/Basic/Input/Input";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import {
  Column,
  StyledReactDataGrid,
} from "components/StyledReactDataGrid/StyledReactDataGrid";
import { useFieldErrors } from "hooks/useFieldErrors";
import { Filter, FilterOption } from "components/Bespoke/Filter/Filter";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo, useState } from "react";
import { UserPlus } from "react-feather";
import { useForm } from "react-hook-form";

import { AxiosError } from "axios";
import { SortColumn } from "react-data-grid";
import { useDebouncedBoolean } from "hooks/useDebouncedBoolean";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";

const pageSize = 10;

const userStatusThemeSelector = (
  status: string
): "success" | "warning" | "failure" => {
  switch (status) {
    case "Pending": {
      return "warning";
    }
    case "Active": {
      return "success";
    }
  }
  return "failure";
};

const columns: Column<TUser>[] = [
  {
    key: "name",
    name: "Name",
    resizable: true,
    sortable: true
  },
  {
    key: "email",
    name: "Email",
    resizable: true,
    sortable: true
  },
  {
    key: "role",
    name: "Role",
    resizable: true,
    sortable: true
  },
  {
    key: "gatekeeper",
    name: "Gatekeeper",
    resizable: true,
    sortable: true,
    formatter: ({ row }) => {
      if (row.gatekeeper) {
        return <Badge theme="success">Yes</Badge>
      }
      return <Badge theme="failure">No</Badge>
    }
  },
  {
    key: "status",
    name: "Status",
    formatter: ({ row }) => (
      <Badge theme={userStatusThemeSelector(row.status)}>{row.status}</Badge>
    ),
    resizable: true,
    sortable: true
  },
];

const schema = {
  type: "object",
  properties: {
    name: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "name field is required" },
    },
    email: {
      type: "string",
      minLength: 1,
      errorMessage: {
        minLength: "email field is required",
      },
    },
  },
  required: ["name", "email"],
};

interface TeamMemberFormData {
  name: string;
  email: string;
  role: string;
  gatekeeper: boolean;
}

const allStatusFilterOptions = [
  {
    label: EUserStatus.ACTIVE,
    value: `status=${EUserStatus.ACTIVE}:eq`,
  },
  {
    label: EUserStatus.INACTIVE,
    value: `status=${EUserStatus.INACTIVE}:eq`,
  },
  {
    label: EUserStatus.PENDING,
    value: `status=${EUserStatus.PENDING}:eq`,
  },
  {
    label: EUserStatus.CREATED,
    value: `status=${EUserStatus.CREATED}:eq`,
  },
  {
    label: EUserStatus.INVITED,
    value: `status=${EUserStatus.INVITED}:eq`,
  },
  {
    label: EUserStatus.FAILURE,
    value: `status=${EUserStatus.FAILURE}:eq`,
  }
]

const defaultStatusOptions = [allStatusFilterOptions[0], allStatusFilterOptions[2], allStatusFilterOptions[3], allStatusFilterOptions[4], allStatusFilterOptions[5]]

const Team: NextPage = () => {
  const { data: session } = useSession();
  const qc = useQueryClient();
  const [isEditTeamMemberOpen, setEditTeamMemberOpen] = useState(false);
  const [isRemoveTeamMemberOpen, setRemoveTeamMemberOpen] = useState(false);
  const [selectedTeamMember, setSelectedTeamMember] = useState<TUser>();
  const [deleteValue, setDeleteValue] = useState<string>();

  const { data: user } = useQuery(
    getUserQuery(session?.user?.ext_id, session?.accessToken)
  );

  const createUserMutationOptions = useMemo(
    () => createUserMutation(qc, session?.accessToken),
    [qc, session?.accessToken]
  );

  const closeForm = () => {
    setEditTeamMemberOpen(false)
    setRemoveTeamMemberOpen(false)
  }

  const clearData = () => {
    setSelectedTeamMember(undefined)
    setDeleteValue(undefined)
    updateUserMutator.reset()
    createUserMutator.reset()
  }

  const createUserMutator = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    Partial<TUser>
  >({
    ...createUserMutationOptions,
    onSuccess: () => {
      createUserMutationOptions.onSuccess();
      closeForm();
    },
    onError: () => { /* This function is intentionally left blank */ }
  });

  const updateUserMutationOptions = useMemo(
    () =>
      updateUserMutation(
        selectedTeamMember?.id ?? "",
        qc,
        session?.accessToken
      ),
    [selectedTeamMember?.id, qc, session?.accessToken]
  );

  const updateUserMutator = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    Partial<TUser>
  >({
    ...updateUserMutationOptions,
    onSuccess: () => {
      updateUserMutationOptions.onSuccess();
      closeForm();
    },
  });

  const resendInviteMutator = useMutation(
    resendInviteMutation(session?.accessToken)
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);
  const [statusFilter, setStatusFilter] = useState<FilterOption[]>(defaultStatusOptions);


  const params = useMemo(() => {
    const filter = statusFilter.map(x => x.value);
    const sort =
      sortColumns.length > 0
        ? `${sortColumns[0].columnKey
        }=${sortColumns[0].direction.toLocaleLowerCase()}`
        : undefined;

    return {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      filter,
      sort,
    };
  }, [currentPage, statusFilter, sortColumns]);

  const { data: teamMembers, isLoading: isLoadingTeamMembers, isError: isLoadingTeamMembersError } = useQuery({
    ...useMemo(
      () => getTeamMembersQuery(params, session?.accessToken),
      [session?.accessToken, params]
    ),
  });

  const totalItems = useMemo(
    () => teamMembers?.pagination.count ?? 0,
    [teamMembers?.pagination.count]
  );

  const deleteUserMutationOptions = useMemo(
    () => deleteUserMutation(qc, session?.accessToken),
    [qc, session?.accessToken]
  );

  const deleteTeamMemberMutator = useMutation({
    ...deleteUserMutationOptions,
    onSuccess: () => {
      deleteUserMutationOptions.onSuccess();
      closeForm();
    },
  });

  const defaultValues: TeamMemberFormData = useMemo(
    () => ({
      name: selectedTeamMember?.name ?? "",
      email: selectedTeamMember?.email ?? "",
      role: selectedTeamMember?.role.toLowerCase() ?? "",
      gatekeeper: selectedTeamMember?.gatekeeper ?? false,
    }),
    [selectedTeamMember]
  );

  const fieldErrors = useFieldErrors(
    selectedTeamMember ? updateUserMutator.error : createUserMutator.error
  );

  const { register, handleSubmit, formState, reset, setError, clearErrors } =
    useForm<TeamMemberFormData>({
      defaultValues,
      resolver: ajvResolver(schema),
    });

  const { errors, isDirty, isSubmitting } = formState;

  const isLoadingDelayedValue = useDebouncedBoolean(isLoadingTeamMembers, 500);
  const [rows, setRows] = useState<TUser[] | undefined>(undefined)

  // 

  // We only update the rows if we've got some data.
  useEffect(() => {
    const newData = teamMembers?.data ?? []
    if (!isLoadingTeamMembers && newData.length === 0) {
      // We're not loading, and the data is zero
      setRows(newData)
    } else {
      setRows(exitingVal => teamMembers?.data ?? exitingVal)
    }
  }, [isLoadingTeamMembers, teamMembers])

  // We want to show the spinner if rows is undefined (that is our initial state before isLoading is kicked off)
  //  or if isLoadingDelayedValue is true
  const showSpinner = useMemo(() => {
    if (rows === undefined && isLoadingTeamMembers) {
      return true
    }
    return isLoadingDelayedValue
  }, [rows, isLoadingTeamMembers, isLoadingDelayedValue])


  useReactHookFormServerErrors<TeamMemberFormData>(fieldErrors, errors, setError, clearErrors);

  const getErrorMessage = function(): string {
    if (errors.root) {
      return errors.root.message ?? ""
    }
    if (updateUserMutator.error?.response?.status == 409 && updateUserMutator.error?.response?.data.detail?.message) {
      return updateUserMutator.error?.response?.data.detail?.message
    }
    return createUserMutator.error?.response?.data.message ?? updateUserMutator.error?.response?.data.message ?? ""
  }

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);


  return (
    <Container>
      <PageHeader
        crumbs={[{ name: "Team", href: "/team" }]}
        title="Manage Team"
      >
      </PageHeader>

      {isLoadingTeamMembersError ?
        <div className="flex items-center justify-center">
          <EmptyAction message="Query failed." />
        </div>
        : showSpinner ? (
          <div className="flex items-center justify-center">
            <Spinner size="lg" message="Loading team members..." />
          </div>
        ) : (
          <>
            <Container className="flex pl-0 pr-0 pb-2">
                <div>
                {user && user.role === "Manager" && (
                  <Button
                    type="button"
                    icon={UserPlus}
                    onClick={() => {
                      reset();
                      clearData();
                      setEditTeamMemberOpen(true);
                    }}
                  >
                    Add team member
                  </Button>
                )}
                </div>
                <div className="ml-auto">
                <Filter
                  label="Status"
                  labelPlural="Statuses"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  options={allStatusFilterOptions}
                  multiple
                />
                </div>
            </Container>
            <StyledReactDataGrid
              rows={rows ?? []}
              columns={columns}
              contextMenuItems={[
                {
                  label: "Edit",
                  onClick: (_e, row) => {
                    clearData();
                    setSelectedTeamMember(row);
                    reset();
                    setEditTeamMemberOpen(true);
                  },
                },
                {
                  label: "Delete",
                  onClick: (e, row) => {
                    clearData();
                    e.preventDefault();
                    setSelectedTeamMember(row);
                    setRemoveTeamMemberOpen(true);
                  },
                },
                {
                  label: "Resend Invite",
                  onClick: (_e, row) => {
                    setSelectedTeamMember(row);
                    resendInviteMutator.mutate(row.id);
                  },
                },
              ]}
              sortColumns={sortColumns}
              onSortColumnsChange={setSortColumns}
              paginationProps={{
                onClick: (page) => setCurrentPage(page),
                currentPage: currentPage,
                totalItems: totalItems,
                pageSize: pageSize,
              }}
            /></>
        )}

      {/* Add/Edit Team Member Dialog */}
      <StyledDialog
        open={isEditTeamMemberOpen}
        onClose={() => { setEditTeamMemberOpen(false); createUserMutator.reset(); updateUserMutator.reset(); }}
        unmount={false}
        title={`${selectedTeamMember ? "Edit" : "Add"} team member`}
        footer={
          <div className="flex gap-xs">
            {selectedTeamMember ? (
              <Button
                type="button"
                disabled={!isDirty || isSubmitting}
                loading={isSubmitting}
                onClick={handleSubmit((formData) =>                  
                  {updateUserMutator.reset();
                  updateUserMutator.mutate(formData);}
                )}
              >
                Update
              </Button>
            ) : (
              <Button
                type="button"
                disabled={!isDirty || isSubmitting}
                loading={isSubmitting}
                onClick={handleSubmit((formData) =>
                  {createUserMutator.reset();
                  createUserMutator.mutate(formData);}
                )}
              >
                Invite
              </Button>
            )}
            <Button
              theme="outline"
              type="button"
              onClick={() => closeForm()}
            >
              Cancel
            </Button>          
          </div>
        }
      >
         <div className={"pb-2 " + ( (createUserMutator.error || updateUserMutator) ? "": "hidden")}>
          { (createUserMutator.error || updateUserMutator.error) && (
             <Alert theme="failure" message={getErrorMessage()}/>
          )
          }          
        </div>
        <form className="form">
          <section className="form-section">
            <Input
              label="Name"
              {...register("name")}
              error={errors.name?.message}
            />
            <Input
              label="Email"
              type="email"
              {...register("email")}
              error={errors.email?.message}
            />
          </section>
          <section className="form-section">
            <legend className="form-legend">Role</legend>
            {
              errors.role && (
                <Alert theme="warning" message={errors.role?.message} />
              )
            }
            <Checkbox
              label="Editor"
              type="radio"
              value="editor"
              description="Able to view offers and add orders"
              {...register("role")}
            />
            <Checkbox
              label="Manager"
              type="radio"
              value="manager"
              description="In addition to editor permissions, managers can add and remove team members"
              {...register("role")}
            />
          </section>
          {
            user?.organisational_role.toLocaleLowerCase() == EUserRole.INTERMEDIARY &&
            <section className="form-section">
              <legend className="form-legend">Wall crossing</legend>
              <Checkbox
                label="Gatekeeper"
                description="Notified of wall crossings and able to consent to be crossed"
                {...register("gatekeeper")}
              />
            </section>
          }
        </form>
       


      </StyledDialog>

      <StyledDialog
        open={isRemoveTeamMemberOpen}
        onClose={() => {
          setSelectedTeamMember(undefined);
          setDeleteValue(undefined);
          setRemoveTeamMemberOpen(false);
        }}
        unmount={false}
        title="Remove team member"
        footer={
          <div className="flex gap-xs">
            <Button
              disabled={
                deleteValue !== "DELETE" || deleteTeamMemberMutator.isLoading
              }
              loading={deleteTeamMemberMutator.isLoading}
              type="button"
              onClick={() =>
                deleteTeamMemberMutator.mutate(selectedTeamMember?.id ?? "")
              }
            >
              Delete
            </Button>
            <Button
              theme="outline"
              type="button"
              onClick={() => {
                setSelectedTeamMember(undefined);
                setRemoveTeamMemberOpen(false);
              }}
            >
              Cancel
            </Button>
          </div>
        }
      >
        <Alert
          className="mb-md"
          theme="warning"
          message={`Are you sure you want to remove ${selectedTeamMember?.name ?? "unknown name"
            }? This action cannot be undone.`}
        />
        <Input
          label="Type 'DELETE' to remove this team member"
          value={deleteValue}
          onChange={(e) => setDeleteValue(e.target.value)}
        />
      </StyledDialog>
    </Container>
  );
};

export default Team;
