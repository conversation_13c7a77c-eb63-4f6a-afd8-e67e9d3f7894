import { Container } from "components/Layout/Container";
import { Prose } from "components/Basic/Prose/Prose";
import { NextPage } from "next";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";

const Terms: NextPage = () => {
  return (
    <Container>
      <PageHeader
        crumbs={[{ name: "Terms and Conditions", href: "/terms" }]}
        title="Terms and Conditions"
      />
      <Prose className="pb-lg-xl"></Prose>
    </Container>
  );
};

export default Terms;
