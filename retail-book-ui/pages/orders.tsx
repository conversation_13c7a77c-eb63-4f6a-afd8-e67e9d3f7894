import { Tab } from "@headlessui/react";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "components/Basic/Badge/Badge";
import { Button } from "components/Basic/Button/Button";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import {
  Column,
  StyledReactDataGrid,
} from "components/StyledReactDataGrid/StyledReactDataGrid";
import { formatCurrency } from "currency";
import { format, parseISO } from "date-fns";
import {
  downloadFile,
  formatNumber,
  orderStatusThemeSelector,
  Zero,
} from "helpers";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useCallback, useMemo, useState } from "react";
import { SortColumn } from "react-data-grid";
import { Download } from "react-feather";
import {
  EOrderStatus,
  EOrderType,
  EUserRole,
  TOffer,
  TOrder,
  TPaginatedData,
} from "types";
import { Api } from "utils/api";
import { getOffersQuery, getOrdersQuery, getUserQuery } from "utils/queries";

const tabs = [
  { title: "All orders", filterOptions: [] },
  {
    title: "Pending approval",
    filterOptions: [
      { label: "pending", value: `status=${EOrderStatus.PENDING}:eq` },
    ],
  },
  {
    title: "Approved",
    filterOptions: [
      { label: "accepted", value: `status=${EOrderStatus.ACCEPTED}:eq` },
    ],
  },
];

interface Row {
  id: string;
  offerId: string;
  offerName: string;
  date_created: string;
  status: EOrderStatus;
  type: EOrderType;
  intermediary?: string;
  applications: string;
  value: string;
  shareholding: string;
}

const columns: Column<Row>[] = [
  {
    key: "date_created",
    name: "Date",
    sortable: true,
    resizable: true,
  },
  {
    key: "offerName",
    name: "Offer",
    sortable: true,
    resizable: true
  },
  {
    key: "intermediary",
    name: "Intermediary",
    resizable: true,  
  },
  {
    key: "status",
    name: "Status",
    formatter: ({ row }) => (
      <Badge theme={orderStatusThemeSelector(row.status)}>{row.status}</Badge>
    ),
    resizable: true,
  },
  {
    key: "type",
    name: "Type",
    resizable: true,
  },
  {
    key: "applications",
    name: "Applications",
    formatter: ({ row }) => (
      <span className="font-mono">{row.applications}</span>
    ),
    resizable: true,
    headerCellClass: "text-right",
    cellClass: "text-right",
  },
  {
    key: "shareholding",
    name: "Shareholding",
    formatter: ({ row }) => (
      <span className="font-mono">{row.shareholding}</span>
    ),
    resizable: true,
    headerCellClass: "text-right",
    cellClass: "text-right",
  },
  {
    key: "value",
    name: "Value",
    formatter: ({ row }) => <span className="font-mono">{row.value}</span>,
    resizable: true,
    headerCellClass: "text-right",
    cellClass: "text-right",
  },
];

const selectOfferCurrencyMap = (data: TPaginatedData<TOffer>) =>
  new Map(data.data?.map((offer) => [offer.id, offer.currency]));

const getRowFromOrder = (order: TOrder, currency: string): Row => {
  return {
    id: order.id,
    offerId: order.offer_id,
    offerName: order.offer_name,
    date_created: format(parseISO(order.order_date), "dd/MM/yyyy HH:mm"),
    status: order.status,
    intermediary: order.intermediary,
    shareholding: formatNumber(order.shareholding?.existing_holding),
    type: order.order_type,
    applications: formatNumber(order.totals.applications),
    value: formatCurrency(order.totals.notional_value ?? Zero, currency),
  };
};

const pageSize = 10;

const Orders: NextPage = () => {
  const router = useRouter();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { data: session } = useSession();

  const { data: user } = useQuery(
    getUserQuery(session?.user?.ext_id, session?.accessToken)
  );

  const { data: offerCurrencyMap, isLoading: isLoadingOffers } = useQuery({
    ...getOffersQuery({ offset: 0, limit: 0 }, session?.accessToken),
    select: selectOfferCurrencyMap,
  });

  const cols = useMemo(
    () => {
      if (user?.organisational_role === "intermediary") {
        return columns.filter( r => r.key !== "intermediary" )
      }
      return columns
    },[user]
  )

  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);

  const params = useMemo(() => {
    const filter = tabs[selectedIndex].filterOptions.map((opt) => opt.value);
    const sort =
      sortColumns.length > 0
        ? `${
            sortColumns[0].columnKey
          }=${sortColumns[0].direction.toLocaleLowerCase()}`
        : undefined;

    return {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      filter,
      sort,
    };
  }, [selectedIndex, currentPage, sortColumns]);

  const { data: orders, isLoading: isLoadingOrders } = useQuery({
    ...getOrdersQuery(params, session?.accessToken),
  });

  const totalItems = useMemo(
    () => orders?.pagination.count ?? 0,
    [orders?.pagination.count]
  );

  const rows = useMemo(
    () =>
      orders?.data?.map((order) =>
        getRowFromOrder(order, offerCurrencyMap?.get(order.offer_id) ?? "")
      ) ?? [],
    [orders, offerCurrencyMap]
  );

  const downloadAllOrders = useCallback(async () => {
    const data = await Api.downloadOrders(session?.accessToken);
    const file = new File([data], "RetailBook: All orders", {
      type: data.type,
    });
    downloadFile(file);
  }, [session?.accessToken]);

  return (
    <>
      <Container>
        <PageHeader title="Orders">
          {user?.organisational_role?.toLowerCase() === EUserRole.ISSUER && (
            <Button
              size="lg"
              type="button"
              onClick={() => downloadAllOrders()}
              icon={Download}
            >
              Download All Orders
            </Button>
          )}
        </PageHeader>
        {/* <dl className="mb-sm flex flex-wrap gap-sm">
          <Stat
            title="Applications"
            // value={formatNumber()}
          />
          <Stat
            title="Shareholding"
            // value={formatNumber()}
          />
        </dl> */}
      </Container>
      <Tab.Group onChange={setSelectedIndex} selectedIndex={selectedIndex}>
        <Container>
          <Tab.List className="tab-list">
            {tabs.map((tab, index) => (
              <StyledTab key={index}>{tab.title}</StyledTab>
            ))}
          </Tab.List>
        </Container>

        <Tab.Panels className="tab-panel tab-panel--bordered">
          <Container>
            {isLoadingOffers || isLoadingOrders ? (
              <div className="flex items-center justify-center">
                <Spinner size="lg" message="Loading orders..." />
              </div>
            ) : (
              <StyledReactDataGrid
                rows={rows}
                columns={cols}
                contextMenuItems={[
                  {
                    label: "View",
                    onClick: (e, row) => {
                      e.preventDefault();
                      router.push(`/offers/${row?.offerId}?tab=order`);
                    },
                  },
                ]}
                sortColumns={sortColumns}
                onSortColumnsChange={setSortColumns}
                paginationProps={{
                  onClick: (page) => setCurrentPage(page),
                  currentPage: currentPage,
                  totalItems: totalItems,
                  pageSize: pageSize,
                }}
              />
            )}
          </Container>
        </Tab.Panels>
      </Tab.Group>
    </>
  );
};

export default Orders;
