import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Alert } from "components/Basic/Alert/Alert";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useMemo, useState } from "react";
import { AxiosError } from "axios";
import { getWallcrossInvite, acceptWallcrossInviteMutation, rejectWallcrossInviteMutation, getUserQuery } from "utils/queries";
import { Container } from "components/Layout/Container";
import { Button } from "components/Basic/Button/Button";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import { X, Check, ArrowLeft } from "react-feather";
import { ApiMutationResponse, ApiError, EWallCrossStatus } from "types";
import { StandaloneLink } from "components/Basic/StandaloneLink/StandaloneLink";
import { downloadFile } from "helpers";
import { toast } from "react-toastify";
import { Api } from "utils/api";
import ReactMarkdown from "react-markdown";
import { Prose } from "components/Basic/Prose/Prose";

const WallCrossing: NextPage = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const inviteId = useMemo(() => router.query?.id as string, [router.query]);
  const [consents, setConsents] = useState(0);
  const qc = useQueryClient();

  const { data: invite, isLoading: inviteIsLoading } = useQuery({
    ...getWallcrossInvite(inviteId, session?.accessToken)
  });
  const isPending = invite?.status == EWallCrossStatus.PENDING;
  const isAccepted = invite?.status == EWallCrossStatus.ACCEPTED;
  const isRejected = invite?.status == EWallCrossStatus.REJECTED;
  const didNotRespond = invite?.status == EWallCrossStatus.NOT_RESPONDED;

  const { mutate: acceptWallCrossMutator } = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    void
  >(acceptWallcrossInviteMutation(invite?.invite_id ?? "", qc, session?.accessToken));

  const { mutate: rejectWallCrossMutator } = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    void
  >(rejectWallcrossInviteMutation(invite?.invite_id ?? "", qc, session?.accessToken));

  const { data: user } = useQuery(
    getUserQuery(session?.user?.ext_id, session?.accessToken)
  );

  const getWallCrossDocumentMutator = useMutation<Blob,AxiosError<Blob>,void>({
    mutationFn: () => {
      return Api.getWallcrossInviteDocument(invite?.invite_id ?? "", invite?.offer_outline_document_id ?? "", session?.accessToken);
    },
    onSuccess: (data:Blob) => {
      const file = new File([data], "Offer Outline", {
        type: data.type,
      });
      downloadFile(file);
    },
    onError: async () => {      
      toast.error("Failed to download file");
    },
  })

  if (inviteIsLoading) {
    return (
      <div className="grow flex items-center justify-center">
        <Spinner size="lg" message="Loading invite..." />
      </div>
    );
  }

  if (!invite) {
    return (
      <div className="grow flex items-center justify-center">
        <Alert
          theme="failure"
          title="Invite not found"
          message="The invite you are looking for does not exist."
        />
      </div>
    );
  }

  const consentOnClick = (i: HTMLInputElement) => {
    i.checked ? setConsents(consents + 1) : setConsents(consents - 1);
  }

  return (
    <>
    <Container className="w-3/4 justify-center">
    <StandaloneLink href="/wallcrossings" icon={ArrowLeft} iconPosition="before" className="mt-sm mb-md-lg">Wall-Crossings</StandaloneLink>
      <div className="mt-xl">
        <div>
          <h2 className="h2 mb-sm">Subject</h2>
          <Prose as={ReactMarkdown}>{invite?.subject}</Prose>
        </div>
        <div className="mt-md mb-xl">
          <h2 className="h2 mb-sm">Consent To Cross</h2>
          <Prose as={ReactMarkdown}>{invite?.consent_to_cross}</Prose>
        </div>
      </div>
      {isPending && (
      <Container>
        <Container className="mt-md">
          <div><Checkbox label="I have read and understand the above" onClick={(e) => {consentOnClick(e.target as HTMLInputElement)}} /></div>
          <div className="pt-sm"><Checkbox label="I am authorised by my firm to act as to receive the market sounding and to respond to this request" onClick={(e) => {consentOnClick(e.target as HTMLInputElement)}} /></div>
          <div className="pt-sm"><Checkbox label="I consent to receiving inside information" onClick={(e) => {consentOnClick(e.target as HTMLInputElement)}} /></div>
        </Container>
        <Container className="mt-md">
          <Button className="mr-md" icon={Check} disabled={consents != 3} onClick={() => {acceptWallCrossMutator()}}>Accept</Button>
          <Button theme="outline" icon={X} onClick={() => {rejectWallCrossMutator()}}>Reject</Button>
        </Container>
      </Container>
      )}
      {isAccepted && (
        <Alert
          className="mt-xl"
          timeClassName="text-lg"
          theme="success"
          title="Invite accepted - you now hold Inside Information as of the time above"
          message={
          <span>
            You have accepted the wall crossing. Please click below to download the retail offer outline.
            <br/>
            <Button className="mt-md" onClick={() => {getWallCrossDocumentMutator.mutate()}}>Download Offer Outline</Button>
          </span>
          }
          date={ invite?.invite_response_time ? new Date(invite.invite_response_time) : undefined}
        />
      )}
      {isRejected && (
        <Alert
        className="mt-xl"
        theme="failure"
        title="Invite rejected"
        message={
        <span>
          You have rejected the wall crossing
          <br/><br/>
          <div>Rejected by: <a href={"mailto:" + user?.email}>{user?.name}</a></div>
        </span>
        }
        date={ invite?.invite_response_time ? new Date(invite.invite_response_time) : undefined}
      />
      )}
      {didNotRespond && (
        <Alert
          className="mt-xl"
          theme="info"
          title="Did not respond"
          message={
          <span>
            You did not respond to the invite
          </span>
          }
        />
      )}
      {!!invite.offer_id && (invite.status == EWallCrossStatus.ACCEPTED || invite.is_launched) && (
        <Button className="mt-xl" onClick={() => {router.push(`/offers/${invite.offer_id}`);}}>Go To Offer</Button>
      )}
    </Container>
    </>
  );
};

export default WallCrossing;
