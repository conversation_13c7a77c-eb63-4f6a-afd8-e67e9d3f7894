import { Tab } from "@headlessui/react";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "components/Basic/Badge/Badge";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { Column, StyledReactDataGrid } from "components/StyledReactDataGrid/StyledReactDataGrid";
import { format, parseISO } from "date-fns";
import { wallCrossStatusThemeSelector } from "helpers";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useMemo, useState } from "react";
import { SortColumn } from "react-data-grid";
import { EOrderStatus, EWallCrossStatus, TWallCrossing } from "types";
import { getUserWallCrossInvitesQuery } from "utils/queries";
import { Check } from "react-feather";

const tabs = [
  { title: "All", filterOptions: [] },
  {
    title: "Pending approval",
    filterOptions: [
      { label: "pending", value: `status=${EOrderStatus.PENDING}:eq` },
    ],
  },
];

interface Row {
  id: string;
  invite_id: string;
  subject: string;
  bank_system_id: string;
  invite_sent_time: string;
  status: EWallCrossStatus;
  invite_response_time?: string;
  offer_id?: string;
  is_launched: boolean;
}

const columns: Column<Row>[] = [
  {
    key: "bank_system_id",
    name: "From",
    sortable: true,
    resizable: true,
  },
  {
    key: "subject",
    name: "Subject",
    sortable: true,
    resizable: true
  },
  {
    key: "invite_sent_time",
    name: "Received",
    sortable: true,
    resizable: true,  
  },
  {
    key: "status",
    name: "Response",
    formatter: ({ row }) => (
      <Badge theme={wallCrossStatusThemeSelector(row.status)}>{row.status == EWallCrossStatus.NOT_RESPONDED ? "No Response" : row.status}</Badge>
    ),
    sortable: true,
    resizable: true,
  },
  {
    key: "invite_response_time",
    name: "Response Time",
    sortable: true,
    resizable: true,  
  },
  {
    key: "inside_time",
    name: "Inside Time",
    sortable: false,
    formatter: ({row}) => {
      if (row.status == EWallCrossStatus.ACCEPTED) {
        return (<p> {row.invite_response_time ?? ""} </p>)
      } else {
        return ""
      }
    }  
  },
  {
    key: "offer_id",
    name: "Offer Available",
    sortable: true,
    resizable: true,
    formatter: ({ row }) => {
      if (isOfferAvailable(row)) {
        return <div><Check/></div>
      }
    },
  },
];

const getRowFromInvite = (invite: TWallCrossing): Row => {
  return {
    id: invite.id,
    invite_id: invite.invite_id,
    subject: invite.subject,
    bank_system_id: invite.bank_system_id,
    invite_sent_time: format(parseISO(invite.invite_sent_time), "dd/MM/yyyy HH:mm"),
    status: invite.status,
    invite_response_time: invite.invite_response_time ? format(parseISO(invite.invite_response_time), "dd/MM/yyyy HH:mm") : undefined,
    offer_id: invite.offer_id,
    is_launched: invite.is_launched
  };
};

const isOfferAvailable = (row: Row): boolean => {
  return !!row.offer_id && (row.status == EWallCrossStatus.ACCEPTED || row.is_launched)
}

const pageSize = 10;

const WallCrossings: NextPage = () => {
  const router = useRouter();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { data: session } = useSession();
  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);

  const params = useMemo(() => {
    const filter = tabs[selectedIndex].filterOptions.map((opt) => opt.value);
    const sort =
      sortColumns.length > 0
        ? `${
            sortColumns[0].columnKey
          }=${sortColumns[0].direction.toLocaleLowerCase()}`
        : undefined;

    return {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      filter,
      sort,
    };
  }, [selectedIndex, currentPage, sortColumns]);

  const { data: invites, isLoading: isLoadingInvites } = useQuery({
    ...getUserWallCrossInvitesQuery(params, session?.user?.ext_id, session?.accessToken)
  });
  const totalItems = useMemo(
    () => invites?.pagination.count ?? 0,
    [invites?.pagination.count]
  );

  const rows = useMemo(
    () =>
      invites?.data?.map((invite) =>
        getRowFromInvite(invite)
      ) ?? [],
    [invites]
  );

  return (
    <>
      <Container>
        <PageHeader title="Wall-Crossings"/>
      </Container>
      <Tab.Group onChange={setSelectedIndex} selectedIndex={selectedIndex}>
        <Container>
          <Tab.List className="tab-list">
            {tabs.map((tab, index) => (
              <StyledTab key={index}>{tab.title}</StyledTab>
            ))}
          </Tab.List>
        </Container>

        <Tab.Panels className="tab-panel tab-panel--bordered">
          <Container>
            {isLoadingInvites ? (
              <div className="flex items-center justify-center">
                <Spinner size="lg" message="Loading wall crossings..." />
              </div>
            ) : (
              <StyledReactDataGrid
                rows={rows}
                columns={columns}
                contextMenuItems={[
                  {
                    label: "View",
                    onClick: (e, row) => {
                      e.preventDefault();
                      router.push(`/wallcrossings/${row?.invite_id}`);
                    },
                  },
                  {
                    label: "View Offer",
                    isEnabled: (row) => { return isOfferAvailable(row) },
                    onClick: (e, row) => {
                      e.preventDefault();
                      router.push(`/offers/${row?.offer_id}`);
                    },
                  }
                ]}
                sortColumns={sortColumns}
                onSortColumnsChange={setSortColumns}
                paginationProps={{
                  onClick: (page) => setCurrentPage(page),
                  currentPage: currentPage,
                  totalItems: totalItems,
                  pageSize: pageSize,
                }}
              />
            )}
          </Container>
        </Tab.Panels>
      </Tab.Group>
    </>
  );
};

export default WallCrossings;
