import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { useFieldErrors } from "hooks/useFieldErrors";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { ApiError, ApiMutationResponse, TOrganisation } from "types";
import { getOrganisation, updateOrganisationMutation } from "utils/queries";
// Components
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { OrganisationForm } from "components/Composite/AccountPage/OrganisationForm";
import { Container } from "components/Layout/Container";

const Organisation: NextPage = () => {
  const qc = useQueryClient();
  const { data: session } = useSession();

  const { mutate: updateOrgansationMutator, error } = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    Partial<TOrganisation>
  >(updateOrganisationMutation(qc, session?.accessToken));
  const fieldErrors = useFieldErrors(error);

  let { data: organisation } = useQuery(
    getOrganisation(session?.accessToken)
  );
  if(!organisation) {
    organisation = {
      name: "",
      crest_account_name: "",
      crest_participant_id: "",
      account_type: "",
      account_name: "",
      account_bank_name: "",
      account_swift_bic: "",
      account_number: "",
      account_sort_code: "",
      address1: "",
      address2: "",
      address3: "",
      address4: "",
      address_city: "",
      address_postal_code: "",
      address_country: "",
      system_id: "",
      system_role: "",
      settlement_reference: "",
    }
  }

  return (
    <>
      <Container>
        <PageHeader
          crumbs={[{ name: "Organisation", href: "/organisation" }]}
          title="Organisation"
        />
      </Container>

      <Container narrow>
        <OrganisationForm
          organisation={organisation}
          onSubmit={updateOrgansationMutator}
          fieldErrors={fieldErrors}
        />
      </Container>

    </>
  );
};

export default Organisation;
