import { useQuery } from "@tanstack/react-query";
import { Alert } from "components/Basic/Alert/Alert";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { PreviewableEditOffer } from "components/Composite/OfferPage/EditOffer/PreviewableEditOffer";
import { ViewOffer } from "components/Composite/OfferPage/ViewOffer/ViewOffer";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useCallback, useMemo } from "react";
import { EUserRole, TOffer } from "types";
import { toTOffer } from "utils/dataConversion";
import { getOfferQuery, getUserQuery } from "utils/queries";

const Offer: NextPage = () => {
  const { data: session } = useSession();
  const { query } = useRouter();
  const offerId = useMemo(() => query?.id as string, [query]);

  const { data: user } = useQuery(
    getUserQuery(session?.user?.ext_id, session?.accessToken)
  );

  const { data: offer, isLoading: offerIsLoading } = useQuery({
    ...getOfferQuery(offerId, session?.accessToken),
    select: useCallback((data: TOffer) => toTOffer(data), []),
  });

  if (offerIsLoading) {
    return (
      <div className="grow flex items-center justify-center">
        <Spinner size="lg" message="Loading offer..." />
      </div>
    );
  }

  if (!offer) {
    return (
      <div className="grow flex items-center justify-center">
        <Alert
          theme="failure"
          title="Offer not found"
          message="The offer you are looking for does not exist."
        />
      </div>
    );
  }

  return user?.organisational_role?.toLowerCase() === EUserRole.INTERMEDIARY ? (
    <ViewOffer offer={offer} />
  ) : (
    <PreviewableEditOffer offer={offer} />
  );
};

export default Offer;
