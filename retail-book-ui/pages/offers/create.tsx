import { NextPage } from "next";
import { createOfferMutation } from "utils/queries";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ApiError, ApiMutationResponse, EOfferType, TNewOffer } from "types";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
// Components
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { Select } from "components/Basic/Select/Select";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import Link from "next/link";
import { useFieldErrors } from "hooks/useFieldErrors";

import { AxiosError } from "axios";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useForm } from "react-hook-form";
import {Checkbox} from "../../components/Basic/Checkbox/Checkbox";
import {useState} from "react";
import {format} from "date-fns";
import {DATETIME_LOCAL_DATE_FNS_FORMAT, formatIsoFromLocal} from "../../helpers";


const defaultValues = {
  name: "", type: EOfferType.IPO, insideTime: undefined
};

const CreateOffer: NextPage = () => {
  const qc = useQueryClient();
  const router = useRouter();
  
  const { data: session } = useSession();
  const { mutate:mutation, error} = useMutation<ApiMutationResponse, AxiosError<ApiError>, TNewOffer>( 
    {...createOfferMutation(qc, session?.accessToken),
      onSuccess: (data: ApiMutationResponse) => {
        qc.invalidateQueries(["offers"]);    
        router.push(`/offers/${data.id}`);
      },
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      onError: () => {}
    }
  );

  const handleOfferCreation = (data: TNewOffer) => {  
    mutation(data);
  };

  const fieldErrors = useFieldErrors(error);

  const { register, getValues, setValue, handleSubmit, formState, reset, setError, clearErrors } =
      useForm<TNewOffer>({
        defaultValues
      });

  const [applyInsideTime, setApplyInsideTime] = useState(false);
  const applyInsideTimeOnChange = (i: HTMLInputElement) => {
    if (i.checked) {
      setValue("inside_time", format(new Date(), DATETIME_LOCAL_DATE_FNS_FORMAT))
    } else {
      // reset
      setValue("inside_time", undefined)
    }
    setApplyInsideTime(i.checked)
  }

  const { errors, isDirty, isSubmitting } = formState;

  useReactHookFormServerErrors<TNewOffer>(fieldErrors, errors, setError, clearErrors);
  
  return (
    <>
      <Container narrow>
        <PageHeader
          crumbs={[
            { name: "Offers", href: "/" },
            { name: "Add", href: "#" },
          ]}
          title="Add New Offer"
        />
        {/* a centered form */}
      
        <form className="form" onSubmit={handleSubmit( (df) =>
            handleOfferCreation({
              ...df,
              inside_information: df.inside_information ? df.inside_information : undefined,
              inside_time: formatIsoFromLocal(df.inside_time)
            }))} >
          <section className="form-section">
            <Input label="Offer Name" {...register("name")} error={ errors?.name?.message } />
            <Select label="Type" {...register("type")} error={ errors?.type?.message } >
              <option>IPO</option>
              <option>Follow On</option>
              <option>Retail Bond</option>
            </Select>
            <div className="pt-xs pb-xs">
              <Checkbox
                  {...register("inside_information")}
                  label="Inside Information"
                  className="font-bold"
                  checked={applyInsideTime}
                  onChange={(e) => applyInsideTimeOnChange(e.target)} />
              <Input
                  { ...(applyInsideTime ? {}: {className:"hidden"}) }
                  type="datetime-local"
                  label=""
                  onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
                  onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
                  max={getValues("inside_time")}
                  {...register("inside_time")} error={ errors?.inside_time?.message }
              />
            </div>
            <div className="form-actions">
              <Button disabled={!isDirty || isSubmitting} loading={isSubmitting}>Create</Button>
              <Link href="/">
                <Button type="button" theme="outline" onClick={() => reset(defaultValues)}>
                  Cancel
                </Button>
              </Link>
              <p className="text-[#FF0000]">{error?.response?.data.message}</p>
            </div>
          </section>
        </form>
      </Container>
    </>
  );
};

export default CreateOffer;
