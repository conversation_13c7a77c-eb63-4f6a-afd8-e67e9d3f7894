import { Layout } from "components/Layout/Layout";
import type { AppProps } from "next/app";
import { useRouter } from "next/router";
import { AlertCircle, Check, Info, X } from "react-feather";
import { toast, ToastContainer } from "react-toastify";
import { SessionProvider } from "next-auth/react";
import { Session } from "next-auth";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import "styles/globals.css";
import Head from "next/head";
import { ApiError } from "types";
import { AxiosError } from "axios";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onError = (error: AxiosError<ApiError> | any) => {
  const message = error?.response?.data?.message;
  if (message) toast.error(message);
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: { onError },
    mutations: { onError },
  },
});

function App({ Component, pageProps }: AppProps<{ session: Session }>) {
  const router = useRouter();

  return (
    <SessionProvider session={pageProps.session} refetchInterval={60}>
      <QueryClientProvider client={queryClient}>
        <Layout currentPath={router.asPath}>
          <Head>
            <link rel="shortcut icon" href="/favicon.ico" />
          </Head>

          <Component {...pageProps} />
          <ToastContainer
            position="bottom-right"
            hideProgressBar
            theme="colored"
            icon={({ type }) => {
              return type === "success" ? (
                <Check aria-hidden="true" className="h-5 w-5" />
              ) : type === "error" ? (
                <X aria-hidden="true" className="h-5 w-5" />
              ) : type === "warning" ? (
                <AlertCircle aria-hidden="true" className="h-5 w-5" />
              ) : (
                <Info aria-hidden="true" className="h-5 w-5" />
              );
            }}
          />
        </Layout>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </SessionProvider>
  );
}

export default App;
