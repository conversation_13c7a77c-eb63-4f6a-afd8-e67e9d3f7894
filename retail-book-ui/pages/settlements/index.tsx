import { Tab } from "@headlessui/react";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { Container } from "components/Layout/Container";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { useMemo } from "react";
import { NextPage } from "next";
import { useRouter } from "next/router";
import InstructionsTab from "components/Composite/SettlementsPage/Tabs/InstructionsTab";
import ObligationsTab from "components/Composite/SettlementsPage/Tabs/ObligationsTab";

const Settlements: NextPage = () => {
    const router = useRouter();

    const tabs = useMemo(() => [
      <InstructionsTab key="instructions" />,
      <ObligationsTab key="obligations" />
    ], [])

    const tabStateIndex = useMemo(
      () => {
          const tabParam = router.query?.tab;
          if (tabParam) {
              for (const [idx, tabPanel] of tabs.entries()) {
                  if (tabPanel.key && tabParam?.includes(tabPanel.key.toString())) {
                      return idx;
                  }
              }
          }
          return 0;
      },
      [router.query, tabs]
  );
  
  return (
    <>
      <Container>
        <PageHeader title="Settlements" />
      </Container>
      <Tab.Group selectedIndex={tabStateIndex} onChange={(idx) => {
        console.log(idx)
          router.replace({pathname: router.pathname, query: {...router.query, tab: tabs[idx].key?.toString()}});
      }}>
        <Container>
          <Tab.List className="tab-list">
            <StyledTab>Instructions</StyledTab>
            <StyledTab>Obligations</StyledTab>
          </Tab.List>
        </Container>

        <Tab.Panels className="tab-panel tab-panel--bordered">
          {tabs}
        </Tab.Panels>

      </Tab.Group>
      </>
  );
};

export default Settlements;
