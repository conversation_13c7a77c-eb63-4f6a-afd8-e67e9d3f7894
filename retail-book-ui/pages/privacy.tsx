import { Container } from "components/Layout/Container";
import { Prose } from "components/Basic/Prose/Prose";
import { NextPage } from "next";
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";

const Privacy: NextPage = () => {
  return (
    <Container className="pt-xs-sm pb-lg-xl">
      <PageHeader
        crumbs={[{ name: "Privacy", href: "/privacy" }]}
        title="Privacy"
      />
      <Prose className="pb-lg-xl"></Prose>
    </Container>
  );
};

export default Privacy;
