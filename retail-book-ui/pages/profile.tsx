import { useQuery } from "@tanstack/react-query";
import { NextPage } from "next";
import { useSession } from "next-auth/react";
import { getUserQuery } from "utils/queries";
// Components
import { PageHeader } from "components/Bespoke/PageHeader/PageHeader";
import { UserForm } from "components/Composite/AccountPage/UserForm";
import { Container } from "components/Layout/Container";

const Profile: NextPage = () => {
  const { data: session } = useSession();

  const { data: user } = useQuery(
    getUserQuery(session?.user?.ext_id, session?.accessToken)
  );

  return (
    <>
      <Container>
        <PageHeader
          crumbs={[{ name: "Profile", href: "/profile" }]}
          title="Profile"
        />
      </Container>
      <Container narrow>
        <UserForm user={user} onSubmit={() => null} />
      </Container>
    </>
  );
};

export default Profile;
