import NextAuth from "next-auth";
import AzureADB2CProvider from "next-auth/providers/azure-ad-b2c";

/**
 * Using an existing JWT access token retrieve a new token with the updated `accessToken`
 * and `accessTokenExpires`. On error conditions (i.e. failing to refresh for e.g.) then return
 * the old token and an error property.
 * @param token The old access token
 */
async function refreshAccessToken(token) {
  try {
    const url =
      `https://${process.env.AZURE_AD_B2C_TENANT_NAME}.b2clogin.com/${process.env.AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/${process.env.AZURE_AD_B2C_PRIMARY_USER_FLOW}/oauth2/v2.0/token?` +
      new URLSearchParams({
        client_id: process.env.AZURE_AD_B2C_CLIENT_ID,
        client_secret: process.env.AZURE_AD_B2C_CLIENT_SECRET,
        grant_type: "refresh_token",
        refresh_token: token.refreshToken,
      });

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      method: "POST",
    });

    const refreshedTokens = await response.json();
    if (!response.ok) {
      console.log(token.user.id + ':' + new Date() + ':' + JSON.stringify(refreshedTokens))
      throw refreshedTokens;
    }

    const accessTokenExpiryDate = new Date();
    accessTokenExpiryDate.setSeconds(
      accessTokenExpiryDate.getSeconds() + refreshedTokens.expires_in
    );

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: accessTokenExpiryDate,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken, // Fall back to old refresh token
    };
  } catch (error) {
    console.log(token.user.id + ':' + new Date() + ':' + error)
    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

export const authOptions = {
  // Configure one or more authentication providers
  providers: [
    AzureADB2CProvider({
      tenantId: process.env.AZURE_AD_B2C_TENANT_NAME,
      clientId: process.env.AZURE_AD_B2C_CLIENT_ID,
      clientSecret: process.env.AZURE_AD_B2C_CLIENT_SECRET,
      primaryUserFlow: process.env.AZURE_AD_B2C_PRIMARY_USER_FLOW,
      authorization: {
        params: {
          scope: `https://${process.env.AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/api/access offline_access openid`,
          prompt: "login",
        },
      },
      httpOptions: {
        timeout: 40000,
      },
    }),
  ],
  debug: true,
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial log-in.
      if (account && user) {
        const accessTokenExpiryDate = new Date(0);
        accessTokenExpiryDate.setUTCSeconds(account.expires_at);

        console.log(user.id + ':' + new Date() + ':access token expires at ' + accessTokenExpiryDate)

        const stopRefreshDate = new Date()
        stopRefreshDate.setHours(24, 0, 0, 0)

        return {
          accessToken: account.access_token,
          accessTokenExpires: accessTokenExpiryDate,
          refreshToken: account.refresh_token,
          refreshExpires: stopRefreshDate,
          user,
        };
      }

      const now = new Date()

      // If we are past the midnight refresh expiry date, we stop requesting more tokens
      if (new Date(token.refreshExpires) < now) {
        console.log(token.user.id + ':' + now + ':refresh token cutoff at ' + new Date(token.refreshExpires))
        // This session.user.error is checked in the Header and forces signout
        return {
          ...token,
          error: "RefreshAccessTokenError",
        };
      }

      const minuteBeforeExpiry = new Date(token.accessTokenExpires);
      minuteBeforeExpiry.setMinutes(minuteBeforeExpiry.getMinutes() - 1);

      // If the access token has not expired yet (with a 1 minute threshold); return previous
      // This is to avoid the access token expiring and forcing re-authentication before session is refetched
      if (minuteBeforeExpiry > now) {
        return token;
      }

      console.log(token.user.id + ':' + now + ':refreshing access token, expires at ' + new Date(token.accessTokenExpires))

      // Expiry... Try and refresh
      return refreshAccessToken(token);
    },
    async session({ session, token }) {
      session.tokenUser = token.user;
      session.user.ext_id = token.user.id; // For back-compatibility only. If new code, use tokenUser above.
      session.user.error = token.error;
      session.accessToken = token.accessToken;
      session.error = token.error;
      return session;
    },
  },
};
export default NextAuth(authOptions);
