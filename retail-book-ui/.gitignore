# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn
.yarnrc.yaml

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/storybook-static

# misc
.DS_Store
*.pem
.vscode

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
/test-results/
/playwright-report/
/playwright/.cache/
/.idea/
/retail-book-ui.iml
