"module name","license","repository"
"@aashutoshrathi/word-wrap@1.2.6","MIT","https://github.com/aashutoshrathi/word-wrap"
"@alloc/quick-lru@5.2.0","MIT","https://github.com/sindresorhus/quick-lru"
"@ampproject/remapping@2.2.1","Apache-2.0","https://github.com/ampproject/remapping"
"@babel/code-frame@7.23.5","MIT","https://github.com/babel/babel"
"@babel/compat-data@7.23.5","MIT","https://github.com/babel/babel"
"@babel/core@7.12.9","MIT","https://github.com/babel/babel"
"@babel/core@7.23.5","MIT","https://github.com/babel/babel"
"@babel/generator@7.23.5","MIT","https://github.com/babel/babel"
"@babel/helper-annotate-as-pure@7.22.5","MIT","https://github.com/babel/babel"
"@babel/helper-builder-binary-assignment-operator-visitor@7.22.15","MIT","https://github.com/babel/babel"
"@babel/helper-compilation-targets@7.22.15","MIT","https://github.com/babel/babel"
"@babel/helper-create-class-features-plugin@7.23.5","MIT","https://github.com/babel/babel"
"@babel/helper-create-regexp-features-plugin@7.22.15","MIT","https://github.com/babel/babel"
"@babel/helper-define-polyfill-provider@0.1.5","MIT","https://github.com/babel/babel-polyfills"
"@babel/helper-define-polyfill-provider@0.4.3","MIT","https://github.com/babel/babel-polyfills"
"@babel/helper-environment-visitor@7.22.20","MIT","https://github.com/babel/babel"
"@babel/helper-function-name@7.23.0","MIT","https://github.com/babel/babel"
"@babel/helper-hoist-variables@7.22.5","MIT","https://github.com/babel/babel"
"@babel/helper-member-expression-to-functions@7.23.0","MIT","https://github.com/babel/babel"
"@babel/helper-module-imports@7.22.15","MIT","https://github.com/babel/babel"
"@babel/helper-module-transforms@7.23.3","MIT","https://github.com/babel/babel"
"@babel/helper-optimise-call-expression@7.22.5","MIT","https://github.com/babel/babel"
"@babel/helper-plugin-utils@7.10.4","MIT","https://github.com/babel/babel"
"@babel/helper-plugin-utils@7.22.5","MIT","https://github.com/babel/babel"
"@babel/helper-remap-async-to-generator@7.22.20","MIT","https://github.com/babel/babel"
"@babel/helper-replace-supers@7.22.20","MIT","https://github.com/babel/babel"
"@babel/helper-simple-access@7.22.5","MIT","https://github.com/babel/babel"
"@babel/helper-skip-transparent-expression-wrappers@7.22.5","MIT","https://github.com/babel/babel"
"@babel/helper-split-export-declaration@7.22.6","MIT","https://github.com/babel/babel"
"@babel/helper-string-parser@7.23.4","MIT","https://github.com/babel/babel"
"@babel/helper-validator-identifier@7.22.20","MIT","https://github.com/babel/babel"
"@babel/helper-validator-option@7.23.5","MIT","https://github.com/babel/babel"
"@babel/helper-wrap-function@7.22.20","MIT","https://github.com/babel/babel"
"@babel/helpers@7.23.5","MIT","https://github.com/babel/babel"
"@babel/highlight@7.23.4","MIT","https://github.com/babel/babel"
"@babel/parser@7.23.5","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-class-properties@7.18.6","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-decorators@7.23.5","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-export-default-from@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-nullish-coalescing-operator@7.18.6","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-object-rest-spread@7.12.1","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-object-rest-spread@7.20.7","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-optional-chaining@7.21.0","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-private-methods@7.18.6","MIT","https://github.com/babel/babel"
"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2","MIT","https://github.com/babel/babel-plugin-proposal-private-property-in-object"
"@babel/plugin-proposal-private-property-in-object@7.21.11","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-async-generators@7.8.4","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"
"@babel/plugin-syntax-bigint@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint"
"@babel/plugin-syntax-class-properties@7.12.13","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-class-static-block@7.14.5","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-decorators@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-dynamic-import@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-dynamic-import"
"@babel/plugin-syntax-export-default-from@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-export-namespace-from@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-export-namespace-from"
"@babel/plugin-syntax-flow@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-import-assertions@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-import-attributes@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-import-meta@7.10.4","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-json-strings@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"
"@babel/plugin-syntax-jsx@7.12.1","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-jsx@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-logical-assignment-operators@7.10.4","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-nullish-coalescing-operator@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"
"@babel/plugin-syntax-numeric-separator@7.10.4","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-object-rest-spread@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"
"@babel/plugin-syntax-optional-catch-binding@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"
"@babel/plugin-syntax-optional-chaining@7.8.3","MIT","https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"
"@babel/plugin-syntax-private-property-in-object@7.14.5","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-top-level-await@7.14.5","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-typescript@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-syntax-unicode-sets-regex@7.18.6","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-arrow-functions@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-async-generator-functions@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-async-to-generator@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-block-scoped-functions@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-block-scoping@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-class-properties@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-class-static-block@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-classes@7.23.5","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-computed-properties@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-destructuring@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-dotall-regex@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-duplicate-keys@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-dynamic-import@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-exponentiation-operator@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-export-namespace-from@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-flow-strip-types@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-for-of@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-function-name@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-json-strings@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-literals@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-logical-assignment-operators@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-member-expression-literals@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-amd@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-commonjs@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-systemjs@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-modules-umd@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-named-capturing-groups-regex@7.22.5","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-new-target@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-nullish-coalescing-operator@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-numeric-separator@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-object-rest-spread@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-object-super@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-optional-catch-binding@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-optional-chaining@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-parameters@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-private-methods@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-private-property-in-object@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-property-literals@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-react-constant-elements@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-react-display-name@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-react-jsx-development@7.22.5","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-react-jsx@7.23.4","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-react-pure-annotations@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-regenerator@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-reserved-words@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-shorthand-properties@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-spread@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-sticky-regex@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-template-literals@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-typeof-symbol@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-typescript@7.23.5","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-escapes@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-property-regex@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-regex@7.23.3","MIT","https://github.com/babel/babel"
"@babel/plugin-transform-unicode-sets-regex@7.23.3","MIT","https://github.com/babel/babel"
"@babel/preset-env@7.23.5","MIT","https://github.com/babel/babel"
"@babel/preset-flow@7.23.3","MIT","https://github.com/babel/babel"
"@babel/preset-modules@0.1.6-no-external-plugins","MIT","https://github.com/babel/preset-modules"
"@babel/preset-react@7.23.3","MIT","https://github.com/babel/babel"
"@babel/preset-typescript@7.23.3","MIT","https://github.com/babel/babel"
"@babel/register@7.22.15","MIT","https://github.com/babel/babel"
"@babel/regjsgen@0.8.0","MIT","https://github.com/bnjmnt4n/regjsgen"
"@babel/runtime@7.23.5","MIT","https://github.com/babel/babel"
"@babel/runtime@7.5.5","MIT","https://github.com/babel/babel/tree/master/packages/babel-runtime"
"@babel/runtime@7.7.2","MIT","https://github.com/babel/babel"
"@babel/template@7.22.15","MIT","https://github.com/babel/babel"
"@babel/traverse@7.23.5","MIT","https://github.com/babel/babel"
"@babel/types@7.23.5","MIT","https://github.com/babel/babel"
"@base2/pretty-print-object@1.0.1","BSD-2-Clause","https://github.com/Chris-Baker/pretty-print-object"
"@bcoe/v8-coverage@0.2.3","MIT","https://github.com/demurgos/v8-coverage"
"@cnakazawa/watch@1.0.4","Apache-2.0","https://github.com/mikeal/watch"
"@colors/colors@1.5.0","MIT","https://github.com/DABH/colors.js"
"@design-systems/utils@2.12.0","MIT","https://github.com/intuit/design-systems-cli"
"@devtools-ds/object-inspector@1.2.1","MIT","https://github.com/intuit/devtools-ds"
"@devtools-ds/object-parser@1.2.1","MIT","https://github.com/intuit/devtools-ds"
"@devtools-ds/themes@1.2.1","MIT","https://github.com/intuit/devtools-ds"
"@devtools-ds/tree@1.2.1","MIT","https://github.com/intuit/devtools-ds"
"@discoveryjs/json-ext@0.5.7","MIT","https://github.com/discoveryjs/json-ext"
"@emotion/babel-plugin@11.11.0","MIT","https://github.com/emotion-js/emotion/tree/main/packages/babel-plugin"
"@emotion/cache@11.11.0","MIT","https://github.com/emotion-js/emotion/tree/main/packages/cache"
"@emotion/hash@0.9.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/hash"
"@emotion/is-prop-valid@1.2.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/is-prop-valid"
"@emotion/memoize@0.8.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/memoize"
"@emotion/react@11.11.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/react"
"@emotion/serialize@1.1.2","MIT","https://github.com/emotion-js/emotion/tree/main/packages/serialize"
"@emotion/sheet@1.2.2","MIT","https://github.com/emotion-js/emotion/tree/main/packages/sheet"
"@emotion/styled@11.11.0","MIT","https://github.com/emotion-js/emotion/tree/main/packages/styled"
"@emotion/unitless@0.8.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/unitless"
"@emotion/use-insertion-effect-with-fallbacks@1.0.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/use-insertion-effect-with-fallbacks"
"@emotion/utils@1.2.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/utils"
"@emotion/weak-memoize@0.3.1","MIT","https://github.com/emotion-js/emotion/tree/main/packages/weak-memoize"
"@eslint-community/eslint-utils@4.4.0","MIT","https://github.com/eslint-community/eslint-utils"
"@eslint-community/regexpp@4.10.0","MIT","https://github.com/eslint-community/regexpp"
"@eslint/eslintrc@2.1.3","MIT","https://github.com/eslint/eslintrc"
"@eslint/js@8.54.0","MIT","https://github.com/eslint/eslint"
"@faker-js/faker@7.6.0","MIT","https://github.com/faker-js/faker"
"@floating-ui/core@1.5.0","MIT","https://github.com/floating-ui/floating-ui"
"@floating-ui/dom@1.5.3","MIT","https://github.com/floating-ui/floating-ui"
"@floating-ui/utils@0.1.6","MIT","https://github.com/floating-ui/floating-ui"
"@gar/promisify@1.1.3","MIT","https://github.com/wraithgar/gar-promisify"
"@headlessui/react@1.7.13","MIT","https://github.com/tailwindlabs/headlessui"
"@hookform/devtools@4.3.1","MIT","https://github.com/react-hook-form/devtools"
"@hookform/resolvers@2.9.11","MIT","https://github.com/react-hook-form/resolvers"
"@humanwhocodes/config-array@0.11.13","Apache-2.0","https://github.com/humanwhocodes/config-array"
"@humanwhocodes/module-importer@1.0.1","Apache-2.0","https://github.com/humanwhocodes/module-importer"
"@humanwhocodes/object-schema@2.0.1","BSD-3-Clause","https://github.com/humanwhocodes/object-schema"
"@istanbuljs/load-nyc-config@1.1.0","ISC","https://github.com/istanbuljs/load-nyc-config"
"@istanbuljs/schema@0.1.3","MIT","https://github.com/istanbuljs/schema"
"@jest/console@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/core@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/environment@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/expect-utils@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/expect@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/fake-timers@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/globals@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/reporters@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/schemas@29.6.3","MIT","https://github.com/jestjs/jest"
"@jest/source-map@29.6.3","MIT","https://github.com/jestjs/jest"
"@jest/test-result@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/test-sequencer@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/transform@26.6.2","MIT","https://github.com/facebook/jest"
"@jest/transform@29.7.0","MIT","https://github.com/jestjs/jest"
"@jest/types@26.6.2","MIT","https://github.com/facebook/jest"
"@jest/types@27.5.1","MIT","https://github.com/facebook/jest"
"@jest/types@29.6.3","MIT","https://github.com/jestjs/jest"
"@jridgewell/gen-mapping@0.3.3","MIT","https://github.com/jridgewell/gen-mapping"
"@jridgewell/resolve-uri@3.1.1","MIT","https://github.com/jridgewell/resolve-uri"
"@jridgewell/set-array@1.1.2","MIT","https://github.com/jridgewell/set-array"
"@jridgewell/source-map@0.3.5","MIT","https://github.com/jridgewell/source-map"
"@jridgewell/sourcemap-codec@1.4.15","MIT","https://github.com/jridgewell/sourcemap-codec"
"@jridgewell/trace-mapping@0.3.20","MIT","https://github.com/jridgewell/trace-mapping"
"@mdx-js/mdx@1.6.22","MIT","https://github.com/mdx-js/mdx"
"@mdx-js/react@1.6.22","MIT","https://github.com/mdx-js/mdx"
"@mdx-js/util@1.6.22","MIT","https://github.com/mdx-js/mdx"
"@mrmlnc/readdir-enhanced@2.2.1","MIT","https://github.com/bigstickcarpet/readdir-enhanced"
"@next/env@12.3.4","MIT","https://github.com/vercel/next.js"
"@next/eslint-plugin-next@12.3.1","MIT","https://github.com/vercel/next.js"
"@next/swc-darwin-arm64@12.3.4","MIT",""
"@nodelib/fs.scandir@2.1.5","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir"
"@nodelib/fs.stat@1.1.3","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat"
"@nodelib/fs.stat@2.0.5","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat"
"@nodelib/fs.walk@1.2.8","MIT","https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk"
"@npmcli/fs@1.1.1","ISC",""
"@npmcli/move-file@1.1.2","MIT","https://github.com/npm/move-file"
"@panva/hkdf@1.1.1","MIT","https://github.com/panva/hkdf"
"@playwright/test@1.40.1","Apache-2.0","https://github.com/microsoft/playwright"
"@pmmmwh/react-refresh-webpack-plugin@0.5.11","MIT","https://github.com/pmmmwh/react-refresh-webpack-plugin"
"@rushstack/eslint-patch@1.6.0","MIT","https://github.com/microsoft/rushstack"
"@sinclair/typebox@0.27.8","MIT","https://github.com/sinclairzx81/typebox"
"@sinonjs/commons@3.0.0","BSD-3-Clause","https://github.com/sinonjs/commons"
"@sinonjs/fake-timers@10.3.0","BSD-3-Clause","https://github.com/sinonjs/fake-timers"
"@storybook/addon-actions@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-backgrounds@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-controls@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-docs@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-essentials@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-interactions@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-links@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-measure@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-outline@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-postcss@2.0.0","MIT","https://github.com/storybookjs/addon-postcss"
"@storybook/addon-toolbars@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addon-viewport@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/addons@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/api@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/builder-webpack4@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/builder-webpack5@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/channel-postmessage@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/channel-websocket@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/channels@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/client-api@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/client-logger@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/components@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/core-client@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/core-common@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/core-events@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/core-server@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/core@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/csf-tools@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/csf@0.0.1","MIT","https://github.com/storybookjs/csf"
"@storybook/csf@0.0.2--canary.4566f4d.1","MIT","https://github.com/ComponentDriven/csf"
"@storybook/docs-tools@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/instrumenter@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/manager-webpack4@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/manager-webpack5@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/mdx1-csf@0.0.1","MIT","https://github.com/storybookjs/csf-mdx1"
"@storybook/node-logger@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/postinstall@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/preview-web@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/react-docgen-typescript-plugin@1.0.2-canary.6.9d540b91e815f8fc2f8829189deb00553559ff63.0","MIT","https://github.com/storybookjs/react-docgen-typescript-plugin"
"@storybook/react@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/router@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/semver@7.3.2","ISC","https://github.com/storybookjs/browser-semver"
"@storybook/source-loader@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/store@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/telemetry@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/testing-library@0.0.13","MIT","https://github.com/storybookjs/testing-library"
"@storybook/theming@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@storybook/ui@6.5.16","MIT","https://github.com/storybookjs/storybook"
"@svgr/babel-plugin-add-jsx-attribute@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-add-jsx-attribute"
"@svgr/babel-plugin-remove-jsx-attribute@8.0.0","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-remove-jsx-attribute"
"@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-remove-jsx-empty-expression"
"@svgr/babel-plugin-replace-jsx-attribute-value@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-replace-jsx-attribute-value"
"@svgr/babel-plugin-svg-dynamic-title@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-svg-dynamic-title"
"@svgr/babel-plugin-svg-em-dimensions@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-svg-em-dimensions"
"@svgr/babel-plugin-transform-react-native-svg@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-transform-react-native-svg"
"@svgr/babel-plugin-transform-svg-component@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-transform-svg-component"
"@svgr/babel-preset@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/babel-preset"
"@svgr/core@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/core"
"@svgr/hast-util-to-babel-ast@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/hast-util-to-babel-ast"
"@svgr/plugin-jsx@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/plugin-jsx"
"@svgr/plugin-svgo@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/plugin-svgo"
"@svgr/webpack@6.5.1","MIT","https://github.com/gregberge/svgr/tree/main/packages/webpack"
"@swc/helpers@0.4.11","MIT","https://github.com/swc-project/swc"
"@tailwindcss/forms@0.5.7","MIT","https://github.com/tailwindlabs/tailwindcss-forms"
"@tanstack/match-sorter-utils@8.8.4","MIT","https://github.com/tanstack/table"
"@tanstack/query-core@4.36.1","MIT","https://github.com/TanStack/query"
"@tanstack/react-query-devtools@4.36.1","MIT","https://github.com/TanStack/query"
"@tanstack/react-query@4.36.1","MIT","https://github.com/TanStack/query"
"@testing-library/dom@8.20.1","MIT","https://github.com/testing-library/dom-testing-library"
"@testing-library/user-event@13.5.0","MIT","https://github.com/testing-library/user-event"
"@tomfreudenberg/next-auth-mock@0.5.6","MIT","https://github.com/TomFreudenberg/next-auth-mock"
"@trysound/sax@0.2.0","ISC","https://github.com/svg/sax"
"@types/aria-query@5.0.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/babel__core@7.20.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/babel__generator@7.6.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/babel__template@7.4.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/babel__traverse@7.20.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/codemirror@5.60.15","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-array@3.2.1","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-color@3.1.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-ease@3.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-interpolate@3.0.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-path@3.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-scale@4.0.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-shape@3.1.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-time@3.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/d3-timer@3.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/debug@4.1.12","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/eslint-scope@3.7.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/eslint@8.44.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/estree@0.0.51","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/estree@1.0.5","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/glob@7.2.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/glob@8.1.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/graceful-fs@4.1.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/hast@2.3.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/html-minifier-terser@5.1.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/html-minifier-terser@6.1.0","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/is-function@1.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/istanbul-lib-coverage@2.0.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/istanbul-lib-report@3.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/istanbul-reports@3.0.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/javascript-color-gradient@2.4.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/json-schema@7.0.15","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/json5@0.0.29","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/lodash@4.14.202","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/marked@4.3.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/mdast@3.0.15","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/minimatch@5.1.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/ms@0.7.34","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/node-fetch@2.6.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/node@16.18.65","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/node@18.8.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/normalize-package-data@2.4.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/npmlog@4.1.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/parse-json@4.0.2","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/parse5@5.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/pretty-hrtime@1.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/prop-types@15.7.11","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/qs@6.9.10","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/react-dom@18.0.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/react@18.0.21","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/scheduler@0.16.8","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/semver@7.5.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/source-list-map@0.1.6","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/stack-utils@2.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/tapable@1.0.12","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/tern@0.23.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/uglify-js@3.17.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/unist@2.0.10","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/uuid@9.0.7","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/webpack-env@1.18.4","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/webpack-sources@3.2.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/webpack@4.41.38","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/yargs-parser@21.0.3","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/yargs@15.0.19","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/yargs@16.0.9","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@types/yargs@17.0.32","MIT","https://github.com/DefinitelyTyped/DefinitelyTyped"
"@typescript-eslint/eslint-plugin@5.62.0","MIT","https://github.com/typescript-eslint/typescript-eslint"
"@typescript-eslint/parser@5.62.0","BSD-2-Clause","https://github.com/typescript-eslint/typescript-eslint"
"@typescript-eslint/scope-manager@5.62.0","MIT","https://github.com/typescript-eslint/typescript-eslint"
"@typescript-eslint/type-utils@5.62.0","MIT","https://github.com/typescript-eslint/typescript-eslint"
"@typescript-eslint/types@5.62.0","MIT","https://github.com/typescript-eslint/typescript-eslint"
"@typescript-eslint/typescript-estree@5.62.0","BSD-2-Clause","https://github.com/typescript-eslint/typescript-eslint"
"@typescript-eslint/utils@5.62.0","MIT","https://github.com/typescript-eslint/typescript-eslint"
"@typescript-eslint/visitor-keys@5.62.0","MIT","https://github.com/typescript-eslint/typescript-eslint"
"@ungap/structured-clone@1.2.0","ISC","https://github.com/ungap/structured-clone"
"@webassemblyjs/ast@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/ast@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/floating-point-hex-parser@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/floating-point-hex-parser@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-api-error@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-api-error@1.9.0","MIT",""
"@webassemblyjs/helper-buffer@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-buffer@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-code-frame@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-fsm@1.9.0","ISC",""
"@webassemblyjs/helper-module-context@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-numbers@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-wasm-bytecode@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-wasm-bytecode@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-wasm-section@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/helper-wasm-section@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/ieee754@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/ieee754@1.9.0","MIT",""
"@webassemblyjs/leb128@1.11.6","Apache-2.0","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/leb128@1.9.0","MIT",""
"@webassemblyjs/utf8@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/utf8@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-edit@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-edit@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-gen@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-gen@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-opt@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-opt@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-parser@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wasm-parser@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wast-parser@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wast-printer@1.11.6","MIT","https://github.com/xtuc/webassemblyjs"
"@webassemblyjs/wast-printer@1.9.0","MIT","https://github.com/xtuc/webassemblyjs"
"@xtuc/ieee754@1.2.0","BSD-3-Clause","https://github.com/feross/ieee754"
"@xtuc/long@4.2.2","Apache-2.0","https://github.com/dcodeIO/long.js"
"accepts@1.3.8","MIT","https://github.com/jshttp/accepts"
"acorn-import-assertions@1.9.0","MIT","https://github.com/xtuc/acorn-import-assertions"
"acorn-jsx@5.3.2","MIT","https://github.com/acornjs/acorn-jsx"
"acorn-walk@7.2.0","MIT","https://github.com/acornjs/acorn"
"acorn@6.4.2","MIT","https://github.com/acornjs/acorn"
"acorn@7.4.1","MIT","https://github.com/acornjs/acorn"
"acorn@8.11.2","MIT","https://github.com/acornjs/acorn"
"address@1.2.2","MIT","https://github.com/node-modules/address"
"adjust-sourcemap-loader@4.0.0","MIT","https://github.com/bholloway/adjust-sourcemap-loader"
"aggregate-error@3.1.0","MIT","https://github.com/sindresorhus/aggregate-error"
"airbnb-js-shims@2.2.1","MIT","https://github.com/airbnb/js-shims"
"ajv-errors@1.0.1","MIT","https://github.com/epoberezkin/ajv-errors"
"ajv-formats@2.1.1","MIT","https://github.com/ajv-validator/ajv-formats"
"ajv-keywords@3.5.2","MIT","https://github.com/epoberezkin/ajv-keywords"
"ajv-keywords@5.1.0","MIT","https://github.com/epoberezkin/ajv-keywords"
"ajv@6.12.6","MIT","https://github.com/ajv-validator/ajv"
"ajv@8.12.0","MIT","https://github.com/ajv-validator/ajv"
"ansi-align@3.0.1","ISC","https://github.com/nexdrew/ansi-align"
"ansi-colors@3.2.4","MIT","https://github.com/doowb/ansi-colors"
"ansi-escapes@4.3.2","MIT","https://github.com/sindresorhus/ansi-escapes"
"ansi-html-community@0.0.8","Apache-2.0","https://github.com/mahdyar/ansi-html-community"
"ansi-regex@2.1.1","MIT","https://github.com/chalk/ansi-regex"
"ansi-regex@5.0.1","MIT","https://github.com/chalk/ansi-regex"
"ansi-styles@3.2.1","MIT","https://github.com/chalk/ansi-styles"
"ansi-styles@4.3.0","MIT","https://github.com/chalk/ansi-styles"
"ansi-styles@5.2.0","MIT","https://github.com/chalk/ansi-styles"
"ansi-to-html@0.6.15","MIT","https://github.com/rburns/ansi-to-html"
"any-promise@1.3.0","MIT","https://github.com/kevinbeaty/any-promise"
"anymatch@2.0.0","ISC","https://github.com/micromatch/anymatch"
"anymatch@3.1.3","ISC","https://github.com/micromatch/anymatch"
"app-root-dir@1.0.2","MIT","https://github.com/philidem/node-app-root-dir"
"aproba@1.2.0","ISC","https://github.com/iarna/aproba"
"aproba@2.0.0","ISC","https://github.com/iarna/aproba"
"are-we-there-yet@2.0.0","ISC","https://github.com/npm/are-we-there-yet"
"arg@5.0.2","MIT","https://github.com/vercel/arg"
"argparse@1.0.10","MIT","https://github.com/nodeca/argparse"
"argparse@2.0.1","Python-2.0","https://github.com/nodeca/argparse"
"aria-query@5.1.3","Apache-2.0","https://github.com/A11yance/aria-query"
"aria-query@5.3.0","Apache-2.0","https://github.com/A11yance/aria-query"
"arr-diff@4.0.0","MIT","https://github.com/jonschlinkert/arr-diff"
"arr-flatten@1.1.0","MIT","https://github.com/jonschlinkert/arr-flatten"
"arr-union@3.1.0","MIT","https://github.com/jonschlinkert/arr-union"
"array-buffer-byte-length@1.0.0","MIT","https://github.com/inspect-js/array-buffer-byte-length"
"array-find-index@1.0.2","MIT","https://github.com/sindresorhus/array-find-index"
"array-flatten@1.1.1","MIT","https://github.com/blakeembrey/array-flatten"
"array-includes@3.1.7","MIT","https://github.com/es-shims/array-includes"
"array-union@1.0.2","MIT","https://github.com/sindresorhus/array-union"
"array-union@2.1.0","MIT","https://github.com/sindresorhus/array-union"
"array-uniq@1.0.3","MIT","https://github.com/sindresorhus/array-uniq"
"array-unique@0.3.2","MIT","https://github.com/jonschlinkert/array-unique"
"array.prototype.findlastindex@1.2.3","MIT","https://github.com/es-shims/Array.prototype.findLastIndex"
"array.prototype.flat@1.3.2","MIT","https://github.com/es-shims/Array.prototype.flat"
"array.prototype.flatmap@1.3.2","MIT","https://github.com/es-shims/Array.prototype.flatMap"
"array.prototype.map@1.0.6","MIT","https://github.com/es-shims/Array.prototype.map"
"array.prototype.reduce@1.0.6","MIT","https://github.com/es-shims/Array.prototype.reduce"
"array.prototype.tosorted@1.1.2","MIT","https://github.com/es-shims/Array.prototype.toSorted"
"arraybuffer.prototype.slice@1.0.2","MIT","https://github.com/es-shims/ArrayBuffer.prototype.slice"
"arrify@2.0.1","MIT","https://github.com/sindresorhus/arrify"
"asn1.js@5.4.1","MIT","https://github.com/indutny/asn1.js"
"assert@1.5.1","MIT","https://github.com/browserify/commonjs-assert"
"assign-symbols@1.0.0","MIT","https://github.com/jonschlinkert/assign-symbols"
"ast-types-flow@0.0.8","MIT","https://github.com/kyldvs/ast-types-flow"
"ast-types@0.14.2","MIT","https://github.com/benjamn/ast-types"
"async-each@1.0.6","MIT","https://github.com/paulmillr/async-each"
"asynciterator.prototype@1.0.0","MIT","https://github.com/ljharb/AsyncIterator.prototype"
"asynckit@0.4.0","MIT","https://github.com/alexindigo/asynckit"
"at-least-node@1.0.0","ISC","https://github.com/RyanZim/at-least-node"
"atob@2.1.2","(MIT OR Apache-2.0)","git://git.coolaj86.com/coolaj86/atob.js"
"attr-accept@2.2.2","MIT","https://github.com/react-dropzone/attr-accept"
"autoprefixer@10.4.16","MIT","https://github.com/postcss/autoprefixer"
"autoprefixer@9.8.8","MIT","https://github.com/postcss/autoprefixer"
"available-typed-arrays@1.0.5","MIT","https://github.com/inspect-js/available-typed-arrays"
"axe-core@4.7.0","MPL-2.0","https://github.com/dequelabs/axe-core"
"axios@1.6.2","MIT","https://github.com/axios/axios"
"axobject-query@3.2.1","Apache-2.0","https://github.com/A11yance/axobject-query"
"babel-jest@29.7.0","MIT","https://github.com/jestjs/jest"
"babel-loader@8.3.0","MIT","https://github.com/babel/babel-loader"
"babel-loader@9.1.3","MIT","https://github.com/babel/babel-loader"
"babel-plugin-add-react-displayname@0.0.5","MIT","https://github.com/opbeat/babel-plugin-add-react-displayname"
"babel-plugin-apply-mdx-type-prop@1.6.22","MIT","https://github.com/mdx-js/mdx"
"babel-plugin-extract-import-names@1.6.22","MIT","https://github.com/mdx-js/mdx"
"babel-plugin-istanbul@6.1.1","BSD-3-Clause","https://github.com/istanbuljs/babel-plugin-istanbul"
"babel-plugin-jest-hoist@29.6.3","MIT","https://github.com/jestjs/jest"
"babel-plugin-macros@3.1.0","MIT","https://github.com/kentcdodds/babel-plugin-macros"
"babel-plugin-named-exports-order@0.0.2","MIT",""
"babel-plugin-polyfill-corejs2@0.4.6","MIT","https://github.com/babel/babel-polyfills"
"babel-plugin-polyfill-corejs3@0.1.7","MIT","https://github.com/babel/babel-polyfills"
"babel-plugin-polyfill-corejs3@0.8.6","MIT","https://github.com/babel/babel-polyfills"
"babel-plugin-polyfill-regenerator@0.5.3","MIT","https://github.com/babel/babel-polyfills"
"babel-plugin-react-docgen@4.2.1","MIT","https://github.com/storybooks/babel-plugin-react-docgen"
"babel-preset-current-node-syntax@1.0.1","MIT","https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax"
"babel-preset-jest@29.6.3","MIT","https://github.com/jestjs/jest"
"bail@1.0.5","MIT","https://github.com/wooorm/bail"
"bail@2.0.2","MIT","https://github.com/wooorm/bail"
"balanced-match@1.0.2","MIT","https://github.com/juliangruber/balanced-match"
"base64-js@1.5.1","MIT","https://github.com/beatgammit/base64-js"
"base@0.11.2","MIT","https://github.com/node-base/base"
"better-opn@2.1.1","MIT","https://github.com/ExiaSR/better-opn"
"big-integer@1.6.52","Unlicense","https://github.com/peterolson/BigInteger.js"
"big.js@5.2.2","MIT","https://github.com/MikeMcl/big.js"
"binary-extensions@1.13.1","MIT","https://github.com/sindresorhus/binary-extensions"
"binary-extensions@2.2.0","MIT","https://github.com/sindresorhus/binary-extensions"
"bindings@1.5.0","MIT","https://github.com/TooTallNate/node-bindings"
"bluebird@3.7.2","MIT","https://github.com/petkaantonov/bluebird"
"bn.js@4.12.0","MIT","https://github.com/indutny/bn.js"
"bn.js@5.2.1","MIT","https://github.com/indutny/bn.js"
"body-parser@1.20.1","MIT","https://github.com/expressjs/body-parser"
"boolbase@1.0.0","ISC","https://github.com/fb55/boolbase"
"boxen@5.1.2","MIT","https://github.com/sindresorhus/boxen"
"bplist-parser@0.1.1","MIT","https://github.com/nearinfinity/node-bplist-parser"
"brace-expansion@1.1.11","MIT","https://github.com/juliangruber/brace-expansion"
"braces@2.3.2","MIT","https://github.com/micromatch/braces"
"braces@3.0.2","MIT","https://github.com/micromatch/braces"
"brorand@1.1.0","MIT","https://github.com/indutny/brorand"
"browser-assert@1.2.1","MIT*","https://github.com/socialally/browser-assert"
"browserify-aes@1.2.0","MIT","https://github.com/crypto-browserify/browserify-aes"
"browserify-cipher@1.0.1","MIT","https://github.com/crypto-browserify/browserify-cipher"
"browserify-des@1.0.2","MIT","https://github.com/crypto-browserify/browserify-des"
"browserify-rsa@4.1.0","MIT","https://github.com/crypto-browserify/browserify-rsa"
"browserify-sign@4.2.2","ISC","https://github.com/crypto-browserify/browserify-sign"
"browserify-zlib@0.2.0","MIT","https://github.com/devongovett/browserify-zlib"
"browserslist@4.22.1","MIT","https://github.com/browserslist/browserslist"
"bser@2.1.1","Apache-2.0","https://github.com/facebook/watchman"
"buffer-from@1.1.2","MIT","https://github.com/LinusU/buffer-from"
"buffer-xor@1.0.3","MIT","https://github.com/crypto-browserify/buffer-xor"
"buffer@4.9.2","MIT","https://github.com/feross/buffer"
"builtin-status-codes@3.0.0","MIT","https://github.com/bendrucker/builtin-status-codes"
"bytes@3.0.0","MIT","https://github.com/visionmedia/bytes.js"
"bytes@3.1.2","MIT","https://github.com/visionmedia/bytes.js"
"c8@7.14.0","ISC","https://github.com/bcoe/c8"
"cacache@12.0.4","ISC","https://github.com/npm/cacache"
"cacache@15.3.0","ISC","https://github.com/npm/cacache"
"cache-base@1.0.1","MIT","https://github.com/jonschlinkert/cache-base"
"call-bind@1.0.5","MIT","https://github.com/ljharb/call-bind"
"call-me-maybe@1.0.2","MIT","https://github.com/limulus/call-me-maybe"
"callsites@3.1.0","MIT","https://github.com/sindresorhus/callsites"
"camel-case@4.1.2","MIT","https://github.com/blakeembrey/change-case"
"camelcase-css@2.0.1","MIT","https://github.com/stevenvachon/camelcase-css"
"camelcase-keys@2.1.0","MIT","https://github.com/sindresorhus/camelcase-keys"
"camelcase@2.1.1","MIT","https://github.com/sindresorhus/camelcase"
"camelcase@5.3.1","MIT","https://github.com/sindresorhus/camelcase"
"camelcase@6.3.0","MIT","https://github.com/sindresorhus/camelcase"
"caniuse-lite@1.0.30001680","CC-BY-4.0","https://github.com/browserslist/caniuse-lite"
"capture-exit@2.0.0","ISC","https://github.com/stefanpenner/capture-exit"
"case-sensitive-paths-webpack-plugin@2.4.0","MIT","https://github.com/Urthen/case-sensitive-paths-webpack-plugin"
"ccount@1.1.0","MIT","https://github.com/wooorm/ccount"
"chalk@2.4.2","MIT","https://github.com/chalk/chalk"
"chalk@4.1.2","MIT","https://github.com/chalk/chalk"
"char-regex@1.0.2","MIT","https://github.com/Richienb/char-regex"
"character-entities-legacy@1.1.4","MIT","https://github.com/wooorm/character-entities-legacy"
"character-entities@1.2.4","MIT","https://github.com/wooorm/character-entities"
"character-entities@2.0.2","MIT","https://github.com/wooorm/character-entities"
"character-reference-invalid@1.1.4","MIT","https://github.com/wooorm/character-reference-invalid"
"chokidar@2.1.8","MIT","https://github.com/paulmillr/chokidar"
"chokidar@3.5.3","MIT","https://github.com/paulmillr/chokidar"
"chownr@1.1.4","ISC","https://github.com/isaacs/chownr"
"chownr@2.0.0","ISC","https://github.com/isaacs/chownr"
"chrome-trace-event@1.0.3","MIT","https://github.com/samccone/chrome-trace-event"
"ci-info@2.0.0","MIT","https://github.com/watson/ci-info"
"ci-info@3.9.0","MIT","https://github.com/watson/ci-info"
"cipher-base@1.0.4","MIT","https://github.com/crypto-browserify/cipher-base"
"cjs-module-lexer@1.2.3","MIT","https://github.com/nodejs/cjs-module-lexer"
"class-utils@0.3.6","MIT","https://github.com/jonschlinkert/class-utils"
"classnames@2.3.2","MIT","https://github.com/JedWatson/classnames"
"clean-css@4.2.4","MIT","https://github.com/jakubpawlowicz/clean-css"
"clean-css@5.3.2","MIT","https://github.com/clean-css/clean-css"
"clean-stack@2.2.0","MIT","https://github.com/sindresorhus/clean-stack"
"cli-boxes@2.2.1","MIT","https://github.com/sindresorhus/cli-boxes"
"cli-table3@0.6.3","MIT","https://github.com/cli-table/cli-table3"
"client-only@0.0.1","MIT",""
"cliui@7.0.4","ISC","https://github.com/yargs/cliui"
"cliui@8.0.1","ISC","https://github.com/yargs/cliui"
"clone-deep@4.0.1","MIT","https://github.com/jonschlinkert/clone-deep"
"clsx@1.1.0","MIT","https://github.com/lukeed/clsx"
"clsx@1.2.1","MIT","https://github.com/lukeed/clsx"
"clsx@2.0.0","MIT","https://github.com/lukeed/clsx"
"co@4.6.0","MIT","https://github.com/tj/co"
"codemirror-spell-checker@1.1.2","MIT","https://github.com/NextStepWebs/codemirror-spell-checker"
"codemirror@5.65.16","MIT","https://github.com/codemirror/CodeMirror"
"collapse-white-space@1.0.6","MIT","https://github.com/wooorm/collapse-white-space"
"collect-v8-coverage@1.0.2","MIT","https://github.com/SimenB/collect-v8-coverage"
"collection-visit@1.0.0","MIT","https://github.com/jonschlinkert/collection-visit"
"color-convert@1.9.3","MIT","https://github.com/Qix-/color-convert"
"color-convert@2.0.1","MIT","https://github.com/Qix-/color-convert"
"color-name@1.1.3","MIT","https://github.com/dfcreative/color-name"
"color-name@1.1.4","MIT","https://github.com/colorjs/color-name"
"color-support@1.1.3","ISC","https://github.com/isaacs/color-support"
"colorette@1.4.0","MIT","https://github.com/jorgebucaran/colorette"
"combined-stream@1.0.8","MIT","https://github.com/felixge/node-combined-stream"
"comma-separated-tokens@1.0.8","MIT","https://github.com/wooorm/comma-separated-tokens"
"comma-separated-tokens@2.0.3","MIT","https://github.com/wooorm/comma-separated-tokens"
"commander@2.20.3","MIT","https://github.com/tj/commander.js"
"commander@4.1.1","MIT","https://github.com/tj/commander.js"
"commander@6.2.1","MIT","https://github.com/tj/commander.js"
"commander@7.2.0","MIT","https://github.com/tj/commander.js"
"commander@8.3.0","MIT","https://github.com/tj/commander.js"
"common-path-prefix@3.0.0","ISC","https://github.com/novemberborn/common-path-prefix"
"commondir@1.0.1","MIT","https://github.com/substack/node-commondir"
"component-emitter@1.3.1","MIT","https://github.com/sindresorhus/component-emitter"
"compressible@2.0.18","MIT","https://github.com/jshttp/compressible"
"compression@1.7.4","MIT","https://github.com/expressjs/compression"
"concat-map@0.0.1","MIT","https://github.com/substack/node-concat-map"
"concat-stream@1.6.2","MIT","https://github.com/maxogden/concat-stream"
"console-browserify@1.2.0","MIT","https://github.com/browserify/console-browserify"
"console-control-strings@1.1.0","ISC","https://github.com/iarna/console-control-strings"
"constants-browserify@1.0.0","MIT","https://github.com/juliangruber/constants-browserify"
"content-disposition@0.5.4","MIT","https://github.com/jshttp/content-disposition"
"content-type@1.0.5","MIT","https://github.com/jshttp/content-type"
"convert-source-map@1.9.0","MIT","https://github.com/thlorenz/convert-source-map"
"convert-source-map@2.0.0","MIT","https://github.com/thlorenz/convert-source-map"
"cookie-signature@1.0.6","MIT","https://github.com/visionmedia/node-cookie-signature"
"cookie@0.5.0","MIT","https://github.com/jshttp/cookie"
"copy-anything@3.0.5","MIT","https://github.com/mesqueeb/copy-anything"
"copy-concurrently@1.0.5","ISC","https://github.com/npm/copy-concurrently"
"copy-descriptor@0.1.1","MIT","https://github.com/jonschlinkert/copy-descriptor"
"core-js-compat@3.33.3","MIT","https://github.com/zloirock/core-js"
"core-js-pure@3.33.3","MIT","https://github.com/zloirock/core-js"
"core-js@3.33.3","MIT","https://github.com/zloirock/core-js"
"core-util-is@1.0.3","MIT","https://github.com/isaacs/core-util-is"
"cosmiconfig@6.0.0","MIT","https://github.com/davidtheclark/cosmiconfig"
"cosmiconfig@7.1.0","MIT","https://github.com/davidtheclark/cosmiconfig"
"cosmiconfig@8.3.6","MIT","https://github.com/cosmiconfig/cosmiconfig"
"cp-file@7.0.0","MIT","https://github.com/sindresorhus/cp-file"
"cpy@8.1.2","MIT","https://github.com/sindresorhus/cpy"
"create-ecdh@4.0.4","MIT","https://github.com/crypto-browserify/createECDH"
"create-hash@1.2.0","MIT","https://github.com/crypto-browserify/createHash"
"create-hmac@1.1.7","MIT","https://github.com/crypto-browserify/createHmac"
"create-jest@29.7.0","MIT","https://github.com/jestjs/jest"
"cross-spawn@6.0.5","MIT","https://github.com/moxystudio/node-cross-spawn"
"cross-spawn@7.0.3","MIT","https://github.com/moxystudio/node-cross-spawn"
"crypto-browserify@3.12.0","MIT","https://github.com/crypto-browserify/crypto-browserify"
"css-loader@3.6.0","MIT","https://github.com/webpack-contrib/css-loader"
"css-loader@5.2.7","MIT","https://github.com/webpack-contrib/css-loader"
"css-select@4.3.0","BSD-2-Clause","https://github.com/fb55/css-select"
"css-tree@1.1.3","MIT","https://github.com/csstree/csstree"
"css-what@6.1.0","BSD-2-Clause","https://github.com/fb55/css-what"
"cssesc@3.0.0","MIT","https://github.com/mathiasbynens/cssesc"
"csso@4.2.0","MIT","https://github.com/css/csso"
"csstype@3.1.2","MIT","https://github.com/frenic/csstype"
"currency-symbol-map@5.1.0","BSD-2-Clause","https://github.com/bengourley/currency-symbol-map"
"currently-unhandled@0.4.1","MIT","https://github.com/jamestalmage/currently-unhandled"
"cyclist@1.0.2","MIT","https://github.com/mafintosh/cyclist"
"d3-array@3.2.4","ISC","https://github.com/d3/d3-array"
"d3-color@3.1.0","ISC","https://github.com/d3/d3-color"
"d3-ease@3.0.1","BSD-3-Clause","https://github.com/d3/d3-ease"
"d3-format@3.1.0","ISC","https://github.com/d3/d3-format"
"d3-interpolate@3.0.1","ISC","https://github.com/d3/d3-interpolate"
"d3-path@3.1.0","ISC","https://github.com/d3/d3-path"
"d3-scale@4.0.2","ISC","https://github.com/d3/d3-scale"
"d3-shape@3.2.0","ISC","https://github.com/d3/d3-shape"
"d3-time-format@4.1.0","ISC","https://github.com/d3/d3-time-format"
"d3-time@3.1.0","ISC","https://github.com/d3/d3-time"
"d3-timer@3.0.1","ISC","https://github.com/d3/d3-timer"
"damerau-levenshtein@1.0.8","BSD-2-Clause","https://github.com/tad-lispy/node-damerau-levenshtein"
"date-fns@2.30.0","MIT","https://github.com/date-fns/date-fns"
"debug@2.6.9","MIT","https://github.com/visionmedia/debug"
"debug@3.2.7","MIT","https://github.com/visionmedia/debug"
"debug@4.3.4","MIT","https://github.com/debug-js/debug"
"decamelize@1.2.0","MIT","https://github.com/sindresorhus/decamelize"
"decimal.js-light@2.5.1","MIT","https://github.com/MikeMcl/decimal.js-light"
"decimal.js@10.4.3","MIT","https://github.com/MikeMcl/decimal.js"
"decode-named-character-reference@1.0.2","MIT","https://github.com/wooorm/decode-named-character-reference"
"decode-uri-component@0.2.2","MIT","https://github.com/SamVerschueren/decode-uri-component"
"dedent@0.7.0","MIT","https://github.com/dmnd/dedent"
"dedent@1.5.1","MIT","https://github.com/dmnd/dedent"
"deep-equal@2.2.3","MIT","https://github.com/inspect-js/node-deep-equal"
"deep-is@0.1.4","MIT","https://github.com/thlorenz/deep-is"
"deepmerge@4.3.1","MIT","https://github.com/TehShrike/deepmerge"
"default-browser-id@1.0.4","MIT","https://github.com/sindresorhus/default-browser-id"
"define-data-property@1.1.1","MIT","https://github.com/ljharb/define-data-property"
"define-lazy-prop@2.0.0","MIT","https://github.com/sindresorhus/define-lazy-prop"
"define-properties@1.2.1","MIT","https://github.com/ljharb/define-properties"
"define-property@0.2.5","MIT","https://github.com/jonschlinkert/define-property"
"define-property@1.0.0","MIT","https://github.com/jonschlinkert/define-property"
"define-property@2.0.2","MIT","https://github.com/jonschlinkert/define-property"
"delayed-stream@1.0.0","MIT","https://github.com/felixge/node-delayed-stream"
"delegates@1.0.0","MIT","https://github.com/visionmedia/node-delegates"
"depd@2.0.0","MIT","https://github.com/dougwilson/nodejs-depd"
"dequal@2.0.3","MIT","https://github.com/lukeed/dequal"
"des.js@1.1.0","MIT","https://github.com/indutny/des.js"
"destroy@1.2.0","MIT","https://github.com/stream-utils/destroy"
"detab@2.0.4","MIT","https://github.com/wooorm/detab"
"detect-newline@3.1.0","MIT","https://github.com/sindresorhus/detect-newline"
"detect-package-manager@2.0.1","MIT","https://github.com/egoist/detect-package-manager"
"detect-port@1.5.1","MIT","https://github.com/node-modules/detect-port"
"didyoumean@1.2.2","Apache-2.0","https://github.com/dcporter/didyoumean.js"
"diff-sequences@29.6.3","MIT","https://github.com/jestjs/jest"
"diff@5.1.0","BSD-3-Clause","https://github.com/kpdecker/jsdiff"
"diffie-hellman@5.0.3","MIT","https://github.com/crypto-browserify/diffie-hellman"
"dir-glob@2.2.2","MIT","https://github.com/kevva/dir-glob"
"dir-glob@3.0.1","MIT","https://github.com/kevva/dir-glob"
"dlv@1.1.3","MIT","https://github.com/developit/dlv"
"doctrine@2.1.0","Apache-2.0","https://github.com/eslint/doctrine"
"doctrine@3.0.0","Apache-2.0","https://github.com/eslint/doctrine"
"dom-accessibility-api@0.5.16","MIT","https://github.com/eps1lon/dom-accessibility-api"
"dom-converter@0.2.0","MIT","https://github.com/AriaMinaei/dom-converter"
"dom-helpers@3.4.0","MIT","https://github.com/jquense/dom-helpers"
"dom-serializer@1.4.1","MIT","https://github.com/cheeriojs/dom-renderer"
"dom-walk@0.1.2","MIT","https://github.com/Raynos/dom-walk"
"domain-browser@1.2.0","MIT","https://github.com/bevry/domain-browser"
"domelementtype@2.3.0","BSD-2-Clause","https://github.com/fb55/domelementtype"
"domhandler@4.3.1","BSD-2-Clause","https://github.com/fb55/domhandler"
"domutils@2.8.0","BSD-2-Clause","https://github.com/fb55/domutils"
"dot-case@3.0.4","MIT","https://github.com/blakeembrey/change-case"
"dotenv-expand@5.1.0","BSD-2-Clause",""
"dotenv@8.6.0","BSD-2-Clause","https://github.com/motdotla/dotenv"
"duplexify@3.7.1","MIT","https://github.com/mafintosh/duplexify"
"easymde@2.18.0","MIT","https://github.com/Ionaru/easy-markdown-editor"
"ee-first@1.1.1","MIT","https://github.com/jonathanong/ee-first"
"electron-to-chromium@1.4.596","ISC","https://github.com/kilian/electron-to-chromium"
"elliptic@6.5.4","MIT","https://github.com/indutny/elliptic"
"emittery@0.13.1","MIT","https://github.com/sindresorhus/emittery"
"emoji-regex@8.0.0","MIT","https://github.com/mathiasbynens/emoji-regex"
"emoji-regex@9.2.2","MIT","https://github.com/mathiasbynens/emoji-regex"
"emojis-list@3.0.0","MIT","https://github.com/kikobeats/emojis-list"
"encodeurl@1.0.2","MIT","https://github.com/pillarjs/encodeurl"
"end-of-stream@1.4.4","MIT","https://github.com/mafintosh/end-of-stream"
"endent@2.1.0","MIT","https://github.com/ZhouHansen/endent"
"enhanced-resolve@4.5.0","MIT","https://github.com/webpack/enhanced-resolve"
"enhanced-resolve@5.15.0","MIT","https://github.com/webpack/enhanced-resolve"
"entities@2.2.0","BSD-2-Clause","https://github.com/fb55/entities"
"entities@4.5.0","BSD-2-Clause","https://github.com/fb55/entities"
"errno@0.1.8","MIT","https://github.com/rvagg/node-errno"
"error-ex@1.3.2","MIT","https://github.com/qix-/node-error-ex"
"error-stack-parser@2.1.4","MIT","https://github.com/stacktracejs/error-stack-parser"
"es-abstract@1.22.3","MIT","https://github.com/ljharb/es-abstract"
"es-array-method-boxes-properly@1.0.0","MIT","https://github.com/ljharb/es-array-method-boxes-properly"
"es-get-iterator@1.1.3","MIT","https://github.com/ljharb/es-get-iterator"
"es-iterator-helpers@1.0.15","MIT","https://github.com/es-shims/iterator-helpers"
"es-module-lexer@1.4.1","MIT","https://github.com/guybedford/es-module-lexer"
"es-set-tostringtag@2.0.2","MIT","https://github.com/es-shims/es-set-tostringtag"
"es-shim-unscopables@1.0.2","MIT","https://github.com/ljharb/es-shim-unscopables"
"es-to-primitive@1.2.1","MIT","https://github.com/ljharb/es-to-primitive"
"es5-shim@4.6.7","MIT","https://github.com/es-shims/es5-shim"
"es6-shim@0.35.8","MIT","https://github.com/paulmillr/es6-shim"
"escalade@3.1.1","MIT","https://github.com/lukeed/escalade"
"escape-html@1.0.3","MIT","https://github.com/component/escape-html"
"escape-string-regexp@1.0.5","MIT","https://github.com/sindresorhus/escape-string-regexp"
"escape-string-regexp@2.0.0","MIT","https://github.com/sindresorhus/escape-string-regexp"
"escape-string-regexp@4.0.0","MIT","https://github.com/sindresorhus/escape-string-regexp"
"escodegen@2.1.0","BSD-2-Clause","https://github.com/estools/escodegen"
"eslint-config-next@12.3.1","MIT","https://github.com/vercel/next.js"
"eslint-config-prettier@8.10.0","MIT","https://github.com/prettier/eslint-config-prettier"
"eslint-import-resolver-node@0.3.9","MIT","https://github.com/import-js/eslint-plugin-import"
"eslint-import-resolver-typescript@2.7.1","ISC","https://github.com/alexgorbatchev/eslint-import-resolver-typescript"
"eslint-module-utils@2.8.0","MIT","https://github.com/import-js/eslint-plugin-import"
"eslint-plugin-import@2.29.0","MIT","https://github.com/import-js/eslint-plugin-import"
"eslint-plugin-jsx-a11y@6.8.0","MIT","https://github.com/jsx-eslint/eslint-plugin-jsx-a11y"
"eslint-plugin-prettier@4.2.1","MIT","https://github.com/prettier/eslint-plugin-prettier"
"eslint-plugin-react-hooks@4.6.0","MIT","https://github.com/facebook/react"
"eslint-plugin-react@7.33.2","MIT","https://github.com/jsx-eslint/eslint-plugin-react"
"eslint-plugin-storybook@0.6.15","MIT","https://github.com/storybookjs/eslint-plugin-storybook"
"eslint-scope@4.0.3","BSD-2-Clause","https://github.com/eslint/eslint-scope"
"eslint-scope@5.1.1","BSD-2-Clause","https://github.com/eslint/eslint-scope"
"eslint-scope@7.2.2","BSD-2-Clause","https://github.com/eslint/eslint-scope"
"eslint-visitor-keys@3.4.3","Apache-2.0","https://github.com/eslint/eslint-visitor-keys"
"eslint@8.54.0","MIT","https://github.com/eslint/eslint"
"espree@9.6.1","BSD-2-Clause","https://github.com/eslint/espree"
"esprima@4.0.1","BSD-2-Clause","https://github.com/jquery/esprima"
"esquery@1.5.0","BSD-3-Clause","https://github.com/estools/esquery"
"esrecurse@4.3.0","BSD-2-Clause","https://github.com/estools/esrecurse"
"estraverse@4.3.0","BSD-2-Clause","https://github.com/estools/estraverse"
"estraverse@5.3.0","BSD-2-Clause","https://github.com/estools/estraverse"
"estree-to-babel@3.2.1","MIT","https://github.com/coderaiser/estree-to-babel"
"esutils@2.0.3","BSD-2-Clause","https://github.com/estools/esutils"
"etag@1.8.1","MIT","https://github.com/jshttp/etag"
"eventemitter3@4.0.7","MIT","https://github.com/primus/eventemitter3"
"events@3.3.0","MIT","https://github.com/Gozala/events"
"evp_bytestokey@1.0.3","MIT","https://github.com/crypto-browserify/EVP_BytesToKey"
"exec-sh@0.3.6","MIT","https://github.com/tsertkov/exec-sh"
"execa@1.0.0","MIT","https://github.com/sindresorhus/execa"
"execa@5.1.1","MIT","https://github.com/sindresorhus/execa"
"exit@0.1.2","MIT","https://github.com/cowboy/node-exit"
"expand-brackets@2.1.4","MIT","https://github.com/jonschlinkert/expand-brackets"
"expect@29.7.0","MIT","https://github.com/jestjs/jest"
"express@4.18.2","MIT","https://github.com/expressjs/express"
"extend-shallow@2.0.1","MIT","https://github.com/jonschlinkert/extend-shallow"
"extend-shallow@3.0.2","MIT","https://github.com/jonschlinkert/extend-shallow"
"extend@3.0.2","MIT","https://github.com/justmoon/node-extend"
"extglob@2.0.4","MIT","https://github.com/micromatch/extglob"
"fast-deep-equal@3.1.3","MIT","https://github.com/epoberezkin/fast-deep-equal"
"fast-diff@1.3.0","Apache-2.0","https://github.com/jhchen/fast-diff"
"fast-equals@5.0.1","MIT","https://github.com/planttheidea/fast-equals"
"fast-glob@2.2.7","MIT","https://github.com/mrmlnc/fast-glob"
"fast-glob@3.3.2","MIT","https://github.com/mrmlnc/fast-glob"
"fast-json-parse@1.0.3","MIT","https://github.com/mcollina/fast-json-parse"
"fast-json-stable-stringify@2.1.0","MIT","https://github.com/epoberezkin/fast-json-stable-stringify"
"fast-levenshtein@2.0.6","MIT","https://github.com/hiddentao/fast-levenshtein"
"fastq@1.15.0","ISC","https://github.com/mcollina/fastq"
"fb-watchman@2.0.2","Apache-2.0","https://github.com/facebook/watchman"
"fetch-retry@5.0.6","MIT","https://github.com/jonbern/fetch-retry"
"figgy-pudding@3.5.2","ISC","https://github.com/npm/figgy-pudding"
"file-entry-cache@6.0.1","MIT","https://github.com/royriojas/file-entry-cache"
"file-loader@6.2.0","MIT","https://github.com/webpack-contrib/file-loader"
"file-selector@0.6.0","MIT","https://github.com/react-dropzone/file-selector"
"file-system-cache@1.1.0","MIT","https://github.com/philcockfield/file-system-cache"
"file-uri-to-path@1.0.0","MIT","https://github.com/TooTallNate/file-uri-to-path"
"fill-range@4.0.0","MIT","https://github.com/jonschlinkert/fill-range"
"fill-range@7.0.1","MIT","https://github.com/jonschlinkert/fill-range"
"finalhandler@1.2.0","MIT","https://github.com/pillarjs/finalhandler"
"find-cache-dir@2.1.0","MIT","https://github.com/avajs/find-cache-dir"
"find-cache-dir@3.3.2","MIT","https://github.com/avajs/find-cache-dir"
"find-cache-dir@4.0.0","MIT","https://github.com/sindresorhus/find-cache-dir"
"find-root@1.1.0","MIT","https://github.com/js-n/find-root"
"find-up@1.1.2","MIT","https://github.com/sindresorhus/find-up"
"find-up@3.0.0","MIT","https://github.com/sindresorhus/find-up"
"find-up@4.1.0","MIT","https://github.com/sindresorhus/find-up"
"find-up@5.0.0","MIT","https://github.com/sindresorhus/find-up"
"find-up@6.3.0","MIT","https://github.com/sindresorhus/find-up"
"flat-cache@3.2.0","MIT","https://github.com/jaredwray/flat-cache"
"flatted@3.2.9","ISC","https://github.com/WebReflection/flatted"
"flush-write-stream@1.1.1","MIT","https://github.com/mafintosh/flush-write-stream"
"focus-lock@0.8.1","MIT","https://github.com/theKashey/focus-lock"
"follow-redirects@1.15.3","MIT","https://github.com/follow-redirects/follow-redirects"
"for-each@0.3.3","MIT","https://github.com/Raynos/for-each"
"for-in@1.0.2","MIT","https://github.com/jonschlinkert/for-in"
"foreground-child@2.0.0","ISC","https://github.com/tapjs/foreground-child"
"fork-ts-checker-webpack-plugin@4.1.6","MIT","https://github.com/TypeStrong/fork-ts-checker-webpack-plugin"
"fork-ts-checker-webpack-plugin@6.5.3","MIT","https://github.com/TypeStrong/fork-ts-checker-webpack-plugin"
"form-data@4.0.0","MIT","https://github.com/form-data/form-data"
"forwarded@0.2.0","MIT","https://github.com/jshttp/forwarded"
"fraction.js@4.3.7","MIT","https://github.com/rawify/Fraction.js"
"fragment-cache@0.2.1","MIT","https://github.com/jonschlinkert/fragment-cache"
"fresh@0.5.2","MIT","https://github.com/jshttp/fresh"
"from2@2.3.0","MIT","https://github.com/hughsk/from2"
"fs-extra@10.1.0","MIT","https://github.com/jprichardson/node-fs-extra"
"fs-extra@9.1.0","MIT","https://github.com/jprichardson/node-fs-extra"
"fs-minipass@2.1.0","ISC","https://github.com/npm/fs-minipass"
"fs-monkey@1.0.5","Unlicense","https://github.com/streamich/fs-monkey"
"fs-write-stream-atomic@1.0.10","ISC","https://github.com/npm/fs-write-stream-atomic"
"fs.realpath@1.0.0","ISC","https://github.com/isaacs/fs.realpath"
"fsevents@1.2.13","MIT","https://github.com/strongloop/fsevents"
"fsevents@2.3.2","MIT","https://github.com/fsevents/fsevents"
"fsevents@2.3.3","MIT","https://github.com/fsevents/fsevents"
"function-bind@1.1.2","MIT","https://github.com/Raynos/function-bind"
"function.prototype.name@1.1.6","MIT","https://github.com/es-shims/Function.prototype.name"
"functions-have-names@1.2.3","MIT","https://github.com/inspect-js/functions-have-names"
"gauge@3.0.2","ISC","https://github.com/iarna/gauge"
"gensync@1.0.0-beta.2","MIT","https://github.com/loganfsmyth/gensync"
"get-caller-file@2.0.5","ISC","https://github.com/stefanpenner/get-caller-file"
"get-intrinsic@1.2.2","MIT","https://github.com/ljharb/get-intrinsic"
"get-package-type@0.1.0","MIT","https://github.com/cfware/get-package-type"
"get-stdin@4.0.1","MIT","https://github.com/sindresorhus/get-stdin"
"get-stream@4.1.0","MIT","https://github.com/sindresorhus/get-stream"
"get-stream@6.0.1","MIT","https://github.com/sindresorhus/get-stream"
"get-symbol-description@1.0.0","MIT","https://github.com/inspect-js/get-symbol-description"
"get-value@2.0.6","MIT","https://github.com/jonschlinkert/get-value"
"github-slugger@1.5.0","ISC","https://github.com/Flet/github-slugger"
"glob-parent@3.1.0","ISC","https://github.com/es128/glob-parent"
"glob-parent@5.1.2","ISC","https://github.com/gulpjs/glob-parent"
"glob-parent@6.0.2","ISC","https://github.com/gulpjs/glob-parent"
"glob-promise@3.4.0","ISC","https://github.com/ahmadnassri/glob-promise"
"glob-to-regexp@0.3.0","BSD*","https://github.com/fitzgen/glob-to-regexp"
"glob-to-regexp@0.4.1","BSD-2-Clause","https://github.com/fitzgen/glob-to-regexp"
"glob@7.1.6","ISC","https://github.com/isaacs/node-glob"
"glob@7.1.7","ISC","https://github.com/isaacs/node-glob"
"glob@7.2.3","ISC","https://github.com/isaacs/node-glob"
"global@4.4.0","MIT","https://github.com/Raynos/global"
"globals@11.12.0","MIT","https://github.com/sindresorhus/globals"
"globals@13.23.0","MIT","https://github.com/sindresorhus/globals"
"globalthis@1.0.3","MIT","https://github.com/ljharb/System.global"
"globby@11.1.0","MIT","https://github.com/sindresorhus/globby"
"globby@9.2.0","MIT","https://github.com/sindresorhus/globby"
"gopd@1.0.1","MIT","https://github.com/ljharb/gopd"
"graceful-fs@4.2.11","ISC","https://github.com/isaacs/node-graceful-fs"
"graphemer@1.4.0","MIT","https://github.com/flmnt/graphemer"
"handlebars@4.7.8","MIT","https://github.com/handlebars-lang/handlebars.js"
"has-bigints@1.0.2","MIT","https://github.com/ljharb/has-bigints"
"has-flag@3.0.0","MIT","https://github.com/sindresorhus/has-flag"
"has-flag@4.0.0","MIT","https://github.com/sindresorhus/has-flag"
"has-glob@1.0.0","MIT","https://github.com/jonschlinkert/has-glob"
"has-property-descriptors@1.0.1","MIT","https://github.com/inspect-js/has-property-descriptors"
"has-proto@1.0.1","MIT","https://github.com/inspect-js/has-proto"
"has-symbols@1.0.3","MIT","https://github.com/inspect-js/has-symbols"
"has-tostringtag@1.0.0","MIT","https://github.com/inspect-js/has-tostringtag"
"has-unicode@2.0.1","ISC","https://github.com/iarna/has-unicode"
"has-value@0.3.1","MIT","https://github.com/jonschlinkert/has-value"
"has-value@1.0.0","MIT","https://github.com/jonschlinkert/has-value"
"has-values@0.1.4","MIT","https://github.com/jonschlinkert/has-values"
"has-values@1.0.0","MIT","https://github.com/jonschlinkert/has-values"
"hash-base@3.1.0","MIT","https://github.com/crypto-browserify/hash-base"
"hash.js@1.1.7","MIT","https://github.com/indutny/hash.js"
"hasown@2.0.0","MIT","https://github.com/inspect-js/hasOwn"
"hast-to-hyperscript@9.0.1","MIT","https://github.com/syntax-tree/hast-to-hyperscript"
"hast-util-from-parse5@6.0.1","MIT","https://github.com/syntax-tree/hast-util-from-parse5"
"hast-util-parse-selector@2.2.5","MIT","https://github.com/syntax-tree/hast-util-parse-selector"
"hast-util-raw@6.0.1","MIT","https://github.com/syntax-tree/hast-util-raw"
"hast-util-to-parse5@6.0.0","MIT","https://github.com/syntax-tree/hast-util-to-parse5"
"hast-util-whitespace@2.0.1","MIT","https://github.com/syntax-tree/hast-util-whitespace"
"hastscript@6.0.0","MIT","https://github.com/syntax-tree/hastscript"
"he@1.2.0","MIT","https://github.com/mathiasbynens/he"
"hmac-drbg@1.0.1","MIT","https://github.com/indutny/hmac-drbg"
"hoist-non-react-statics@3.3.2","BSD-3-Clause","https://github.com/mridgway/hoist-non-react-statics"
"hosted-git-info@2.8.9","ISC","https://github.com/npm/hosted-git-info"
"html-entities@2.4.0","MIT","https://github.com/mdevils/html-entities"
"html-escaper@2.0.2","MIT","https://github.com/WebReflection/html-escaper"
"html-minifier-terser@5.1.1","MIT","https://github.com/DanielRuf/html-minifier-terser"
"html-minifier-terser@6.1.0","MIT","https://github.com/terser/html-minifier-terser"
"html-tags@3.3.1","MIT","https://github.com/sindresorhus/html-tags"
"html-void-elements@1.0.5","MIT","https://github.com/wooorm/html-void-elements"
"html-webpack-plugin@4.5.2","MIT","https://github.com/jantimon/html-webpack-plugin"
"html-webpack-plugin@5.5.3","MIT","https://github.com/jantimon/html-webpack-plugin"
"htmlparser2@6.1.0","MIT","https://github.com/fb55/htmlparser2"
"http-errors@2.0.0","MIT","https://github.com/jshttp/http-errors"
"https-browserify@1.0.0","MIT","https://github.com/substack/https-browserify"
"human-signals@2.1.0","Apache-2.0","https://github.com/ehmicky/human-signals"
"iconv-lite@0.4.24","MIT","https://github.com/ashtuchkin/iconv-lite"
"icss-utils@4.1.1","ISC","https://github.com/css-modules/icss-utils"
"icss-utils@5.1.0","ISC","https://github.com/css-modules/icss-utils"
"ieee754@1.2.1","BSD-3-Clause","https://github.com/feross/ieee754"
"iferr@0.1.5","MIT","https://github.com/shesek/iferr"
"ignore@4.0.6","MIT","https://github.com/kaelzhang/node-ignore"
"ignore@5.3.0","MIT","https://github.com/kaelzhang/node-ignore"
"image-size@1.0.2","MIT","https://github.com/image-size/image-size"
"import-fresh@3.3.0","MIT","https://github.com/sindresorhus/import-fresh"
"import-local@3.1.0","MIT","https://github.com/sindresorhus/import-local"
"imurmurhash@0.1.4","MIT","https://github.com/jensyt/imurmurhash-js"
"indent-string@2.1.0","MIT","https://github.com/sindresorhus/indent-string"
"indent-string@4.0.0","MIT","https://github.com/sindresorhus/indent-string"
"infer-owner@1.0.4","ISC","https://github.com/npm/infer-owner"
"inflight@1.0.6","ISC","https://github.com/npm/inflight"
"inherits@2.0.3","ISC","https://github.com/isaacs/inherits"
"inherits@2.0.4","ISC","https://github.com/isaacs/inherits"
"inline-style-parser@0.1.1","MIT","https://github.com/remarkablemark/inline-style-parser"
"internal-slot@1.0.6","MIT","https://github.com/ljharb/internal-slot"
"internmap@2.0.3","ISC","https://github.com/mbostock/internmap"
"interpret@2.2.0","MIT","https://github.com/gulpjs/interpret"
"ip@2.0.0","MIT","https://github.com/indutny/node-ip"
"ipaddr.js@1.9.1","MIT","https://github.com/whitequark/ipaddr.js"
"is-absolute-url@3.0.3","MIT","https://github.com/sindresorhus/is-absolute-url"
"is-accessor-descriptor@1.0.1","MIT","https://github.com/inspect-js/is-accessor-descriptor"
"is-alphabetical@1.0.4","MIT","https://github.com/wooorm/is-alphabetical"
"is-alphanumerical@1.0.4","MIT","https://github.com/wooorm/is-alphanumerical"
"is-arguments@1.1.1","MIT","https://github.com/inspect-js/is-arguments"
"is-array-buffer@3.0.2","MIT","https://github.com/inspect-js/is-array-buffer"
"is-arrayish@0.2.1","MIT","https://github.com/qix-/node-is-arrayish"
"is-async-function@2.0.0","MIT","https://github.com/inspect-js/is-async-function"
"is-bigint@1.0.4","MIT","https://github.com/inspect-js/is-bigint"
"is-binary-path@1.0.1","MIT","https://github.com/sindresorhus/is-binary-path"
"is-binary-path@2.1.0","MIT","https://github.com/sindresorhus/is-binary-path"
"is-boolean-object@1.1.2","MIT","https://github.com/inspect-js/is-boolean-object"
"is-buffer@1.1.6","MIT","https://github.com/feross/is-buffer"
"is-buffer@2.0.5","MIT","https://github.com/feross/is-buffer"
"is-callable@1.2.7","MIT","https://github.com/inspect-js/is-callable"
"is-ci@2.0.0","MIT","https://github.com/watson/is-ci"
"is-core-module@2.13.1","MIT","https://github.com/inspect-js/is-core-module"
"is-data-descriptor@1.0.1","MIT","https://github.com/inspect-js/is-data-descriptor"
"is-date-object@1.0.5","MIT","https://github.com/inspect-js/is-date-object"
"is-decimal@1.0.4","MIT","https://github.com/wooorm/is-decimal"
"is-descriptor@0.1.7","MIT","https://github.com/inspect-js/is-descriptor"
"is-descriptor@1.0.3","MIT","https://github.com/inspect-js/is-descriptor"
"is-docker@2.2.1","MIT","https://github.com/sindresorhus/is-docker"
"is-dom@1.1.0","MIT","https://github.com/npm-dom/is-dom"
"is-extendable@0.1.1","MIT","https://github.com/jonschlinkert/is-extendable"
"is-extendable@1.0.1","MIT","https://github.com/jonschlinkert/is-extendable"
"is-extglob@2.1.1","MIT","https://github.com/jonschlinkert/is-extglob"
"is-finalizationregistry@1.0.2","MIT","https://github.com/inspect-js/is-finalizationregistry"
"is-finite@1.1.0","MIT","https://github.com/sindresorhus/is-finite"
"is-fullwidth-code-point@3.0.0","MIT","https://github.com/sindresorhus/is-fullwidth-code-point"
"is-function@1.0.2","MIT","https://github.com/grncdr/js-is-function"
"is-generator-fn@2.1.0","MIT","https://github.com/sindresorhus/is-generator-fn"
"is-generator-function@1.0.10","MIT","https://github.com/inspect-js/is-generator-function"
"is-glob@3.1.0","MIT","https://github.com/jonschlinkert/is-glob"
"is-glob@4.0.3","MIT","https://github.com/micromatch/is-glob"
"is-hexadecimal@1.0.4","MIT","https://github.com/wooorm/is-hexadecimal"
"is-map@2.0.2","MIT","https://github.com/inspect-js/is-map"
"is-negative-zero@2.0.2","MIT","https://github.com/inspect-js/is-negative-zero"
"is-number-object@1.0.7","MIT","https://github.com/inspect-js/is-number-object"
"is-number@3.0.0","MIT","https://github.com/jonschlinkert/is-number"
"is-number@7.0.0","MIT","https://github.com/jonschlinkert/is-number"
"is-object@1.0.2","MIT","https://github.com/inspect-js/is-object"
"is-path-inside@3.0.3","MIT","https://github.com/sindresorhus/is-path-inside"
"is-plain-obj@2.1.0","MIT","https://github.com/sindresorhus/is-plain-obj"
"is-plain-obj@4.1.0","MIT","https://github.com/sindresorhus/is-plain-obj"
"is-plain-object@2.0.4","MIT","https://github.com/jonschlinkert/is-plain-object"
"is-plain-object@5.0.0","MIT","https://github.com/jonschlinkert/is-plain-object"
"is-regex@1.1.4","MIT","https://github.com/inspect-js/is-regex"
"is-set@2.0.2","MIT","https://github.com/inspect-js/is-set"
"is-shared-array-buffer@1.0.2","MIT","https://github.com/inspect-js/is-shared-array-buffer"
"is-stream@1.1.0","MIT","https://github.com/sindresorhus/is-stream"
"is-stream@2.0.1","MIT","https://github.com/sindresorhus/is-stream"
"is-string@1.0.7","MIT","https://github.com/ljharb/is-string"
"is-symbol@1.0.4","MIT","https://github.com/inspect-js/is-symbol"
"is-typed-array@1.1.12","MIT","https://github.com/inspect-js/is-typed-array"
"is-typedarray@1.0.0","MIT","https://github.com/hughsk/is-typedarray"
"is-utf8@0.2.1","MIT","https://github.com/wayfind/is-utf8"
"is-weakmap@2.0.1","MIT","https://github.com/inspect-js/is-weakmap"
"is-weakref@1.0.2","MIT","https://github.com/inspect-js/is-weakref"
"is-weakset@2.0.2","MIT","https://github.com/inspect-js/is-weakset"
"is-what@4.1.16","MIT","https://github.com/mesqueeb/is-what"
"is-whitespace-character@1.0.4","MIT","https://github.com/wooorm/is-whitespace-character"
"is-window@1.0.2","MIT","https://github.com/gearcase/is-window"
"is-windows@1.0.2","MIT","https://github.com/jonschlinkert/is-windows"
"is-word-character@1.0.4","MIT","https://github.com/wooorm/is-word-character"
"is-wsl@1.1.0","MIT","https://github.com/sindresorhus/is-wsl"
"is-wsl@2.2.0","MIT","https://github.com/sindresorhus/is-wsl"
"isarray@1.0.0","MIT","https://github.com/juliangruber/isarray"
"isarray@2.0.5","MIT","https://github.com/juliangruber/isarray"
"isexe@2.0.0","ISC","https://github.com/isaacs/isexe"
"isobject@2.1.0","MIT","https://github.com/jonschlinkert/isobject"
"isobject@3.0.1","MIT","https://github.com/jonschlinkert/isobject"
"isobject@4.0.0","MIT","https://github.com/jonschlinkert/isobject"
"isomorphic-unfetch@3.1.0","MIT","https://github.com/developit/unfetch"
"istanbul-lib-coverage@3.2.2","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-instrument@5.2.1","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-instrument@6.0.1","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-report@3.0.1","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-lib-source-maps@4.0.1","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"istanbul-reports@3.1.6","BSD-3-Clause","https://github.com/istanbuljs/istanbuljs"
"iterate-iterator@1.0.2","MIT","https://github.com/ljharb/iterate-iterator"
"iterate-value@1.0.2","MIT","https://github.com/ljharb/iterate-value"
"iterator.prototype@1.1.2","MIT","https://github.com/ljharb/Iterator.prototype"
"javascript-color-gradient@2.4.4","MIT","https://github.com/Adrinlol/javascript-color-gradient"
"jest-changed-files@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-circus@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-cli@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-config@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-diff@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-docblock@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-each@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-environment-node@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-get-type@29.6.3","MIT","https://github.com/jestjs/jest"
"jest-haste-map@26.6.2","MIT","https://github.com/facebook/jest"
"jest-haste-map@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-leak-detector@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-matcher-utils@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-message-util@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-mock@27.5.1","MIT","https://github.com/facebook/jest"
"jest-mock@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-pnp-resolver@1.2.3","MIT","https://github.com/arcanis/jest-pnp-resolver"
"jest-regex-util@26.0.0","MIT","https://github.com/facebook/jest"
"jest-regex-util@29.6.3","MIT","https://github.com/jestjs/jest"
"jest-resolve-dependencies@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-resolve@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-runner@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-runtime@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-serializer@26.6.2","MIT","https://github.com/facebook/jest"
"jest-snapshot@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-util@26.6.2","MIT","https://github.com/facebook/jest"
"jest-util@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-validate@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-watcher@29.7.0","MIT","https://github.com/jestjs/jest"
"jest-worker@26.6.2","MIT","https://github.com/facebook/jest"
"jest-worker@27.5.1","MIT","https://github.com/facebook/jest"
"jest-worker@29.7.0","MIT","https://github.com/jestjs/jest"
"jest@29.7.0","MIT","https://github.com/jestjs/jest"
"jiti@1.21.0","MIT","https://github.com/unjs/jiti"
"jose@4.15.4","MIT","https://github.com/panva/jose"
"js-string-escape@1.0.1","MIT","https://github.com/joliss/js-string-escape"
"js-tokens@4.0.0","MIT","https://github.com/lydell/js-tokens"
"js-yaml@3.14.1","MIT","https://github.com/nodeca/js-yaml"
"js-yaml@4.1.0","MIT","https://github.com/nodeca/js-yaml"
"jsesc@0.5.0","MIT","https://github.com/mathiasbynens/jsesc"
"jsesc@2.5.2","MIT","https://github.com/mathiasbynens/jsesc"
"json-buffer@3.0.1","MIT","https://github.com/dominictarr/json-buffer"
"json-parse-better-errors@1.0.2","MIT","https://github.com/zkat/json-parse-better-errors"
"json-parse-even-better-errors@2.3.1","MIT","https://github.com/npm/json-parse-even-better-errors"
"json-schema-traverse@0.4.1","MIT","https://github.com/epoberezkin/json-schema-traverse"
"json-schema-traverse@1.0.0","MIT","https://github.com/epoberezkin/json-schema-traverse"
"json-stable-stringify-without-jsonify@1.0.1","MIT","https://github.com/samn/json-stable-stringify"
"json5@1.0.2","MIT","https://github.com/json5/json5"
"json5@2.2.3","MIT","https://github.com/json5/json5"
"jsonfile@6.1.0","MIT","https://github.com/jprichardson/node-jsonfile"
"jsx-ast-utils@3.3.5","MIT","https://github.com/jsx-eslint/jsx-ast-utils"
"junk@3.1.0","MIT","https://github.com/sindresorhus/junk"
"keyv@4.5.4","MIT","https://github.com/jaredwray/keyv"
"kind-of@3.2.2","MIT","https://github.com/jonschlinkert/kind-of"
"kind-of@4.0.0","MIT","https://github.com/jonschlinkert/kind-of"
"kind-of@6.0.3","MIT","https://github.com/jonschlinkert/kind-of"
"kleur@3.0.3","MIT","https://github.com/lukeed/kleur"
"kleur@4.1.5","MIT","https://github.com/lukeed/kleur"
"klona@2.0.6","MIT","https://github.com/lukeed/klona"
"language-subtag-registry@0.3.22","CC0-1.0","https://github.com/mattcg/language-subtag-registry"
"language-tags@1.0.9","MIT","https://github.com/mattcg/language-tags"
"lazy-universal-dotenv@3.0.1","Apache-2.0","https://github.com/storybooks/lazy-universal-dotenv"
"leven@3.1.0","MIT","https://github.com/sindresorhus/leven"
"levn@0.4.1","MIT","https://github.com/gkz/levn"
"lilconfig@2.1.0","MIT","https://github.com/antonk52/lilconfig"
"lilconfig@3.0.0","MIT","https://github.com/antonk52/lilconfig"
"lines-and-columns@1.2.4","MIT","https://github.com/eventualbuddha/lines-and-columns"
"little-state-machine@4.8.0","MIT","https://github.com/bluebill1049/little-state-machine"
"load-json-file@1.1.0","MIT","https://github.com/sindresorhus/load-json-file"
"loader-runner@2.4.0","MIT","https://github.com/webpack/loader-runner"
"loader-runner@4.3.0","MIT","https://github.com/webpack/loader-runner"
"loader-utils@1.4.2","MIT","https://github.com/webpack/loader-utils"
"loader-utils@2.0.4","MIT","https://github.com/webpack/loader-utils"
"loader-utils@3.2.1","MIT","https://github.com/webpack/loader-utils"
"locate-path@3.0.0","MIT","https://github.com/sindresorhus/locate-path"
"locate-path@5.0.0","MIT","https://github.com/sindresorhus/locate-path"
"locate-path@6.0.0","MIT","https://github.com/sindresorhus/locate-path"
"locate-path@7.2.0","MIT","https://github.com/sindresorhus/locate-path"
"lodash.debounce@4.0.8","MIT","https://github.com/lodash/lodash"
"lodash.merge@4.6.2","MIT","https://github.com/lodash/lodash"
"lodash.uniq@4.5.0","MIT","https://github.com/lodash/lodash"
"lodash@4.17.21","MIT","https://github.com/lodash/lodash"
"loose-envify@1.4.0","MIT","https://github.com/zertosh/loose-envify"
"loud-rejection@1.6.0","MIT","https://github.com/sindresorhus/loud-rejection"
"lower-case@2.0.2","MIT","https://github.com/blakeembrey/change-case"
"lru-cache@5.1.1","ISC","https://github.com/isaacs/node-lru-cache"
"lru-cache@6.0.0","ISC","https://github.com/isaacs/node-lru-cache"
"lz-string@1.5.0","MIT","https://github.com/pieroxy/lz-string"
"make-dir@2.1.0","MIT","https://github.com/sindresorhus/make-dir"
"make-dir@3.1.0","MIT","https://github.com/sindresorhus/make-dir"
"make-dir@4.0.0","MIT","https://github.com/sindresorhus/make-dir"
"makeerror@1.0.12","BSD-3-Clause","https://github.com/daaku/nodejs-makeerror"
"map-age-cleaner@0.1.3","MIT","https://github.com/SamVerschueren/map-age-cleaner"
"map-cache@0.2.2","MIT","https://github.com/jonschlinkert/map-cache"
"map-obj@1.0.1","MIT","https://github.com/sindresorhus/map-obj"
"map-or-similar@1.5.0","MIT","https://github.com/thinkloop/map-or-similar"
"map-visit@1.0.0","MIT","https://github.com/jonschlinkert/map-visit"
"markdown-escapes@1.0.4","MIT","https://github.com/wooorm/markdown-escapes"
"marked@4.3.0","MIT","https://github.com/markedjs/marked"
"md5.js@1.3.5","MIT","https://github.com/crypto-browserify/md5.js"
"mdast-squeeze-paragraphs@4.0.0","MIT","https://github.com/syntax-tree/mdast-squeeze-paragraphs"
"mdast-util-definitions@4.0.0","MIT","https://github.com/syntax-tree/mdast-util-definitions"
"mdast-util-definitions@5.1.2","MIT","https://github.com/syntax-tree/mdast-util-definitions"
"mdast-util-from-markdown@1.3.1","MIT","https://github.com/syntax-tree/mdast-util-from-markdown"
"mdast-util-to-hast@10.0.1","MIT","https://github.com/syntax-tree/mdast-util-to-hast"
"mdast-util-to-hast@12.3.0","MIT","https://github.com/syntax-tree/mdast-util-to-hast"
"mdast-util-to-string@1.1.0","MIT","https://github.com/syntax-tree/mdast-util-to-string"
"mdast-util-to-string@3.2.0","MIT","https://github.com/syntax-tree/mdast-util-to-string"
"mdn-data@2.0.14","CC0-1.0","https://github.com/mdn/data"
"mdurl@1.0.1","MIT","https://github.com/markdown-it/mdurl"
"media-typer@0.3.0","MIT","https://github.com/jshttp/media-typer"
"mem@8.1.1","MIT","https://github.com/sindresorhus/mem"
"memfs@3.5.3","Unlicense","https://github.com/streamich/memfs"
"memoizerific@1.11.3","MIT","https://github.com/thinkloop/memoizerific"
"memory-fs@0.4.1","MIT","https://github.com/webpack/memory-fs"
"memory-fs@0.5.0","MIT","https://github.com/webpack/memory-fs"
"meow@3.7.0","MIT","https://github.com/sindresorhus/meow"
"merge-descriptors@1.0.1","MIT","https://github.com/component/merge-descriptors"
"merge-stream@2.0.0","MIT","https://github.com/grncdr/merge-stream"
"merge2@1.4.1","MIT","https://github.com/teambition/merge2"
"methods@1.1.2","MIT","https://github.com/jshttp/methods"
"microevent.ts@0.1.1","MIT","https://github.com/DirtyHairy/microevent"
"micromark-core-commonmark@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-core-commonmark"
"micromark-factory-destination@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-factory-destination"
"micromark-factory-label@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-factory-label"
"micromark-factory-space@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-factory-space"
"micromark-factory-title@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-factory-title"
"micromark-factory-whitespace@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-factory-whitespace"
"micromark-util-character@1.2.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-character"
"micromark-util-chunked@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-chunked"
"micromark-util-classify-character@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-classify-character"
"micromark-util-combine-extensions@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-combine-extensions"
"micromark-util-decode-numeric-character-reference@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-decode-numeric-character-reference"
"micromark-util-decode-string@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-decode-string"
"micromark-util-encode@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-encode"
"micromark-util-html-tag-name@1.2.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-html-tag-name"
"micromark-util-normalize-identifier@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-normalize-identifier"
"micromark-util-resolve-all@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-resolve-all"
"micromark-util-sanitize-uri@1.2.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-sanitize-uri"
"micromark-util-subtokenize@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-subtokenize"
"micromark-util-symbol@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-symbol"
"micromark-util-types@1.1.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark-util-types"
"micromark@3.2.0","MIT","https://github.com/micromark/micromark/tree/main/packages/micromark"
"micromatch@3.1.10","MIT","https://github.com/micromatch/micromatch"
"micromatch@4.0.5","MIT","https://github.com/micromatch/micromatch"
"miller-rabin@4.0.1","MIT","https://github.com/indutny/miller-rabin"
"mime-db@1.52.0","MIT","https://github.com/jshttp/mime-db"
"mime-types@2.1.35","MIT","https://github.com/jshttp/mime-types"
"mime@1.6.0","MIT","https://github.com/broofa/node-mime"
"mime@2.6.0","MIT","https://github.com/broofa/mime"
"mimic-fn@2.1.0","MIT","https://github.com/sindresorhus/mimic-fn"
"mimic-fn@3.1.0","MIT","https://github.com/sindresorhus/mimic-fn"
"min-document@2.19.0","MIT","https://github.com/Raynos/min-document"
"min-indent@1.0.1","MIT","https://github.com/thejameskyle/min-indent"
"mini-svg-data-uri@1.4.4","MIT","https://github.com/tigt/mini-svg-data-uri"
"minimalistic-assert@1.0.1","ISC","https://github.com/calvinmetcalf/minimalistic-assert"
"minimalistic-crypto-utils@1.0.1","MIT","https://github.com/indutny/minimalistic-crypto-utils"
"minimatch@3.1.2","ISC","https://github.com/isaacs/minimatch"
"minimist@1.2.8","MIT","https://github.com/minimistjs/minimist"
"minipass-collect@1.0.2","ISC",""
"minipass-flush@1.0.5","ISC","https://github.com/isaacs/minipass-flush"
"minipass-pipeline@1.2.4","ISC",""
"minipass@3.3.6","ISC","https://github.com/isaacs/minipass"
"minipass@5.0.0","ISC","https://github.com/isaacs/minipass"
"minizlib@2.1.2","MIT","https://github.com/isaacs/minizlib"
"mississippi@3.0.0","BSD-2-Clause","https://github.com/maxogden/mississippi"
"mixin-deep@1.3.2","MIT","https://github.com/jonschlinkert/mixin-deep"
"mkdirp@0.5.6","MIT","https://github.com/substack/node-mkdirp"
"mkdirp@1.0.4","MIT","https://github.com/isaacs/node-mkdirp"
"move-concurrently@1.0.1","ISC","https://github.com/npm/move-concurrently"
"mri@1.2.0","MIT","https://github.com/lukeed/mri"
"ms@2.0.0","MIT","https://github.com/zeit/ms"
"ms@2.1.1","MIT","https://github.com/zeit/ms"
"ms@2.1.2","MIT","https://github.com/zeit/ms"
"ms@2.1.3","MIT","https://github.com/vercel/ms"
"mz@2.7.0","MIT","https://github.com/normalize/mz"
"nan@2.18.0","MIT","https://github.com/nodejs/nan"
"nanoid@3.3.7","MIT","https://github.com/ai/nanoid"
"nanomatch@1.2.13","MIT","https://github.com/micromatch/nanomatch"
"natural-compare-lite@1.4.0","MIT","https://github.com/litejs/natural-compare-lite"
"natural-compare@1.4.0","MIT","https://github.com/litejs/natural-compare-lite"
"negotiator@0.6.3","MIT","https://github.com/jshttp/negotiator"
"neo-async@2.6.2","MIT","https://github.com/suguru03/neo-async"
"nested-error-stacks@2.1.1","MIT","https://github.com/mdlavin/nested-error-stacks"
"next-auth@4.24.5","ISC","https://github.com/nextauthjs/next-auth"
"next@12.3.4","MIT","https://github.com/vercel/next.js"
"nice-try@1.0.5","MIT","https://github.com/electerious/nice-try"
"no-case@3.0.4","MIT","https://github.com/blakeembrey/change-case"
"node-dir@0.1.17","MIT","https://github.com/fshost/node-dir"
"node-fetch@2.7.0","MIT","https://github.com/bitinn/node-fetch"
"node-int64@0.4.0","MIT","https://github.com/broofa/node-int64"
"node-libs-browser@2.2.1","MIT","https://github.com/webpack/node-libs-browser"
"node-releases@2.0.13","MIT","https://github.com/chicoxyzzy/node-releases"
"normalize-package-data@2.5.0","BSD-2-Clause","https://github.com/npm/normalize-package-data"
"normalize-path@2.1.1","MIT","https://github.com/jonschlinkert/normalize-path"
"normalize-path@3.0.0","MIT","https://github.com/jonschlinkert/normalize-path"
"normalize-range@0.1.2","MIT","https://github.com/jamestalmage/normalize-range"
"npm-run-path@2.0.2","MIT","https://github.com/sindresorhus/npm-run-path"
"npm-run-path@4.0.1","MIT","https://github.com/sindresorhus/npm-run-path"
"npmlog@5.0.1","ISC","https://github.com/npm/npmlog"
"nth-check@2.1.1","BSD-2-Clause","https://github.com/fb55/nth-check"
"num2fraction@1.2.2","MIT","https://github.com/yisibl/num2fraction"
"oauth@0.9.15","MIT","https://github.com/ciaranj/node-oauth"
"object-assign@4.1.1","MIT","https://github.com/sindresorhus/object-assign"
"object-copy@0.1.0","MIT","https://github.com/jonschlinkert/object-copy"
"object-hash@2.2.0","MIT","https://github.com/puleos/object-hash"
"object-hash@3.0.0","MIT","https://github.com/puleos/object-hash"
"object-inspect@1.13.1","MIT","https://github.com/inspect-js/object-inspect"
"object-is@1.1.5","MIT","https://github.com/es-shims/object-is"
"object-keys@1.1.1","MIT","https://github.com/ljharb/object-keys"
"object-visit@1.0.1","MIT","https://github.com/jonschlinkert/object-visit"
"object.assign@4.1.4","MIT","https://github.com/ljharb/object.assign"
"object.entries@1.1.7","MIT","https://github.com/es-shims/Object.entries"
"object.fromentries@2.0.7","MIT","https://github.com/es-shims/Object.fromEntries"
"object.getownpropertydescriptors@2.1.7","MIT","https://github.com/es-shims/object.getownpropertydescriptors"
"object.groupby@1.0.1","MIT","https://github.com/es-shims/Object.groupBy"
"object.hasown@1.1.3","MIT","https://github.com/es-shims/Object.hasOwn"
"object.pick@1.3.0","MIT","https://github.com/jonschlinkert/object.pick"
"object.values@1.1.7","MIT","https://github.com/es-shims/Object.values"
"objectorarray@1.0.5","ISC","https://github.com/ZhouHansen/objectnotnull"
"oidc-token-hash@5.0.3","MIT","https://github.com/panva/oidc-token-hash"
"on-finished@2.4.1","MIT","https://github.com/jshttp/on-finished"
"on-headers@1.0.2","MIT","https://github.com/jshttp/on-headers"
"once@1.4.0","ISC","https://github.com/isaacs/once"
"onetime@5.1.2","MIT","https://github.com/sindresorhus/onetime"
"open@7.4.2","MIT","https://github.com/sindresorhus/open"
"open@8.4.2","MIT","https://github.com/sindresorhus/open"
"openid-client@5.6.1","MIT","https://github.com/panva/node-openid-client"
"optionator@0.9.3","MIT","https://github.com/gkz/optionator"
"os-browserify@0.3.0","MIT","https://github.com/CoderPuppy/os-browserify"
"os-homedir@1.0.2","MIT","https://github.com/sindresorhus/os-homedir"
"p-all@2.1.0","MIT","https://github.com/sindresorhus/p-all"
"p-defer@1.0.0","MIT","https://github.com/sindresorhus/p-defer"
"p-event@4.2.0","MIT","https://github.com/sindresorhus/p-event"
"p-filter@2.1.0","MIT","https://github.com/sindresorhus/p-filter"
"p-finally@1.0.0","MIT","https://github.com/sindresorhus/p-finally"
"p-limit@2.3.0","MIT","https://github.com/sindresorhus/p-limit"
"p-limit@3.1.0","MIT","https://github.com/sindresorhus/p-limit"
"p-limit@4.0.0","MIT","https://github.com/sindresorhus/p-limit"
"p-locate@3.0.0","MIT","https://github.com/sindresorhus/p-locate"
"p-locate@4.1.0","MIT","https://github.com/sindresorhus/p-locate"
"p-locate@5.0.0","MIT","https://github.com/sindresorhus/p-locate"
"p-locate@6.0.0","MIT","https://github.com/sindresorhus/p-locate"
"p-map@2.1.0","MIT","https://github.com/sindresorhus/p-map"
"p-map@3.0.0","MIT","https://github.com/sindresorhus/p-map"
"p-map@4.0.0","MIT","https://github.com/sindresorhus/p-map"
"p-timeout@3.2.0","MIT","https://github.com/sindresorhus/p-timeout"
"p-try@2.2.0","MIT","https://github.com/sindresorhus/p-try"
"pako@1.0.11","(MIT AND Zlib)","https://github.com/nodeca/pako"
"parallel-transform@1.2.0","MIT","https://github.com/mafintosh/parallel-transform"
"param-case@3.0.4","MIT","https://github.com/blakeembrey/change-case"
"parent-module@1.0.1","MIT","https://github.com/sindresorhus/parent-module"
"parse-asn1@5.1.6","ISC","https://github.com/crypto-browserify/parse-asn1"
"parse-entities@2.0.0","MIT","https://github.com/wooorm/parse-entities"
"parse-json@2.2.0","MIT","https://github.com/sindresorhus/parse-json"
"parse-json@5.2.0","MIT","https://github.com/sindresorhus/parse-json"
"parse5@6.0.1","MIT","https://github.com/inikulin/parse5"
"parseurl@1.3.3","MIT","https://github.com/pillarjs/parseurl"
"pascal-case@3.1.2","MIT","https://github.com/blakeembrey/change-case"
"pascalcase@0.1.1","MIT","https://github.com/jonschlinkert/pascalcase"
"path-browserify@0.0.1","MIT","https://github.com/substack/path-browserify"
"path-browserify@1.0.1","MIT","https://github.com/browserify/path-browserify"
"path-dirname@1.0.2","MIT","https://github.com/es128/path-dirname"
"path-exists@2.1.0","MIT","https://github.com/sindresorhus/path-exists"
"path-exists@3.0.0","MIT","https://github.com/sindresorhus/path-exists"
"path-exists@4.0.0","MIT","https://github.com/sindresorhus/path-exists"
"path-exists@5.0.0","MIT","https://github.com/sindresorhus/path-exists"
"path-is-absolute@1.0.1","MIT","https://github.com/sindresorhus/path-is-absolute"
"path-key@2.0.1","MIT","https://github.com/sindresorhus/path-key"
"path-key@3.1.1","MIT","https://github.com/sindresorhus/path-key"
"path-parse@1.0.7","MIT","https://github.com/jbgutierrez/path-parse"
"path-to-regexp@0.1.7","MIT","https://github.com/component/path-to-regexp"
"path-type@1.1.0","MIT","https://github.com/sindresorhus/path-type"
"path-type@3.0.0","MIT","https://github.com/sindresorhus/path-type"
"path-type@4.0.0","MIT","https://github.com/sindresorhus/path-type"
"pbkdf2@3.1.2","MIT","https://github.com/crypto-browserify/pbkdf2"
"picocolors@0.2.1","ISC","https://github.com/alexeyraspopov/picocolors"
"picocolors@1.0.0","ISC","https://github.com/alexeyraspopov/picocolors"
"picomatch@2.3.1","MIT","https://github.com/micromatch/picomatch"
"pify@2.3.0","MIT","https://github.com/sindresorhus/pify"
"pify@3.0.0","MIT","https://github.com/sindresorhus/pify"
"pify@4.0.1","MIT","https://github.com/sindresorhus/pify"
"pinkie-promise@2.0.1","MIT","https://github.com/floatdrop/pinkie-promise"
"pinkie@2.0.4","MIT","https://github.com/floatdrop/pinkie"
"pirates@4.0.6","MIT","https://github.com/danez/pirates"
"pkg-dir@3.0.0","MIT","https://github.com/sindresorhus/pkg-dir"
"pkg-dir@4.2.0","MIT","https://github.com/sindresorhus/pkg-dir"
"pkg-dir@5.0.0","MIT","https://github.com/sindresorhus/pkg-dir"
"pkg-dir@7.0.0","MIT","https://github.com/sindresorhus/pkg-dir"
"playwright-core@1.40.1","Apache-2.0","https://github.com/microsoft/playwright"
"playwright@1.40.1","Apache-2.0","https://github.com/microsoft/playwright"
"pnp-webpack-plugin@1.6.4","MIT","https://github.com/arcanis/pnp-webpack-plugin"
"polished@4.2.2","MIT","https://github.com/styled-components/polished"
"posix-character-classes@0.1.1","MIT","https://github.com/jonschlinkert/posix-character-classes"
"postcss-flexbugs-fixes@4.2.1","MIT","https://github.com/luisrudge/postcss-flexbugs-fixes"
"postcss-import@15.1.0","MIT","https://github.com/postcss/postcss-import"
"postcss-js@4.0.1","MIT","https://github.com/postcss/postcss-js"
"postcss-load-config@4.0.2","MIT","https://github.com/postcss/postcss-load-config"
"postcss-loader@4.3.0","MIT","https://github.com/webpack-contrib/postcss-loader"
"postcss-loader@7.3.3","MIT","https://github.com/webpack-contrib/postcss-loader"
"postcss-modules-extract-imports@2.0.0","ISC","https://github.com/css-modules/postcss-modules-extract-imports"
"postcss-modules-extract-imports@3.0.0","ISC","https://github.com/css-modules/postcss-modules-extract-imports"
"postcss-modules-local-by-default@3.0.3","MIT","https://github.com/css-modules/postcss-modules-local-by-default"
"postcss-modules-local-by-default@4.0.3","MIT","https://github.com/css-modules/postcss-modules-local-by-default"
"postcss-modules-scope@2.2.0","ISC","https://github.com/css-modules/postcss-modules-scope"
"postcss-modules-scope@3.0.0","ISC","https://github.com/css-modules/postcss-modules-scope"
"postcss-modules-values@3.0.0","ISC","https://github.com/css-modules/postcss-modules-values"
"postcss-modules-values@4.0.0","ISC","https://github.com/css-modules/postcss-modules-values"
"postcss-nested@6.0.1","MIT","https://github.com/postcss/postcss-nested"
"postcss-selector-parser@6.0.13","MIT","https://github.com/postcss/postcss-selector-parser"
"postcss-value-parser@4.2.0","MIT","https://github.com/TrySound/postcss-value-parser"
"postcss@7.0.39","MIT","https://github.com/postcss/postcss"
"postcss@8.4.14","MIT","https://github.com/postcss/postcss"
"postcss@8.4.31","MIT","https://github.com/postcss/postcss"
"preact-render-to-string@5.2.6","MIT","https://github.com/developit/preact-render-to-string"
"preact@10.19.2","MIT","https://github.com/preactjs/preact"
"prelude-ls@1.2.1","MIT","https://github.com/gkz/prelude-ls"
"prettier-linter-helpers@1.0.0","MIT","https://github.com/prettier/prettier-linter-helpers"
"prettier@2.3.0","MIT","https://github.com/prettier/prettier"
"prettier@2.8.8","MIT","https://github.com/prettier/prettier"
"pretty-error@2.1.2","MIT","https://github.com/AriaMinaei/pretty-error"
"pretty-error@4.0.0","MIT","https://github.com/AriaMinaei/pretty-error"
"pretty-format@27.5.1","MIT","https://github.com/facebook/jest"
"pretty-format@29.7.0","MIT","https://github.com/jestjs/jest"
"pretty-format@3.8.0","MIT","https://github.com/thejameskyle/pretty-format"
"pretty-hrtime@1.0.3","MIT","https://github.com/robrich/pretty-hrtime"
"process-nextick-args@2.0.1","MIT","https://github.com/calvinmetcalf/process-nextick-args"
"process@0.11.10","MIT","https://github.com/shtylman/node-process"
"promise-inflight@1.0.1","ISC","https://github.com/iarna/promise-inflight"
"promise.allsettled@1.0.7","MIT","https://github.com/es-shims/Promise.allSettled"
"promise.prototype.finally@3.1.7","MIT","https://github.com/es-shims/Promise.prototype.finally"
"prompts@2.4.2","MIT","https://github.com/terkelg/prompts"
"prop-types@15.8.1","MIT","https://github.com/facebook/prop-types"
"property-information@5.6.0","MIT","https://github.com/wooorm/property-information"
"property-information@6.4.0","MIT","https://github.com/wooorm/property-information"
"proxy-addr@2.0.7","MIT","https://github.com/jshttp/proxy-addr"
"proxy-from-env@1.1.0","MIT","https://github.com/Rob--W/proxy-from-env"
"prr@1.0.1","MIT","https://github.com/rvagg/prr"
"public-encrypt@4.0.3","MIT","https://github.com/crypto-browserify/publicEncrypt"
"pump@2.0.1","MIT","https://github.com/mafintosh/pump"
"pump@3.0.0","MIT","https://github.com/mafintosh/pump"
"pumpify@1.5.1","MIT","https://github.com/mafintosh/pumpify"
"punycode@1.4.1","MIT","https://github.com/bestiejs/punycode.js"
"punycode@2.3.1","MIT","https://github.com/mathiasbynens/punycode.js"
"pure-rand@6.0.4","MIT","https://github.com/dubzzz/pure-rand"
"qs@6.11.0","BSD-3-Clause","https://github.com/ljharb/qs"
"qs@6.11.2","BSD-3-Clause","https://github.com/ljharb/qs"
"querystring-es3@0.2.1","MIT","https://github.com/mike-spainhower/querystring"
"queue-microtask@1.2.3","MIT","https://github.com/feross/queue-microtask"
"queue@6.0.2","MIT","https://github.com/jessetane/queue"
"ramda@0.28.0","MIT","https://github.com/ramda/ramda"
"randombytes@2.1.0","MIT","https://github.com/crypto-browserify/randombytes"
"randomfill@1.0.4","MIT","https://github.com/crypto-browserify/randomfill"
"range-parser@1.2.1","MIT","https://github.com/jshttp/range-parser"
"raw-body@2.5.1","MIT","https://github.com/stream-utils/raw-body"
"raw-loader@4.0.2","MIT","https://github.com/webpack-contrib/raw-loader"
"react-data-grid@7.0.0-beta.29","MIT","https://github.com/adazzle/react-data-grid"
"react-docgen-typescript@2.2.2","MIT","https://github.com/styleguidist/react-docgen-typescript"
"react-docgen@5.4.3","MIT","https://github.com/reactjs/react-docgen"
"react-dom@18.2.0","MIT","https://github.com/facebook/react"
"react-dropzone@14.2.3","MIT","https://github.com/react-dropzone/react-dropzone"
"react-element-to-jsx-string@14.3.4","MIT","https://github.com/algolia/react-element-to-jsx-string"
"react-feather@2.0.10","MIT","https://github.com/feathericons/react-feather"
"react-hook-form@7.48.2","MIT","https://github.com/react-hook-form/react-hook-form"
"react-inspector@5.1.1","MIT","https://github.com/xyc/react-inspector"
"react-intersection-observer@8.34.0","MIT","https://github.com/thebuilder/react-intersection-observer"
"react-is@16.13.1","MIT","https://github.com/facebook/react"
"react-is@17.0.2","MIT","https://github.com/facebook/react"
"react-is@18.2.0","MIT","https://github.com/facebook/react"
"react-lifecycles-compat@3.0.4","MIT","https://github.com/reactjs/react-lifecycles-compat"
"react-markdown@8.0.7","MIT","https://github.com/remarkjs/react-markdown"
"react-merge-refs@1.1.0","MIT","https://github.com/gregberge/react-merge-refs"
"react-number-format@5.3.1","MIT","https://github.com/s-yadav/react-number-format"
"react-refresh@0.11.0","MIT","https://github.com/facebook/react"
"react-simple-animate@3.5.2","MIT*","https://github.com/bluebill1049/react-simple-animation"
"react-simplemde-editor@5.2.0","MIT","https://github.com/RIP21/react-simplemde-editor"
"react-smooth@2.0.5","MIT","https://github.com/recharts/react-smooth"
"react-toastify@9.1.3","MIT","https://github.com/fkhadra/react-toastify"
"react-tooltip@5.24.0","MIT","https://github.com/ReactTooltip/react-tooltip"
"react-transition-group@2.9.0","BSD-3-Clause","https://github.com/reactjs/react-transition-group"
"react-vertical-timeline-component@3.6.0","MIT","https://github.com/stephane-monnot/react-vertical-timeline"
"react@18.2.0","MIT","https://github.com/facebook/react"
"read-cache@1.0.0","MIT","https://github.com/TrySound/read-cache"
"read-pkg-up@1.0.1","MIT","https://github.com/sindresorhus/read-pkg-up"
"read-pkg-up@7.0.1","MIT","https://github.com/sindresorhus/read-pkg-up"
"read-pkg@1.1.0","MIT","https://github.com/sindresorhus/read-pkg"
"read-pkg@5.2.0","MIT","https://github.com/sindresorhus/read-pkg"
"readable-stream@2.3.8","MIT","https://github.com/nodejs/readable-stream"
"readable-stream@3.6.2","MIT","https://github.com/nodejs/readable-stream"
"readdirp@2.2.1","MIT","https://github.com/paulmillr/readdirp"
"readdirp@3.6.0","MIT","https://github.com/paulmillr/readdirp"
"recharts-scale@0.4.5","MIT","https://github.com/recharts/recharts-scale"
"recharts@2.10.2","MIT","https://github.com/recharts/recharts"
"redent@1.0.0","MIT","https://github.com/sindresorhus/redent"
"reflect.getprototypeof@1.0.4","MIT","https://github.com/es-shims/Reflect.getPrototypeOf"
"regenerate-unicode-properties@10.1.1","MIT","https://github.com/mathiasbynens/regenerate-unicode-properties"
"regenerate@1.4.2","MIT","https://github.com/mathiasbynens/regenerate"
"regenerator-runtime@0.13.11","MIT","https://github.com/facebook/regenerator/tree/main/packages/runtime"
"regenerator-runtime@0.14.0","MIT","https://github.com/facebook/regenerator/tree/main/packages/runtime"
"regenerator-transform@0.15.2","MIT","https://github.com/facebook/regenerator/tree/main/packages/transform"
"regex-not@1.0.2","MIT","https://github.com/jonschlinkert/regex-not"
"regex-parser@2.2.11","MIT","https://github.com/IonicaBizau/regex-parser.js"
"regexp.prototype.flags@1.5.1","MIT","https://github.com/es-shims/RegExp.prototype.flags"
"regexpu-core@5.3.2","MIT","https://github.com/mathiasbynens/regexpu-core"
"regjsparser@0.9.1","BSD-2-Clause","https://github.com/jviereck/regjsparser"
"relateurl@0.2.7","MIT","https://github.com/stevenvachon/relateurl"
"remark-external-links@8.0.0","MIT","https://github.com/remarkjs/remark-external-links"
"remark-footnotes@2.0.0","MIT","https://github.com/remarkjs/remark-footnotes"
"remark-mdx@1.6.22","MIT","https://github.com/mdx-js/mdx"
"remark-parse@10.0.2","MIT","https://github.com/remarkjs/remark/tree/main/packages/remark-parse"
"remark-parse@8.0.3","MIT","https://github.com/remarkjs/remark/tree/main/packages/remark-parse"
"remark-rehype@10.1.0","MIT","https://github.com/remarkjs/remark-rehype"
"remark-slug@6.1.0","MIT","https://github.com/remarkjs/remark-slug"
"remark-squeeze-paragraphs@4.0.0","MIT","https://github.com/remarkjs/remark-squeeze-paragraphs"
"remove-accents@0.4.2","MIT","https://github.com/tyxla/remove-accents"
"remove-trailing-separator@1.1.0","ISC","https://github.com/darsain/remove-trailing-separator"
"renderkid@2.0.7","MIT","https://github.com/AriaMinaei/RenderKid"
"renderkid@3.0.0","MIT","https://github.com/AriaMinaei/RenderKid"
"repeat-element@1.1.4","MIT","https://github.com/jonschlinkert/repeat-element"
"repeat-string@1.6.1","MIT","https://github.com/jonschlinkert/repeat-string"
"repeating@2.0.1","MIT","https://github.com/sindresorhus/repeating"
"require-directory@2.1.1","MIT","https://github.com/troygoode/node-require-directory"
"require-from-string@2.0.2","MIT","https://github.com/floatdrop/require-from-string"
"requireindex@1.2.0","MIT","https://github.com/stephenhandley/requireindex"
"resolve-cwd@3.0.0","MIT","https://github.com/sindresorhus/resolve-cwd"
"resolve-from@4.0.0","MIT","https://github.com/sindresorhus/resolve-from"
"resolve-from@5.0.0","MIT","https://github.com/sindresorhus/resolve-from"
"resolve-url-loader@5.0.0","MIT","https://github.com/bholloway/resolve-url-loader"
"resolve-url@0.2.1","MIT","https://github.com/lydell/resolve-url"
"resolve.exports@2.0.2","MIT","https://github.com/lukeed/resolve.exports"
"resolve@1.22.8","MIT","https://github.com/browserify/resolve"
"resolve@2.0.0-next.5","MIT","https://github.com/browserify/resolve"
"ret@0.1.15","MIT","https://github.com/fent/ret.js"
"retail-book-clearleft-next@0.1.0","UNLICENSED",""
"reusify@1.0.4","MIT","https://github.com/mcollina/reusify"
"rimraf@2.7.1","ISC","https://github.com/isaacs/rimraf"
"rimraf@3.0.2","ISC","https://github.com/isaacs/rimraf"
"ripemd160@2.0.2","MIT","https://github.com/crypto-browserify/ripemd160"
"rsvp@4.8.5","MIT","https://github.com/tildeio/rsvp.js"
"run-parallel@1.2.0","MIT","https://github.com/feross/run-parallel"
"run-queue@1.0.3","ISC","https://github.com/iarna/run-queue"
"sade@1.8.1","MIT","https://github.com/lukeed/sade"
"safe-array-concat@1.0.1","MIT","https://github.com/ljharb/safe-array-concat"
"safe-buffer@5.1.1","MIT","https://github.com/feross/safe-buffer"
"safe-buffer@5.1.2","MIT","https://github.com/feross/safe-buffer"
"safe-buffer@5.2.1","MIT","https://github.com/feross/safe-buffer"
"safe-regex-test@1.0.0","MIT","https://github.com/ljharb/safe-regex-test"
"safe-regex@1.1.0","MIT","https://github.com/substack/safe-regex"
"safer-buffer@2.1.2","MIT","https://github.com/ChALkeR/safer-buffer"
"sane@4.1.0","MIT","https://github.com/amasad/sane"
"sass-loader@13.3.2","MIT","https://github.com/webpack-contrib/sass-loader"
"scheduler@0.23.0","MIT","https://github.com/facebook/react"
"schema-utils@1.0.0","MIT","https://github.com/webpack-contrib/schema-utils"
"schema-utils@2.7.0","MIT","https://github.com/webpack/schema-utils"
"schema-utils@2.7.1","MIT","https://github.com/webpack/schema-utils"
"schema-utils@3.3.0","MIT","https://github.com/webpack/schema-utils"
"schema-utils@4.2.0","MIT","https://github.com/webpack/schema-utils"
"semver@5.7.2","ISC","https://github.com/npm/node-semver"
"semver@6.3.1","ISC","https://github.com/npm/node-semver"
"semver@7.5.4","ISC","https://github.com/npm/node-semver"
"send@0.18.0","MIT","https://github.com/pillarjs/send"
"serialize-javascript@4.0.0","BSD-3-Clause","https://github.com/yahoo/serialize-javascript"
"serialize-javascript@5.0.1","BSD-3-Clause","https://github.com/yahoo/serialize-javascript"
"serialize-javascript@6.0.1","BSD-3-Clause","https://github.com/yahoo/serialize-javascript"
"serve-favicon@2.5.0","MIT","https://github.com/expressjs/serve-favicon"
"serve-static@1.15.0","MIT","https://github.com/expressjs/serve-static"
"set-blocking@2.0.0","ISC","https://github.com/yargs/set-blocking"
"set-function-length@1.1.1","MIT","https://github.com/ljharb/set-function-length"
"set-function-name@2.0.1","MIT","https://github.com/ljharb/set-function-name"
"set-value@2.0.1","MIT","https://github.com/jonschlinkert/set-value"
"setimmediate@1.0.5","MIT","https://github.com/YuzuJS/setImmediate"
"setprototypeof@1.2.0","ISC","https://github.com/wesleytodd/setprototypeof"
"sha.js@2.4.11","(MIT AND BSD-3-Clause)","https://github.com/crypto-browserify/sha.js"
"shallow-clone@3.0.1","MIT","https://github.com/jonschlinkert/shallow-clone"
"shebang-command@1.2.0","MIT","https://github.com/kevva/shebang-command"
"shebang-command@2.0.0","MIT","https://github.com/kevva/shebang-command"
"shebang-regex@1.0.0","MIT","https://github.com/sindresorhus/shebang-regex"
"shebang-regex@3.0.0","MIT","https://github.com/sindresorhus/shebang-regex"
"side-channel@1.0.4","MIT","https://github.com/ljharb/side-channel"
"signal-exit@3.0.7","ISC","https://github.com/tapjs/signal-exit"
"sisteransi@1.0.5","MIT","https://github.com/terkelg/sisteransi"
"slash@2.0.0","MIT","https://github.com/sindresorhus/slash"
"slash@3.0.0","MIT","https://github.com/sindresorhus/slash"
"snapdragon-node@2.1.1","MIT","https://github.com/jonschlinkert/snapdragon-node"
"snapdragon-util@3.0.1","MIT","https://github.com/jonschlinkert/snapdragon-util"
"snapdragon@0.8.2","MIT","https://github.com/jonschlinkert/snapdragon"
"source-list-map@2.0.1","MIT","https://github.com/webpack/source-list-map"
"source-map-js@1.0.2","BSD-3-Clause","https://github.com/7rulnik/source-map-js"
"source-map-resolve@0.5.3","MIT","https://github.com/lydell/source-map-resolve"
"source-map-support@0.5.13","MIT","https://github.com/evanw/node-source-map-support"
"source-map-support@0.5.21","MIT","https://github.com/evanw/node-source-map-support"
"source-map-url@0.4.1","MIT","https://github.com/lydell/source-map-url"
"source-map@0.5.7","BSD-3-Clause","https://github.com/mozilla/source-map"
"source-map@0.6.1","BSD-3-Clause","https://github.com/mozilla/source-map"
"source-map@0.7.4","BSD-3-Clause","https://github.com/mozilla/source-map"
"space-separated-tokens@1.1.5","MIT","https://github.com/wooorm/space-separated-tokens"
"space-separated-tokens@2.0.2","MIT","https://github.com/wooorm/space-separated-tokens"
"spdx-correct@3.2.0","Apache-2.0","https://github.com/jslicense/spdx-correct.js"
"spdx-exceptions@2.3.0","CC-BY-3.0","https://github.com/kemitchell/spdx-exceptions.json"
"spdx-expression-parse@3.0.1","MIT","https://github.com/jslicense/spdx-expression-parse.js"
"spdx-license-ids@3.0.16","CC0-1.0","https://github.com/jslicense/spdx-license-ids"
"split-string@3.1.0","MIT","https://github.com/jonschlinkert/split-string"
"sprintf-js@1.0.3","BSD-3-Clause","https://github.com/alexei/sprintf.js"
"ssri@6.0.2","ISC","https://github.com/zkat/ssri"
"ssri@8.0.1","ISC","https://github.com/npm/ssri"
"stable@0.1.8","MIT","https://github.com/Two-Screen/stable"
"stack-utils@2.0.6","MIT","https://github.com/tapjs/stack-utils"
"stackframe@1.3.4","MIT","https://github.com/stacktracejs/stackframe"
"state-toggle@1.0.3","MIT","https://github.com/wooorm/state-toggle"
"static-extend@0.1.2","MIT","https://github.com/jonschlinkert/static-extend"
"statuses@2.0.1","MIT","https://github.com/jshttp/statuses"
"stop-iteration-iterator@1.0.0","MIT","https://github.com/ljharb/stop-iteration-iterator"
"store2@2.14.2","(MIT OR GPL-3.0)","https://github.com/nbubna/store"
"storybook-addon-next@1.8.0","MIT","https://github.com/RyanClementsHax/storybook-addon-next"
"stream-browserify@2.0.2","MIT","https://github.com/browserify/stream-browserify"
"stream-each@1.2.3","MIT","https://github.com/mafintosh/stream-each"
"stream-http@2.8.3","MIT","https://github.com/jhiesey/stream-http"
"stream-shift@1.0.1","MIT","https://github.com/mafintosh/stream-shift"
"string-length@4.0.2","MIT","https://github.com/sindresorhus/string-length"
"string-width@4.2.3","MIT","https://github.com/sindresorhus/string-width"
"string.prototype.matchall@4.0.10","MIT","https://github.com/es-shims/String.prototype.matchAll"
"string.prototype.padend@3.1.5","MIT","https://github.com/es-shims/String.prototype.padEnd"
"string.prototype.padstart@3.1.5","MIT","https://github.com/es-shims/String.prototype.padStart"
"string.prototype.trim@1.2.8","MIT","https://github.com/es-shims/String.prototype.trim"
"string.prototype.trimend@1.0.7","MIT","https://github.com/es-shims/String.prototype.trimEnd"
"string.prototype.trimstart@1.0.7","MIT","https://github.com/es-shims/String.prototype.trimStart"
"string_decoder@1.1.1","MIT","https://github.com/nodejs/string_decoder"
"string_decoder@1.3.0","MIT","https://github.com/nodejs/string_decoder"
"strip-ansi@3.0.1","MIT","https://github.com/chalk/strip-ansi"
"strip-ansi@6.0.1","MIT","https://github.com/chalk/strip-ansi"
"strip-bom@2.0.0","MIT","https://github.com/sindresorhus/strip-bom"
"strip-bom@3.0.0","MIT","https://github.com/sindresorhus/strip-bom"
"strip-bom@4.0.0","MIT","https://github.com/sindresorhus/strip-bom"
"strip-eof@1.0.0","MIT","https://github.com/sindresorhus/strip-eof"
"strip-final-newline@2.0.0","MIT","https://github.com/sindresorhus/strip-final-newline"
"strip-indent@1.0.1","MIT","https://github.com/sindresorhus/strip-indent"
"strip-indent@3.0.0","MIT","https://github.com/sindresorhus/strip-indent"
"strip-json-comments@3.1.1","MIT","https://github.com/sindresorhus/strip-json-comments"
"style-loader@1.3.0","MIT","https://github.com/webpack-contrib/style-loader"
"style-loader@2.0.0","MIT","https://github.com/webpack-contrib/style-loader"
"style-to-object@0.3.0","MIT","https://github.com/remarkablemark/style-to-object"
"style-to-object@0.4.4","MIT","https://github.com/remarkablemark/style-to-object"
"styled-jsx@5.0.7","MIT","https://github.com/vercel/styled-jsx"
"stylis@4.2.0","MIT","https://github.com/thysultan/stylis.js"
"sucrase@3.34.0","MIT","https://github.com/alangpierce/sucrase"
"superjson@1.13.3","MIT","https://github.com/blitz-js/superjson"
"supports-color@5.5.0","MIT","https://github.com/chalk/supports-color"
"supports-color@7.2.0","MIT","https://github.com/chalk/supports-color"
"supports-color@8.1.1","MIT","https://github.com/chalk/supports-color"
"supports-preserve-symlinks-flag@1.0.0","MIT","https://github.com/inspect-js/node-supports-preserve-symlinks-flag"
"svg-parser@2.0.4","MIT","https://github.com/Rich-Harris/svg-parser"
"svgo@2.8.0","MIT","https://github.com/svg/svgo"
"symbol.prototype.description@1.0.5","MIT","https://github.com/es-shims/Symbol.prototype.description"
"synchronous-promise@2.0.17","BSD-3-Clause","https://github.com/fluffynuts/synchronous-promise"
"tailwindcss@3.3.5","MIT","https://github.com/tailwindlabs/tailwindcss"
"tapable@1.1.3","MIT","https://github.com/webpack/tapable"
"tapable@2.2.1","MIT","https://github.com/webpack/tapable"
"tar@6.2.0","ISC","https://github.com/isaacs/node-tar"
"telejson@6.0.8","MIT","https://github.com/storybookjs/telejson"
"terser-webpack-plugin@1.4.5","MIT","https://github.com/webpack-contrib/terser-webpack-plugin"
"terser-webpack-plugin@4.2.3","MIT","https://github.com/webpack-contrib/terser-webpack-plugin"
"terser-webpack-plugin@5.3.9","MIT","https://github.com/webpack-contrib/terser-webpack-plugin"
"terser@4.8.1","BSD-2-Clause","https://github.com/terser/terser"
"terser@5.24.0","BSD-2-Clause","https://github.com/terser/terser"
"test-exclude@6.0.0","ISC","https://github.com/istanbuljs/test-exclude"
"text-table@0.2.0","MIT","https://github.com/substack/text-table"
"thenify-all@1.6.0","MIT","https://github.com/thenables/thenify-all"
"thenify@3.3.1","MIT","https://github.com/thenables/thenify"
"through2@2.0.5","MIT","https://github.com/rvagg/through2"
"timers-browserify@2.0.12","MIT","https://github.com/jryans/timers-browserify"
"tiny-invariant@1.3.1","MIT","https://github.com/alexreardon/tiny-invariant"
"tmpl@1.0.5","BSD-3-Clause","https://github.com/daaku/nodejs-tmpl"
"to-arraybuffer@1.0.1","MIT","https://github.com/jhiesey/to-arraybuffer"
"to-fast-properties@2.0.0","MIT","https://github.com/sindresorhus/to-fast-properties"
"to-object-path@0.3.0","MIT","https://github.com/jonschlinkert/to-object-path"
"to-regex-range@2.1.1","MIT","https://github.com/micromatch/to-regex-range"
"to-regex-range@5.0.1","MIT","https://github.com/micromatch/to-regex-range"
"to-regex@3.0.2","MIT","https://github.com/jonschlinkert/to-regex"
"toidentifier@1.0.1","MIT","https://github.com/component/toidentifier"
"tr46@0.0.3","MIT","https://github.com/Sebmaster/tr46.js"
"trim-lines@3.0.1","MIT","https://github.com/wooorm/trim-lines"
"trim-newlines@1.0.0","MIT","https://github.com/sindresorhus/trim-newlines"
"trim-trailing-lines@1.1.4","MIT","https://github.com/wooorm/trim-trailing-lines"
"trim@0.0.1","MIT*",""
"trough@1.0.5","MIT","https://github.com/wooorm/trough"
"trough@2.1.0","MIT","https://github.com/wooorm/trough"
"ts-dedent@2.2.0","MIT","https://github.com/tamino-martinius/node-ts-dedent"
"ts-interface-checker@0.1.13","Apache-2.0","https://github.com/gristlabs/ts-interface-checker"
"ts-pnp@1.2.0","MIT","https://github.com/arcanis/ts-pnp"
"tsconfig-paths-webpack-plugin@4.1.0","MIT","https://github.com/dividab/tsconfig-paths-webpack-plugin"
"tsconfig-paths@3.14.2","MIT","https://github.com/dividab/tsconfig-paths"
"tsconfig-paths@4.2.0","MIT","https://github.com/dividab/tsconfig-paths"
"tslib@1.14.1","0BSD","https://github.com/Microsoft/tslib"
"tslib@2.6.2","0BSD","https://github.com/Microsoft/tslib"
"tsutils@3.21.0","MIT","https://github.com/ajafff/tsutils"
"tty-browserify@0.0.0","MIT","https://github.com/substack/tty-browserify"
"type-check@0.4.0","MIT","https://github.com/gkz/type-check"
"type-detect@4.0.8","MIT","https://github.com/chaijs/type-detect"
"type-fest@0.20.2","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest"
"type-fest@0.21.3","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest"
"type-fest@0.6.0","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest"
"type-fest@0.8.1","(MIT OR CC0-1.0)","https://github.com/sindresorhus/type-fest"
"type-is@1.6.18","MIT","https://github.com/jshttp/type-is"
"typed-array-buffer@1.0.0","MIT","https://github.com/ljharb/typed-array-buffer"
"typed-array-byte-length@1.0.0","MIT","https://github.com/inspect-js/typed-array-byte-length"
"typed-array-byte-offset@1.0.0","MIT","https://github.com/inspect-js/typed-array-byte-offset"
"typed-array-length@1.0.4","MIT","https://github.com/inspect-js/typed-array-length"
"typedarray-to-buffer@3.1.5","MIT","https://github.com/feross/typedarray-to-buffer"
"typedarray@0.0.6","MIT","https://github.com/substack/typedarray"
"typescript@4.9.5","Apache-2.0","https://github.com/Microsoft/TypeScript"
"typo-js@1.2.3","BSD-3-Clause","https://github.com/cfinke/Typo.js"
"uglify-js@3.17.4","BSD-2-Clause","https://github.com/mishoo/UglifyJS"
"unbox-primitive@1.0.2","MIT","https://github.com/ljharb/unbox-primitive"
"unfetch@4.2.0","MIT","https://github.com/developit/unfetch"
"unherit@1.1.3","MIT","https://github.com/wooorm/unherit"
"unicode-canonical-property-names-ecmascript@2.0.0","MIT","https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript"
"unicode-match-property-ecmascript@2.0.0","MIT","https://github.com/mathiasbynens/unicode-match-property-ecmascript"
"unicode-match-property-value-ecmascript@2.1.0","MIT","https://github.com/mathiasbynens/unicode-match-property-value-ecmascript"
"unicode-property-aliases-ecmascript@2.1.0","MIT","https://github.com/mathiasbynens/unicode-property-aliases-ecmascript"
"unified@10.1.2","MIT","https://github.com/unifiedjs/unified"
"unified@9.2.0","MIT","https://github.com/unifiedjs/unified"
"union-value@1.0.1","MIT","https://github.com/jonschlinkert/union-value"
"unique-filename@1.1.1","ISC","https://github.com/iarna/unique-filename"
"unique-slug@2.0.2","ISC","https://github.com/iarna/unique-slug"
"unist-builder@2.0.3","MIT","https://github.com/syntax-tree/unist-builder"
"unist-util-generated@1.1.6","MIT","https://github.com/syntax-tree/unist-util-generated"
"unist-util-generated@2.0.1","MIT","https://github.com/syntax-tree/unist-util-generated"
"unist-util-is@4.1.0","MIT","https://github.com/syntax-tree/unist-util-is"
"unist-util-is@5.2.1","MIT","https://github.com/syntax-tree/unist-util-is"
"unist-util-position@3.1.0","MIT","https://github.com/syntax-tree/unist-util-position"
"unist-util-position@4.0.4","MIT","https://github.com/syntax-tree/unist-util-position"
"unist-util-remove-position@2.0.1","MIT","https://github.com/syntax-tree/unist-util-remove-position"
"unist-util-remove@2.1.0","MIT","https://github.com/syntax-tree/unist-util-remove"
"unist-util-stringify-position@2.0.3","MIT","https://github.com/syntax-tree/unist-util-stringify-position"
"unist-util-stringify-position@3.0.3","MIT","https://github.com/syntax-tree/unist-util-stringify-position"
"unist-util-visit-parents@3.1.1","MIT","https://github.com/syntax-tree/unist-util-visit-parents"
"unist-util-visit-parents@5.1.3","MIT","https://github.com/syntax-tree/unist-util-visit-parents"
"unist-util-visit@2.0.3","MIT","https://github.com/syntax-tree/unist-util-visit"
"unist-util-visit@4.1.2","MIT","https://github.com/syntax-tree/unist-util-visit"
"universalify@2.0.1","MIT","https://github.com/RyanZim/universalify"
"unpipe@1.0.0","MIT","https://github.com/stream-utils/unpipe"
"unset-value@1.0.0","MIT","https://github.com/jonschlinkert/unset-value"
"untildify@2.1.0","MIT","https://github.com/sindresorhus/untildify"
"upath@1.2.0","MIT","https://github.com/anodynos/upath"
"update-browserslist-db@1.0.13","MIT","https://github.com/browserslist/update-db"
"uri-js@4.4.1","BSD-2-Clause","https://github.com/garycourt/uri-js"
"urix@0.1.0","MIT","https://github.com/lydell/urix"
"url-loader@4.1.1","MIT","https://github.com/webpack-contrib/url-loader"
"url@0.11.3","MIT","https://github.com/defunctzombie/node-url"
"use-deep-compare-effect@1.8.1","MIT","https://github.com/kentcdodds/use-deep-compare-effect"
"use-sync-external-store@1.2.0","MIT","https://github.com/facebook/react"
"use@3.1.1","MIT","https://github.com/jonschlinkert/use"
"util-deprecate@1.0.2","MIT","https://github.com/TooTallNate/util-deprecate"
"util.promisify@1.0.0","MIT","https://github.com/ljharb/util.promisify"
"util@0.10.4","MIT","https://github.com/defunctzombie/node-util"
"util@0.11.1","MIT","https://github.com/defunctzombie/node-util"
"utila@0.4.0","MIT","https://github.com/AriaMinaei/utila"
"utils-merge@1.0.1","MIT","https://github.com/jaredhanson/utils-merge"
"uuid-browser@3.1.0","MIT","https://github.com/heikomat/uuid-browser"
"uuid@3.4.0","MIT","https://github.com/uuidjs/uuid"
"uuid@8.3.2","MIT","https://github.com/uuidjs/uuid"
"uuid@9.0.1","MIT","https://github.com/uuidjs/uuid"
"uvu@0.5.6","MIT","https://github.com/lukeed/uvu"
"v8-to-istanbul@9.2.0","ISC","https://github.com/istanbuljs/v8-to-istanbul"
"validate-npm-package-license@3.0.4","Apache-2.0","https://github.com/kemitchell/validate-npm-package-license.js"
"vary@1.1.2","MIT","https://github.com/jshttp/vary"
"vfile-location@3.2.0","MIT","https://github.com/vfile/vfile-location"
"vfile-message@2.0.4","MIT","https://github.com/vfile/vfile-message"
"vfile-message@3.1.4","MIT","https://github.com/vfile/vfile-message"
"vfile@4.2.1","MIT","https://github.com/vfile/vfile"
"vfile@5.3.7","MIT","https://github.com/vfile/vfile"
"victory-vendor@36.7.0","MIT AND ISC","https://github.com/FormidableLabs/victory"
"vm-browserify@1.1.2","MIT","https://github.com/substack/vm-browserify"
"walker@1.0.8","Apache-2.0","https://github.com/daaku/nodejs-walker"
"watchpack-chokidar2@2.0.1","MIT","https://github.com/webpack/watchpack"
"watchpack@1.7.5","MIT","https://github.com/webpack/watchpack"
"watchpack@2.4.0","MIT","https://github.com/webpack/watchpack"
"web-namespaces@1.1.4","MIT","https://github.com/wooorm/web-namespaces"
"webidl-conversions@3.0.1","BSD-2-Clause","https://github.com/jsdom/webidl-conversions"
"webpack-dev-middleware@3.7.3","MIT","https://github.com/webpack/webpack-dev-middleware"
"webpack-dev-middleware@4.3.0","MIT","https://github.com/webpack/webpack-dev-middleware"
"webpack-filter-warnings-plugin@1.2.1","MIT","https://github.com/mattlewis92/webpack-filter-warnings-plugin"
"webpack-hot-middleware@2.25.4","MIT","https://github.com/webpack-contrib/webpack-hot-middleware"
"webpack-log@2.0.0","MIT","https://github.com/webpack-contrib/webpack-log"
"webpack-sources@1.4.3","MIT","https://github.com/webpack/webpack-sources"
"webpack-sources@3.2.3","MIT","https://github.com/webpack/webpack-sources"
"webpack-virtual-modules@0.2.2","MIT","https://github.com/sysgears/webpack-virtual-modules"
"webpack-virtual-modules@0.4.6","MIT","https://github.com/sysgears/webpack-virtual-modules"
"webpack@4.47.0","MIT","https://github.com/webpack/webpack"
"webpack@5.89.0","MIT","https://github.com/webpack/webpack"
"whatwg-url@5.0.0","MIT","https://github.com/jsdom/whatwg-url"
"which-boxed-primitive@1.0.2","MIT","https://github.com/inspect-js/which-boxed-primitive"
"which-builtin-type@1.1.3","MIT","https://github.com/inspect-js/which-builtin-type"
"which-collection@1.0.1","MIT","https://github.com/inspect-js/which-collection"
"which-typed-array@1.1.13","MIT","https://github.com/inspect-js/which-typed-array"
"which@1.3.1","ISC","https://github.com/isaacs/node-which"
"which@2.0.2","ISC","https://github.com/isaacs/node-which"
"wide-align@1.1.5","ISC","https://github.com/iarna/wide-align"
"widest-line@3.1.0","MIT","https://github.com/sindresorhus/widest-line"
"wordwrap@1.0.0","MIT","https://github.com/substack/node-wordwrap"
"worker-farm@1.7.0","MIT","https://github.com/rvagg/node-worker-farm"
"worker-rpc@0.1.1","MIT","https://github.com/DirtyHairy/worker-rpc"
"wrap-ansi@7.0.0","MIT","https://github.com/chalk/wrap-ansi"
"wrappy@1.0.2","ISC","https://github.com/npm/wrappy"
"write-file-atomic@3.0.3","ISC","https://github.com/npm/write-file-atomic"
"write-file-atomic@4.0.2","ISC","https://github.com/npm/write-file-atomic"
"ws@8.14.2","MIT","https://github.com/websockets/ws"
"x-default-browser@0.4.0","MIT","https://github.com/jakub-g/x-default-browser"
"xtend@4.0.2","MIT","https://github.com/Raynos/xtend"
"y18n@4.0.3","ISC","https://github.com/yargs/y18n"
"y18n@5.0.8","ISC","https://github.com/yargs/y18n"
"yallist@3.1.1","ISC","https://github.com/isaacs/yallist"
"yallist@4.0.0","ISC","https://github.com/isaacs/yallist"
"yaml@1.10.2","ISC","https://github.com/eemeli/yaml"
"yaml@2.3.4","ISC","https://github.com/eemeli/yaml"
"yargs-parser@20.2.9","ISC","https://github.com/yargs/yargs-parser"
"yargs-parser@21.1.1","ISC","https://github.com/yargs/yargs-parser"
"yargs@16.2.0","MIT","https://github.com/yargs/yargs"
"yargs@17.7.2","MIT","https://github.com/yargs/yargs"
"yocto-queue@0.1.0","MIT","https://github.com/sindresorhus/yocto-queue"
"yocto-queue@1.0.0","MIT","https://github.com/sindresorhus/yocto-queue"
"zwitch@1.0.5","MIT","https://github.com/wooorm/zwitch"