import { Badge } from "components/Basic/Badge/Badge";
import { Table } from "components/Basic/Table/Table";
import { formatNumber } from "helpers";
import { TOrderLineItem } from "types";

interface OrderLineItemsTableProps {
  className?: string;
  lineItems: TOrderLineItem[];
  currencyCode: string;
}

//deprecated
export const OrderLineItemsTable = ({
  className,
  lineItems,
  currencyCode,
}: OrderLineItemsTableProps) => {
  return (
    <Table compact className={className}>
      <Table.Head>
        <Table.Row>
          <Table.HeaderCell>Client Order Ref</Table.HeaderCell>
          <Table.HeaderCell className="text-center">
            Existing Holding
          </Table.HeaderCell>
          <Table.HeaderCell className="text-right">
            Value ({currencyCode})
          </Table.HeaderCell>
          <Table.HeaderCell className="text-right">
            Commission due?
          </Table.HeaderCell>
          <Table.HeaderCell className="text-right">
            Tax Wrapper?
          </Table.HeaderCell>
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {lineItems.map((row, index) => (
          <Table.Row key={index}>
            <Table.Cell heading="Client Order Ref">
              {row.client_order_ref}
            </Table.Cell>
            <Table.Cell heading="Existing Holding" className="sm:text-center">
              <span className="font-mono">
                {formatNumber(row.existing_holding)}
              </span>
            </Table.Cell>
            <Table.Cell heading="Value" className="sm:text-right">
              <span className="font-mono">
                {formatNumber(row.notional_value)}
              </span>
            </Table.Cell>
            <Table.Cell heading="Commission due?" className="sm:text-right">
              <Badge theme={row.commission_due ? "success" : "failure"}>
                {row.commission_due ? "True" : "False"}
              </Badge>
            </Table.Cell>
            <Table.Cell heading="Tax Wrapper?" className="sm:text-right">
              <Badge theme={row.tax_wrapper ? "success" : "failure"}>
                {row.tax_wrapper ? "True" : "False"}
              </Badge>
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
      <Table.Caption>Showing first {lineItems.length} items</Table.Caption>
    </Table>
  );
};
