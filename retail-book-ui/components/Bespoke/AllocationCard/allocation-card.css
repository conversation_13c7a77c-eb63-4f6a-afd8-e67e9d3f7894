.allocation-card {
  /* @apply flex flex-col w-full p-sm rounded gap-md bg-white; */
  @apply w-3/4 p-sm rounded flex flex-col items-start gap-md bg-white;

  &__content {
    @apply grow;
  }

  &__definitions {
    @apply grow grid items-start grid-cols-5 gap-xs;
  }

  &__definition {
    @apply block mb-3xs;
  }

  &__breakdown {
    @apply text-sm text-grey-step-1 leading-tight;
  }

  &__header {
    @apply flex mb-xs;

    &__asOf {
      @apply align-middle text-xs text-grey-step-1 leading-tight;
    }

    &__download {
      @apply ml-auto text-xs text-grey-step-1;

      &__link {
        @apply text-xs text-grey-step-1;
      } 
    }
  }
  @media screen(sm) {
    &__definitions {
      @apply grid-cols-2 gap-x-md gap-y-xs;
    }

    &__actions {
      @apply flex-row;
    }
  }

  @media screen(lg) {
    @apply flex-row;

    &__meta {
      @apply flex-row gap-md;
    }

    &__definitions {
      @apply grid-cols-5 gap-md;
    }

    &__actions {
      @apply w-auto flex-col;
    }
  }
}