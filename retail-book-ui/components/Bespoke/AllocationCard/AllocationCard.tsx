import classNames from "classnames";
import { Button } from "components/Basic/Button/Button";
import { Definition } from "components/Basic/Definition/Definition";
import { formatCurrency } from "currency";
import { format, parse } from "date-fns";
import { Zero, formatNumber } from "helpers";
import { Download, Upload } from "react-feather";
import { TAllocation, TOffer } from "types";
import { Percent } from "../../Composite/OfferPage/EditOffer/components/Percent";

interface AllocationCardProps {
  className?: string;
  offer: TOffer;
  allocation: TAllocation | undefined;
  downloadAllocationXlsx: (allocation: TAllocation | undefined) => void;
  uploadAllocation?: ()=>void;
}

export const AllocationCard = ({
  className,
  offer,
  allocation,
  downloadAllocationXlsx,
  uploadAllocation,
}: AllocationCardProps) => {
  const hasAllocation = allocation && allocation.id != "";
  return (
    <article className={classNames("allocation-card", className)}>
      <div className="allocation-card__content">
        <div className="allocation-card__header">
          {hasAllocation && 
            <div className="allocation-card__header__asOf">
              Allocated as of: {allocation?.date_created && (format(parse(allocation?.date_created, "yyyy-MM-dd'T'HH:mm:ssXXX", new Date()), "dd MMM yy HH:mm:ss"))}
            </div>
          }
        </div>
        <dl className="allocation-card__definitions">
          <div>
            <Definition
              term="Allocated Quantity"
              description={formatNumber(allocation?.totals.allocation_quantity)}
              descriptionSuffix={(<Percent reference={allocation?.totals.order_quantity} value={allocation?.totals.allocation_quantity}/>)}
              size="lg"
              className="allocation-card__definition"
            />
            <ul className="allocation-card__breakdown">
              <li>
                Shareholder:{" "}
                <strong>
                  {formatNumber(allocation?.shareholdings?.allocation_quantity)}
                </strong>
              </li>
              <li>
                Non Shareholder:{" "}
                <strong>
                  {formatNumber(allocation?.non_shareholdings?.allocation_quantity)}
                </strong>
              </li>
            </ul>
          </div>
          <div>
            <Definition
              term="Allocated Value"
              description={formatCurrency(
                allocation?.totals.allocation_value ?? Zero,
                offer.currency
              )}
              descriptionSuffix={(<Percent reference={allocation?.totals.notional_value} value={allocation?.totals.allocation_value}/>)}
              size="lg"
              className="allocation-card__definition"
            />
            <ul className="allocation-card__breakdown">
              <li>
                Shareholder:{" "}
                <strong>
                  {formatCurrency(
                    allocation?.shareholdings?.allocation_value ?? Zero,
                    offer.currency
                  )}
                </strong>
              </li>
              <li>
                Non Shareholder:{" "}
                <strong>
                  {formatCurrency(
                    allocation?.non_shareholdings?.allocation_value ?? Zero,
                    offer.currency
                  )}
                </strong>
              </li>
            </ul>
          </div>
          <div>
            <Definition
              term="Number of Applications"
              description={formatNumber(allocation?.totals.applications)}
              size="lg"
              className="allocation-card__definition"
            />
            <ul className="allocation-card__breakdown">
              <li>
                Shareholder:{" "}
                <strong>
                  {formatNumber(allocation?.shareholdings?.applications)}
                </strong>
              </li>
              <li>
                Non Shareholder:{" "}
                <strong>
                  {formatNumber(allocation?.non_shareholdings?.applications)}
                </strong>
              </li>
            </ul>
          </div>
          <div>
            <div className="flex flex-col space-y-2">
              {uploadAllocation && <Button size="sm" icon={Upload} onClick= { () => uploadAllocation && uploadAllocation() }>Upload</Button>}
              {hasAllocation && <Button size="sm" icon={Download} onClick={ () => downloadAllocationXlsx(allocation)}>Download</Button>}
            </div>
          </div>
        </dl>
      </div>
    </article>
  )
};