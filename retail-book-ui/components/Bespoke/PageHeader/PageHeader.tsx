import classNames from "classnames";
import { Breadcrumb, Crumb } from "components/Basic/Breadcrumb/Breadcrumb";
import { useRouter } from "next/router";
import { ReactNode } from "react";

interface PageHeaderProps {
  className?: string;
  crumbs?: Crumb[];
  title: string;
  standfirst?: string;
  children?: ReactNode;
}

export const PageHeader = ({
  className,
  crumbs,
  title,
  standfirst,
  children,
}: PageHeaderProps) => {
  const { asPath } = useRouter();
  return (
    <header className={classNames("mb-md", className)}>
      {crumbs && (
        <Breadcrumb className="pt-xs-sm" crumbs={crumbs} currentPath={asPath} />
      )}
      <div className="flex justify-between items-center flex-wrap gap-md pt-md-lg">
        <div>
          <h1
            className={classNames("h1 font-brand", {
              "mb-sm": !!standfirst,
            })}
          >
            {title}
          </h1>
          {standfirst && <p className="lead max-w-prose">{standfirst}</p>}
        </div>
        {children}
      </div>
    </header>
  );
};
