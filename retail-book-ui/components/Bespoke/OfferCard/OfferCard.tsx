import classNames from "classnames";
import { Badge } from "components/Basic/Badge/Badge";
import { Definition } from "components/Basic/Definition/Definition";
import { TimeDefinition } from "components/Basic/Definition/TimeDefinition";
import { IssuerLogo } from "components/Bespoke/IssuerLogo/IssuerLogo";
import { formatCurrency } from "currency";
import { formatDistanceToNowStrict, isFuture, parseISO } from "date-fns";
import { formatNumber } from "helpers";
import Link from "next/link";
import { useMemo } from "react";
import { EOfferStatus, TOfferSummary } from "types";

interface OfferCardProps {
  className?: string;
  offer: TOfferSummary;
}

export const OfferCard = ({ className, offer }: OfferCardProps) => {
  const { id, status, issuer_logo, issued_by, name, type, currency } = offer;

  const regDate = useMemo(() => {
    if (!offer.registration_date) return null;
    return parseISO(offer.registration_date);
  }, [offer.registration_date]);

  const openDate = useMemo(() => {
    if (!offer.open_date) return null;
    return parseISO(offer.open_date);
  }, [offer.open_date]);

  const closeDate = useMemo(() => {
    if (!offer.close_date) return null;
    return parseISO(offer.close_date);
  }, [offer.close_date]);

  const progressLabel = useMemo(() => {
    if (status === EOfferStatus.BUILDING) {
      if (!regDate) return "";
      return `registration ${isFuture(regDate) ? "opens" : "due to open"
        } ${formatDistanceToNowStrict(regDate, { addSuffix: true })}`;
    }

    if (status === EOfferStatus.PRE_LAUNCH) {
      if (!openDate) return "";
      return `${isFuture(openDate) ? "opens" : "due to open"
        } ${formatDistanceToNowStrict(openDate, { addSuffix: true })}`;
    }

    if (status === EOfferStatus.APPLICATIONS_OPEN) {
      if (!closeDate) return "";
      return `${isFuture(closeDate) ? "closes" : "due to close"
        } ${formatDistanceToNowStrict(closeDate, { addSuffix: true })}`;
    }

    return "";
  }, [status, regDate, openDate, closeDate]);

  const statusTheme = useMemo(() => {
    if (status === EOfferStatus.PRE_LAUNCH) return "success";
    if (status === EOfferStatus.APPLICATIONS_OPEN) return "success-strong";
    if (status === EOfferStatus.APPLICATIONS_CLOSED || status === EOfferStatus.ALLOCATING) return "failure-strong";
    return;
  }, [status]);

  const readyForOrders = useMemo(
    () => offer.totals || status === EOfferStatus.APPLICATIONS_OPEN,
    [offer, status]
  )
  const defaultTab = useMemo(
    () => {
      let tab = ""
      if (status === EOfferStatus.ALLOCATING || status === EOfferStatus.ALLOCATED || status === EOfferStatus.INSTRUCTIONS_SENT || status === EOfferStatus.SETTLED) {
        tab = "allocation"
      }
      else if (offer.totals || status === EOfferStatus.APPLICATIONS_OPEN) {
        tab = "order"
      }

      if (tab != "") {
        return "?tab=" + tab
      }
      return tab
    },
    [offer, status]
  );

  const offerPrice = useMemo(() => {
    if (offer.offer_price) {
      return formatCurrency(offer.offer_price, offer.currency, 12)
    } else if (offer.price_range_low && offer.price_range_high) {
      return formatCurrency(offer.price_range_low, offer.currency, 8) + " - " + formatCurrency(offer.price_range_high, offer.currency, 8)
    }
    return "TBC"
  }, [offer]);

  return (
    <Link href={`offers/${id}${defaultTab}`}>
      <a className={classNames("offer-card", className)}>
        <IssuerLogo
          issuer={issued_by}
          logo={issuer_logo}
          offerId={offer.id}
          className="offer-card__logo"
        >
          {readyForOrders && (
            <span className="offer-card__flair">
              {offer.totals ? "View Order" : "Ready for orders"}
            </span>
          )}
        </IssuerLogo>
        <div className="offer-card__content">
          <header className="offer-card__header">
            <Badge className="offer-card__status" theme={statusTheme} size="lg">
              {status}
            </Badge>
            <ul className="offer-card__tags">
              <Badge as="li" theme="ducati">
                {type}
              </Badge>
              {offer.shareholders_only && (
                <Badge as="li" theme="pink">
                  Shareholders Only
                </Badge>
              )}
              <Badge as="li" theme="vivid-orange">
                {currency}
              </Badge>
            </ul>
            <h1 className="offer-card__title">{name}</h1>
          </header>

          <dl className="offer-card__stats">
            <Definition className="offer-card__stat" size="xl" term="Price" description={offerPrice} />
            {offer.totals && (
              <><Definition
                className="offer-card__stat"
                size="xl"
                term="Total Value"
                description={
                  offer.totals?.notional_value
                    ? formatCurrency(
                      offer.totals?.notional_value,
                      offer.currency
                    )
                    : "--"
                }
              />
                <Definition
                  className="offer-card__stat"
                  size="xl"
                  term="Total Applications"
                  description={formatNumber(offer.totals?.applications)}
                /></>
            )}
          </dl>

          {progressLabel && (
            <p
              className={classNames("offer-card__progress-label", {
                "text-center": status === EOfferStatus.PRE_LAUNCH,
                "text-right": status === EOfferStatus.APPLICATIONS_OPEN,
              })}
            >
              {progressLabel}
            </p>
          )}
          {/* 
            Note: I decided not to use date functions to drive the code in the front end as the back end will be ensuring the status is correct based on the date, 
            and it's more robust to drive the code with concrete statuses.

            To that end some other stauses may need to be accounted for here with the display but I've not coded against them as they may change.
            Assuming the lifecycle of an offer remains as currently defined in the EOfferStatus enum, the following stauses may all be considered the same as 'CLOSED'
            for the purposes of display.

            ALLOCATED
            INSTRUCTIONS_SENT
            SETTLED
            ABANDONED
          */}
          <span
            className={classNames("offer-card__progress", {
              "offer-card__progress--active-1": [
                EOfferStatus.PRE_LAUNCH,
                EOfferStatus.APPLICATIONS_OPEN,
                EOfferStatus.APPLICATIONS_CLOSED,
              ].includes(status),
              "offer-card__progress--active-2": [
                EOfferStatus.APPLICATIONS_OPEN,
                EOfferStatus.APPLICATIONS_CLOSED,
              ].includes(status),
              "offer-card__progress--active-3":
                status === EOfferStatus.APPLICATIONS_CLOSED,
            })}
            aria-hidden="true"
          />

          <dl className="offer-card__timeline">
            <TimeDefinition
              term="Pre Launch"
              date={regDate}
              size="sm"
              showTime
            />
            <TimeDefinition term="Open" date={openDate} size="sm" showTime />
            <TimeDefinition term="Close" date={closeDate} size="sm" showTime />
          </dl>
        </div>
      </a>
    </Link>
  );
};
