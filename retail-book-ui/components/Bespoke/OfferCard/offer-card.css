.offer-card {
  @apply flex flex-col relative;

  &:before {
    content: '';
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 box-content bg-bluetiful-step-0;
    @apply transition-all duration-150 ease-linear;

    /* Stop any sub pixel bleed */
    width: 99%;
    height: 99%;
  }

  &:hover:before {
    @apply h-full w-full p-3xs;
  }

  &__logo {
    @apply bg-white relative;
  }

  &__flair {
    @apply absolute right-0 top-sm  px-sm py-2xs rounded-l-full bg-pink-step-0 text-white font-bold;
  }

  &__content {
    @apply bg-white relative grow flex flex-col px-sm pb-sm;
  }

  &__header {
    @apply mb-sm;
  }

  &__status {
    @apply relative -translate-y-1/2;
  }

  &__tags {
    @apply flex gap-2xs mb-xs;
  }

  &__title {
    @apply h2 text-duke-blue-step-0;
  }

  &__stats {
    @apply flex flex-col grow;
  }

  &__stat {
    @apply block mb-xs;
  }

  &__progress,
  &__timeline {
    @apply grid grid-cols-3;
  }

  &__progress-label {
    @apply text-sm font-bold mb-3xs;
  }

  &__progress {
    @apply grid grid-cols-3 mb-xs h-2xs bg-grey-step-4;

    transform: skew(var(--brand-slant-angle));

    &:before,
    &:after {
      content: '';
    }

    &:before {
      @apply bg-grey-step-1;
    }

    &:after {
      @apply bg-grey-step-2;
    }

    &--active-1:before,
    &--active-2:before {
      @apply bg-pink-step-0;
    }

    &--active-2:after {
      @apply bg-pink-step-0;
    }

    &--active-3 {
      @apply bg-pink-step-0;

      &:before,
      &:after {
        @apply content-none;
      }
    }
  }

  &__timeline {
    @apply gap-2xs items-end;
  }
}
