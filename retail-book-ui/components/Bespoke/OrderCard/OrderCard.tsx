import classNames from "classnames";
import { Badge } from "components/Basic/Badge/Badge";
import { Button } from "components/Basic/Button/Button";
import { Definition } from "components/Basic/Definition/Definition";
import { TimeDefinition } from "components/Basic/Definition/TimeDefinition";
import { StandaloneLink } from "components/Basic/StandaloneLink/StandaloneLink";
import { formatCurrency } from "currency";
import { format, parseISO } from "date-fns";
import { formatNumber, orderStatusThemeSelector, Zero } from "helpers";
import { Download, Eye, RefreshCw } from "react-feather";
import { EOfferType, EOrderType, TOffer, TOrder } from "types";

interface OrderCardProps {
  className?: string;
  order: TOrder;
  offer: TOffer;
  editable: boolean;
  onEditOrder?: () => void;
  onDeleteOrder?: () => void;  
  onRefreshOrder?: () => void;
  onViewApplications?: () =>void;
  onDownloadApplications?: ()=>void;
  dataUpdatedAt: number;
}

export const OrderCard = ({
  className,
  order,
  offer,
  editable,
  onEditOrder,
  onDeleteOrder,  
  onRefreshOrder,
  onViewApplications,
  onDownloadApplications,
  dataUpdatedAt
}: OrderCardProps) => (
  <article className={classNames("order-card", className)}>
    <div className="order-card__content">
      <div className="order-card__asOf">
          As of: {format(dataUpdatedAt, "dd MMM yy HH:mm:ss")}
      </div>
      <div className="order-card__meta">
        <div className="order-card__tags">          
          <Button icon={RefreshCw} onClick={onRefreshOrder} />
          <Badge
            theme={orderStatusThemeSelector(order.status)}
            size="lg"
            className="order-card__tag"
          >
            {order.status}
          </Badge>
          <Badge theme="red-orange" size="lg" className="order-card__tag">
            {order.order_type} Order
          </Badge>
        </div>
        {order.order_type === EOrderType.DETAILED && (
          <div className="order-card__links">
            
            <StandaloneLink icon={Eye} size="sm" className="order-card__link" onClick={onViewApplications}>
              View applications
            </StandaloneLink>
            {/* Note: Download the originally uploaded file */}
            <StandaloneLink
              icon={Download}
              size="sm"
              className="order-card__link"
              onClick={onDownloadApplications}
              
            >
              Download applications
            </StandaloneLink>
          </div>
        )}
      </div>
      <dl className="order-card__definitions">
        <TimeDefinition
          term="Order Date"
          date={parseISO(order.order_date)}
          className="order-card__definition"
          size="lg"
        />

        { offer?.type !== EOfferType.IPO && (
        <Definition
          term="Existing Holding"
          description={formatNumber(order.totals.existing_holding)}
          className="order-card__definition"
          size="lg"
        />)}
        <div>
          <Definition
            term="Applications"
            description={formatNumber(order.totals.applications)}
            className="order-card__definition"
            size="lg"
          />
          <ul className="order-card__breakdown">
            <li>
              Shareholder:{" "}
              <strong>{formatNumber(order.shareholding?.applications)}</strong>
            </li>
            <li>
              Non Shareholder:{" "}
              <strong>
                {formatNumber(order.non_shareholding?.applications)}
              </strong>
            </li>
          </ul>
        </div>
        <div>
          <Definition
            term="Value"
            description={formatCurrency(
              order.totals.notional_value ?? Zero,
              offer.currency
            )}
            className="order-card__definition"
            size="lg"
          />
          <ul className="order-card__breakdown">
            <li>
              Shareholder:{" "}
              <strong>
                {formatCurrency(
                  order.shareholding?.notional_value ?? Zero,
                  offer.currency
                )}
              </strong>
            </li>
            <li>
              Non Shareholder:{" "}
              <strong>
                {formatCurrency(
                  order.non_shareholding?.notional_value ?? Zero,
                  offer.currency
                )}
              </strong>
            </li>
          </ul>
        </div>
        <div>
          <Definition term="Entered By" description={order.entered_by} className="order-card__breakdown" size="sm"/>
        </div>
      </dl>
    </div>
    <div className="order-card__actions">
      {onEditOrder && (
        <Button className="order-card__action" onClick={onEditOrder} disabled={!editable}>
          Update
        </Button>
      )}
      {onDeleteOrder && (
        <Button
          className="order-card__action"
          theme="outline"
          onClick={onDeleteOrder}
          disabled={!editable}
        >
          Delete
        </Button>
      )}            
    </div>
  </article>
);
