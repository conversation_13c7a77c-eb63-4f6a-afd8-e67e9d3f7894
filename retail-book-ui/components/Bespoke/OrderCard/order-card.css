.order-card {
  @apply w-full p-sm rounded flex flex-col items-start gap-md bg-white;

  &__content {
    @apply grow;
  }

  &__meta {
    @apply flex flex-col gap-xs mb-md;
  }

  &__tags {
    @apply flex gap-xs;
  }

  &__links {
    @apply flex items-center divide-x divide-black gap-xs;
  }

  &__link:not(:first-child) {
    @apply pl-xs-sm;
  }

  &__definitions {
    @apply grow grid items-start grid-cols-1 gap-xs;
  }

  &__definition {
    @apply block mb-3xs;
  }

  &__breakdown {
    @apply text-sm text-grey-step-1 leading-tight;
  }

  &__asOf {
    @apply mb-xs text-xs text-grey-step-1 leading-tight;
  }

  &__actions {
    @apply w-full flex flex-col gap-xs;
  }

  @media screen(sm) {
    &__definitions {
      @apply grid-cols-2 gap-x-md gap-y-xs;
    }

    &__actions {
      @apply flex-row;
    }
  }

  @media screen(lg) {
    @apply flex-row;

    &__meta {
      @apply flex-row gap-md;
    }

    &__definitions {
      @apply grid-cols-5 gap-md;
    }

    &__actions {
      @apply w-auto flex-col;
    }
  }
}
