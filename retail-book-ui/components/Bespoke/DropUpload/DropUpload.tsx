import classNames from "classnames";
import { Button } from "components/Basic/Button/Button";
import { useDropzone, Accept } from "react-dropzone";
import { Plus, UploadCloud } from "react-feather";

interface DropUploadProps {
  className?: string;
  accept?: Accept;
  maxFiles?: number;
  onDrop: (_acceptedFiles: File[]) => void;
}

export const DropUpload = ({ className, onDrop, maxFiles, accept }: DropUploadProps) => {
  const { getRootProps, getInputProps, isDragActive,open } = useDropzone({ 
    onDrop:onDrop, 
    noClick:true,
    maxFiles: maxFiles,
    accept: accept,
  });

  return (
    <div
      className={classNames(
        "drop-upload",
        { "drop-upload--active": isDragActive },
        className
      )}
      {...getRootProps()}
    >
      <UploadCloud className="drop-upload__icon" aria-hidden="true" />
      <p className="drop-upload__message">
        {isDragActive ? <>Drop the files here...</> : <>Drag files here, or </>}
      </p>
      <Button
        className="drop-upload__action"
        icon={Plus}
        theme="special"
        type="button"
        onClick={open}
      >
        Browse files
      </Button>

      <input {...getInputProps()} />
    </div>
  );
};
