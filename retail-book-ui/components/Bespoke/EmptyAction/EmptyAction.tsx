import classNames from "classnames";
import { Button } from "components/Basic/Button/Button";
import React, { ElementType } from "react";
import { Info } from "react-feather";

interface EmptyActionProps {
  as?: ElementType;
  className?: string;
  message: string;
  icon?: ElementType;
  iconClassName?: string;
  action?: {
    label: string;
    onClick: (e: React.MouseEvent) => void;
    icon?: ElementType;
    disabled?: boolean;
  };
}

export const EmptyAction = ({
  className,
  icon: Icon = Info,
  iconClassName,
  message,
  action,
}: EmptyActionProps) => (
  <div className={classNames("empty-action", className)}>
    <Icon
      className={classNames("empty-action__icon", iconClassName)}
      aria-hidden="true"
    />
    { message != null && message != "" && (<p className="empty-action__message">{message}</p>)}
    {action && (
      <Button
        className="empty-action__action"
        icon={action.icon}
        onClick={action?.onClick}
        disabled={action?.disabled}
        theme="special"
      >
        {action.label}
      </Button>
    )}
  </div>
);
