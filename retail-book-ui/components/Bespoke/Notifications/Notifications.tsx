import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Alert } from "components/Basic/Alert/Alert";
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from "react";
import { Button } from "components/Basic/Button/Button";
import { Container } from "components/Layout/Container";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getUserNotificationsQuery, updateUserNotificationStatusMutation, updateAllUserNotificationsStatusMutation } from "utils/queries";
import { ApiGetParams, Notification, ENotificationCategory, ENotificationStatus } from "types";
import { Pagination } from "components/Basic/Pagination/Pagination";
import { Filter, FilterOption } from "../Filter/Filter";
import { useRouter } from "next/router";
import { parseISO } from "date-fns";

interface NotificationProps {
  open: boolean;
  onClose: () => void;
  hasNotification: Dispatch<SetStateAction<boolean>>;
  extId?: string;
  token?: string;
}

const pageSizeOptions:FilterOption<string>[] = [
  {
    label: "5",
    value: "5"
  },
  {
    label: "10",
    value: "10"
  },
  {
    label: "25",
    value: "25"
  }
]

export const toActionPath = (notification?: Notification) : string => {
  let actionPath = ""
  if (notification?.action?.category) {
    switch (notification.action?.category) {
      case ENotificationCategory.OFFER:
      case ENotificationCategory.OFFER_ORDER:
      case ENotificationCategory.OFFER_DOCUMENT:
      case ENotificationCategory.OFFER_ALLOCATION:
        actionPath = "/offers/" + notification.action?.target_id;
        break;
      case ENotificationCategory.WALL_CROSSING:
        actionPath = "/wallcrossings/" + notification.action?.target_id
        break;
    }
    switch (notification.action?.category) {
      case ENotificationCategory.OFFER_ORDER:
        actionPath = actionPath + "?tab=order";
        break;
      case ENotificationCategory.OFFER_DOCUMENT:
        actionPath = actionPath + "?tab=documents";
        break;
      case ENotificationCategory.OFFER_ALLOCATION:
        actionPath = actionPath + "?tab=allocation";
        break;
    }
  } else if (notification?.action?.path) {
    // TODO deprecated, back-compatible
    actionPath = notification.action?.path;
  }
  return actionPath
}

export const Notifications = ({ open, onClose, extId, token, hasNotification }: NotificationProps) => {
  const [currentPage,setCurrentPage] = useState(1);
  const [pageSize,setPageSize] = useState("5");
  const qc = useQueryClient();
  
  const pageSizeNumber = useMemo(() => Number(pageSize), [pageSize])
  
  const params: ApiGetParams = useMemo<ApiGetParams>(() => { return {
    limit: pageSizeNumber,
    offset: (currentPage - 1) * pageSizeNumber,
    sort:`created=desc`,
    filter: [`status=${ENotificationStatus.UNREAD}:eq`],
  }}, [currentPage, pageSizeNumber]);

  const { data: notifications } = useQuery(
    getUserNotificationsQuery(params, extId, token)
  );

  useEffect( () => {
    hasNotification(  (notifications?.pagination?.count ?? 0) > 0)
  },[notifications, hasNotification])

  const updateStatusMutationOption = useMemo(
    () => updateUserNotificationStatusMutation(qc, extId, token),
    [qc, token, extId]
  );

  const updateAllStatusMutationOption = useMemo(
    () => updateAllUserNotificationsStatusMutation(qc, extId, token),
    [qc, token, extId]
  );

  const updateStatusMutator = useMutation({
    ...updateStatusMutationOption,
  });

  const updateAllStatusMutator = useMutation({
    ...updateAllStatusMutationOption,
  });

  const setNotificationStatus = (id:string, status:string) => {
    const newState = status.toLocaleLowerCase()

    updateStatusMutator.mutate({id: id, status: newState, userId: extId ?? ""})
    qc.invalidateQueries(["user", extId, "notifications", params])
  }

  const setAllNotificationStatus = (status:string) => {
    const newState = status.toLocaleLowerCase()

    updateAllStatusMutator.mutate({status: newState, userId: extId ?? ""})
    qc.invalidateQueries(["user", extId, "notifications", params])
  }



  const router = useRouter()

  return (
    <StyledDialog
      bodyClassName="pl-0 pr-0 pt-2 overflow-auto h-full-screen"
      type="flyout"
      title="Notifications"
      open={open}
      onClose={onClose}
      footer={
        <Pagination onClick={ (page) => setCurrentPage(page) } currentPage={currentPage} pageSize={pageSizeNumber} totalItems={ notifications?.pagination.count ?? 0 }></Pagination>        
      }
    >
      <>
      <Container className="w-full overflow-hidden">
        <Container className="flex justify-left pb-1 pl-0 space-between">
          <Button size="sm" onClick={()=> {
            onClose();
            router.push("/notifications");
          }}>View all</Button>
          <div className="pr-2"></div>
          <Button size="sm" onClick={()=> {
            setAllNotificationStatus(ENotificationStatus.READ)
            onClose();
          }}>Mark all as read</Button>
        </Container>
        <Container className="flex justify-left pb-0 pl-0">
          <Filter label="Results per page"
                  className="pb-0"                
                    value={  pageSizeOptions.find( (x) => x.value === pageSize ) ?? pageSizeOptions[0] }
                    onChange={ (v:FilterOption<string>) => setPageSize(v.value) }
                    options={pageSizeOptions}
                    size="sm"
                                  />
        </Container>
        
        <Container className="flex pl-0 pr-0 flex-col space-y-1 overflow-auto w-full">
        { (notifications?.data ?? []).length < 1 ? (
          <EmptyAction message="There are currently no unread notifications to show" />
        ) : (
                    
              (notifications?.data ?? [])
              .map(( row ) => {
                // assemble the action path
                const actionPath = toActionPath(row)

                return (
                  <Alert
                    as="div"
                    className="flex-initial"
                    key={row.id}
                    message={ (<div><p><b>{row.title}</b></p><p>{row.message}</p></div>) }
                    date={ parseISO(row.created) }
                    action={{
                      // TODO back-compatibility for previously created notifications, can be removed once all current notifications have been generated by >= 1.4.x code
                      label: row.action.label == "Offer" || row.action.label == "WallCross" ? "View" : row.action.label,
                      onClick: () => {
                        router.push(actionPath)
                        setNotificationStatus(row.id, ENotificationStatus.READ)
                        onClose();
                      }
                    }}
                  />
              );
              })
          
        ) }
        </Container>
      </Container>
      </>
    </StyledDialog>
  );
};
