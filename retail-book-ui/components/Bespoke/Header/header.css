.header {
  @apply bg-duke-blue-step-0 sticky top-0 z-40;

  &__content {
    @apply relative py-sm flex items-center justify-between;
  }

  &__logo-link {
    @apply flex;
  }

  &__link {
    @apply text-white font-bold inline-flex items-center gap-xs;
    @apply transition-colors hover:text-pink-step-0;

    &--active {
      @apply relative;

      &:after {
        content: '';
        height: 3px;
        @apply absolute block w-full top-full bg-white;
      }
    }

    &--has-notification {
      @apply relative;

      &:before {
        content: '';
        @apply absolute block h-2 w-2 top-0 right-0 rounded-full bg-utility-red-step-0;
      }
    }
  }

  &__button {
    &--open {
      @apply text-pink-step-0;
    }
  }

  &__icon {
    @apply h-6 w-6;
  }

  &__desktop-navigation-wrapper {
    @apply flex items-center space-x-md;
  }

  &__desktop-navigation {
    @apply hidden;
  }

  &__dropdown-menu-wrapper {
    @apply flex;
  }

  &__dropdown-menu {
    @apply absolute z-10 mt-px right-sm-md top-full origin-top-right min-w-fit shadow-xl px-xs py-2xs bg-duke-blue-step-0 text-white;
  }

  &__dropdown-menu-item {
    @apply transition-colors;

    &--active {
      @apply text-pink-step-0;
    }
  }

  &__dropdown-menu-link {
    @apply block px-xs py-2xs font-bold;

    &--active {
      @apply underline underline-offset-8;
    }
  }

  &__menu-button {
    @apply inline-flex items-center justify-center text-white;
  }

  &__menu-button-icon {
    @apply w-7 h-7;
  }

  &__mobile-navigation {
    @apply py-sm border-t border-white;
  }

  &__mobile-submenu-button {
    @apply flex justify-between w-full;
  }

  &__mobile-submenu-button-label {
    @apply grow;
  }

  &__mobile-submenu-button-icon {
    @apply text-white;

    &--open {
      @apply rotate-180;
    }
  }

  &__mobile-navigation-items {
    @apply space-y-sm;

    &--submenu {
      /* ml-6 to match h-6 of header__icon, pl-xs to match gap-xs of header__link */
      @apply pt-sm ml-6 pl-xs;
    }
  }

  @media screen(lg) {
    &__menu-button,
    &__mobile-navigation {
      @apply hidden;
    }

    &__desktop-navigation {
      @apply flex gap-md items-center;
    }
  }
}
