import { Disclosure, Menu, Transition } from "@headlessui/react";
import { useQuery } from "@tanstack/react-query";
import classNames from "classnames";
import { Notifications } from "components/Bespoke/Notifications/Notifications";
import { Container } from "components/Layout/Container";
import { useNavigation } from "hooks/useNavigation";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { Fragment, useEffect, useState } from "react";
import { Bell, ChevronUp, <PERSON>u as Burger, User, X, Pie<PERSON>hart } from "react-feather";
import { TUser } from "types";
import { getUserQuery } from "utils/queries";

interface HeaderProps {
  currentPath?: string;
}

export const Header = ({ currentPath }: HeaderProps) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [hasNotifications, setHasNotifications] = useState(false);
  const { data: session, status } = useSession();
  const { data: user } = useQuery(
   { ...getUserQuery(session?.user?.ext_id, session?.accessToken),
    onError: () => {
      // Getting the user details is very important; if this fails, code elsewere defaults to different views
      //  therefore; if we can't get the user - we sign the user out - as there is clearly an issue.
      // This however might not have enough feedback for the users - so we will potentially need to implement a flow around this later.
      signOut();
    }}
  );

  useEffect(() => {
    if (session?.user.error === "RefreshAccessTokenError") {
      signOut();
    }
  }, [session]);

  return (
    <>
      <Disclosure as="header" className="header">
        {({ open }) => (
          <>
            <Container className="header__content">
              <Link href="/">
                <a className="header__logo-link">
                  <Image
                    alt="RetailBook Logo"
                    src="/RetailBookLogo-White.svg"
                    width="196"
                    height="36"
                  />
                  <h1 className="sr-only">RetailBook</h1>
                </a>
              </Link>
              {status === "authenticated" && (
                <>
                  <div className="header__desktop-navigation-wrapper">
                    <DesktopNavigation
                      currentPath={currentPath}
                      user={user}
                      showNotifications={() => setShowNotifications(true)}
                      hasNotification={hasNotifications}
                    />
                    <Disclosure.Button className="header__menu-button">
                      <span className="sr-only">Open main menu</span>
                      {open ? (
                        <X
                          className="header__menu-button-icon"
                          aria-hidden="true"
                        />
                      ) : (
                        <Burger
                          className="header__menu-button-icon"
                          aria-hidden="true"
                        />
                      )}
                    </Disclosure.Button>
                  </div>
                </>
              )}
            </Container>

            <MobileNavigation
              currentPath={currentPath}
              user={user}
              showNotifications={() => setShowNotifications(true)}
              hasNotification={hasNotifications}
            />
          </>
        )}
      </Disclosure>
      <Notifications
        open={showNotifications}
        onClose={() => setShowNotifications(false)}
        hasNotification={setHasNotifications}
        token={session?.accessToken}
        extId={session?.user.ext_id}
      />
    </>
  );
};

interface NavigationProps {
  currentPath?: string;
  user?: TUser;
  showNotifications: () => void;
  hasNotification?: boolean;
}

const MobileNavigation = ({
  currentPath,
  user,
  hasNotification,
  showNotifications,
}: NavigationProps) => {
  const { navigation, userNavigation } = useNavigation(user);

  return (
    <Disclosure.Panel>
      <Container as="nav" className="header__mobile-navigation">
        <ul className="header__mobile-navigation-items">
          {navigation.map(({ label, href }, index) => (
            <li key={index}>
              <Link href={href}>
                <a
                  className={classNames("header__link", {
                    "header__link--active": href === currentPath,
                  })}
                >
                  {label}
                </a>
              </Link>
            </li>
          ))}

          {user && (
            <li>
              <Link href={"/metrics"}>
                <a
                  className={classNames("header__link")}
                >
                  <span className="sr-only">View metrics</span>
                  
                  <PieChart className="header__icon" aria-hidden="true" />
                  <span>Metrics</span>
                </a>
              </Link>
            </li>
          )}

          {user && (
            <li>
              <button
                onClick={showNotifications}
                className={classNames("header__link", {
                  "header__link--has-notification": hasNotification,
                })}
              >
                <Bell className="header__icon_notify" aria-hidden="true" />
                <span>Notifications</span>
              </button>
            </li>
          )}

          <Disclosure>
            {({ open }) => (
              <li>
                <Disclosure.Button className="header__mobile-submenu-button">
                  <div className="header__link header__mobile-submenu-button-label">
                    <User className="header__icon" aria-hidden="true" />
                    <span>Profile</span>
                  </div>
                  <ChevronUp
                    className={classNames(
                      "header__icon header__mobile-submenu-button-icon",
                      {
                        "header__mobile-submenu-button-icon--open": open,
                      }
                    )}
                    aria-hidden="true"
                  />
                </Disclosure.Button>
                <Disclosure.Panel
                  as="ul"
                  className="header__mobile-navigation-items header__mobile-navigation-items--submenu"
                >
                  {userNavigation.map(({ label, href }, index) => (
                    <li key={index}>
                      <Link href={href}>
                        <a
                          className={classNames("header__link", {
                            "header__link--active": href === currentPath,
                          })}
                        >
                          {label}
                        </a>
                      </Link>
                    </li>
                  ))}
                </Disclosure.Panel>
              </li>
            )}
          </Disclosure>
        </ul>
      </Container>
    </Disclosure.Panel>
  );
};

const DesktopNavigation = ({
  currentPath,
  user,
  hasNotification,
  showNotifications,
}: NavigationProps) => {
  const { navigation, userNavigation } = useNavigation(user);

  return (
    <nav className="header__desktop-navigation">
      {navigation.map(({ label, href }, index) => (
        <Link key={index} href={href}>
          <a
            className={classNames("header__link", {
              "header__link--active": href === currentPath,
            })}
          >
            {label}
          </a>
        </Link>
      ))}

      {user && (
        <Link href="/metrics">
          <a
            className={classNames("header__link")}
          >
            <span className="sr-only">View metrics</span>
            
            <PieChart className="header__icon" aria-hidden="true" />
          </a>
        </Link>
      )}

      {user && (
        <button
          onClick={showNotifications}
          className={classNames("header__link", {
            "header__link--has-notification": hasNotification,
          })}
        >
          <span className="sr-only">View notifications</span>
          
          <Bell className="header__icon" aria-hidden="true" />
        </button>
      )}

      <Menu>
        {({ open }) => (
          <>
            <Menu.Button
              className={classNames("header__link header__button", {
                "header__button--open": open,
              })}
            >
              <span className="sr-only">Open user menu</span>
              <User className="header__icon" aria-hidden={true} />
            </Menu.Button>

            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="header__dropdown-menu" as="ul">
                {userNavigation.map(({ label, href }, index) => (
                  <Menu.Item key={index}>
                    {({ active }) => (
                      <li
                        className={classNames("header__dropdown-menu-item", {
                          "header__dropdown-menu-item--active": active,
                        })}
                      >
                        <Link href={href}>
                          <a
                            className={classNames(
                              "header__dropdown-menu-link",
                              {
                                "header__dropdown-menu-link--active":
                                  currentPath === href,
                              }
                            )}
                          >
                            {label}
                          </a>
                        </Link>
                      </li>
                    )}
                  </Menu.Item>
                ))}
              </Menu.Items>
            </Transition>
          </>
        )}
      </Menu>
    </nav>
  );
};
