import { useQuery } from "@tanstack/react-query";
import classNames from "classnames";
import { useSession } from "next-auth/react";
import { ReactNode } from "react";
import { getLogoQuery } from "utils/queries";

interface IssuerLogoProps {
  className?: string;
  children?: ReactNode;
  issuer: string;
  offerId: string;
  logo?: string;
}

export const IssuerLogo = ({
  className,
  issuer,
  logo,
  offerId,
  children,
}: IssuerLogoProps) => {
  const { data: session } = useSession();

  const { data: logoUrl, isLoading } = useQuery(
    getLogoQuery(offerId, logo, session?.accessToken)
  );

  return (
    <div className={classNames("issuer-logo", className)}>
      {!isLoading && logoUrl ? (
        <figure className="issuer-logo__figure">
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src={logoUrl}
            id={`${offerId}-image`}
            alt={`${issuer} logo`}
            className="issuer-logo__image"
          />
        </figure>
      ) : (
        <div className="issuer-logo__issuer">{issuer}</div>
      )}
      {children}
    </div>
  );
};
