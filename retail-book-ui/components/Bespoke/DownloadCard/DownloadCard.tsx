import classNames from "classnames";
import { Badge } from "components/Basic/Badge/Badge";
import { StandaloneLink } from "components/Basic/StandaloneLink/StandaloneLink";
import { formatFileSize } from "helpers";
import { ButtonHTMLAttributes, ElementType, useMemo } from "react";
import { Download, File, FileText, Image, Video } from "react-feather";
import { EContractStatus } from "types";

interface DownloadCardProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  as?: ElementType;
  className?: string;
  title: string;
  fileType?: string;
  size?: number;
  status?: EContractStatus;
}

export const DownloadCard = ({
  as: Component = "button",
  className,
  title,
  fileType = "document",
  size,
  status,
  ...props
}: DownloadCardProps) => {
  const FileIcon = useMemo(() => {
    /**
     * Note: These are the icons to be used and the below logic is illustrative, but precise implementation may depend on the format of fileType.
     * Ideally it would be the file extension as users are more likely to be familiar with that than a MIME type,
     * so the checks below may have to be expanded to accomodate that
     */
    if (~fileType.indexOf("image")) return Image;
    if (~fileType.indexOf("video")) return Video;
    if (~fileType.indexOf("pdf")) return FileText;

    return File;
  }, [fileType]);

  return (
    <Component className={classNames("download-card", className)} {...props}>
      <FileIcon className="download-card__file-icon" aria-hidden="true" />

      <div className="download-card__body">
        <h4 className="download-card__title">{title}</h4>
        <ul className="download-card__meta">
          {status && (
            <li>
              <Badge
                theme={
                  status === EContractStatus.SIGNED ? "success" : "warning"
                }
              >
                {status}
              </Badge>
            </li>
          )}
          <li>{fileType}</li>
          <li>{formatFileSize(size)}</li>
        </ul>
      </div>

      <div className="download-card__download">
        <StandaloneLink as="span" icon={Download} iconPosition="before">
          <span className="sr-only md:not-sr-only">Download</span>
        </StandaloneLink>
      </div>
    </Component>
  );
};
