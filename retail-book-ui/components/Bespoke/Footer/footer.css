.footer {
  @apply py-lg bg-duke-blue-step-0 text-white;

  &__header {
    @apply flex flex-col items-center justify-center gap-lg;
  }

  &__logo-link {
    @apply flex;
  }

  &__navigation {
    @apply flex flex-row items-start gap-xs mb-md;
  }

  &__link {
    @apply text-white;
    @apply transition-colors hover:text-pink-step-0;

    &--active {
      @apply underline underline-offset-4;
    }
  }

  &__social {
    @apply flex gap-sm;
  }

  &__social-link {
    @apply text-white;
    @apply transition-colors hover:text-pink-step-0;
  }

  &__social-icon {
    @apply h-6 w-6;
  }

  @media screen(lg) {
    &__header {
      @apply flex-row justify-between;
    }
  }
}
