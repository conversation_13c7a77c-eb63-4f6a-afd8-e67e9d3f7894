import classNames from "classnames";
import { Container } from "components/Layout/Container";
import { useNavigation } from "hooks/useNavigation";
import Link from "next/link";
import Linkedin from "assets/icons/linkedin.svg";
import Youtube from "assets/icons/youtube.svg";
import Twitter from "assets/icons/twitter.svg";
import { TUser } from "types";

interface FooterProps {
  currentPath?: string;
  user?: TUser;
}

export const Footer = ({ currentPath, user }: FooterProps) => {
  const { footerNavigation } = useNavigation(user);
  return (
    <footer className="footer pt-xs pb-xs">
      <Container>
        <div className="footer__header">
          <nav>
          {footerNavigation.map(({ label, href }, index) => (
            <Link key={index} href={href}>
              <a
                className={classNames("footer__link", {
                  "footer__link--active": href === currentPath,
                }, "pr-xs")}
              >
                {label}
              </a>
            </Link>
          ))}
          </nav>
          <p className="footer__copyright">
            <i>&copy; {new Date().getFullYear()}. All rights reserved.</i>
          </p>
          <nav className="footer__social">
            <a href="https://twitter.com/RetailBook_" className="footer__social-link">
              <Twitter className="footer__social-icon" />
            </a>
            <a href="https://www.youtube.com/@retailbook" className="footer__social-link">
              <Youtube className="footer__social-icon" />
            </a>
            <a href="https://www.linkedin.com/company/retail-book" className="footer__social-link">
              <Linkedin className="footer__social-icon" />
            </a>
          </nav>
        </div>
      </Container>
    </footer>
  );
};
