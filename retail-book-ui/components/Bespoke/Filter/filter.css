/* Uses HeadlessUI/Listbox */
.filter {
  @apply relative inline-block;

  &__button {
    @apply w-full flex items-center gap-xs text-black text-base border border-black px-sm py-2xs rounded-l-full rounded-r-full bg-white font-bold;
    @apply transition-colors hover:border-pink-step-0 hover:text-pink-step-0;

    &--has-value {
      @apply bg-pink-step-2;
      @apply hover:border-black hover:text-black;
    }

    &--open {
      @apply bg-bluetiful-step-2;
      @apply hover:border-black hover:text-black;
    }
  }

  &__button-label {
    @apply block truncate;
  }

  &__button-icon {
    @apply pointer-events-none h-5 w-5;
  }

  &__options {
    @apply dropdown absolute z-10 top-full mt-3xs right-0;
    @apply w-48;
  }

  &__option {
    @apply w-full cursor-default flex items-center justify-between p-xs select-none transition-colors;
  }

  &__option--active {
    @apply bg-pink-step-2;
  }

  &--sm &__button {
    @apply border-0 px-xs py-3xs text-sm bg-transparent;
  }

  &--sm &__options {
    @apply text-sm;
  }
}
