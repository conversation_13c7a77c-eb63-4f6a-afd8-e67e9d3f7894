import { Listbox, Transition } from "@headlessui/react";
import classNames from "classnames";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import { Fragment, useMemo } from "react";
import { ChevronDown } from "react-feather";
import { ExtractProps } from "types";

export interface FilterOption<T = string> {
  value: T;
  label: string;
}

interface FilterProps {
  value: FilterOption | FilterOption[];
  label: string;
  labelPlural?: string;
  options: FilterOption[];
  size?: "sm";
}

export const Filter = ({
  className,
  options,
  value,
  label,
  labelPlural,
  multiple,
  size,
  ...props
}: ExtractProps<typeof Listbox> & FilterProps) => {
  const hasValue = useMemo(() => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return !!value;
  }, [value]);

  const buttonLabel = useMemo(() => {
    if (Array.isArray(value) && hasValue) {
      if (value.length === 1) return value[0].label;
      if (value.length > 1) return `${value.length} ${labelPlural}`;
    }

    if (!Array.isArray(value) && hasValue) return `${label}: ${value.label}`;

    return label;
  }, [label, labelPlural, value, hasValue]);

  return (
    <Listbox
      value={value}
      as="div"
      className={classNames("filter", className, {
        [`filter--${size}`]: size,
      })}
      multiple={multiple}
      {...props}
    >
      <Listbox.Button as={Fragment}>
        {({ open }) => (
          <button
            className={classNames("filter__button", {
              "filter__button--open": open,
              "filter__button--has-value": multiple && hasValue,
            })}
          >
            <span className="filter__button-label">{buttonLabel}</span>
            <ChevronDown className="filter__button-icon" aria-hidden="true" />
          </button>
        )}
      </Listbox.Button>
      <Transition
        as={Fragment}
        leave="transition ease-in duration-100"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <Listbox.Options className="filter__options">
          {options.map((option) => (
            <Listbox.Option
              key={`${option?.label}-${option.value}`}
              className={({ active }) =>
                classNames("filter__option", {
                  "filter__option--active": active,
                })
              }
              value={option}
            >
              {({ selected }) => (
                <>
                  {multiple ? (
                    <Checkbox
                      className={classNames("pointer-events-none", {
                        "text-sm": size,
                      })}
                      checked={selected}
                      label={option.label}
                      value={option.value}
                      readOnly
                    />
                  ) : (
                    <> {option.label ?? option.value}</>
                  )}
                </>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </Transition>
    </Listbox>
  );
};
