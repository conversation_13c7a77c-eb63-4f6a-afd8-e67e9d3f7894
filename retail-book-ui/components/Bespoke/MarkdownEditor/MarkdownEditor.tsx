import classNames from "classnames";
import dynamic from "next/dynamic";
import { SimpleMDEReactProps } from "react-simplemde-editor";

const SimpleMDE = dynamic(() => import("react-simplemde-editor"), {
  ssr: false,
});

const simpleMDEOptions = {
  spellChecker: false,
  sideBySideFullscreen: false,
};

interface MarkdownEditorProps extends SimpleMDEReactProps {
  label: string;
  showLabel?: boolean;
  valid?: boolean;
  error?: string;
  disabled?: boolean;
}

export const MarkdownEditor = ({
  className,
  id,
  label,
  showLabel = true,
  disabled,
  error,
  valid,
  ...props
}: MarkdownEditorProps) => {
  return (
    <div
      className={classNames("markdown-editor texarea input", className, {
        "input--disabled": disabled,
        "input--invalid": error || valid === false,
      })}
    >
      <label
        htmlFor={id}
        className={classNames("input__label", {
          "sr-only": !showLabel,
        })}
      >
        {label}
      </label>
      <div className="markdown-editor__input-wrapper input__input-wrapper">
        <SimpleMDE
          options={simpleMDEOptions}
          textareaProps={{
            className: "input__input",
          }}
          {...props}
        />
      </div>
      {error && <span className="input__error">{error}</span>}
    </div>
  );
};
