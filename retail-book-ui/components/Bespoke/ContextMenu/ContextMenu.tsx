import { Fragment } from "react";
import { Menu, Transition } from "@headlessui/react";
import { MoreVertical } from "react-feather";
import classNames from "classnames";
import Link from "next/link";

export interface ContextMenuPropItem<R> {
  label: string;
  link?: string;
  onClick?: (e: React.MouseEvent, row: R) => void;
}

export interface ContextMenuProps<R> {
  className?: string;
  row: R;
  items: ContextMenuPropItem<R>[];  
}

export const ContextMenu = <R,>({
  items,
  row,
  className,
}: ContextMenuProps<R>) => {
  return (
    <Menu as="div" className={classNames("context-menu", className)}>
      {({ open }) => (
        <>
          <Menu.Button
            className={classNames("context-menu__button", {
              "context-menu__button--open": open,
            })}
          >
            <span className="sr-only">Open team member menu</span>
            <MoreVertical aria-hidden={true} />
          </Menu.Button>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-100"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Items className="context-menu__dropdown" as="ul">
              {items.map((item) => (
                <Menu.Item
                  as="li"
                  key={item.label}
                  className={({ active }) =>
                    classNames("context-menu__dropdown-item", {
                      "context-menu__dropdown-item--active": active,
                    })
                  }
                >
                  <Link href={item.link ?? ""}>
                    <a
                      className="context-menu__dropdown-link"
                      onClick={(e) => item.onClick?.(e, row)}
                    
                    >
                      {item.label}
                    </a>
                  </Link>
                </Menu.Item>
              ))}
            </Menu.Items>
          </Transition>
        </>
      )}
    </Menu>
  );
};
