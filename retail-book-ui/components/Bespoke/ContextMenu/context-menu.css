.context-menu {
  @apply inline-flex relative;

  &__button {
    @apply text-duke-blue-step-0;
  }

  &__dropdown {
    @apply dropdown border-none absolute right-0 top-full origin-top-right z-10;
    @apply w-28 divide-y divide-grey-step-3;
  }

  &__dropdown-item {
    @apply transition-colors;
  }

  &__dropdown-item--active {
    @apply bg-pink-step-2;
  }

  &__dropdown-link {
    @apply block text-sm text-right leading-normal px-xs py-2xs;
  }
}
