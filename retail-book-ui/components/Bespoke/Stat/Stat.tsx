import classNames from "classnames";
import { Delta } from "components/Basic/Delta/Delta";

export interface StatProps {
  className?: string;
  title: string;
  value?: string | number;
  change?: number | string;
  changeType?: "increase" | "decrease";
}

export const Stat = ({
  className,
  title,
  value,
  change,
  changeType,
}: StatProps) => {
  return (
    <figure className={classNames("stat", className)}>
      <div className="stat__body">
        <span className="stat__value">{value}</span>
        {changeType && !!change && (
          <Delta
            className="stat__delta"
            changeType={changeType}
            value={change}
          />
        )}
      </div>
      <figcaption className="stat__title">{title}</figcaption>
    </figure>
  );
};
