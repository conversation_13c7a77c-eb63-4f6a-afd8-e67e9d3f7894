import { Badge } from "components/Basic/Badge/Badge";
import { Delta } from "components/Basic/Delta/Delta";
import { Pagination } from "components/Basic/Pagination/Pagination";
import { Table } from "components/Basic/Table/Table";
import { formatCurrency } from "currency";
import { format, parseISO } from "date-fns";
import Decimal from "decimal.js";
import { formatNumber, formatStatus, orderStatusThemeSelector, Zero } from "helpers";
import { useMemo, useState } from "react";
import { EOfferType, TOffer, TOrderHistory } from "types";

interface OrderHistoryTableProps {
  className?: string;
  offer: TOffer;
  orderHistory: TOrderHistory[];
  paging: boolean;
  pagesize?: number;
}

const getChangeType = (v: Decimal | undefined) => {
  if (!v) {
    return "same";
  }

  if (v.lt(Zero)) {
    return "decrease";
  }
  if (v.gt(Zero)) {
    return "increase";
  }
  return "same";
};

const getDelta = (v: Decimal | undefined): string => {
  if (!v || v.eq(Zero)) {
    return "";
  }
  return formatNumber(Decimal.abs(v));
};

export const OrderHistoryTable = ({
  className,
  offer,
  orderHistory,
  paging=false,
  pagesize=4
}: OrderHistoryTableProps) => {

  const showExisting = useMemo(() => offer?.type !== EOfferType.IPO, [offer])

  const [currentPage, setCurrentPage] = useState(1);

  const rows = useMemo( () => {
    if (!paging) return orderHistory

    return orderHistory.slice(pagesize*(currentPage-1), pagesize*(currentPage))
  }, [paging, pagesize, currentPage, orderHistory])

  return (
    <>
    <Table className={className} compact>
      <Table.Head>
        <Table.Row>
          <Table.HeaderCell>Date</Table.HeaderCell>
          <Table.HeaderCell>Entered By</Table.HeaderCell>
          <Table.HeaderCell>Status</Table.HeaderCell>
          <Table.HeaderCell className="sm:text-right">
            Applications
          </Table.HeaderCell>
          <Table.HeaderCell>
            <span className="sr-only">Applications Delta</span>
          </Table.HeaderCell>
          <Table.HeaderCell className="sm:text-right">Value</Table.HeaderCell>
          <Table.HeaderCell>
            <span className="sr-only">Value Delta</span>
          </Table.HeaderCell>
          {showExisting && (<>
            <Table.HeaderCell className="sm:text-right">
              Existing Holding
            </Table.HeaderCell>
            <Table.HeaderCell>
              <span className="sr-only">Existing Holding Delta</span>
            </Table.HeaderCell></>)}
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {rows.map((historyItem, i) => (
          <Table.Row key={i}>
            <Table.Cell heading="Date">
              {format(
                parseISO(historyItem.order.order_date),
                "dd/MM/yyyy HH:mm"
              )}
            </Table.Cell>
            <Table.Cell heading="Entered By">
              {
                historyItem.order.entered_by
              }
            </Table.Cell>
            <Table.Cell heading="Status">
              <Badge
                theme={orderStatusThemeSelector(historyItem.order.status)}
                className="order-card__tag"
              >
                {formatStatus(historyItem.order.status)}
              </Badge>
            </Table.Cell>
            <Table.Cell heading="Applications" className="sm:text-right">
              <span className="font-mono">
                {formatNumber(historyItem.order.totals.applications)}
              </span>
            </Table.Cell>
            <Table.Cell heading="Applications Delta">
              <Delta
                className="font-mono"
                changeType={getChangeType(historyItem.delta.applications)}
                value={getDelta(historyItem.delta.applications)}
              />
            </Table.Cell>
            <Table.Cell heading="Value (£)" className="sm:text-right">
              <span className="font-mono">
                {formatCurrency(
                  historyItem.order.totals.notional_value ?? Zero,
                  offer.currency
                )}
              </span>
            </Table.Cell>
            <Table.Cell heading="Value Delta">
              <Delta
                className="font-mono"
                changeType={getChangeType(historyItem.delta.notional_value)}
                value={getDelta(historyItem.delta.notional_value)}
              />
            </Table.Cell>
            {showExisting && (
              <><Table.Cell heading="Existing Holding" className="sm:text-right">
                <span className="font-mono">
                  {formatNumber(historyItem.order.totals.existing_holding)}
                </span>
              </Table.Cell>
                <Table.Cell heading="Existing Holding Delta">
                  <Delta
                    className="font-mono"
                    changeType={getChangeType(historyItem.delta.existing_holding)}
                    value={getDelta(historyItem.delta.existing_holding)}
                  />
                </Table.Cell> </>
            )}
          </Table.Row>
        ))}
      </Table.Body>
    </Table>
    { paging && <div className="w-full flex place-content-end pt-2"><Pagination 
        onClick={ (page: number) => setCurrentPage(page) }
        currentPage={currentPage} 
        pageSize={pagesize} 
        totalItems={orderHistory.length} /></div>
    }
    </>
  );
};
