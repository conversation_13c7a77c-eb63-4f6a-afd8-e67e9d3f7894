import classNames from "classnames";
import { Button } from "components/Basic/Button/Button";
import { Definition } from "components/Basic/Definition/Definition";
import { format, parse } from "date-fns";
import { RefreshCw } from "react-feather";
import { TSettlementBook } from "types";

interface SettlementCardProps {
  className?: string;
  book: TSettlementBook | undefined;
  canAmendInstructions: boolean;
  generateObligations?: () => void;
}

export const SettlementCard = ({
  className,
  book,
  canAmendInstructions,
  generateObligations,
}: SettlementCardProps) => {
  
  if (!canAmendInstructions && !book) {
    return <></>
  }

  return (
    <article className={classNames("settlement-card", className)}>
      <div className="settlement-card__content">
        <div className="settlement-card__header">
          {book?.created && (
            <div className="settlement-card__header__asOf">
              Generated at: {format(parse(book.created?.substring(0, 19), "yyyy-MM-dd'T'HH:mm:ss", new Date()), "dd MMM yy HH:mm:ss")}
            </div>
          )}
        </div>
        <dl className="settlement-card__definitions">
          <div>
            <Definition
              term="ISIN"
              description={book?.isin_at_creation}
              size="sm"
              className="settlement-card__definition"
            />
          </div>
          <div>
            <Definition
              term="Security Name"
              description={book?.security_name_at_creation}
              size="sm"
              className="settlement-card__definition"
            />
          </div>
          <div>
            <Definition
              term="Settlement Date"
              description={book?.settlement_date_at_creation ? format(parse(book.settlement_date_at_creation, "yyyy-MM-dd'T'HH:mm:ss'Z'", new Date()), "dd MMM yy") : undefined}
              size="sm"
              className="settlement-card__definition"
            />
          </div>
          <div>
            <div className="flex flex-col space-y-2">
              {canAmendInstructions && <Button size="sm" icon={RefreshCw} onClick= { () => generateObligations && generateObligations() }>Generate Settlements</Button>}
            </div>
          </div>
        </dl>
      </div>
    </article>
  )
};