import SpinnerSvg from "assets/Spinner.svg";
import classNames from "classnames";

interface SpinnerProps {
  className?: string;
  message?: string;
  size?: "sm" | "lg";
}

export const Spinner = ({ message, size, className }: SpinnerProps) => (
  <div
    className={classNames("spinner", className, {
      [`spinner--${size}`]: size,
    })}
  >
    <SpinnerSvg className="spinner__icon" />
    {message && <p className="spinner__message">{message}</p>}
  </div>
);
