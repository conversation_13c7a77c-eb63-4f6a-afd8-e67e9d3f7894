import classNames from "classnames";
import {
  HTMLAttributes,
  ReactNode,
  TableHTMLAttributes,
  TdHTMLAttributes,
  ThHTMLAttributes,
} from "react";

interface TableProps extends TableHTMLAttributes<HTMLTableElement> {
  children: ReactNode;
  compact?: boolean;
}

export const Table = ({
  className,
  children,
  compact = false,
  ...props
}: TableProps) => {
  return (
    <table
      {...props}
      className={classNames("tbl", className, { "tbl--compact": compact })}
    >
      {children}
    </table>
  );
};

interface TableHeadProps extends HTMLAttributes<HTMLTableSectionElement> {
  className?: string;
  children: ReactNode;
}
const TableHead = ({ children, className, ...props }: TableHeadProps) => (
  <thead {...props} className={classNames("tbl__thead", className)}>
    {children}
  </thead>
);

interface TableBodyProps extends HTMLAttributes<HTMLTableSectionElement> {
  className?: string;
  children: ReactNode;
}
const TableBody = ({ children, className, ...props }: TableBodyProps) => (
  <tbody {...props} className={classNames("tbl__tbody", className)}>
    {children}
  </tbody>
);

interface TableRowProps extends HTMLAttributes<HTMLTableRowElement> {
  className?: string;
  children: ReactNode;
}
const TableRow = ({ children, className, ...props }: TableRowProps) => (
  <tr {...props} className={classNames("tbl__row", className)}>
    {children}
  </tr>
);

interface TableHeaderCellProps extends ThHTMLAttributes<HTMLTableCellElement> {
  className?: string;
  children: ReactNode;
}
const TableHeaderCell = ({
  children,
  className,
  ...props
}: TableHeaderCellProps) => (
  <th
    {...props}
    scope="col"
    className={classNames("tbl__cell tbl__cell--head", className)}
  >
    {children}
  </th>
);

interface TableCellProps extends TdHTMLAttributes<HTMLTableCellElement> {
  className?: string;
  heading: string;
  children: ReactNode;
}
const TableCell = ({
  children,
  heading,
  className,
  ...props
}: TableCellProps) => (
  <td {...props} className={classNames("tbl__cell", "align-middle", className)}>
    <span className="tbl__cell-heading">{heading}</span>
    {children}
  </td>
);

interface TableCaptionProps extends HTMLAttributes<HTMLTableCaptionElement> {
  className?: string;
  children: ReactNode;
}
const TableCaption = ({ children, className, ...props }: TableCaptionProps) => (
  <caption {...props} className={classNames("tbl__caption", className)}>
    {children}
  </caption>
);

Table.Head = TableHead;
Table.Body = TableBody;
Table.Row = TableRow;
Table.HeaderCell = TableHeaderCell;
Table.Cell = TableCell;
Table.Caption = TableCaption;
