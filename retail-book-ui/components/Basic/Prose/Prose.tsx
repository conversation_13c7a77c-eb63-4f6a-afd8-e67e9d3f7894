import classNames from "classnames";
import { ElementType, forwardRef, HTMLAttributes, ReactNode } from "react";

interface ProseProps extends HTMLAttributes<HTMLElement> {
  as?: ElementType;
  children?: ReactNode;
  className?: string;
}

export const Prose = forwardRef<HTMLElement, ProseProps>(
  ({ as: Component = "div", children, className, ...props }, ref) => {
    return (
      <Component
        ref={ref}
        className={classNames("prose", className)}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Prose.displayName = "Prose";
