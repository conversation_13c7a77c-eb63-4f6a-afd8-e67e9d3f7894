import classNames from "classnames";
import { ElementType, ReactNode } from "react";

interface BadgeProps {
  as?: ElementType;
  className?: string;
  children?: ReactNode;
  theme?:
    | "success"
    | "failure"
    | "warning"
    | "info"
    | "success-strong"
    | "failure-strong"
    | "warning-strong"
    | "vivid-orange"
    | "pink"
    | "red-orange"
    | "ducati"
    | "jade"
    | "light-bluetiful";
  size?: "lg";
}

export const Badge = ({
  as: Component = "span",
  children,
  className,
  theme,
  size,
}: BadgeProps) => (
  <Component
    className={classNames("badge", className, {
      [`badge--${theme}`]: theme,
      [`badge--${size}`]: size,
    })}
  >
    {children}
  </Component>
);
