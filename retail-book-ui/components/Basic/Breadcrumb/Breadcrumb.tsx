import { ChevronRight, Home } from "react-feather";
import classNames from "classnames";
import Link from "next/link";

export interface Crumb {
  name: string;
  href: string;
}

interface BreadcrumbProps {
  className?: string;
  crumbs: Crumb[];
  currentPath: string;
}

export const Breadcrumb = ({
  crumbs,
  className,
  currentPath,
}: BreadcrumbProps) => (
  <nav className={classNames("breadcrumb", className)} aria-label="Breadcrumb">
    <ol className="breadcrumb__list">
      <li className="breadcrumb__item">
        <Link href="/">
          <a className="breadcrumb__link">
            <Home className="breadcrumb__icon" aria-hidden="true" />
            <span className="sr-only">Home</span>
          </a>
        </Link>
      </li>
      {crumbs.map((crumb) => (
        <li
          key={crumb.name}
          className={classNames("breadcrumb__item", {
            "breadcrumb__item--active": crumb.href === currentPath,
          })}
        >
          <ChevronRight className="breadcrumb__icon" />
          <Link href={crumb.href}>
            <a
              className="breadcrumb__link"
              aria-current={crumb.href === currentPath ? "page" : undefined}
            >
              {crumb.name}
            </a>
          </Link>
        </li>
      ))}
    </ol>
  </nav>
);
