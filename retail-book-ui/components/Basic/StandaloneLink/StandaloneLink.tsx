import classNames from "classnames";
import { ElementType, ReactNode, AnchorHTMLAttributes } from "react";
import { ArrowRight } from "react-feather";

/* Note: Could be useful to wrap this in a next/link */

interface StandaloneLinkProps extends AnchorHTMLAttributes<HTMLAnchorElement> {
  as?: ElementType;
  children: ReactNode;
  size?: "sm" | "lg";
  icon?: ElementType|null;
  iconPosition?: "before" | "after";
}

export const StandaloneLink = ({
  as: Component = "a",
  children,
  size,
  className,
  icon: Icon = ArrowRight,
  iconPosition = "after",
  ...props
}: StandaloneLinkProps) => (
  <Component
    className={classNames("standalone-link", className, {
      [`standalone-link--${size}`]: size,
      [`standalone-link--icon-${iconPosition}`]: Icon && iconPosition,
    })}
    {...props}
  >
    {children}
    {Icon && <Icon className="standalone-link__icon" aria-hidden="true" />}
  </Component>
);
