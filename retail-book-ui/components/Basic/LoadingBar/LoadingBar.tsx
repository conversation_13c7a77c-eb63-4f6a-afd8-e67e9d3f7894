import classNames from "classnames";

interface LoadinBarProps {
  className?: string;
  progress?: number; // 0 - 100
  message?: string;
}

export const LoadingBar = ({
  progress,
  message,
  className,
}: LoadinBarProps) => (
  <div className={classNames("loading-bar", className)}>
    {progress && <p className="loading-bar__progress">{progress}%</p>}
    <div className="loading-bar__bar">
      <span
        className="loading-bar__bar-progress"
        style={{ width: `${progress}%` }}
      ></span>
    </div>
    {message && <p className="loading-bar__message">{message}</p>}
  </div>
);
