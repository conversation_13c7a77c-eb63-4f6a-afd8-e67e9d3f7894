import {
  Info,
  XCircle,
  CheckCircle,
  AlertCircle,
  ChevronRight,
} from "react-feather";
import classNames from "classnames";
import { ElementType, ReactNode } from "react";
import { format } from "date-fns";
import { Button } from "components/Basic/Button/Button";

interface AlertProps {
  as?: ElementType;
  className?: string;
  timeClassName?: string;
  title?: string;
  message: ReactNode;
  date?: Date;
  theme?: "info" | "success" | "failure" | "warning";
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const Alert = ({
  as: Component = "aside",
  className,
  theme = "info",
  title,
  message,
  date,
  action,
  timeClassName,
}: AlertProps) => (
  <Component
    role="alert"
    className={classNames(`alert alert--${theme}`, className)}
  >
    {theme === "success" ? (
      <CheckCircle aria-hidden="true" className="alert__icon" />
    ) : theme === "failure" ? (
      <XCircle aria-hidden="true" className="alert__icon" />
    ) : theme === "warning" ? (
      <AlertCircle aria-hidden="true" className="alert__icon" />
    ) : (
      <Info aria-hidden="true" className="alert__icon" />
    )}
    <div className="alert__content">
      <>
        {(date || title) && (
          <header className="alert__header">
            {date && (
              <time className={classNames("alert__datetime", timeClassName)}>
                {format(date, "dd MMM yyyy HH:mm")}
              </time>
            )}
            {title && <h3 className="alert__title">{title}</h3>}
          </header>
        )}
        <div className="alert__message">{message}</div>
        {action && (
          <Button
            icon={ChevronRight}
            className="alert__action"
            onClick={action.onClick}
          >
            {action.label}
          </Button>
        )}
      </>
    </div>
  </Component>
);
