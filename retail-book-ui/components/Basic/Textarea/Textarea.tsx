import classNames from "classnames";
import { forwardRef, TextareaHTMLAttributes } from "react";

interface TextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  showLabel?: boolean;
  valid?: boolean;
  error?: string;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      name,
      id,
      label,
      showLabel = true,
      disabled,
      error,
      valid,
      ...props
    },
    ref
  ) => {
    return (
      <div
        className={classNames("texarea input", className, {
          "input--disabled": disabled,
          "input--invalid": error || valid === false,
        })}
      >
        <label
          htmlFor={id ?? name}
          className={classNames("input__label", {
            "sr-only": !showLabel,
          })}
        >
          {label}
        </label>
        <div className="input__input-wrapper">
          <textarea
            ref={ref}
            className="input__input"
            name={name}
            disabled={disabled}
            id={id ?? name}
            {...props}
          />
        </div>
        {error && <span className="input__error">{error}</span>}
      </div>
    );
  }
);

Textarea.displayName = "Textarea";
