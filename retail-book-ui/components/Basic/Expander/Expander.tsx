import { FunctionComponent } from "react";
import { ChevronDown, ChevronRight} from "react-feather";

interface IProps {
    expanded:boolean
    onExpand:()=>void
    onContract:()=>void
}

export const Expander:FunctionComponent<IProps> = ({expanded, onExpand, onContract}) => {    
    return (
        expanded ? 
            <ChevronDown className="cursor-pointer" onClick={onContract}/> : 
            <ChevronRight className="cursor-pointer" onClick={onExpand}/>       
    )
}