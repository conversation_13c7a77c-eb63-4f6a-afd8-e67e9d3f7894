import Spinner from "assets/icons/spinner.svg";
import classNames from "classnames";
import { ElementType, HTMLAttributes, ReactNode, useMemo } from "react";
import { Tooltip } from "react-tooltip";
import { v4 as uuidv4 } from "uuid";

interface ButtonProps extends HTMLAttributes<HTMLElement> {
  children?: ReactNode;
  href?: string;
  size?: "sm" | "lg";
  theme?: "outline" | "light" | "outline-light" | "special" | "link";
  icon?: ElementType;
  iconPosition?: "before" | "after";
  loading?: boolean;
  type?: "submit" | "reset" | "button";
  disabled?: boolean;
  tooltip?: string;
}

export const Button = ({
  children,
  size,
  theme,
  className,
  icon: Icon,
  iconPosition = "after",
  loading = false,
  tooltip,
  ...props
}: ButtonProps) => {
  const Component = props.href ? "a" : "button";
  const uuid = useMemo(() => (tooltip ? uuidv4() : undefined), [tooltip]);

  return (
    <>
      <Component
        className={classNames("button", className, {
          [`button--${size}`]: size,
          [`button--${theme}`]: theme,
          [`button--icon-${iconPosition}`]: Icon,
          [`button--icon-only`]: Icon && !children,
        })}
        {...props}
        data-tooltip-id={uuid}
      >
        {children}
        {Icon && !loading && (
          <Icon className="button__icon" aria-hidden="true" />
        )}
        {loading && (
          <span className="button__icon">
            <Spinner className="button__loader" aria-hidden="true" />
          </span>
        )}
      </Component>
      {tooltip && (
        <Tooltip
          id={uuid}
          place="bottom"
          variant="info"
          className="button__tooltip"
        >
          {tooltip}
        </Tooltip>
      )}
    </>
  );
};
