.button {
  @apply px-md py-2xs text-base inline-flex items-center justify-center rounded-full border font-bold text-center transition-colors gap-3xs;
  @apply border-bluetiful-step-0 bg-bluetiful-step-0 text-white;
  @apply hover:bg-bluetiful-step--1 hover:border-bluetiful-step--1;

  &:active {
    @apply border-bluetiful-step-0 bg-bluetiful-step-0;
    box-shadow: 0 3px 0 0 theme(colors.bluetiful.step--1) inset;
  }

  &[disabled] {
    @apply bg-grey-step-4 border-grey-step-4 text-grey-step-1 pointer-events-none;
  }

  &__icon {
    @apply text-current translate-x-1/4;
    height: 1.25em;
    width: 1.25em;
  }

  &__loader {
    @apply animate-spin;
    animation-duration: 2s;
    height: 1.25em;
    width: 1.25em;
  }

  &__tooltip {
    @apply text-black;
  }

  &--icon-before &__icon {
    @apply -translate-x-1/4;
    order: -1;
  }

  &--sm {
    @apply px-sm py-3xs text-sm;
  }

  &--lg {
    @apply text-lg;
  }

  &--outline {
    @apply bg-transparent border-bluetiful-step-0 text-bluetiful-step-0;
    @apply hover:border-bluetiful-step--1 hover:text-bluetiful-step--1 hover:bg-bluetiful-step-2;
    @apply focus:bg-transparent focus:border-bluetiful-step-0 focus:text-bluetiful-step-0;
    @apply active:border-bluetiful-step--1;
  }

  &--outline[disabled] {
    @apply bg-transparent border-grey-step-1 text-grey-step-1;
  }

  &--light {
    @apply bg-white border-white text-bluetiful-step-0;
    @apply hover:border-bluetiful-step-2 hover:text-bluetiful-step--1 hover:bg-bluetiful-step-2;
    @apply focus:bg-bluetiful-step-0 focus:border-bluetiful-step-0 focus:text-white;
  }

  &--outline-light {
    @apply bg-transparent border-white text-white;
    @apply hover:bg-bluetiful-step--1;
    @apply focus:bg-transparent;
    @apply active:border-bluetiful-step-2 active:text-white;
  }

  &--outline-light[disabled] {
    @apply border-grey-step-2 text-grey-step-2;
  }

  &--light:active,
  &--outline-light:active {
    @apply bg-white border-white text-bluetiful-step-0;

    box-shadow: 0 3px 0 0 theme(colors.bluetiful.step-2) inset;
  }

  &--special {
    @apply border-none bg-pink-step-0 bg-right bg-no-repeat;
    @apply transition-all hover:bg-pink-step-0 hover:border-none;

    background-image: url("/SpecialButton.svg");
    background-size: 50% auto;

    &:active {
      @apply border-none;
      box-shadow: 0 3px 0 0 theme(colors.pink.step--1) inset;
    }

    &:hover {
      background-position: 120% center;
    }
  }

  &--special[disabled] {
    @apply bg-pink-step-0 text-grey-step--1 opacity-50;
  }

  &--link {
    @apply link font-normal;
    @apply no-underline border-none bg-transparent;
    @apply hover:border-none hover:bg-transparent;
    @apply active:border-none active:bg-transparent active:shadow-none;
  }

  &--icon-only {
    @apply rounded-full p-3xs;
  }

  &--icon-only &__icon {
    @apply translate-x-0;
  }

  &--icon-only.button--special {
    @apply bg-cover hover:bg-right;
  }
}
