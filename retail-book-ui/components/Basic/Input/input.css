.input {
  &__label {
    @apply block font-bold text-black mb-3xs;
  }

  &__input-wrapper {
    @apply flex border items-center border-grey-step-1 bg-white text-base;
    border-radius: 1px;
  }

  &__input-wrapper:focus-within {
    outline: 2px solid theme(colors.utility-yellow.step-0);
    outline-offset: 2px;
  }

  &__input {
    @apply w-full border-none text-base p-2xs text-grey-step-0 placeholder:text-grey-step-2;
  }

  &__input[disabled] {
    @apply bg-grey-step-4;
  }

  &__input:focus {
    border: none;
    box-shadow: none;
  }

  &__affix {
    @apply shrink-0 pointer-events-none flex items-center justify-center px-2xs border-none text-grey-step-1;
    line-height: 1.5rem;
  }

  &__icon {
    @apply shrink-0 pointer-events-none mr-2xs h-7 w-7;
  }

  &__icon--invalid {
    @apply text-utility-red-step-0;
  }

  &__icon--valid {
    @apply text-utility-green-step-0;
  }

  &__error {
    @apply block text-utility-red-step-0 mt-3xs;
  }

  &--disabled &__input-wrapper {
    @apply bg-grey-step-4 text-grey-step-1;
  }

  &--invalid &__input-wrapper {
    @apply border-utility-red-step-0;
  }
}
