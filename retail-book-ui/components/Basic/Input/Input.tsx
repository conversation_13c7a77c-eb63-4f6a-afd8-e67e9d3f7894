import { CheckCircle, XCircle } from "react-feather";
import classNames from "classnames";
import { forwardRef, InputHTMLAttributes } from "react";

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  showLabel?: boolean;
  valid?: boolean;
  error?: string;
  before?: string;
  after?: string;
}

const Affix = ({ label }: { label: string }) => (
  <div className="input__affix">
    <span>{label}</span>
  </div>
);

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      name,
      id,
      type = "text",
      label,
      showLabel = true,
      disabled,
      error,
      valid,
      before,
      after,
      hidden,
      ...props
    },
    ref
  ) => {
    return (
      <div
        className={classNames("input", className, {
          "input--disabled": disabled,
          "input--invalid": error || valid === false,
          hidden,
        })}
      >
        <label
          htmlFor={id ?? name}
          className={classNames("input__label", {
            "sr-only": !showLabel,
          })}
        >
          {label}
        </label>
        <div className="input__input-wrapper">
          {before && <Affix label={before} />}
          <input
            ref={ref}
            className="input__input"
            name={name}
            type={type}
            disabled={disabled}
            hidden={hidden}
            id={id ?? name}
            {...props}
          />
          {after && <Affix label={after} />}
          {error && (
            <XCircle
              aria-hidden="true"
              className="input__icon input__icon--invalid"
            />
          )}
          {valid && (
            <CheckCircle
              aria-hidden="true"
              className="input__icon input__icon--valid"
            />
          )}
        </div>
        {error && <span className="input__error">{error}</span>}
      </div>
    );
  }
);

Input.displayName = "Input";
