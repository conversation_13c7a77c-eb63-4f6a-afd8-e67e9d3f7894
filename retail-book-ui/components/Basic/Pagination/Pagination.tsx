import classNames from "classnames";
import { use<PERSON>allback, useMemo } from "react";
import { ChevronLeft, ChevronRight } from "react-feather";

export interface PaginationProps {
  className?: string;
  onClick: (page: number) => void;
  currentPage: number;
  pageSize: number;
  totalItems: number;
}

const PageNumberLinks = ({
  totalPages,
  currentPage,
  onClick,
}: {
  totalPages: number;
  currentPage: number;
  onClick: PaginationProps["onClick"];
}) => {
  const pages = useMemo(() => {
    if (totalPages <= 1) return [];
    let pages: number[] = [currentPage];
    if (currentPage - 1 > 0) pages = [currentPage - 1, ...pages];
    if (currentPage + 1 <= totalPages) pages = [...pages, currentPage + 1];
    if (currentPage === totalPages && currentPage - 2 > 0)
      pages = [currentPage - 2, ...pages];
    if (currentPage === 1 && currentPage + 2 <= totalPages)
      pages = [...pages, currentPage + 2];
    return pages;
  }, [currentPage, totalPages]);

  return (
    <>
      {pages.map((page) => (
        <li
          key={page}
          className={classNames("pagination__page", {
            "pagination__page--active": page === currentPage,
          })}
        >
          <a
            className="pagination__link"
            onClick={() => onClick(page)}
            aria-current={page === currentPage ? "page" : undefined}
          >
            {page}
          </a>
        </li>
      ))}
    </>
  );
};

export const Pagination = ({
  className,
  onClick,
  currentPage,
  totalItems,
  pageSize,
}: PaginationProps) => {
  const totalPages = useMemo(
    () => Math.ceil(totalItems / pageSize),
    [totalItems, pageSize]
  );

  const handleNextClick = useCallback(() => {
    if (currentPage < totalPages) {
      onClick(currentPage + 1);
    }
  }, [currentPage, totalPages, onClick]);

  const handlePrevClick = useCallback(() => {
    if (currentPage > 1) {
      onClick(currentPage - 1);
    }
  }, [currentPage, onClick]);

  return (
    <nav
      className={classNames("pagination", className)}
      aria-label="pagination"
    >
      {totalItems > 0 && (
        <span className="pagination__count">
          {/* show item numbers that are showing. example: "6-10 of 12" */}
          {currentPage * pageSize - pageSize + 1}-
          {currentPage * pageSize > totalItems
            ? totalItems
            : currentPage * pageSize}{" "}
          of {totalItems}
        </span>
      )}
      {totalPages > 1 && (
        <ol className="pagination__pages">
          {currentPage > 1 && (
            <li className="pagination__page">
              <a
                onClick={handlePrevClick}
                className="pagination__link pagination__link--icon"
              >
                <span className="sr-only">Previous page</span>
                <ChevronLeft className="pagination__icon" />
              </a>
            </li>
          )}

          <PageNumberLinks
            totalPages={totalPages}
            currentPage={currentPage}
            onClick={onClick}
          />

          {currentPage < totalPages && (
            <li className="pagination__page">
              <a
                onClick={handleNextClick}
                className="pagination__link pagination__link--icon"
              >
                <span className="sr-only">Next page</span>
                <ChevronRight className="pagination__icon" />
              </a>
            </li>
          )}
        </ol>
      )}
    </nav>
  );
};
