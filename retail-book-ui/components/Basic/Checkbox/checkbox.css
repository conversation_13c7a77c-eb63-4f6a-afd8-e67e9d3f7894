.checkbox {
  @apply inline-flex items-start text-black;

  &__label {
    @apply ml-xs leading-tight;
  }

  &__description {
    @apply block text-sm mt-3xs text-grey-step-0;
  }

  &__input {
    @apply h-5 w-5 border border-grey-step-0 transition-colors;
    @apply checked:bg-pink-step--1;
    @apply focus:checked:bg-pink-step--1;
  }

  &__input:focus {
    box-shadow: none;
  }

  &__input[disabled] {
    @apply border-grey-step-2;
    @apply checked:bg-grey-step-2;
    @apply focus:checked:bg-grey-step-2;
  }

  &__input[type='checkbox'] {
    border-radius: 2px;
  }

  &:hover &__input:checked {
    @apply bg-bluetiful-step-0;
  }
}
