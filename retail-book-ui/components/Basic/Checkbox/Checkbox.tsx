import classNames from "classnames";
import { forwardRef, InputHTMLAttributes } from "react";

interface CheckboxProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  type?: "checkbox" | "radio";
  description?: string;
  showLabel?: boolean;
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  (
    {
      className,
      name,
      type = "checkbox",
      showLabel = true,
      label,
      description,
      ...props
    },
    ref
  ) => {
    return (
      <label className={classNames("checkbox", className)}>
        <input
          ref={ref}
          className="checkbox__input"
          name={name}
          type={type}
          {...props}
        />
        <div
          className={classNames("checkbox__label", {
            "sr-only": !showLabel,
          })}
        >
          {label}
          {description && (
            <span className="checkbox__description">{description}</span>
          )}
        </div>
      </label>
    );
  }
);

Checkbox.displayName = "Checkbox";
