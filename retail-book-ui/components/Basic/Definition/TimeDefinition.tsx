import classNames from "classnames";
import { format, formatDistanceToNowStrict } from "date-fns";
import { DefinitionProps } from "./Definition";

interface TimeDefinitionProps extends DefinitionProps {
  showCountdown?: boolean;
  date: Date | null;
  showTime?: boolean;
}

export const TimeDefinition = ({
  as: Component = "div",
  termAs: Term = "dt",
  descriptionAs: Description = "dd",
  className,
  term,
  size,
  icon: Icon,
  iconClassName,
  showCountdown,
  date,
  showTime,
}: TimeDefinitionProps) => (
  <Component
    className={classNames("definition definition--time", className, {
      [`definition--${size}`]: size,
    })}
  >
    {Icon && <Icon className={classNames("definition__icon", iconClassName)} />}
    <div className="definition__content">
      <Term className="definition__term">{term}</Term>
      <Description className="definition__description">
        {date ? (
          <time className="definition__date" dateTime={date.toISOString()}>
            <strong>{format(date, "dd MMM yy")}</strong>
            {showTime && <span>{format(date, "HH:mm")}</span>}
          </time>
        ) : (
          <>
            <span className="definition__date">
              <strong>---</strong>
              <span>--</span>
            </span>
          </>
        )}
        {date && showCountdown && (
          <span className="definition__date-description">
            {formatDistanceToNowStrict(date, { addSuffix: true })}
          </span>
        )}
      </Description>
    </div>
  </Component>
);
