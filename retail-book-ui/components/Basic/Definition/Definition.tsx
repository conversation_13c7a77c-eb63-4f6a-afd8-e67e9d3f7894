import classNames from "classnames";
import { ElementType, useCallback, useMemo, useState, ReactNode } from "react";
import { Info } from "react-feather";

export interface DefinitionProps {
  as?: ElementType;
  termAs?: ElementType;
  descriptionAs?: ElementType;
  className?: string;
  iconClassName?: string;
  term: string;
  description?: string;
  descriptionSuffix?: ReactNode;
  icon?: ElementType;
  size?: "sm" | "lg" | "xl";
  help?: boolean;
  onHelp?: () => void;
  onHelpHover?: () => void;
}

export const Definition = ({
  as: Component = "div",
  termAs: Term = "dt",
  descriptionAs: Description = "dd",
  className,
  term,
  description,
  descriptionSuffix,
  size,
  help,
  onHelp,
  onHelpHover,
  icon: Icon,
  iconClassName,
}: DefinitionProps) => {
  
  const [timerId, setTimerId] = useState<NodeJS.Timeout>();

  const onEnter = useCallback( () => {
    setTimerId(setTimeout( () => {
      onHelpHover && onHelpHover();
    },1000))
  },[setTimerId, onHelpHover])
  
  const onLeave = useCallback( () => timerId && clearTimeout(timerId), [timerId]);

  const helpIconClass = useMemo( () => { 
    if (onHelp) {
      return "definition__term_help_clickable"
    }
    return "definition__term_help"
  },[onHelp])

  return (
  <Component
    className={classNames("definition", className, {
      [`definition--${size}`]: size,
    })}
  >
    {Icon && <Icon className={classNames("definition__icon", iconClassName)} />}
    <div className="definition__content">
      <div className="definition__term_container">
        <Term className="definition__term">{term}</Term>
        { !!help && <Info className={helpIconClass} onClick={onHelp} onMouseEnter={onEnter} onMouseLeave={onLeave} /> }
      </div>
      <Description className="definition__description">
        {description ?? "-"} <span className="definition__description_suffix">{descriptionSuffix}</span>
      </Description>
    </div>
  </Component>
)};
