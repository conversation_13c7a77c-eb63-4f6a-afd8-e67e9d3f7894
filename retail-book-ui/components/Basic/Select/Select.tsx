import { CheckCircle, XCircle } from "react-feather";
import classNames from "classnames";
import { forwardRef, SelectHTMLAttributes } from "react";

interface SelectProps extends SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  showLabel?: boolean;
  valid?: boolean;
  error?: string;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      children,
      className,
      name,
      id,
      label,
      showLabel = true,
      disabled,
      error,
      valid,
      ...props
    },
    ref
  ) => {
    return (
      <div
        className={classNames("select input", className, {
          "input--disabled": disabled,
          "input--invalid": error || valid === false,
        })}
      >
        <label
          htmlFor={id ?? name}
          className={classNames("input__label", {
            "sr-only": !showLabel,
          })}
        >
          {label}
        </label>
        <div className="input__input-wrapper">
          <select
            ref={ref}
            className="input__input"
            name={name}
            disabled={disabled}
            id={id ?? name}
            {...props}
          >
            {children}
          </select>
          {error && (
            <XCircle
              aria-hidden="true"
              className="input__icon input__icon--invalid"
            />
          )}
          {valid && (
            <CheckCircle
              aria-hidden="true"
              className="input__icon input__icon--valid"
            />
          )}
        </div>
        {error && <span className="input__error">{error}</span>}
      </div>
    );
  }
);

Select.displayName = "Select";
