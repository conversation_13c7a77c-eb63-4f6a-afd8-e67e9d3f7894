import classNames from "classnames";
import { ArrowDown, ArrowUp } from "react-feather";

export interface DeltaProps {
  className?: string;
  value: string | number;
  changeType?: "same" | "increase" | "decrease";
}

export const Delta = ({
  className,
  value,
  changeType = "same",
}: DeltaProps) => {
  return (
    <span
      className={classNames("delta", className, {
        "delta--decrease": changeType === "decrease",
        "delta--increase": changeType === "increase",
      })}
    >
      {changeType === "increase" && (
        <>
          <span className="sr-only">Increased by</span>
          <ArrowUp className="delta__icon" aria-hidden="true" />{" "}
        </>
      )}
      {changeType === "decrease" && (
        <>
          <span className="sr-only">Decrease by</span>
          <ArrowDown className="delta__icon" aria-hidden="true" />{" "}
        </>
      )}
      {value || "-"}
    </span>
  );
};
