import classNames from "classnames";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import {
  Pagination,
  PaginationProps,
} from "components/Basic/Pagination/Pagination";
import {
  ContextMenu,
  ContextMenuPropItem,
} from "components/Bespoke/ContextMenu/ContextMenu";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Key, ReactNode, useMemo, useState } from "react";
import DataGrid, {
  CheckboxFormatterProps,
  Column as DataGridColumn,
  DataGridProps,
  RowHeightArgs,
  SortColumn,
  SortStatusProps,
  useFocusRef,
} from "react-data-grid";
import { ChevronDown, ChevronRight, ChevronUp } from "react-feather";

interface CellExpanderFormatterProps {
  isCellSelected: boolean;
  expanded: boolean;
  onCellExpand: () => void;
}

const CellExpanderFormatter = ({
  isCellSelected,
  expanded,
  onCellExpand,
}: CellExpanderFormatterProps) => {
  const { ref, tabIndex } = useFocusRef<HTMLButtonElement>(isCellSelected);

  function handleKeyDown(e: React.KeyboardEvent<HTMLButtonElement>) {
    if (e.key === " " || e.key === "Enter") {
      e.preventDefault();
      onCellExpand();
    }
  }

  return (
    <button
      onClick={onCellExpand}
      onKeyDown={handleKeyDown}
      ref={ref}
      tabIndex={tabIndex}
      className="outline-none"
    >
      {expanded ? (
        <ChevronDown className="text-duke-blue-step-0" />
      ) : (
        <ChevronRight className="text-duke-blue-step-0" />
      )}
    </button>
  );
};


// Which row type it is we've inserted; either row or detail 
export enum ERowType { ROW = "row", DETAIL = "detail" };


export interface Row<> {
  id: Key;
  parentId?: Key;
  _row_type: ERowType; //Variable formatted with leading underscore to prevent clashes with row data.
}

export interface Column<T> extends DataGridColumn<T> {
  comparator?: (a: T, b: T) => number;
}

export interface DynamicContextMenuPropItem<R> extends ContextMenuPropItem<R> {
  isEnabled?: (x: R) => boolean;
}

interface ExpandedRows {
  [key: string]: 1 | 0;
}


const ExpandColumnCustomProps = {
  key: "expand",
  name: "",
  maxWidth: undefined,
  minWidth: 60,
  width: 60,
  cellClass: "flex items-center justify-center",
  frozen: false,
};

const ContextMenuColumnCustomProps = {
  name: "",
  key: "contextmenu",
  maxWidth: undefined,
  minWidth: 60,
  width: 60,
  cellClass: "flex items-center justify-center overflow-visible",
};

const expandRows = <R extends Row>(
  rows: readonly R[],
  expanded: ExpandedRows
): readonly R[] => {
  const newRows = [...rows];
  Object.entries(expanded).forEach(([id, v]) => {
    if (v === 1) {
      const rowIndex = newRows.findIndex((r) => r.id === id);
      const row = newRows[rowIndex];

      if (row) {
        newRows.splice(rowIndex + 1, 0, { ...row, id: row.id + "_detail", _row_type: ERowType.DETAIL });
      }
    }
  });
  return newRows;
};

const sortRows = <R extends Row>(
  rows: readonly R[],
  columns: readonly Column<R>[],
  sortColumns: readonly SortColumn[]
): readonly R[] => {
  if (sortColumns.length === 0) return rows;

  return [...rows].sort((a, b) => {
    for (const sort of sortColumns) {
      const comparator = columns.find(
        (col) => col.key === sort.columnKey
      )?.comparator;
      if (!comparator) {
        throw new Error(`unsupported sortColumn: "${sort.columnKey}"`);
      }
      const compResult = comparator(a, b);
      if (compResult !== 0) {
        return sort.direction === "ASC" ? compResult : -compResult;
      }
    }
    return 0;
  });
};

const checkboxFormatter = (
  { onChange, ...props }: CheckboxFormatterProps,
  ref: React.RefObject<HTMLInputElement>
) => {
  return (
    <Checkbox
      label="Select Row"
      showLabel={false}
      ref={ref}
      {...props}
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.checked, (e.nativeEvent as MouseEvent).shiftKey);
      }}
    />
  );
};

const sortFormatter = ({ sortDirection }: SortStatusProps) => {
  if (sortDirection === "ASC")
    return <ChevronUp className="text-duke-blue-step-0" />;
  if (sortDirection === "DESC")
    return <ChevronDown className="text-duke-blue-step-0" />;
  return null;
};

function ctxFilter<R>(row: R, items?: DynamicContextMenuPropItem<R>[]): ContextMenuPropItem<R>[] {
  const rval: ContextMenuPropItem<R>[] = []
  if (!items || items.length == 0) return rval

  for (let c = 0; c < items.length; c++) {
    const filter = items[c].isEnabled ?? ((): boolean => true);

    if (filter(row)) {
      rval.push(items[c] as ContextMenuPropItem<R>)
    }
  }

  return rval
}


function ctxMenu<R>(row: R, contextMenuItems?: DynamicContextMenuPropItem<R>[]) {
  const filtered = ctxFilter(row, contextMenuItems)
  if (filtered.length == 0) return;
  return <ContextMenu row={row} items={filtered} />
}

export interface IProps<R> extends DataGridProps<R> {
  contextMenuItems?: DynamicContextMenuPropItem<R>[];
  pageSize?: number;
  className?: string;
  paginationProps?: PaginationProps;
  expandedSize?: number;
  detailControl?: (props:R) => ReactNode;
}

const defaultRowSize = 48;

export const ExpandingStyledReactDataGrid = <R extends Row>({
  rows,
  columns,
  contextMenuItems,
  pageSize,
  className,
  paginationProps,  
  detailControl,
  expandedSize,
  ...rest
}: IProps<R>) => {

  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);

  const [expandedRows, setExpandedRows] = useState<ExpandedRows>({});
  const [selectedRows, setSelectedRows] = useState<ReadonlySet<Key>>(
    () => new Set()
  );

  const cols = useMemo(() => {
    const colSpan = columns.length+2

    const expandColumnDefinition:DataGridColumn<R,unknown> = {
      ...ExpandColumnCustomProps,
      colSpan(args) {
        return (args.type === 'ROW' && args.row._row_type === ERowType.DETAIL) ? colSpan+1 : undefined
      },
      formatter({ row, isCellSelected }) {
        return (
          (row._row_type === ERowType.DETAIL) ? (detailControl ? (<div className="pt-1 leading-none h-full w-full">{detailControl(row)}</div>) : <p>Programmer error</p>) :

            <CellExpanderFormatter
              isCellSelected={isCellSelected}
              expanded={expandedRows?.[row.id] === 1}
              onCellExpand={() => {
                setExpandedRows(
                  (expanded: ExpandedRows) =>
                  ({
                    ...expanded,
                    [row.id]: expanded[row.id] ? 0 : 1,
                  } as ExpandedRows)
                );
              }}

            />);
      }
    }
    
    
    const newCols = [expandColumnDefinition,...columns];
    if (contextMenuItems) {
      const contextColumnDefinition:DataGridColumn<R,unknown> = {...ContextMenuColumnCustomProps,formatter: ({ row }) => ctxMenu(row, contextMenuItems)}
      newCols.push(contextColumnDefinition)
    }
    
    return newCols;
  }, [columns, contextMenuItems, expandedRows, detailControl]);

  // Row processing
  const typedRows = useMemo(() => rows.map((x) => { return { ...x, _row_type: ERowType.ROW } }), [rows])

  const rowsSorted = useMemo(
    () => sortRows(typedRows, columns, sortColumns),
    [typedRows, columns, sortColumns]
  );

  const rowsSortedPaged = useMemo(() => {
    if (!pageSize) return rowsSorted;
    const start = currentPage * pageSize - pageSize;
    const end =
      currentPage * pageSize > rowsSorted.length
        ? undefined
        : currentPage * pageSize;
    return rowsSorted.slice(start, end);
  }, [rowsSorted, currentPage, pageSize]);

  const rowsSortedPagedExpanded = useMemo(
    () => expandRows(rowsSortedPaged, expandedRows),
    [rowsSortedPaged, expandedRows]
  );

  const rowHeight = useMemo(() => {
    return (args:RowHeightArgs<R>) => {
      if (args.type === 'ROW' && args.row._row_type === ERowType.DETAIL ) {
        return expandedSize ?? defaultRowSize
      }
      return defaultRowSize
    }
  }, [expandedSize]
  )

  return (
    <div className={classNames("rdg-wrapper", className)}>
      <DataGrid
        columns={cols}
        rows={rowsSortedPagedExpanded}
        sortColumns={sortColumns}
        onSortColumnsChange={setSortColumns}
        selectedRows={selectedRows}
        onSelectedRowsChange={setSelectedRows}
        renderers={{
          sortStatus: sortFormatter,
          checkboxFormatter,
          noRowsFallback: (
            <div className="col-span-full p-sm">
              <EmptyAction message="No data to show." />
            </div>
          ),
        }}        
        rowHeight={rowHeight} 
        rowKeyGetter={(row) => row.id}
        rowClass={(row) => (row._row_type == ERowType.DETAIL ? "rdg-row--child" : "")}
        {...rest}
      />
      {pageSize && rows.length > 0 ? (
        <div className="rdg-wrapper__footer">
          <Pagination
            pageSize={pageSize}
            currentPage={currentPage}
            onClick={(page: number) => setCurrentPage(page)}
            totalItems={rows.length}
          />
        </div>
      ) : paginationProps && rows.length > 0 ? (
        <div className="rdg-wrapper__footer">
          <Pagination {...paginationProps} />
        </div>
      ) : null}
    </div>
  );
};
