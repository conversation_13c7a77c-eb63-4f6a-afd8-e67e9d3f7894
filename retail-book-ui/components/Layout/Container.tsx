import classNames from "classnames";
import { ReactNode, ElementType, HTMLAttributes, forwardRef } from "react";

interface ContainerProps extends HTMLAttributes<HTMLElement> {
  as?: ElementType;
  children?: ReactNode;
  className?: string;
  narrow?: boolean;
}

export const Container = forwardRef<HTMLElement, ContainerProps>(
  ({ as: Component = "div", children, className, narrow, ...props }, ref) => {
    return (
      <Component
        ref={ref}
        className={classNames("mx-auto w-full", className, {
          "max-w-screen-xl px-xs-md": !narrow,
          "max-w-screen-md": narrow,
        })}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Container.displayName = "Container";
