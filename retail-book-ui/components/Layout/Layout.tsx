import { Footer } from "components/Bespoke/Footer/Footer";
import { Header } from "components/Bespoke/Header/Header";
import { ReactNode } from "react";

interface LayoutProps {
  children?: ReactNode;
  currentPath?: string;
}

export const Layout = ({ children, ...props }: LayoutProps) => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header {...props} />
      <main className="w-full flex flex-1 flex-col">{children}</main>
      <Footer {...props} />
    </div>
  );
};
