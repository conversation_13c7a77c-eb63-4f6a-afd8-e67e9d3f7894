/* Custom CSS */
.rdg-wrapper {
  @apply bg-white rounded;

  &__footer {
    @apply flex justify-end py-xs px-sm border-t border-grey-step-0;
  }
}

/* Vendor classes */
.rdg {
  --rdg-color: theme(colors.black);
  --rdg-border-color: theme(colors.grey.step-3);
  --rdg-summary-border-color: theme(colors.grey.step-1);
  --rdg-background-color: theme(colors.white);
  --rdg-header-background-color: theme(colors.white);
  --rdg-row-hover-background-color: theme(colors.bluetiful.step-2);
  --rdg-row-selected-background-color: theme(colors.vivid-orange.step-3);
  --rdg-row-selected-hover-background-color: theme(colors.bluetiful.step-2);
  --rdg-selection-color: theme(colors.utility-yellow.step-0);
  --rdg-font-size: theme(fontSize.sm);

  @apply overflow-visible bg-transparent;
  block-size: 100%;
  content-visibility: inherit;
  contain: inherit;

  &-header-row &-cell {
    @apply border-b border-b-grey-step-0;
  }

  &-header-row &-cell:first-child {
    @apply rounded-tl;
  }

  &-header-row &-cell:last-child {
    @apply rounded-tr;
  }

  &-header-sort-cell {
    @apply flex items-center;
  }

  &-row {
    @apply transition-colors;
  }

  &-row--child {
    @apply bg-grey-step-5;
  }

  &-row--child &-cell {
    @apply border-none;
  }

  &-cell {
    @apply px-sm;
    border-bottom: 1px solid var(--rdg-border-color);

    &[aria-selected="true"],
    &[aria-selected="true"]:focus {
      @apply -outline-offset-2;
      outline: 2px solid var(--rdg-selection-color);
    }
  }
}
