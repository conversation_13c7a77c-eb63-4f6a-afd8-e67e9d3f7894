import { Prose } from "components/Basic/Prose/Prose";
import { Container } from "components/Layout/Container";
import { XAxis, YA<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, ComposedChart } from 'recharts';
import { numberFormater } from "./Utils"

interface RbBarChartProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any[],
  dataKey: string,
  bars: JSX.Element[],
  xAxisInterval?: number,
  xAxisHeight?: number,
  additionalElements?: JSX.Element[],
  title?: string,
  height?: number,
  width?: number | string,
  barCategoryGap?: string | number | undefined,
  rightYAxisStroke?: string | undefined,
  subTitle?: JSX.Element,
}

export const RbBarChart = (props: RbBarChartProps) => {
  return (
    <Container >
        {props.title && (
            <Prose>
                <h3>{props.title}</h3>
            </Prose>
        )}
        {props.subTitle && (props.subTitle)}
        <ResponsiveContainer height={props.height ? props.height : 550} width={props.width ? props.width : "100%"}>
            <ComposedChart
                data={props.data}
                margin={{
                    top: 30,
                    right: 20,
                    left: 20,
                    bottom: 20,
                }}
                barCategoryGap={props.barCategoryGap}
            >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={props.dataKey} textAnchor="end" height={props.xAxisHeight??150} angle={-45}  />
                <YAxis tickFormatter={numberFormater} />
                <YAxis yAxisId="right" tickFormatter={numberFormater} allowDecimals={false} orientation="right" stroke={props.rightYAxisStroke} />
                <Tooltip />
                <Legend />
                {props.bars}
            </ComposedChart>
        </ResponsiveContainer>
    </Container>
  );
};
