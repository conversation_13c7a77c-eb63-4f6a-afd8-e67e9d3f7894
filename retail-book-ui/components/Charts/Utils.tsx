import { TIntermediaryOfferMetric, TOfferMetrics } from "types";
import Gradient from 'javascript-color-gradient'

// For charts we might want a gradient of colours from the style guide.
// We often want to reuse these colour mappings to ensure charts with the same values map to the same colours.
export const getColourMap = (values: string[]): Map<string, string> => {
    const deduped = Array.from(new Set<string>(values));
    const colourMap = new Map<string, string>();
    if (deduped.length <= 0) {
      return colourMap; 
    }
    if (deduped.length == 1) {
        colourMap.set(values[0], '#040194'); //RB blue
        return colourMap;
    }
    const colourGradient = new Gradient();
    
    if (deduped.length > 2) {
        colourGradient.setColorGradient('#140075', '#040194', '#006300'); 
    } else {
        colourGradient.setColorGradient('#140075', '#040194');        
    }
    colourGradient.setMidpoint(deduped.length);
    const colours = colourGradient.getColors();
    deduped.forEach( (v, i) => colourMap.set(v, colours[i]) );
    return colourMap;
};

/*
    For a barchart recharts expects an array of objects of essentially x and y axis values, but for a stacked bar chart it expects an
    array of objects where each object doesn't necessarily have the same properties - TypeScript doesn't like this so have hacked around it with LooseObject.
    [
        {
            date: '05/09/22',
            hargreaveslans: 10000,
        },
        {
            date: '06/09/22',
            hargreaveslans: 10000,
            ajbell: 25000,
        },
    ]
*/
interface LooseObject {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any
}
export const tOfferMetricsToRechartsFormat = (metrics: TOfferMetrics | undefined, includeCurrency: boolean, dateformatter: (d: Date) => string, getter: (int: TIntermediaryOfferMetric) => number): LooseObject[] => {
    if (!metrics) {
        return [];
    }
    return metrics.data.map( (snapshot) => {
        const loose: LooseObject = {};
        loose.date = dateformatter(new Date(snapshot.date));
        if (snapshot.number_of_offers) {
            loose["Offers"] = snapshot.number_of_offers.toNumber();
        }
        snapshot.intermediaries.forEach( (int) => {
            if (includeCurrency) {
                loose[int.name + " " + int.currency] = getter(int);
            } else {
                loose[int.name] = getter(int);
            }
        });
        return loose;
    });
};

export const tOfferMetricsToRechartsFormatOrderValue = (metrics: TOfferMetrics | undefined, dateformatter: (d: Date) => string): LooseObject[] => {
    return tOfferMetricsToRechartsFormat(metrics, false, dateformatter, (int: TIntermediaryOfferMetric) => int.order_value?.toNumber()??0);
};

export const tOfferMetricsToRechartsFormatAllocationValue = (metrics: TOfferMetrics | undefined, dateformatter: (d: Date) => string): LooseObject[] => {
    return tOfferMetricsToRechartsFormat(metrics, true, dateformatter, (int: TIntermediaryOfferMetric) => int.allocation_value?.toNumber()??0);
};
interface Intermediary {
    name: string,
    system_id: string
}
export const tOfferMetricsToIntermediarySet = (metrics: TOfferMetrics | undefined): Intermediary[] => {
    if (!metrics) {
        return [];
    }
    const seen = new Set<string>;
    const returnArray: Intermediary[] = [];
    metrics.data.forEach( (snapshot) => {
        snapshot.intermediaries.forEach( (int) => {
            if (!seen.has(int.name+int.system_id)) {
                seen.add(int.name+int.system_id);
                returnArray.push({name: int.name, system_id: int.system_id});
            }
        });
    });
    return returnArray;
};
export const tOfferMetricsToCurrencySet = (metrics: TOfferMetrics | undefined): string[] => {
    if (!metrics) {
        return [];
    }
    const currencies = new Set<string>;
    metrics.data.forEach( (snapshot) => {
        snapshot.intermediaries.forEach( (int) => {
            currencies.add(int.currency);
        });
    });
    return Array.from(currencies.values());
};
interface IntermediaryCurrency {
    intermediary: string,
    currency: string
}
export const tOfferMetricsToIntermediaryCurrencySet = (metrics: TOfferMetrics | undefined): IntermediaryCurrency[] => {
    if (!metrics) {
        return [];
    }
    const seen = new Set<string>;
    const returnArray: IntermediaryCurrency[] = [];
    metrics.data.forEach( (snapshot) => {
        snapshot.intermediaries.forEach( (int) => {
            if (!seen.has(int.name+int.currency)) {
                seen.add(int.name+int.currency);
                returnArray.push({intermediary: int.name, currency: int.currency});
            }
        });
    });
    return returnArray;
};

export const numberFormater = (number: number): string => {
    if(number > 1000000000) {
        return (number/1000000000).toString() + 'B';
    } else if(number > 1000000) {
        return (number/1000000).toString() + 'M';
    } else if(number > 1000) {
        return (number/1000).toString() + 'K';
    } else {
        return number.toString();
    }
};

export const getYearMonthFormat = (d: Date): string => {
    const splitDate = d.toISOString().split('T')[0].split('-');
    return splitDate[0] + "-" + d.toLocaleString('default', { month: 'short' });
};

export const getMonthYearFormat = (d: Date): string => {
    const splitDate = d.toISOString().split('T')[0].split('-');
    return d.toLocaleString('default', { month: 'short' }) + "-" + splitDate[0];
};

export const getDateFormat = (d: Date): string => {
    const splitDate = d.toISOString().split('T')[0].split('-');
    return splitDate[2] + "-" + d.toLocaleString('default', { month: 'short' }) + "-" + splitDate[0];
};

export const  getDateTimeFormat = (d: Date): string => {
    const time = d.toISOString().split('T')[1].split('.')[0].split(':');
    return getDateFormat(d) + " " + time[0] + ":" + time[1];
};