import { Alert } from "components/Basic/Alert/Alert";
import { Button } from "components/Basic/Button/Button";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { StandaloneLink } from "components/Basic/StandaloneLink/StandaloneLink";
import { DropUpload } from "components/Bespoke/DropUpload/DropUpload";
import { Stat, StatProps } from "components/Bespoke/Stat/Stat";
import { OrderLineItemGrid } from "components/Grids/Order/OrderLineItemGrid";
import { Container } from "components/Layout/Container";
import { formatCurrency } from "currency";
import Decimal from "decimal.js";
import { downloadFile, formatNumber } from "helpers";
import { useSession } from "next-auth/react";
import { useMemo } from "react";
import { Download } from "react-feather";
import { ApiItemisedUploadResponse, EOfferType, TOffer, TOrder } from "types";
import { Api } from "utils/api";
import { SpreadsheetUploadAccept } from "utils/constants";

interface OrderBulkFormProps {
  className?: string;
  offer?: TOffer;
  previousOrder?: TOrder;
  orderItemisedSummary?: ApiItemisedUploadResponse;
  onDrop: (files: File[]) => void;
  isLoading?: boolean;
  onSubmit?: () => void;
  step: number;
  setStep: (n: number) => void;
  onClearError?: () => void;
  onClearItemised?: () => void;
  onDownload?: () => void;
  isDownloading?:boolean;
  errorMessage: string|undefined;
  loadingProgressText: string|undefined;
}

export const OrderBulkForm = ({
  offer,
  previousOrder,
  orderItemisedSummary,
  onDrop,
  isLoading,
  onSubmit,
  step,
  setStep,
  onClearError,
  onClearItemised,
  onDownload,
  isDownloading,
  errorMessage,
  loadingProgressText
}: OrderBulkFormProps) => {
  const { data: session } = useSession();

  const valueStat: StatProps | undefined = useMemo(() => {
    const orderToShow = orderItemisedSummary || previousOrder;

    if (orderToShow?.totals?.notional_value === undefined) return;

    return {
      title: "Total Value",
      value: formatCurrency(
        orderToShow.totals.notional_value,
        offer?.currency ?? ""
      ),
      change: previousOrder?.totals.notional_value
        ? formatCurrency(
            Decimal.abs(
              Decimal.sub(
                orderToShow.totals.notional_value,
                previousOrder?.totals.notional_value
              )
            ),
            offer?.currency ?? ""
          )
        : undefined,
      changeType: previousOrder?.totals.notional_value
        ? orderToShow.totals.notional_value.eq(
            previousOrder?.totals.notional_value
          )
          ? undefined
          : orderToShow.totals.notional_value.gte(
              previousOrder?.totals.notional_value
            )
          ? "increase"
          : "decrease"
        : undefined,
    };
  }, [previousOrder, orderItemisedSummary, offer?.currency]);

  const applicationsStat: StatProps | undefined = useMemo(() => {
    const orderToShow = orderItemisedSummary || previousOrder;

    if (!orderToShow?.totals?.applications) return;

    return {
      title: "Total Applications",
      value: formatNumber(orderToShow.totals.applications),
      change: previousOrder?.totals.applications
        ? Decimal.abs(
            Decimal.sub(
              orderToShow.totals.applications,
              previousOrder?.totals.applications
            )
          ).toString()
        : undefined,
      changeType: previousOrder?.totals.applications
        ? orderToShow.totals.applications.eq(previousOrder?.totals.applications)
          ? undefined
          : orderToShow.totals.applications.gte(
              previousOrder?.totals.applications
            )
          ? "increase"
          : "decrease"
        : undefined,
    };
  }, [previousOrder, orderItemisedSummary]);

  const showExisting = useMemo( () => {
    return offer?.type === EOfferType.FOLLOW_ON
  },[offer])

  const existingHoldingStat: StatProps | undefined = useMemo(() => {
    const orderToShow = orderItemisedSummary || previousOrder;

    if (!orderToShow?.totals?.existing_holding) return;
    if (!showExisting) return;

    return {
      title: "Total Existing Holding",
      value: formatNumber(orderToShow?.totals.existing_holding),
      change: previousOrder?.totals.existing_holding
        ? Decimal.abs(
            Decimal.sub(
              orderToShow.totals.existing_holding,
              previousOrder?.totals.existing_holding
            )
          ).toString()
        : undefined,
      changeType: previousOrder?.totals.existing_holding
        ? orderToShow.totals.existing_holding.eq(
            previousOrder?.totals.existing_holding
          )
          ? undefined
          : orderToShow.totals.existing_holding.gte(
              previousOrder?.totals.existing_holding
            )
          ? "increase"
          : "decrease"
        : undefined,
    };
  }, [previousOrder, orderItemisedSummary, showExisting]);

  const stats = useMemo(() => {
    const stats = [];
    if (applicationsStat) stats.push(applicationsStat);
    if (valueStat) stats.push(valueStat);
    if (existingHoldingStat) stats.push(existingHoldingStat);
    return stats;
  }, [applicationsStat, valueStat, existingHoldingStat]);

 


  return (
    <Container >
      {stats.length > 0 && (
        <dl className="mb-sm flex flex-wrap gap-sm">
          {stats.map((stat, index) => (
            <Stat key={index} {...stat} />
          ))}
        </dl>
      )}

      <form className="form">
        {step === 1 && (
          <>
            <section className="form-section">
              <legend className="form-legend">Upload order</legend>
              {
                errorMessage && <Alert theme="failure" message={errorMessage}/>
              }
             
              <StandaloneLink
                onClick={async () => {
                  const data = await Api.downloadOrderTemplate(
                    session?.accessToken
                  );

                  const file = new File(
                    [data],
                    "RetailBook - application upload template",
                    { type: data.type }
                  );

                  downloadFile(file);
                }}
                iconPosition="before"
                icon={Download}
              >
                Download order template
              </StandaloneLink>

              {isLoading ? (
                <div className="flex items-center justify-center">
                  <Spinner message={ loadingProgressText ? loadingProgressText : "Please wait while your file is uploaded..." } />
                </div>
              ) : (
                <DropUpload onDrop={onDrop} maxFiles={1} accept={ SpreadsheetUploadAccept } />
              )}
            </section>
          </>
        )}

        {step === -2 && (
          <>
            <section className="form-section">
              <legend className="form-legend">Validation Failed</legend>
              {
                errorMessage && <Alert theme="failure" message={errorMessage}/>
              }
              <div className="overflow-auto">
              <OrderLineItemGrid currency={offer?.currency??""} 
                                 orderId={orderItemisedSummary?.order_book_id}
                                 offerId={offer?.id}
                                 token={session?.accessToken}
                                 showExisting={showExisting}
                                 showErrors={true}/>
              </div>
              <div className="form-actions">              
                <Button type="button" onClick={() => {
                  if (onClearItemised) {
                    onClearItemised();
                  }

                  if (onClearError) {
                    onClearError();
                  }
                  setStep(1);
                } } theme="outline">Back</Button>
                <Button type="button"  theme="outline" icon={Download} disabled={isDownloading} onClick={onDownload}>Download Sheet</Button>
              </div>         
            </section>
          </>
        )}
        {step === 2 && (
          <>
            <section className="form-section">
              <legend className="form-legend">Review order</legend>
              <OrderLineItemGrid currency={offer?.currency ?? ""} 
                                 orderId={orderItemisedSummary?.order_book_id} 
                                 offerId={offer?.id} 
                                 token={session?.accessToken}
                                 showExisting={showExisting} />
             
              <div className="form-actions">
                <Button
                  type="button"
                  onClick={() => setStep(step - 1)}
                  theme="outline"
                >
                  Back
                </Button>
                <Button
                  type="button"
                  onClick={onSubmit}
                >
                  Send Orders
                </Button>
              </div>
            </section>
          </>
        )}       
      </form>
    </Container>
  );
};
