import CalendarClose from "assets/icons/calendar-close.svg";
import CalendarOpen from "assets/icons/calendar-open.svg";
import { Definition } from "components/Basic/Definition/Definition";
import { TimeDefinition } from "components/Basic/Definition/TimeDefinition";
import { IssuerLogo } from "components/Bespoke/IssuerLogo/IssuerLogo";
import { formatCurrency } from "currency";
import { isFuture, parseISO } from "date-fns";
import { useMemo, useState } from "react";
import { File, ShoppingCart, Target, TrendingUp } from "react-feather";
import { EOfferStatus, TOffer } from "types";
import { OfferHeaderAlert } from "../OfferHeaderAlert";
import { StateHelpDialog } from "./dialogs/StateHelpDialog";

interface ViewOfferHeaderProps {
  offer: TOffer;
  token?: string;
}

export const ViewOfferHeader = ({ offer, token}: ViewOfferHeaderProps) => {

  const expectedApplicationPrefix = useMemo(() => {
    if (!offer.status ||
      offer.status == EOfferStatus.BUILDING ||
      offer.status == EOfferStatus.PRE_LAUNCH) {
      return "Expect "
    }

    return ""
  }, [offer.status]);

  const registrationDate = useMemo(() => {
    if (!offer.registration_date) return null;
    return parseISO(offer.registration_date);
  }, [offer.registration_date]);

  const openDate = useMemo(() => {
    if (!offer.open_date) return null;
    return parseISO(offer.open_date);
  }, [offer.open_date]);

  const closeDate = useMemo(() => {
    if (!offer.close_date) return null;
    return parseISO(offer.close_date);
  }, [offer.close_date]);

  const offerPrice = useMemo(() => {
    if (offer.offer_price) {
      return formatCurrency(offer.offer_price, offer.currency, 8)
    } else if (offer.price_range_low && offer.price_range_high) {
      return formatCurrency(offer.price_range_low, offer.currency, 8) + " - " + formatCurrency(offer.price_range_high, offer.currency, 8)
    }
    return "TBC"
  }, [offer]);

  const [showStateHelp, setShowStateHelp] = useState(false);

  return (
    <header className="view-offer-header">
      <div className="view-offer-header__main">
        <h1 className="view-offer-header__title">{offer.name}</h1>
        <dl className="view-offer-header__stats">
          <Definition term="Offer type" description={offer.type} icon={File} />
          <Definition
            term="Minimum Order"
            description={
              offer.min_order_amount
                ? formatCurrency(offer.min_order_amount, offer.currency, 0)
                : "--"
            }
            icon={ShoppingCart}
          />
          <Definition
            term="Target Raise"
            description={offer.raise_amount ? formatCurrency(offer.raise_amount, offer.currency, 0) : "--"}
            icon={Target}
          />          
          <Definition
              term="Offer Price"
              description={offerPrice}
              icon={TrendingUp}
          />          
        </dl>
        <dl className="view-offer-header__stats view-offer-header__stats--compact">
          <Definition term="Shareholders Only" description={offer.shareholders_only ? "Yes" : "No"} className="overflow-clip" />
          <Definition term="ISIN" description={offer.isin} className="overflow-clip" />
          <Definition term="SEDOL" description={offer.sedol} className="overflow-clip" />
          <Definition term="Ticker" description={offer.ticker} className="overflow-clip"/>
        </dl>
        <p className="view-offer-header__description">{offer.description}</p>

        <div className="view-offer-header__footer">
          {offer.status == EOfferStatus.PRE_LAUNCH ?
            <OfferHeaderAlert
              headingMessage="This offer is not yet open, and thus the information provided is subject to change. It should also be considered confidential information, and is therefore inside information as defined in UK MAR.  Please do not disclose improperly to any other person."
              isBasic={false} /> :
            ""
          }
        </div>

      </div>

      <div className="view-offer-header__sidebar">
        <IssuerLogo
          issuer={offer.issued_by}
          offerId={offer.id}
          logo={offer.issuer_logo}
        />
        <div className="view-offer-header__sidebar-body">
          <dl className="view-offer-header__timeline">
            <Definition term="Status" description={offer.status} size="lg"  help={true} onHelpHover={ () => setShowStateHelp(true) } onHelp={() => setShowStateHelp(true)} />

            {registrationDate && isFuture(registrationDate) && (
              <TimeDefinition
                term="Pre Launch"
                date={registrationDate}
                icon={CalendarOpen}
                showCountdown={isFuture(registrationDate)}
                showTime
              />
            )}

            {openDate && (
              <TimeDefinition
                term={expectedApplicationPrefix + " Applications Open"}
                date={openDate}
                icon={CalendarOpen}
                showCountdown={isFuture(openDate)}
                showTime
              />
            )}

            {closeDate && (
              <TimeDefinition
                term={expectedApplicationPrefix + " Applications Close"}
                date={closeDate}
                icon={CalendarClose}
                showCountdown={isFuture(closeDate)}
                showTime
              />
            )}
          </dl>
        </div>
      </div>
      <StateHelpDialog open={showStateHelp} close={ () => setShowStateHelp(false)} token={token}/>
    </header>
  );
};
