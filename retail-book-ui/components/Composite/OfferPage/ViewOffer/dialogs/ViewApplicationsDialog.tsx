import { OrderLineItemGrid } from "components/Grids/Order/OrderLineItemGrid";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";

import { FunctionComponent } from "react";
import { TOffer, TOrder } from "types";

interface IProps {
    token?: string;
    open: boolean;
    onClose: () => void;
    order?:TOrder;
    offer?:TOffer;
};
 
export const ViewApplicationsDialog:FunctionComponent<IProps> = ({open, onClose, order, offer, token}) => {

    return (<StyledDialog open={open} onClose={onClose} title="View Applications" fullWidth={true} >
                <div className="overflow-auto"><OrderLineItemGrid 
                    offerId={offer?.id}
                    orderId={order?.order_book_id}
                    token={token}
                    currency={offer?.currency ?? ""} 
                    showExisting={false} 
                    pageSize={10}/></div>
            </StyledDialog>)
}