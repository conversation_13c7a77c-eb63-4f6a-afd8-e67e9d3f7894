import { useQuery } from "@tanstack/react-query";
import { Prose } from "components/Basic/Prose/Prose";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { FunctionComponent } from "react";
import { TOfferState } from "types";
import { getOfferStatesQuery } from "utils/queries";

interface IProps {
    open: boolean;
    close: () => void;
    token?: string;
};

interface ICardProperties {
    state: TOfferState;
};

const StateCard : FunctionComponent<ICardProperties> = ({state}) => {
    return (
        <div className="statehelp__card">
            <span className="statehelp__state">{state.state_name}</span>            
            <Prose>{state.state_description}</Prose>
        </div>
    )
};

export const StateHelpDialog : FunctionComponent<IProps> = ({ open, close, token }) => {
    const {data: states} = useQuery ( getOfferStatesQuery(token) )

    return (<StyledDialog className="max-h-full" open={open} onClose={close} title="Offer State Information" fullWidth>        
        <div className="flex flex-col space-y-4">

            { states && states.map( x => <StateCard key={x.state_name} state={x}/>)  }
            
        </div>
    </StyledDialog>)
};