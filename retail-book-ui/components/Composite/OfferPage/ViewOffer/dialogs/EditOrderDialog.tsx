import { Tab } from "@headlessui/react";
import { QueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { Alert } from "components/Basic/Alert/Alert";
import { OrderAggregateForm } from "components/Composite/OrderAggregateForm/OrderAggregateForm";
import { OrderBulkForm } from "components/Composite/OrderBulkForm/OrderBulkForm";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { createOrderFromAggregateFormData, downloadFile } from "helpers";
import { useFieldErrors } from "hooks/useFieldErrors";

import { FunctionComponent, useCallback, useEffect, useMemo, useState } from "react";
import { ApiError, ApiOrderUploadResponse, ApiItemisedUploadResponse, ApiMutationResponse, EOrderType, TAggregateOrder, TOffer, TOrder } from "types";
import { Api } from "utils/api";
import { toApiError, toItemisedUploadResponse } from "utils/dataConversion";
import { uploadItemisedOrderMutation, confirmItemisedOrderMutation, createAggregateOrderMutation, validateAggregateOrderMutation, getTask } from "utils/queries";

interface IProps {
    open: boolean;
    onClose: () => void;

    qc: QueryClient;
    accessToken?: string;
    offer: TOffer;
    order?: TOrder;
}

export const EditOrderDialog: FunctionComponent<IProps> = ({ open, onClose, order, offer, accessToken, qc }) => {
    const [uploadTaskId, setUploadTaskId] = useState<string>();
    const [taskError, setTaskError] = useState<string>();
    
    const [aggregateOrderStep, setAggregateOrderStep] = useState(1);
    const [bulkOrderStep, setBulkOrderStep] = useState(1);
    const [orderItemisedSummary, setOrderItemisedSummary] = useState<ApiItemisedUploadResponse>();
    const [loadingProgressText, setLoadingProgressText] = useState<string>();

    const validateAggregateOrderMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>,TAggregateOrder>({
        ...validateAggregateOrderMutation(offer.id, accessToken),
        onSuccess: (validatedOrderData) => {
            // in validate_only mode the original TAggregateOrder is echoed back if validated without error
            createAggregateOrderMutator.mutate(validatedOrderData as TAggregateOrder);
        },
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        onError: () => {}
    })

    const createAggregateOrderMutator = useMutation<
        ApiMutationResponse,
        AxiosError<ApiError>,
        TAggregateOrder
    >({
        ...createAggregateOrderMutation(offer.id, accessToken),
        onSuccess: () => {            
            qc.invalidateQueries(["orders"]);
            qc.invalidateQueries(["orderHistory"]);

            onClose();            
        },
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        onError: () => {}
    });

    const uploadItemisedOrderMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>,FormData>({
        ...uploadItemisedOrderMutation(offer.id, accessToken),
        onSuccess: (data) => {
            setTaskError(undefined);
            setUploadTaskId(data.id);
        },
        onError: (err) => {
            if (err && err.message) {
                // This'll happen if the upload fials
                const msg = "Error uploading file. This is likely a transient issue, please try again later\n" + err.message
                setTaskError(msg);
            }
        }
    });

    const { data: task } = useQuery(
        getTask(uploadTaskId??"", accessToken)
    );

    useEffect( () => {
        if (task?.status == "success") {
            setLoadingProgressText(undefined);
            setUploadTaskId(undefined);
            setBulkOrderStep(2);
            setOrderItemisedSummary(toItemisedUploadResponse(task.response as ApiItemisedUploadResponse));
        } else if (task?.status == "failed") {
            setUploadTaskId(undefined);
            setLoadingProgressText(undefined);
            setTaskError(task.message);
            const apiError = task.response as ApiOrderUploadResponse;
            if (apiError) {
                if (apiError.message) {
                    // This is when the upload work but the backend is unhappy
                    setTaskError(task.message + " - " + apiError.message)
                }
                onError(apiError);
            }
        } else {
            if (task?.message) {
                setLoadingProgressText(task.message);
            }
        }
    }, [task]);

    const onError = (resp: ApiOrderUploadResponse) => {
        const fieldErrors = resp.field_errors
        if (fieldErrors && fieldErrors?.id) {
            // A hacky way of determining if an error condition was set through field errors.
            if (fieldErrors.root) {
                setTaskError(fieldErrors.root);
            }
            // summary_id is not set if validation fails in finalizing the order book
            setOrderItemisedSummary({
                order_book_id: fieldErrors.id,
                order_summary_id: fieldErrors.summary_id
            })
            setBulkOrderStep(-2);
        }
    }

    const confirmItemisedOrderMutator = useMutation({
        ...confirmItemisedOrderMutation(offer.id, accessToken),
        onSuccess: () => {
            
            setBulkOrderStep(1);
            setOrderItemisedSummary(undefined);
            qc.invalidateQueries(["orders"]);
            qc.invalidateQueries(["orderHistory"]);

            myCloseNoCancel();
        },
    });
    
     // If we're not open we reset; the dialog
     useEffect( () => {
        if (!open) {
            setAggregateOrderStep(1);
            setBulkOrderStep(1);
            setOrderItemisedSummary(undefined);
            setUploadTaskId(undefined);
            setTaskError(undefined);          
        }
    }, [open])

    const onDrop = useCallback(
        (files: File[]) => {
            if (files.length > 0) {
                const formData = new FormData();
                formData.append("file", files[0], files[0].name);
                uploadItemisedOrderMutator.mutate(formData);
            }
        },
        [uploadItemisedOrderMutator]
    );

    const fieldErrors = useFieldErrors(validateAggregateOrderMutator.error);

    const handleCancelledItemised = useCallback(async () => {
        if (!orderItemisedSummary?.order_summary_id) return;
    
        try {
            await Api.deleteOrderSummary(offer.id,orderItemisedSummary?.order_summary_id,accessToken);
        } catch (e) {
            // This might fail; the reason being that hook state isn't necessarily ordered
            //   and this function is called and orderItemisedSummary might not have been updated
            //   for the moment we just make this silent. 
            console.log("failed to delete order summary");
            console.log(e);
        }
        setOrderItemisedSummary(undefined);
      }, [offer.id, orderItemisedSummary, accessToken]);


    const [isDownloadError, setDownloadOrderError] = useState<ApiError>()
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const downloadSheetMutator = useMutation<any, AxiosError<Blob>, string>({
          mutationFn: async (order?: string) => {
            return await Api.downloadOfferOrderXLSX(offer.id, order, true, accessToken);
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSuccess: (data: any, order: string) => {
            const file = new File([data], `rb_order_${order}`, {
              type: data.type,
            });
            downloadFile(file);
          },
          onError: async (blob: AxiosError<Blob>) => {
            setDownloadOrderError(toApiError(await blob.response?.data.text()));
          }
        });
    
    const myCloseNoCancel = useMemo( () => {
        return () => {
            setBulkOrderStep(1);
            setAggregateOrderStep(1);
        
            createAggregateOrderMutator.reset();
            uploadItemisedOrderMutator.reset();
            onClose();
        };
    },[createAggregateOrderMutator, uploadItemisedOrderMutator, onClose]);

    const myClose = useMemo( () => {
        return () => {
            handleCancelledItemised();
            myCloseNoCancel();
        }
    }, [handleCancelledItemised, myCloseNoCancel])

    return (
        <>
        <StyledDialog open={open}
            fullWidth
            onClose={ () => {
                if (uploadItemisedOrderMutator.isLoading || !!uploadTaskId) {
                    return;
                } 
                myClose();
            }}
            unmount={false}
            title={`${order ? "Edit" : "Add"} Order`}>

            <Tab.Group defaultIndex={order?.order_type === EOrderType.DETAILED ? 0 : 1}>
                <Tab.List className="tab-list tab-list--bordered">
                    <StyledTab disabled={aggregateOrderStep > 1}>
                        Upload order list
                    </StyledTab>
                    <StyledTab disabled={bulkOrderStep > 1}>
                        Add order as summary
                    </StyledTab>
                </Tab.List>

                {/* Upload Itemised order tab */}
                <Tab.Panels className="pt-lg">
                    <Tab.Panel>
                        <OrderBulkForm
                            step={bulkOrderStep}
                            setStep={setBulkOrderStep}
                            offer={offer}
                            previousOrder={order}
                            orderItemisedSummary={orderItemisedSummary}
                            onDrop={onDrop}
                            isLoading={uploadItemisedOrderMutator.isLoading || !!uploadTaskId}
                            loadingProgressText={loadingProgressText}
                            onSubmit={() => {
                                if (!orderItemisedSummary?.order_summary_id) return;

                                confirmItemisedOrderMutator.mutate(
                                    orderItemisedSummary?.order_summary_id
                                );
                            }}

                            onDownload={() => orderItemisedSummary?.order_book_id && downloadSheetMutator.mutate(orderItemisedSummary?.order_book_id) }
                            isDownloading={downloadSheetMutator.isLoading}
                            
                            onClearItemised= { handleCancelledItemised }
                            onClearError={ () => { setDownloadOrderError(undefined); uploadItemisedOrderMutator.reset(); setTaskError(""); }}
                            errorMessage={taskError}
                        />
                    </Tab.Panel>

                    {/* Upload summarized order tab */}
                    <Tab.Panel>
                        <OrderAggregateForm
                            offer={offer}
                            order={order}
                            fieldErrors={fieldErrors}
                            
                            onConfirm={(formData)=> {
                                const newOrder = createOrderFromAggregateFormData(formData);
                                return  validateAggregateOrderMutator.mutate(newOrder);                                
                            }}
                           
                            errorMessage={validateAggregateOrderMutator.error?.response?.data.message}
                        />
                    </Tab.Panel>
                </Tab.Panels>
            </Tab.Group>
            <StyledDialog open={!!isDownloadError} title="Error Downloading Spreadsheet" onClose={() => setDownloadOrderError(undefined) }>
                    <Alert theme="failure" message={isDownloadError?.message}/>
            </StyledDialog>
        </StyledDialog >
        </>
    )
}
