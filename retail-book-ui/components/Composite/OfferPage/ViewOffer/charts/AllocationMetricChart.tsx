import { Bar } from 'recharts';
import { RbBarChart } from "components/Charts/RbBarChart";
import React, { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import {  getLatestAllocationMetrics } from "utils/queries";

interface AllocationMetricChartProps {
    offerId: string;
    token?: string;
}

export const AllocationMetricChart = ({offerId, token}: AllocationMetricChartProps) => {
    const allocationMetricsQuery = useQuery({...getLatestAllocationMetrics(offerId, token)});
    const allocationMetrics = useMemo(() => { 
      return (allocationMetricsQuery.data?.data ?? []).flatMap((snapshot) => snapshot.intermediaries).map((int) => {
        return {name: int.name, value: int.allocation_value?.toNumber()??0}
      });
    }, [allocationMetricsQuery.data]);
    
    const barchartBars = [
      <Bar key="value" dataKey='value' name="Allocated Value" fill="#040194" />
    ]

    return (
      <RbBarChart title="Allocation vs Market" data={allocationMetrics} dataKey="name" bars={barchartBars} xAxisHeight={100} barCategoryGap="70%" />
    );
};
