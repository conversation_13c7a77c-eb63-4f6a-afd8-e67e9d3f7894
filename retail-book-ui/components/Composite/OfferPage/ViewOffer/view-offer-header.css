.view-offer-header {
  @apply mb-md;

  &__main {
    @apply flex flex-col gap-md-lg mb-md;
  }

  &__title {
    @apply h0 font-brand;
  }

  &__stats {
    @apply grid grid-cols-2 gap-sm;
  }

  &__description {
    @apply lead;
  }

  &__sidebar {
    @apply bg-grey-step-4 border border-grey-step-4 rounded overflow-hidden;
  }

  &__sidebar-body {
    @apply p-xs-sm;
  }

  &__timeline {
    @apply flex flex-col gap-md;
  }

  &__footer {
    @apply mt-auto;
  }
  
  @media screen(md) {
    &__stats {
      @apply flex gap-lg;
    }
  }

  @media screen(lg) {
    @apply grid grid-cols-3 gap-lg;

    &__main {
      @apply col-span-2 mb-0;
    }

    &__sidebar {
      @apply col-span-1;
    }
  }
}
