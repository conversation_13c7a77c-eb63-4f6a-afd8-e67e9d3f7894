import { Tab } from "@headlessui/react";
import { Prose } from "components/Basic/Prose/Prose";
import { Container } from "components/Layout/Container";
import { FunctionComponent } from "react";
import ReactMarkdown from "react-markdown";
import { TOffer } from "types";

interface IProps {
  offer: TOffer;
}

export const ViewOfferOverviewTab: FunctionComponent<IProps> = ({ offer }) => {
  return (
    <Tab.Panel>
      <Container>
        <h2 className="h2 mb-sm">Offer details</h2>
        <div className="mb-md">
          <Prose as={ReactMarkdown}>{offer.details}</Prose>
        </div>

        <h2 className="h2 mb-sm">Allocation Principles</h2>
        <div className="mb-md">
          <Prose as={ReactMarkdown}>{offer.allocation_principles}</Prose>
        </div>

        <h2 className="h2 mb-sm">Settlement Details</h2>
        <div className="mb-md">
          <Prose as={ReactMarkdown}>{offer.settlement_details}</Prose>
        </div>
      </Container>
    </Tab.Panel>
  );
};
