import { Tab } from "@headlessui/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Alert } from "components/Basic/Alert/Alert";
import { Button } from "components/Basic/Button/Button";
import { Prose } from "components/Basic/Prose/Prose";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { OrderCard } from "components/Bespoke/OrderCard/OrderCard";
import { OrderHistoryTable } from "components/Bespoke/OrderHistoryTable/OrderHistoryTable";
import { Container } from "components/Layout/Container";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { StyledDisclosure } from "components/StyledHeadlessUI/StyledDisclosure";
import { downloadFile } from "helpers";
import { useSession } from "next-auth/react";
import {
  FunctionComponent,
  useMemo,
  useState,
} from "react";
import { CheckCircle, Plus, Trash } from "react-feather";
import {
  ApiError,
  EOfferStatus,
  <PERSON><PERSON><PERSON>rType,
  TOffer,
  TOrder,
} from "types";
import { Api } from "utils/api";
import {
  deleteOrderSummaryMutation,
  getOrderHistoryQuery,
  getOrderQuery,
  getOrderMetrics,
  getDailyOrderMetrics,
} from "utils/queries";
import { EditOrderDialog } from "../dialogs/EditOrderDialog";
import { ViewApplicationsDialog } from "../dialogs/ViewApplicationsDialog";
import { AxiosError } from "axios";
import { toApiError } from "utils/dataConversion";
import { OrderMetricCharts } from "../../EditOffer/charts/OrderMetricCharts";

const getDeleteText = (offer: TOffer) => {
  switch (offer.status) {
    case EOfferStatus.BUILDING:
    case EOfferStatus.PRE_LAUNCH:
    case EOfferStatus.APPLICATIONS_OPEN:
      return "Are you sure you want to delete your order? This action cannot be undone.";
  }
  return "Applications are now closed. The offer co-ordinator will be informed of your request but may reject it.";
};

const isOrdersAvailable = (status: EOfferStatus) => {
  switch(status) {
    case EOfferStatus.BUILDING:
    case EOfferStatus.PRE_LAUNCH:
      return false;
    default:
      return true;
  }
};

const isOrdersEditable = (status: EOfferStatus) => {
  switch(status) {
    case EOfferStatus.APPLICATIONS_OPEN:
    case EOfferStatus.APPLICATIONS_CLOSED:
      return true;
  }
  return false
}

interface IProps {
  offer: TOffer;
}
interface INoTermsProps {
  offer: TOffer;
  token : string | undefined;
}

const NoTermsWarning: FunctionComponent<INoTermsProps> = ({offer, token}) => {
  const { data: offerTermsResponse } = useQuery({
    queryKey: ["offerTerms", offer.id],
    queryFn: () => Api.getOfferTerms(offer.id, token),
  });

  if (offerTermsResponse?.accepted === false && offer.status === EOfferStatus.APPLICATIONS_OPEN) {
    return (<Container>
        <Alert
          className="mb-md-lg"
          theme="warning"
          message="The terms for this offer have not been signed. You can still enter orders, but acceptance of those orders will be at the discretion of the issuer."
        />
      </Container>)
  }

  return (<></>)
}

const OfferClosedWarning: FunctionComponent<IProps> = ({offer}) => {
  if (offer.status === EOfferStatus.APPLICATIONS_CLOSED) {
    return (<Container>
        <Alert
          className="mb-md-lg"
          theme="warning"
          message="This offer is now closed. Acceptance of order amendments will be at the discretion of the issuer."
        />
      </Container>)
  }

  return (<></>)
}


export const ViewOfferOrderTab: FunctionComponent<IProps> = ({ offer }) => {
  const { data: session } = useSession();
  const qc = useQueryClient();
  const [isOrderOpen, setIsOrderOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [isViewApplicationsOpen, setViewApplicationsOpen]=useState(false);
  const [isDownloadError, setDownloadOrderError] = useState<ApiError>()
  

  const orderQueryOptions = useMemo(() => getOrderQuery(offer.id, session?.accessToken),[offer, session]);

  const { data: orderData } = useQuery(orderQueryOptions);

  const orderHistoryQueryOptions = useMemo(() =>   getOrderHistoryQuery(offer.id, session?.accessToken), [offer,session]);
  const { data: orderHistory, dataUpdatedAt } = useQuery(orderHistoryQueryOptions);
  
  const order = useMemo(() => orderData?.orders?.[0], [orderData]);

  const { data: rawOrderMetrics } = useQuery({
    ...getOrderMetrics(offer.id, session?.accessToken),
  });
  const { data: rawOrderDailyMetrics } = useQuery({
    ...getDailyOrderMetrics(offer.id, session?.accessToken),
  });

  const deleteOrderSummaryMutator = useMutation(
    deleteOrderSummaryMutation(offer.id, qc, session?.accessToken)
  );
 
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const downloadOrderMutation = useMutation<any, AxiosError<Blob>, TOrder>({
    mutationFn: async (order?: TOrder) => {
      return await Api.downloadOfferOrderXLSX(offer.id, order?.order_book_id, false, session?.accessToken);
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (data: any) => {
      const fname = `${offer.name.replaceAll(" ","_")}_applications`;
      
      const file = new File([data], fname, {
        type: data.type,
      });
      downloadFile(file);
    },
    onError: async (blob: AxiosError<Blob>) => {
      setDownloadOrderError(toApiError(await blob.response?.data.text()));
    }
  });

  return (
    <>
      <Tab.Panel>
        {!isOrdersAvailable(offer.status) ? (
          <Container narrow className="content-center">
            <Prose className="flex flex-wrap space-around">
              <h3>Applications are not open.</h3>
              <p>New applications may not be made in the &apos;{offer.status}&apos; offer state.</p>
            </Prose>
          </Container>
        ) : (
          <>
          <Container>
          { <NoTermsWarning token={session?.accessToken} offer={offer}/> }
          { <OfferClosedWarning offer={offer}/> }
          
          </Container>
            {order ? (
              <Container>
                <OrderCard
                  order={order}
                  offer={offer}

                  editable={isOrdersEditable(offer.status)}

                  onViewApplications={ () => setViewApplicationsOpen(true) }
                  onDownloadApplications={ () => order && order?.order_book_id && downloadOrderMutation.mutate(order) }
                  
                  onEditOrder={() => {
                    qc.invalidateQueries(orderQueryOptions.queryKey);
                    setIsOrderOpen(true);
                  }}
                  onDeleteOrder={() => {
                    qc.invalidateQueries(orderQueryOptions.queryKey);
                    setIsConfirmDeleteOpen(true);
                  }}
                  
                  onRefreshOrder={ () => {
                      qc.invalidateQueries(orderQueryOptions.queryKey);
                      qc.invalidateQueries(orderHistoryQueryOptions.queryKey);
                  }}
                  dataUpdatedAt={dataUpdatedAt}
                />
              </Container>
            ) : (
              <Container narrow>
                {
                  isOrdersEditable(offer.status) ?
                
                (<EmptyAction
                  icon={CheckCircle}
                  iconClassName="text-ducati-step-0"
                  message="You don’t have any orders yet"
                  action={{
                    label: "Add order",
                    icon: Plus,
                    onClick: () => setIsOrderOpen(true),
                  }}
                />)
                 :
                  (<EmptyAction icon={CheckCircle} iconClassName="text-ducati-step-0" message="You don't have any orders, and orders cannot be entered at this time."/>)
                }
              </Container>
            )}
            {orderHistory && orderHistory?.length > 0 && (
              <Container>
                <StyledDisclosure className="mt-md" title="Order History">
                  <OrderHistoryTable
                    orderHistory={orderHistory}
                    offer={offer}
                    paging 
                    pagesize={25}
                  />
                </StyledDisclosure>
              </Container>
            )}
            <OrderMetricCharts dailyMetrics={rawOrderDailyMetrics} metrics={rawOrderMetrics} />

          </>
        )}
      </Tab.Panel>

      <EditOrderDialog open={isOrderOpen} qc={qc} accessToken={session?.accessToken} offer={offer} order={order} onClose={ () => {
        setIsOrderOpen(false)
      }}/>

      {/* View allocations modal */}
      { order && order.order_type === EOrderType.DETAILED && (
        <ViewApplicationsDialog open={isViewApplicationsOpen} onClose={() => setViewApplicationsOpen(false) } offer={offer} order={order} token={session?.accessToken} />
      )}

      {/* Delete order modal */}
      {order && (
        <StyledDialog
          open={isConfirmDeleteOpen}
          onClose={() => setIsConfirmDeleteOpen(false)}
          title="Delete order"
          footer={
            <div className="flex gap-sm">
              <Button
                type="button"
                theme="outline"
                onClick={() => setIsConfirmDeleteOpen(false)}
              >
                Cancel
              </Button>
              <Button
                icon={Trash}
                onClick={() => {
                  deleteOrderSummaryMutator.mutate(order.id);
                  setIsConfirmDeleteOpen(false);
                }}
              >
                Confirm
              </Button>
            </div>
          }
        >
          {getDeleteText(offer)}
        </StyledDialog>
      )}

      {/* Download Error dialog */}
      <StyledDialog open={!!isDownloadError} onClose={() => setDownloadOrderError(undefined)} title="Error downloading all applications" footer={<Button type="button" theme="outline" onClick={() => setDownloadOrderError(undefined)}>Ok</Button>}>
          <Alert theme="failure" message={ isDownloadError?.message } />
      </StyledDialog>

    </>
  );
};


