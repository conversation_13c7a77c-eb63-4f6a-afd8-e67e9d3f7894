import { Tab } from "@headlessui/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { Alert } from "components/Basic/Alert/Alert";
import { Container } from "components/Layout/Container";
import { useFieldErrors } from "hooks/useFieldErrors";
import { useSession } from "next-auth/react";
import { FunctionComponent, useCallback, useMemo, useState } from "react";
import { ApiError, ApiMutationResponse, EDocumentType, TOffer } from "types";
import { Api } from "utils/api";
import { TermsForm, TermsFormData } from "../forms/TermsForm";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { Button } from "components/Basic/Button/Button";
import { downloadResource } from "utils/misc";
import { Prose } from "components/Basic/Prose/Prose";

interface IProps {
  offer: TOffer;
}

export const ViewOfferTermsTabs: FunctionComponent<IProps> = ({ offer }) => {
  const { data: session } = useSession();
  const qc = useQueryClient();

  const [formData, setFormData] = useState<TermsFormData>();

  const { data: offerTermsResponse } = useQuery({
    queryKey: ["offerTerms", offer.id],
    queryFn: () => Api.getOfferTerms(offer.id, session?.accessToken),
  });

  const { data: ronMeta } = useQuery({
    queryKey: ["signed_id", offerTermsResponse?.retail_offer_notice_id],
    queryFn: () => Api.getDocumentMeta(offer.id, offerTermsResponse?.retail_offer_notice_id ?? "", "id", session?.accessToken),
    enabled: !!offer.id && !!offerTermsResponse?.retail_offer_notice_id
  });

  const acceptTermsMutation = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    TermsFormData
  >({
    mutationFn: (formData: TermsFormData) =>
      Api.updateOfferTerms(offer.id, formData, session?.accessToken),
    onSuccess: () => {
      qc.invalidateQueries(["offerTerms", offer.id]);      
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onError: () => {}
  });

  const termsDocument = useMemo( () => {
    return offer.documents.find( x => x.type === EDocumentType.RETAIL_OFFER_NOTICE)
  },[offer])

  const fieldErrors = useFieldErrors(acceptTermsMutation.error);

  const receiveOrNotReceive = useMemo( () => {
    return offerTermsResponse?.receive_commission ? "will":"will not";
  },[offerTermsResponse?.receive_commission])

  const downloadTerms = useCallback( () => {
    if (termsDocument) {
      downloadResource(termsDocument, offer, session?.accessToken)
    }
  }, [termsDocument, offer, session?.accessToken])

  const downloadSignedTerms = useCallback( () => {
    if (ronMeta) {
      downloadResource(ronMeta, offer, session?.accessToken)
    }
  },[ronMeta, offer, session?.accessToken])



  return (
    <Tab.Panel>
      <Container narrow>

        {offerTermsResponse?.accepted && (
          <Alert
            theme="success"
            title="Terms accepted"
            message={
            <span>
            You have accepted the terms of this offer, and {receiveOrNotReceive} receive commission.
            <br/><br/>
            <div>Accepted by: <a href={"mailto:" + offerTermsResponse.accepted_by_email}>{offerTermsResponse.accepted_by}</a></div>
            <Button className="mt-md" onClick={() => {downloadSignedTerms()}}>Download Terms</Button>
            </span>
            }
            date={ offerTermsResponse?.accepted_time ? new Date(offerTermsResponse?.accepted_time) : undefined}
            
          />
        ) }
        { !offerTermsResponse?.accepted && termsDocument && (
          <>
          <Alert
            theme="warning"
            title="Terms not accepted"
            message={
            <span>
              Please download the retail offer notice and affirm your acceptance of the terms below.
              <br/><br/>
              <Button onClick={() => {downloadTerms()}}>Download Terms</Button>
            </span>
          }
          />
          <div className="pt-2">
            <TermsForm onSubmit={ (fd) => setFormData(fd) } fieldErrors={fieldErrors} />
          </div>
          </>
        )}
        { !offerTermsResponse?.accepted && !termsDocument && (
          <EmptyAction message={"There is no Retail Offer Notice attached to this offer, it is not possible to accept the terms of the offer until one has been attached."} />
        )}

      <StyledDialog open={!!acceptTermsMutation.error} 
                    onClose={() => acceptTermsMutation.reset()} 
                    title="Failed to accept the terms" 
                    footer={<Button type="button" theme="outline" onClick={() => acceptTermsMutation.reset()}>Ok</Button>}>
          
          <Alert theme="failure" message={ acceptTermsMutation.error?.message } />
      </StyledDialog>      

      <StyledDialog open={!!formData} onClose={ () => {        
        setFormData(undefined);
      }}  title="Confirm Term Acceptance" footer={<Button type="button" theme="outline" onClick={() => { formData && acceptTermsMutation.mutate(formData); setFormData(undefined); }}>Confirm</Button>} >
        <Prose>
          Please confirm you accept the terms and will { formData?.receive_commission ? "" : "not"} receive commission.
        </Prose>
      </StyledDialog>

      
      </Container>
    </Tab.Panel>
  );
};
