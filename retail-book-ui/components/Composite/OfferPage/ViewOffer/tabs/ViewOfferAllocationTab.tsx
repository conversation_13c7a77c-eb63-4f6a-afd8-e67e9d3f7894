import { Tab } from "@headlessui/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Alert } from "components/Basic/Alert/Alert";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { AllocationCard } from "components/Bespoke/AllocationCard/AllocationCard";
import { AllocationLineItemGrid } from "components/Grids/Allocation/AllocationLineItemGrid";
import { Container } from "components/Layout/Container";
import { downloadFile } from "helpers";
import { useSession } from "next-auth/react";
import { FunctionComponent, useCallback, useMemo } from "react";
import { EOfferStatus, EOrderType, TAllocation, TOffer } from "types";
import { toTAllocation } from "utils/dataConversion";
import { downloadAllocationAnnotatedByOfferAllocationMutator, getOfferAllocationQuery, getOrderQuery } from "utils/queries";
import { AllocationChart } from "../../EditOffer/charts/AllocationChart"
import { AllocationMetricChart } from "../charts/AllocationMetricChart"

interface IProps {
  offer: TOffer;
}

interface IWarningProps {
  offer: TOffer;
}

const isAllocating = (offer: TOffer) => {
  return offer.status === EOfferStatus.ALLOCATING;
};

const isAllocationCleared = (allocation: TAllocation) => {
  return allocation.totals?.applications?.isZero() || allocation.totals?.notional_value?.isZero();
}

const NotAllocatedWarning: FunctionComponent<IWarningProps> = ({ offer }) => {
  if (isAllocating(offer)) {
    return (
      <Container>
        <Alert
          className="mb-md-lg"
          theme="warning"
          message={"The offer has not been locked into the allocation yet - the below figures may change!"}
        />
      </Container>)
  }

  return (<></>)
}

const detailColumns = ["ref","applications","existing","value","qty","allocQty","allocValue","taxWrapper"]


export const ViewOfferAllocationTab: FunctionComponent<IProps> = ({ offer }) => {
  const { data: session } = useSession();

  const { data: allocation, isLoading: allocationIsLoading } = useQuery({
    ...getOfferAllocationQuery(offer.id, session?.accessToken),
    select: useCallback((data: TAllocation) => toTAllocation(data), []),
  });

  const orderQueryOptions = useMemo(() => getOrderQuery(offer.id, session?.accessToken),[offer, session]);
  const { data: orderData } = useQuery(orderQueryOptions);

  const downloadMutatorOption = useMemo(
    () => downloadAllocationAnnotatedByOfferAllocationMutator(session?.accessToken),
    [session?.accessToken]
  );

  const downloadAllocationMutator = useMutation({
    ...downloadMutatorOption,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (data: any) => {
      // Download the file.
      const file = new File([data], `allocation_${offer?.id}`, {
        type: data.type,
      });
      downloadFile(file);
    },
  });

  const downloadAllocation = (allocation: TAllocation | undefined) => {
    downloadAllocationMutator.mutate({ offerId: offer.id, offerAllocationId: allocation?.id ?? ""})
  };

  return (
    <Tab.Panel>
      {allocationIsLoading &&
        <div className="grow flex items-center justify-center">
          <Spinner size="lg" message="Loading allocation..." />
        </div>
      }


      {allocation && isAllocationCleared(allocation) &&
        <div className="grow flex items-center justify-center">
          <Alert
            theme="warning"
            title="Allocation not found"
            message="The allocation has not been set or is awaiting clearance by the deal runner."
          />
        </div>
      }

      {!allocationIsLoading && allocation && !isAllocationCleared(allocation) &&
        <>
          <Container className="flex flex-col space-y-xl items-center w-1/2">
            <NotAllocatedWarning offer={offer} />
            <AllocationCard offer={offer} allocation={allocation} downloadAllocationXlsx={downloadAllocation}/>
          </Container>
          
          { orderData && orderData.orders?.[0].order_type == EOrderType.DETAILED &&
          
          <Container className="pt-xl w-full">
            <AllocationLineItemGrid offerId={offer.id} allocationBookId={allocation.allocation_book_id} showErrors={false} token={session?.accessToken} colNames={detailColumns}/>
          </Container>
          }

          <Container className="pt-md grid grid-cols-1 gap-md md:grid-cols-2 lg:grid-cols-2">
            <AllocationChart offer={offer} allocation={allocation} token={session?.accessToken} />
            <AllocationMetricChart offerId={offer.id} token={session?.accessToken} />
          </Container>
        </>
      }
    </Tab.Panel>
  );
};
