import { Tab } from "@headlessui/react";
import { Button } from "components/Basic/Button/Button";
import { DownloadCard } from "components/Bespoke/DownloadCard/DownloadCard";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Container } from "components/Layout/Container";
import { downloadFile } from "helpers";
import { useSession } from "next-auth/react";
import { FunctionComponent } from "react";
import { Download } from "react-feather";
import { TOffer } from "types";
import { Api } from "utils/api";
import { downloadResource } from "utils/misc";

interface IProps {
  offer: TOffer;
}

export const ViewOfferDocumentsTab: FunctionComponent<IProps> = ({ offer }) => {
  const { data: session } = useSession();

  return (
    <Tab.Panel>
      {offer?.documents.length > 0 ? (
        <Container>
          <Button
            icon={Download}
            className="mb-sm"
            onClick={async () => {
              const data = await Api.getDocuments(
                offer.id,
                session?.accessToken
              );

              const file = new File([data], `${offer.name} - Resources`, {
                type: data.type,
              });

              downloadFile(file);
            }}
          >
            Download All
          </Button>
          <ul className="space-y-sm">
            {offer?.documents.map((file) => (
              <li key={file.id}>
                <DownloadCard
                  title={file.title}
                  size={file.size}
                  fileType={file.type}
                  onClick={async () => {
                    downloadResource(file, offer, session?.accessToken)
                  }}
                />
              </li>
            ))}
          </ul>
        </Container>
      ) : (
        <Container narrow>
          <EmptyAction message="No documents are available for this offer yet" />
        </Container>
      )}
    </Tab.Panel>
  );
};
