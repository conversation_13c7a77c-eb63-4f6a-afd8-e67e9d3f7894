import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
// import { Input } from "components/Basic/Input/Input";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo } from "react";
import { FieldErrors, useForm } from "react-hook-form";
import { TTerms } from "types";

const schema = {
  type: "object",
  properties: {},
  required: [],
};


export interface TermsFormData {
  receive_commission: boolean;
}

interface TermsFormProps {
  terms?: TTerms;
  onSubmit: (data: TermsFormData) => void;
  fieldErrors?: FieldErrors<TermsFormData>;
}

export const TermsForm = ({ terms, onSubmit, fieldErrors }: TermsFormProps) => {
  const defaultValues: TermsFormData = useMemo(
    () => ({
      receive_commission: terms?.receive_commission ?? true
    }),
    [terms]
  );

  const { register, handleSubmit, formState, reset, setError, clearErrors } =
    useForm<TermsFormData>({
      defaultValues,
      resolver: ajvResolver(schema),
    });

  const { errors, isSubmitting } = formState;

  useReactHookFormServerErrors<TermsFormData>(fieldErrors, errors, setError, clearErrors);

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);


  return (
    <form className="form" onSubmit={handleSubmit(onSubmit)}>
      <section className="form-section">                              
        <Checkbox label="Receive Commission" {...register("receive_commission")}/>        
      </section>

      <div className="form-actions">
        <Button
          theme="special"
          loading={isSubmitting}
          disabled={isSubmitting}       
        >
          {/* Save */}
          Accept Terms
        </Button>        
      </div>
    </form>
  );
};
