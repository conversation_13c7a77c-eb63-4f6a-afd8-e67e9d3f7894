import { Tab } from "@headlessui/react";
import { StandaloneLink } from "components/Basic/StandaloneLink/StandaloneLink";
import { ViewOfferHeader } from "components/Composite/OfferPage/ViewOffer/ViewOfferHeader";
import { ViewOfferTermsTabs } from "components/Composite/OfferPage/ViewOffer/tabs/ViewOfferTermsTab";
import { ViewOfferDocumentsTab } from "components/Composite/OfferPage/ViewOffer/tabs/ViewOfferDocumentsTab";
import { ViewOfferOrderTab } from "components/Composite/OfferPage/ViewOffer/tabs/ViewOfferOrderTab";
import { Container } from "components/Layout/Container";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { useRouter } from "next/router";
import { FunctionComponent, useMemo } from "react";
import { ArrowLeft } from "react-feather";
import { EOfferStatus, TOffer } from "types";
import { TeamAccessForm } from "../TeamAccessForm";
import { ViewOfferOverviewTab } from "./tabs/ViewOfferOverviewTab";
import { useQuery } from "@tanstack/react-query";
import { getOfferUIState } from "utils/queries";
import { useSession } from "next-auth/react";
import { ViewOfferAllocationTab } from "./tabs/ViewOfferAllocationTab";
import { OfferTabPanelProps } from "../model/OfferFieldModel";

interface IProps {
  offer: TOffer;
  preview?: boolean;
}

export const ViewOffer: FunctionComponent<IProps> = ({ offer, preview }) => {
  const { data: session } = useSession();
  const router = useRouter();

  const { data: state } = useQuery(
    getOfferUIState(offer.id, session?.accessToken)
  );

  const readonly = useMemo(
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
    () => state?.fields.reduce( (map:any, x) => {map[x.field] = !x.editable; return map}, {}),
    [state]
  )

  const areAllocationsAvailable = offer.status === EOfferStatus.ALLOCATING || state?.states.has_allocation;


  const tabPanels = useMemo(
      () => {
          const tabs: OfferTabPanelProps[] = [];
          tabs.push({tabName: "Overview", tabPanel: <ViewOfferOverviewTab key="overview" offer={offer} />})

          if (!preview) {
              tabs.push({tabName: "Access", tabPanel: <Tab.Panel key="access"><TeamAccessForm
                  offer={offer}
                  retail={true}
                  description="Team members will have access to view and edit orders for this offer."
                  readonlyFields={readonly}
              /></Tab.Panel>});

              tabs.push({tabName: "Terms", tabPanel: <ViewOfferTermsTabs key="terms" offer={offer} />})
          }

          tabs.push({tabName: "Documents", tabPanel: <ViewOfferDocumentsTab key="documents" offer={offer} />})

          if (!preview) {
              tabs.push({tabName: "Order", tabPanel: <ViewOfferOrderTab key="order" offer={offer} />})
              tabs.push({tabName: "Allocation", disabled: !areAllocationsAvailable, tabPanel: <ViewOfferAllocationTab key="allocation" offer={offer} />})
          }

          return tabs;
      },
      [offer, readonly, preview, areAllocationsAvailable]
  );

  const tabStateIndex = useMemo(
    () => {
       const tabParam = router.query?.tab;
       if (tabParam) {
           for (const [idx, tabPanel] of tabPanels.entries()) {
               if (tabPanel.tabPanel.key && tabParam?.includes(tabPanel.tabPanel.key.toString()) && !tabPanel.disabled) {
                   return idx;
               }
           }
       }
       return 0;
    },
    [router.query, tabPanels]
  );
  
  return (
    <>
      <Container className="pt-xs-sm">
        { !preview && (<StandaloneLink
          href="/"
          icon={ArrowLeft}
          iconPosition="before"
          className="mb-md-lg"
        >
          Offers
        </StandaloneLink>)}

        <ViewOfferHeader offer={offer} token={session?.accessToken}/>
      </Container>

      <Tab.Group selectedIndex={tabStateIndex} onChange={(idx) => {
          router.replace({pathname: router.pathname, query: {...router.query, tab: tabPanels[idx].tabPanel.key?.toString()}});
      }}>
        <Container>
          <Tab.List className="tab-list">
            {tabPanels.map((tabPanel) => <StyledTab key={tabPanel.tabPanel.key?.toString()} disabled={tabPanel.disabled}>{tabPanel.tabName}</StyledTab>)}
          </Tab.List>
        </Container>
        <Tab.Panels className="tab-panel tab-panel--bordered">
            {tabPanels.map((tabPanel) => tabPanel.tabPanel)}
        </Tab.Panels>
      </Tab.Group>
    </>
  );
};
