
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { Alert } from "components/Basic/Alert/Alert";
import { Button } from "components/Basic/Button/Button";
import { Prose } from "components/Basic/Prose/Prose";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { TeamAccessTable } from "components/Composite/OfferPage/TeamAccessTable";
import { Container } from "components/Layout/Container";
import {
  StyledCombobox,
  StyledComboboxOption,
} from "components/StyledHeadlessUI/StyledCombobox";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { useSession } from "next-auth/react";
import {FunctionComponent, useMemo, useState} from "react";
import { Plus, User } from "react-feather";
import {ApiError, ApiGetParams, ApiMutationResponse, TOffer, TUser} from "types";
import {
  getTeamMembersQuery,
  getUsersQuery,
  sendRemoveUserMutation,
  sendUserInviteMutation,
} from "utils/queries";

interface IProps {
  offer: TOffer;
  retail: boolean;
  title?: string;
  description?: string;  
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonlyFields: any;
}

export const TeamAccessForm: FunctionComponent<IProps> = ({
  offer,
  retail,
  title = "Team members",
  description,
  readonlyFields,
}) => {
  const { data: session } = useSession();
  const qc = useQueryClient();
  const [isInviteUserModalOpen, setInviteUserModelOpen] = useState(false);
  const [selectedTeamMembers, setSelectedTeamMembers] = useState<
    StyledComboboxOption<TUser>[]
  >([]);

  const { data: invitedTeamMembers } = useQuery(
    getUsersQuery(offer.id, session?.accessToken)
  );

  const params = useMemo((): ApiGetParams => {
    return {
      limit: 0,
      offset: 0,      
    }
  },[]);


  const teamMembersQuery = useQuery(getTeamMembersQuery(params, session?.accessToken))

  const teamMemberOptions = useMemo( () => {
    const data = teamMembersQuery?.data?.data ?? []
    
    return data.filter(
      (user) => !invitedTeamMembers?.find(({id}) => id === user.id)).map((user)=>({label: `${user.name} (${user.email}) ${user.role_timestamps}`, value: user, id: user.id}))
    
  }, [teamMembersQuery, invitedTeamMembers])

  const [errorsFromAdd, setErrorsFromAdd] = useState<string[]>([]);
  
  const sendTeamMemberInviteMutationOptions = useMemo( () => {
    return sendUserInviteMutation(session?.accessToken, offer.id, retail, false, qc) 
  },[session?.accessToken, offer.id, retail, qc])

  const sendTeamMemberInviteMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>, string> (
    {...sendTeamMemberInviteMutationOptions, 
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      onError: () => {}}  
  );

  
  const remoteTeamMemberMutatorOptions = useMemo( () => {
    return sendRemoveUserMutation(session?.accessToken, offer.id, retail, qc)
  },[session?.accessToken, offer.id, retail, qc])

  const removeTeamMemberMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>,string>(
    { ...remoteTeamMemberMutatorOptions, onError: () => { setShowRemoveErrorDialog(true) }}  
  );

  const teamMemberOptionsFiltered = useMemo(
    () =>
      teamMemberOptions.filter((option) => {
        if (selectedTeamMembers.find((selected) => selected.id === option.id)) {
          return false;
        }

        if (invitedTeamMembers?.find((invited) => invited.id === option.id)) {
          return false;
        }
        return true;
      }),
    [teamMemberOptions, invitedTeamMembers, selectedTeamMembers]
  );

  const disabled = useMemo(() => readonlyFields?.team_access ?? false, [readonlyFields]);

  const [showRemoveErrorDialog, setShowRemoveErrorDialog] = useState(false);

  const inviteTeamMembers = async function (users: TUser[]) {
        const errors:string[] = []

        for (const user of users) {
            try {
                await sendTeamMemberInviteMutator.mutateAsync(user.id)
            } catch( err ) {
                errors.push(`Could not add ${user.name} (${user.email}) - ${(err as AxiosError<ApiError>).response?.data.message ?? "unknown"}`)
            }
        }
        if (errors.length == 0) {
            setInviteUserModelOpen(false);
            setSelectedTeamMembers([]);
        }
        setErrorsFromAdd(errors);
    }

  return (
    <>
      <Container>
        <Prose className="mb-md">
          <h2>{title}</h2>
          {description && <p>{description}</p>}
        </Prose>
        {!invitedTeamMembers?.length ? (
          <EmptyAction
            icon={User}
            message="You have not added any team members to this offer"
            
            action={{
              label: "Add team members",
              icon: Plus,
              onClick: () => setInviteUserModelOpen(true),
              disabled: disabled,
            }}
          />
        ) : (
          <>
            <TeamAccessTable
              users={invitedTeamMembers}
              onRemove={(row) => removeTeamMemberMutator.mutate(row.id)}
            />
            <Button
              type="button"
              loading={sendTeamMemberInviteMutator.isLoading}
              disabled={
                sendTeamMemberInviteMutator.isLoading ||
                !teamMemberOptions.length ||
                disabled
              }
              onClick={() => setInviteUserModelOpen(true)}
              icon={Plus}
            >
              Add team members
            </Button>
          </>
        )}
      </Container>

      

      {/* Remove Error Model */}
      <StyledDialog 
        open={showRemoveErrorDialog}
        onClose={ () => { setShowRemoveErrorDialog(false); }}
        title="Error removing team member"
        footer = {
          <div className="flex gap-xs">
            <Button type="button" onClick={() => setShowRemoveErrorDialog(false) }>Close</Button>
          </div>
        }
      > 
        <Alert theme="failure" message={removeTeamMemberMutator.error?.response?.data.message ?? "unknown error"}/>
      </StyledDialog>

      {/* Invite Team Member Model */}
      <StyledDialog
        open={isInviteUserModalOpen}
        onClose={() => {
          setInviteUserModelOpen(false);
          setSelectedTeamMembers([]);
          setErrorsFromAdd([]);
        }}
        title="Add team members"
        footer={

          <div className="flex gap-xs">
            <Button
              type="button"
              disabled={selectedTeamMembers.length < 1}
              loading={sendTeamMemberInviteMutator.isLoading}              
              onClick={async (e) => {
                e.preventDefault();

                if (!selectedTeamMembers) return;
                await inviteTeamMembers(selectedTeamMembers.map((opt) => {return opt.value}))
              }}
            >
              Add ({selectedTeamMembers.length})
            </Button>
            <Button
                  type="button"
                  disabled={teamMemberOptions.length < 1}
                  loading={sendTeamMemberInviteMutator.isLoading}
                  onClick={() => {
                      setSelectedTeamMembers((selected) => [
                          ...selected,
                          ...teamMemberOptionsFiltered,
                      ]);
                  }}
              >
                Select all
            </Button>
            <Button
              theme="outline"
              type="button"
              onClick={() => {
                setInviteUserModelOpen(false);
                setSelectedTeamMembers([]);
                setErrorsFromAdd([]);
              }}
            >
              Cancel
            </Button>
          </div>
        }
      >

        <div className="w-screen max-w-screen-sm">
          {description && <p className="body mb-sm">{description}</p>}
          <StyledCombobox
            options={teamMemberOptionsFiltered}
            onChange={(options: StyledComboboxOption<TUser>[]) =>
              setSelectedTeamMembers(options)
            }
            onRemove={(option: StyledComboboxOption<TUser>) =>
              setSelectedTeamMembers((existing) =>
                existing.filter((o) => o.id !== option.id)
              )
            }
            value={selectedTeamMembers}
            multiple
            placeholder="Search team members"
            label="Search team members"
            showLabel={false}
          />
          <div className="pt-2"/>

          {errorsFromAdd.map( (x, idx) => <Alert key={`err_${idx}`} theme="failure" className="pt-1" message={x}/>)}
        </div>
      </StyledDialog>
    </>
  );
};
