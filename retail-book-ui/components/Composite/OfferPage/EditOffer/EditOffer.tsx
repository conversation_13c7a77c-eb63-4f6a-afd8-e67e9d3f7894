import { Tab } from "@headlessui/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { FunctionComponent, useMemo } from "react";
import { ApiError, ApiMutationResponse, Clearable, EOfferStatus, TOffer } from "types";
import { editOfferMutation, getCurrencies, getOfferUIState } from "utils/queries";
import { EditOfferHeader } from "components/Composite/OfferPage/EditOffer/EditOfferHeader";
import { Container } from "components/Layout/Container";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { useFieldErrors } from "hooks/useFieldErrors";
import { AlertCircle, AlertTriangle } from "react-feather";
import { OverviewForm } from "./forms/OverviewForm";
import { SettlementForm } from "./forms/SettlementForm";
import { EditOfferIntermediaryAccessForm } from "./forms/EditOfferIntermediaryAccessForm";
import { EditOfferDocumentsTab } from "./tabs/EditOfferDocumentsTab";
import { EditOfferOrdersTab } from "./tabs/EditOfferOrdersTab";
import { EditOfferSettlementsTab } from "./tabs/EditOfferSettlementsTab";
import { TeamAccessForm } from "../TeamAccessForm";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import { AxiosError } from "axios";
import { ErrorCountByTab, OfferTabPanelProps, WarningCountByTab } from "../model/OfferFieldModel";
import { FieldErrors } from "react-hook-form";
import { EditOfferAllocationsTab } from "./tabs/EditOfferAllocationsTab";
import { selectApiError } from "utils/misc";
import { WallCrossTab } from "./tabs/WallCrossTab";
import { EditOfferInsidersTab } from "./tabs/EditOfferInsidersTab";

interface IProps {
  offer: TOffer;
}

export const EditOffer: FunctionComponent<IProps> = ({ offer }) => {
  const qc = useQueryClient();
  const { data: session } = useSession();
  const router = useRouter();

  const editOfferMutationOptions = useMemo( () => {
    return editOfferMutation(qc, offer.id, session?.accessToken)
  },[qc, offer.id, session?.accessToken])

  const { mutate: editOffer, error, reset } = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    Clearable<Partial<TOffer>>
  >({...editOfferMutationOptions, 
    //eslint-disable-next-line @typescript-eslint/no-empty-function
    onError: () => {}});

  const { data: state } = useQuery(
    getOfferUIState(offer.id, session?.accessToken)
  );

  const { data: currencies } = useQuery(
    getCurrencies(session?.accessToken)
  )

  const errors = useMemo(
    () => {
      return state?.fields.filter(({ error, warning }) => !!error || !!warning) ?? []
    },
    [state?.fields]
  );

  const readonly = useMemo(
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
    () => state?.fields.reduce( (map:any, x) => {map[x.field] = !x.editable; return map}, {}),
    [state?.fields]
  )

  const operations = useMemo(
    () => state?.operations.filter(({ label }) => label!="") ?? [],
    [state?.operations]
  );

  /**
   * This is quite verbose but the only way we have of indicating which tab has errors
   */
  const tabErrors = useMemo(() => ErrorCountByTab(errors), [errors])
  const tabWarnings = useMemo(() => WarningCountByTab(errors), [errors])

  const amendFieldErrors = useFieldErrors(error);

  const areAllocationsAvailable = offer.status === EOfferStatus.ALLOCATING || state?.states.has_allocation;

  const fieldErrors = useMemo( () => {
    const fieldErrors: FieldErrors = {}
    
    errors.forEach( x => fieldErrors[x.field] = { type: "server", message: x.error})

    return {
      ...fieldErrors, ...amendFieldErrors
    }
  },[errors, amendFieldErrors])

  const tabPanels = useMemo(
      () => {
        const tabs: OfferTabPanelProps[] = [];

        tabs.push({tabPanel:
          <Tab.Panel key="overview">
            <Container narrow>
              <OverviewForm
                offer={offer}
                currencies={currencies}
                onSubmit={editOffer}
                onCancel={() => reset()}
                fieldErrors={fieldErrors}
                readonlyFields={readonly}
                errorMessage={selectApiError(error?.response?.data)}
              />
            </Container>
          </Tab.Panel>
        });

        tabs.push({tabPanel: <WallCrossTab key="wallcross" offer={offer} fieldErrors={fieldErrors} readonlyFields={readonly}/>});

        tabs.push({tabPanel: <EditOfferInsidersTab key="insider" offer={offer}/>});

        tabs.push({tabPanel:
          <Tab.Panel key="settlement_details">
            <Container narrow>
              <SettlementForm
                  offer={offer}
                  onSubmit={editOffer}
                  fieldErrors={fieldErrors}
                  readonlyFields={readonly}
              />
            </Container>
          </Tab.Panel>
        });

        tabs.push({tabPanel:
          <Tab.Panel key="access">
            <Container>
              <TeamAccessForm
                  offer={offer}
                  retail={false}
                  readonlyFields={readonly}
                  description="Team members will have access to view and edit this offer."
              />
            </Container>
            <Container className="mt-xl">
              <EditOfferIntermediaryAccessForm offer={offer} readonlyFields={readonly}/>
            </Container>
          </Tab.Panel>
        });

        tabs.push({tabPanel: <EditOfferDocumentsTab key="document" offer={offer} readonlyFields={readonly} />});

        tabs.push({tabPanel: <EditOfferOrdersTab key="order" offer={offer} />});

        tabs.push({tabPanel: <EditOfferAllocationsTab key="allocation" offer={offer} />});

        tabs.push({tabPanel: <EditOfferSettlementsTab key="settlements" offer={offer} state={state} />});

        return tabs;
      },
      [offer, readonly, fieldErrors, editOffer, currencies, error?.response?.data, reset, state]
  );

  const tabStateIndex = useMemo(
      () => {
          const tabParam = router.query?.tab;
          if (tabParam) {
              for (const [idx, tabPanel] of tabPanels.entries()) {
                  if (tabPanel.tabPanel.key && tabParam?.includes(tabPanel.tabPanel.key.toString()) && !tabPanel.disabled) {
                      return idx;
                  }
              }
          }
          return 0;
      },
      [router.query, tabPanels]
  );

  return (
    <>
      <Container className="pt-xs-sm">       
        <EditOfferHeader
          offer={offer}
          errors={errors}
          operations={operations}
        />
      </Container>

      <Tab.Group selectedIndex={tabStateIndex} onChange={(idx) => {
          reset();
          router.replace({pathname: router.pathname, query: {...router.query, tab: tabPanels[idx].tabPanel.key?.toString()}});
      }}>
        <Container>
          <Tab.List className="tab-list">
            <StyledTab>
              Overview
              {tabErrors.Overview > 0 && (
                <AlertCircle
                  className="h-5 w-5 text-utility-red-step-0"
                  aria-hidden="true"
                />
              )}
              {
                tabWarnings.Overview > 0 && tabErrors.Overview === 0 && (
                  <AlertTriangle className="h-5 w-5 text-utility-yellow-step-0" aria-hidden="true" />
                )
              }
            </StyledTab>
            <StyledTab>Wall-Crossing</StyledTab>
            <StyledTab>Insiders</StyledTab>
            <StyledTab>
              Settlement Details
              {tabErrors.SettlementDetails > 0 && (
                <AlertCircle
                  className="h-5 w-5 text-utility-red-step-0"
                  aria-hidden="true"
                />
              )}
              {
                tabWarnings.SettlementDetails > 0 && tabErrors.SettlementDetails === 0 && (
                  <AlertTriangle className="h-5 w-5 text-utility-yellow-step-0" aria-hidden="true" />
                )
              }
            </StyledTab>
            <StyledTab>Access</StyledTab>        
            <StyledTab>
              Documents
              {tabErrors.Document > 0 && (
                <AlertCircle
                  className="h-5 w-5 text-utility-red-step-0"
                  aria-hidden="true"
                />
              )}              
              {                
                tabWarnings.Document > 0 && tabErrors.Document === 0 && (
                  <AlertTriangle className="h-5 w-5 text-utility-yellow-step-0" aria-hidden="true" />
                )
              }
            </StyledTab>
            <StyledTab>
              Orders
              {tabErrors.Orders > 0 && (
                <AlertCircle
                  className="h-5 w-5 text-utility-red-step-0"
                  aria-hidden="true"
                />
              )}
              {
                tabWarnings.Orders > 0 && tabErrors.Orders === 0 && (
                  <AlertTriangle className="h-5 w-5 text-utility-yellow-step-0" aria-hidden="true" />
                )
              }
            </StyledTab>
            <StyledTab disabled={!areAllocationsAvailable}>
              Allocations
              {tabErrors.Allocations > 0 && (
                <AlertCircle
                  className="h-5 w-5 text-utility-red-step-0"
                  aria-hidden="true"
                />
              )}
              {
                tabWarnings.Allocations > 0 && tabErrors.Allocations === 0 && (
                  <AlertTriangle className="h-5 w-5 text-utility-yellow-step-0" aria-hidden="true" />
                )
              }
            </StyledTab>
            <StyledTab disabled={!state?.states.has_allocation}>
              Settlements
              {tabErrors.Settlements > 0 && (
                <AlertCircle
                  className="h-5 w-5 text-utility-red-step-0"
                  aria-hidden="true"
                />
              )}
              {
                tabWarnings.Settlements > 0 && tabErrors.Settlements === 0 && (
                  <AlertTriangle className="h-5 w-5 text-utility-yellow-step-0" aria-hidden="true" />
                )
              }
            </StyledTab>
          </Tab.List>
        </Container>

        <Tab.Panels className="tab-panel tab-panel--bordered">
            {tabPanels.map((tabPanel) => tabPanel.tabPanel)}
        </Tab.Panels>
      </Tab.Group>
    </>
  );
};
