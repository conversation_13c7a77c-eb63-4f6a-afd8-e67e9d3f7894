import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { MarkdownEditor } from "components/Bespoke/MarkdownEditor/MarkdownEditor";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo, useState } from "react";
import { FieldErrors, useForm, Controller } from "react-hook-form";
import { TOffer, TWallCrossInfo, ApiMutationResponse, ApiError, WallCrossInviteResponse, UploadResource } from "types";
import { Tab } from "@headlessui/react";
import { Container } from "components/Layout/Container";
import { getOfferWallcross, getInvitesQuery, updateWallcrossMutation, deleteWallcrossMutation, sendWallCrossInviteMutation, getOfferWallCrossInvitesQuery, uploadWallcrossDocumentMutation } from "utils/queries";
import { useSession } from "next-auth/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Shield, Plus, Trash2, User } from "react-feather";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { AxiosError } from "axios";
import { ChooseIntermediariesDialog } from "../dialogs/ChooseIntermediariesDialog";
import { Prose } from "components/Basic/Prose/Prose";
import { Column, StyledReactDataGrid } from "components/StyledReactDataGrid/StyledReactDataGrid";
import { Badge } from "components/Basic/Badge/Badge";
import { GridDateCompare } from "../forms/EditOfferIntermediaryAccessForm"
import { DropUpload } from "components/Bespoke/DropUpload/DropUpload";
import { toast } from "react-toastify";
import { downloadFile } from "helpers";
import { Api } from "utils/api";
import ReactMarkdown from "react-markdown";

const schema = {
  type: "object",
  properties: {
    subject: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "subject is required" },
    },
    consent_to_cross: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "offer outline is required" },
    },
  },
  required: ["subject", "consent_to_cross"],
};

export interface WallCrossTabData {
  id: string;
  subject: string;
  consent_to_cross: string
}

interface WallCrossTabProps {
  offer: TOffer;
  fieldErrors: FieldErrors<WallCrossTabData>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonlyFields: any;
}

interface IGetWallcrossDocumentProps {
  offerId: string;
  fileId: string;  
}

interface IntermediaryRow {
  id: string;
  date?: Date;
  sendDate?: Date;
  name: string;
  status: string;
}

interface ResponseRow {
  id: string;
  name: string;
  email: string;
  status: string;
  response_time?: Date;
  intermediary: string;
}

const intermediaryColumns: Column<IntermediaryRow>[] = [
 
  {
    key: "name",
    name: "Name",
    sortable: true,
    comparator: (a, b) => a.name.localeCompare(b.name),
   
  },
  {
    key:"status",
    name: "Status",
    sortable: true,
    formatter: ({row}) => (
      <Badge theme={ row.status === "Rejected" ? "failure" : row.status === "Accepted" ? "success" : "info"}>{row.status === "Pending" ? "Sent" : row.status}</Badge>
    ),
    comparator: (a, b) => a.status.localeCompare(b.status)
  },
  {
    key: "sendDate",
    name: "Invite Sent",
    sortable: true,
    comparator: (a,b) => {
      return GridDateCompare(a.sendDate, b.sendDate)
    },
    formatter: ({row}) => (<p> {row.sendDate?.toLocaleDateString() ?? ""} {row.sendDate?.toLocaleTimeString() ?? ""}</p>)  
  }
];

const InvitedIntermediariesTable = ({
  intermediaries,
}: {
  intermediaries: WallCrossInviteResponse[];
}) => {
  const rows = useMemo(
    () =>
      intermediaries.map((intermediary) => {
        return {
          id: intermediary.id ?? "",
          sendDate: intermediary.invite_sent_time ? new Date(intermediary.invite_sent_time) : undefined,
          name: intermediary.intermediary_display_name,
          status: intermediary.status ?? ""
        };
      }),
    [intermediaries]
  );

  return (
    <StyledReactDataGrid
      className="mb-sm"
      rows={rows}
      columns={intermediaryColumns}
      contextMenuItems={[] /*[
        {
          label: "Resend Invite",
          onClick: (e) => {
            e.preventDefault();
            toast.error("Placeholder for resend invite");
          },
        },
        {
          label: "Revoke invite",
          onClick: (e) => {
            e.preventDefault();
            toast.error("Placeholder for revoke invite");
          },
        },
      ] - NB Removed as resend, revoke aren't supported*/}
    />
  );
};


const responseColumns: Column<ResponseRow>[] = [ 
  {
    key: "intermediary",
    name: "Intermediary",
    sortable: true,
    comparator: (a, b) => a.intermediary.localeCompare(b.intermediary),
   
  },
  {
    key: "name",
    name: "Name",
    sortable: true,
    comparator: (a, b) => a.name.localeCompare(b.name),
   
  },
  {
    key: "email",
    name: "Email",
    sortable: true,
    comparator: (a, b) => a.email.localeCompare(b.email),
  },
  {
    key:"status",
    name: "Status",
    sortable: true,
    formatter: ({row}) => (
      <Badge theme={ row.status === "Rejected" ? "failure" : row.status === "Accepted" ? "success" : "info"}>{row.status === "Pending" ? "Sent" : row.status}</Badge>
    ),
    comparator: (a, b) => a.status.localeCompare(b.status),
  },
  {
    key: "response_time",
    name: "Response Time",
    sortable: true,
    comparator: (a,b) => {
      return GridDateCompare(a.response_time, b.response_time)
    },
    formatter: ({row}) => (<p> {row.response_time?.toLocaleDateString() ?? ""} {row.response_time?.toLocaleTimeString() ?? ""}</p>)  
  }
];

const InviteResponsesTable = ({
  invites,
}: {
  invites: WallCrossInviteResponse[];
}) => {
  const rows = useMemo(
    () => {
      return invites.flatMap((invite) => {
        return (invite.responses ?? []).map((response) => {
          return {
            id: response.id,
            response_time: response.response_time ? new Date(response.response_time) : undefined,
            name: response.name ?? "",
            email: response.email ?? "",
            status: response.status,
            intermediary: invite.intermediary_display_name
          };
        })
      })
    },
    [invites]
  );

  return (
    <StyledReactDataGrid
      className="mb-sm"
      rows={rows}
      columns={responseColumns}
      contextMenuItems={[] /*[
        {
          label: "Resend Invite",
          onClick: (e) => {
            e.preventDefault();
            toast.error("Placeholder for resend invite");
          },
        },
        {
          label: "Revoke invite",
          onClick: (e) => {
            e.preventDefault();
            toast.error("Placeholder for revoke invite");
          },
        },
      ] - NB Removed as resend, revoke aren't supported*/}
    />
  );
};

export const WallCrossTab = ({
  offer,
  fieldErrors,
  readonlyFields,
}: WallCrossTabProps) => {
  const qc = useQueryClient();
  const { data: session } = useSession();
  const [isInviteIntermediaryModalOpen, setInviteIntermediaryModalOpen] = useState(false);
  const hasWallCrossInfo = offer.wall_cross_info_id != null && offer.wall_cross_info_id != ""
  const isLaunched = offer.is_launched
  const isInsideInformation = offer.inside_information

  const { data: wallCrossInfo } = useQuery(
    getOfferWallcross(offer.id, session?.accessToken, offer.wall_cross_info_id)
  );
  const hasOfferOutline = wallCrossInfo?.offer_outline_document_id ? true : false;
  const [showWallCross, setShowWallCross] = useState(wallCrossInfo != null);
  const defaultValues: WallCrossTabData = useMemo(
    () => ({
      id: wallCrossInfo?.id ?? "",
      subject: wallCrossInfo?.subject ?? "",
      consent_to_cross: wallCrossInfo?.consent_to_cross ?? "",
    }), [wallCrossInfo]
  );
  useEffect(() => {
    setShowWallCross(wallCrossInfo != null);
  }, [wallCrossInfo]);

  const { mutate: updateWallcrossMutator } = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    Partial<TWallCrossInfo>
  >(updateWallcrossMutation(offer.id, qc, session?.accessToken));

  const { mutate: deleteWallcrossMutator } = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    void
  >(deleteWallcrossMutation(offer.id, qc, session?.accessToken));

  const uploadOfferOutlineOptions = useMemo(
    () => uploadWallcrossDocumentMutation(offer.id, qc, session?.accessToken),
    [offer.id, qc, session?.accessToken]
  );

    const uploadOfferOutlineMutator = useMutation<ApiMutationResponse,AxiosError<ApiError>,Partial<UploadResource>>({
      ...uploadOfferOutlineOptions,
      onSuccess: () => {
        toast.success("Offer outline uploaded");
      },
      onError: () => {
        toast.error("Error uploading file");
      }
  });

  const getWallCrossDocumentMutator = useMutation<Blob,AxiosError<Blob>,IGetWallcrossDocumentProps>({
    mutationFn: (props?: IGetWallcrossDocumentProps) => {
      return Api.getOfferWallcrossDocument(props?.offerId ?? "", props?.fileId ?? "", session?.accessToken);
    },
    onSuccess: (data:Blob) => {
      const file = new File([data], "Offer Outline", {
        type: data.type,
      });
      downloadFile(file);
    },
    onError: async () => {      
      toast.error("Failed to download file");
    },
  })

  const { register, handleSubmit, formState, reset, setError, control, clearErrors } =
    useForm<WallCrossTabData>({
      defaultValues,
      resolver: ajvResolver(schema),
    });

  const { errors, isDirty, isSubmitting } = formState;

  useReactHookFormServerErrors<WallCrossTabData>(fieldErrors, errors, setError, clearErrors);

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const sendIntermediaryInviteMutator = useMutation({
    ...sendWallCrossInviteMutation(offer.id, qc, session?.accessToken, offer.wall_cross_info_id),
    onError: () => { toast.error("") }
  });
  const { data: wcInvitesQueryResponse } = useQuery(
    getOfferWallCrossInvitesQuery(offer.id, session?.accessToken, offer.wall_cross_info_id)
  )
  const invitesSent = (wcInvitesQueryResponse??[]).length > 0
  const inviteResponses = (wcInvitesQueryResponse??[]).map((inter) => inter.responses ?? []).reduce((acc, curVal) => {
      return acc.concat(curVal);
  }, [])
  const { data: invitesQueryResponse } = useQuery(
    getInvitesQuery(offer.id, session?.accessToken)
  );

  const isReadonly = (field: string) => {
    if ( readonlyFields ) {
      return readonlyFields[field] ?? false
    }
    return false
  }
  const inviteIntermediariesDisabled = useMemo( () => {
    return readonlyFields?.wallcross_intermediaries ?? false
  },[readonlyFields]);

  return (
    <Tab.Panel>
      <Container>
        { !showWallCross && (
            isInsideInformation ?
                <EmptyAction
                    icon={Shield}
                    message="There is no wall-cross attached to this offer"
                    action={{
                      label: "Enable Wall-Crossing",
                      onClick: () => setShowWallCross(true),
                      disabled: isLaunched || !isInsideInformation,
                      icon: Plus,
                    }}
                />
                :
                <EmptyAction
                  icon={Shield}
                  message="A wall-cross can only be added if the offer is flagged as having inside information"
                />
        )}
        <div>
          { showWallCross && (
          <form
            className="form"
            onSubmit={handleSubmit((formData) => {
              updateWallcrossMutator(formData);
            }
            )}
          >
            <section className="form-section">
              { invitesSent || isLaunched ?
                (<div>
                  <h2 className="h2 mb-sm">Subject</h2>
                  <Prose as={ReactMarkdown}>{wallCrossInfo?.subject}</Prose>
                </div>) :
                (<Input
                    {...register("subject")}
                    label="Subject"
                    error={errors.subject?.message}
                    readOnly={isReadonly("subject") || invitesSent}
                  />
                )
              }


              { invitesSent || isLaunched ? 
              (<div>
                <h2 className="h2 mb-sm">Consent To Cross</h2>
                <Prose as={ReactMarkdown}>{wallCrossInfo?.consent_to_cross}</Prose>
              </div>) :
            
              <Controller
                name="consent_to_cross"
                control={control}
                render={({
                  field: { onChange, value, name },
                  fieldState: { error },
                }) => (
                  
                  <MarkdownEditor
                    id={name}
                    label="Consent To Cross"
                    value={value}
                    onChange={(value) => onChange(value)}
                    error={error?.message}
                    disabled={isReadonly("consent_to_cross")}
                  />
                )}
              />
              }

            </section>

            { showWallCross && hasWallCrossInfo && (
              <section>
                <label className="input__label">Offer Outline</label>
                <Button className="mt-sm mb-sm" disabled={!hasOfferOutline} onClick={
                  (e) => {
                    e.preventDefault();
                    getWallCrossDocumentMutator.mutate({
                      offerId: offer.id,
                      fileId: wallCrossInfo?.offer_outline_document_id ?? "",
                    })
                  }
                } >Download</Button>
                { !invitesSent && !isLaunched && (
                  <DropUpload maxFiles={1} onDrop={ (f) => { 
                      uploadOfferOutlineMutator.mutate({ file: f[0], title: "Offer Outline", type: "Offer Outline" },uploadOfferOutlineOptions);
                  }} />
                )}
              </section>
            )}

            { !invitesSent && !isLaunched && (
              <div className="form-actions">
                <Button loading={isSubmitting} disabled={ isReadonly("wallcrossing") || !isDirty || isSubmitting || invitesSent}>
                  Save
                </Button>
                <Button
                  type="button"
                  theme="outline"
                  disabled={ isReadonly("wallcrossing") || isSubmitting || invitesSent || !((showWallCross && wallCrossInfo == null) || isDirty )}
                  onClick={() => {
                    reset(defaultValues);
                    setShowWallCross(hasWallCrossInfo);
                  }}
                >
                  Cancel
                </Button>
                <Button icon={Trash2} theme="outline" disabled={isSubmitting || wallCrossInfo == null || invitesSent} onClick={(event) => { event.preventDefault(); deleteWallcrossMutator();}}>
                  Remove Wall-Crossing
                </Button>
              </div>
            )}
          </form>
          )}
          { showWallCross && hasWallCrossInfo && hasOfferOutline && (
          <Container className="mt-xl">
            <Prose className="mb-md">
              <h2>Intermediaries</h2>
              <p>Intermediaries will immediately be sent a wall crossing request to view the offer outline</p>
            </Prose>
            {wcInvitesQueryResponse && wcInvitesQueryResponse.length > 0 ? (
              <>
                <InvitedIntermediariesTable
                  intermediaries={wcInvitesQueryResponse}
                />
                <div className="flex gap-sm">
                  <Button
                    loading={sendIntermediaryInviteMutator.isLoading}
                    disabled={
                      isLaunched ||
                      inviteIntermediariesDisabled ||
                      sendIntermediaryInviteMutator.isLoading
                    }
                    type="button"
                    onClick={(e) => {e.preventDefault(); setInviteIntermediaryModalOpen(true);}}
                    icon={Plus}
                  >
                    Invite intermediaries
                  </Button>
                </div>
              </>
            ) : (
              <EmptyAction
                icon={User}
                message="You haven't invited any intermediaries to Wall-Cross"
                action={{
                  label: "Invite Intermediaries",
                  onClick: () => setInviteIntermediaryModalOpen(true),
                  disabled: inviteIntermediariesDisabled || isLaunched,
                  icon: Plus,
                }}
              />
            )}
          </Container>
          )}
          {showWallCross && hasWallCrossInfo && hasOfferOutline && wcInvitesQueryResponse && inviteResponses.length > 0 && (
              <Container>
              <Prose className="mb-md mt-xl">
                <h2>Responses</h2>
              </Prose>
              <InviteResponsesTable invites={wcInvitesQueryResponse} />
              </Container>
            )}
          <ChooseIntermediariesDialog
            description="Wall-Crossing invitations will immediately be sent out"
            open={isInviteIntermediaryModalOpen}
            isLoading={sendIntermediaryInviteMutator.isLoading}
            invites={(wcInvitesQueryResponse?? []).concat(invitesQueryResponse ?? [])} //include intermediaries invited via the access tab - we don't allow an inter to be invited via both tabs
            onInvite={(invites) => sendIntermediaryInviteMutator.mutateAsync(invites)}
            onClose={() => setInviteIntermediaryModalOpen(false)}
          />
        </div>
      </Container>
    </Tab.Panel>
  );
};
