import { Tab } from "@headlessui/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Alert } from "components/Basic/Alert/Alert";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { AllocationCard } from "components/Bespoke/AllocationCard/AllocationCard";
import { Container } from "components/Layout/Container";
import { useSession } from "next-auth/react";
import { FunctionComponent, useCallback, useMemo, useState } from "react";
import { EOfferStatus, TAllocation, TOffer } from "types";
import { toTAllocation } from "utils/dataConversion";
import { downloadAllocationAnnotatedByOfferAllocationMutator, getOfferAllocationQuery } from "utils/queries";
import { EditAllocationDialog } from "../dialogs/EditAllocationDialog";
import { downloadFile } from "helpers";
import { AllocationByIntermediaryGrid } from "../grids/AllocationByIntermediaryGrid";
import { AllocationChart } from "../charts/AllocationChart"

interface IProps {
  offer: TOffer;
}

interface IWarningProps {
  offer: TOffer;
}

const isAllocating = (offer: TOffer) => {
  return offer.status === EOfferStatus.ALLOCATING;
};

const NoAmendmentsWarning: FunctionComponent<IWarningProps> = ({offer}) => {
    if (!isAllocating(offer)) {
      return (
        <Container>
          <Alert
            className="mb-md-lg"
            theme="warning"
            message={"Allocations can only be entered/altered in the '" + EOfferStatus.ALLOCATING + "' state"}
          />
        </Container>)
    }
  
    return (<></>)
}


export const EditOfferAllocationsTab: FunctionComponent<IProps> = ({ offer }) => {
  const { data: session } = useSession();
  const [isUpdateAllocationModalOpen, setUpdateAllocationModalOpen] = useState(false);

  const { data: allocation, isLoading: allocationIsLoading } = useQuery({
    ...getOfferAllocationQuery(offer.id, session?.accessToken),
    select: useCallback((data: TAllocation) => toTAllocation(data), []),
  });

  const downloadMutatorOption = useMemo(
    () => downloadAllocationAnnotatedByOfferAllocationMutator(session?.accessToken),
    [session?.accessToken]
  );

  const downloadAllocationMutator = useMutation({
    ...downloadMutatorOption,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (data: any) => {
      // Download the file.
      const file = new File([data], `allocation_${offer?.id}`, {
        type: data.type,
      });
      downloadFile(file);
    },
  });

  const downloadAllocation = (allocation: TAllocation | undefined) => {
    downloadAllocationMutator.mutate({ offerId: offer.id, offerAllocationId: allocation?.id ?? ""})
  };
  
  const uploadAllocation = isAllocating(offer) ? () => setUpdateAllocationModalOpen(true) : undefined

  const hasAllocation = !allocationIsLoading && allocation?.id != ""

  return (
    <Tab.Panel>
        { allocationIsLoading &&       
          <div className="grow flex items-center justify-center">
            <Spinner size="lg" message="Loading allocations..." />
          </div>
        }

        { !allocationIsLoading && 
          <>
            <NoAmendmentsWarning offer={offer}/>           
            <Container className="flex flex-col space-y-xl items-center w-1/2">
              <AllocationCard offer={offer} allocation={allocation} downloadAllocationXlsx={downloadAllocation} uploadAllocation={uploadAllocation}/> 
            </Container>
            { hasAllocation &&
              <Container className="pt-5 w-full">
                <AllocationByIntermediaryGrid offer={offer} allocation={allocation} token={session?.accessToken}/>
              </Container>
            }
            <Container as="div" className="pt-5">
              <AllocationChart offer={offer} allocation={allocation} token={session?.accessToken} />
            </Container>

            { allocation && 
              <EditAllocationDialog 
                offer={offer}
                allocation={allocation} 
                isModalOpen={isUpdateAllocationModalOpen} 
                modalOpenTrigger={setUpdateAllocationModalOpen} 
              />
            }
            
          </>
        }
    </Tab.Panel>
  );
};