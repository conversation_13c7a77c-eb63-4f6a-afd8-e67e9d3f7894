import { Tab } from "@headlessui/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Alert } from "components/Basic/Alert/Alert";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { SettlementCard } from "components/Bespoke/SettlementCard/SettlementCard";
import { Container } from "components/Layout/Container";
import { useSession } from "next-auth/react";
import { FunctionComponent, useMemo, useState } from "react";
import { EOfferStatus, TOffer, ApiError, ApiMutationResponse, TOfferUIState } from "types";
import { generateSettlementObligationsMutation, getOfferSettlementObligationsQuery, getOfferSettlementBookQuery } from "utils/queries";
import { AxiosError } from "axios";
import { SettlementObligationsGrid } from "../grids/SettlementObligationsGrid";

interface IWarningProps {
  offer: TOffer;
}

const canAmendInstructions = (offer: TOffer) => {
  return offer.status === EOfferStatus.ALLOCATED ;
};

const NoAmendmentsWarning: FunctionComponent<IWarningProps> = ({offer}) => {
    if (!canAmendInstructions(offer)) {
      return (
        <Container>
          <Alert
            className="mb-md-lg"
            theme="warning"
            message={"Settlements can only be created in the  '" + EOfferStatus.ALLOCATED + "' state"}
          />
        </Container>)
    }
  
    return (<></>)
}

interface IFailedProps {
  error: string | undefined;
}

const FailedToGenerateError: FunctionComponent<IFailedProps> = ({error}) => {
  if (error && error.length > 0) {
    return (
      <>
        <Container as="div" className="mt-0">
          <Alert
            className="mb-md-lg mt-0"
            theme="failure"
            message={"Failed to generate settlements - " + error}
          />
        </Container>
      </>
    )
  }

  return (<></>)
}

const getErrorMessage = (err: AxiosError<ApiError>): string => {
  const error = err?.response?.data?.message ?? "Unknown error";
  if (err?.response?.data.detail?.message && err?.response?.data.detail?.message.length > 1) {
    return err?.response?.data.detail?.message;
  }
  return error;
}

interface IProps {
  offer: TOffer;
  state: TOfferUIState | undefined;
}

export const EditOfferSettlementsTab: FunctionComponent<IProps> = ({ offer, state }) => {
  const qc = useQueryClient();
  const { data: session } = useSession();

  const [generateError, setGenerateError] = useState<string>();

  const generateSettlementObligationsMutatorOption = useMemo(
    () => generateSettlementObligationsMutation(qc, offer.id, session?.accessToken, () => { setGenerateError(undefined); }),
    [session?.accessToken, offer.id, setGenerateError, qc]
  );

  const generateSettlementObligationsMutator = useMutation<
  ApiMutationResponse, AxiosError<ApiError>, string>({
    ...generateSettlementObligationsMutatorOption,
    onError: (err: AxiosError<ApiError>) => {
      setGenerateError(getErrorMessage(err));
    }
  });
  
  const obligationsQuery = useQuery({...getOfferSettlementObligationsQuery(offer?.id, state?.states.has_settlements ?? false, session?.accessToken)});
  const bookQuery = useQuery({...getOfferSettlementBookQuery(offer?.id, state?.states.has_settlements ?? false, session?.accessToken)});

  const isLoadingObligations = generateSettlementObligationsMutator.isLoading || obligationsQuery.isFetching || bookQuery.isFetching
  
  return (
    <Tab.Panel>
      <>
        <NoAmendmentsWarning offer={offer}/> 
        {isLoadingObligations && (
          <div className="flex items-center justify-center pt-md">
            <Spinner size="lg" message="Loading settlements..." />
          </div>
        )}
        {!isLoadingObligations && (
          <Container>
            <FailedToGenerateError error={generateError} />
            <Container as="div" className="flex flex-col space-y-xl items-center w-1/2">
              <SettlementCard book={bookQuery.data?.data}  canAmendInstructions={canAmendInstructions(offer)} generateObligations={() => { generateSettlementObligationsMutator.mutate(offer.id); }} /> 
            </Container>
            <Container className="pt-5 w-full">
              <SettlementObligationsGrid data={obligationsQuery.data?.data ?? []}/>
            </Container>
          </Container>
        )}
      </>
    </Tab.Panel>
  );
};