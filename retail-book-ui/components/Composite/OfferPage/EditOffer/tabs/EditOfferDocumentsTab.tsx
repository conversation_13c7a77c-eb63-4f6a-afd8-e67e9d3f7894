import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import {
  FunctionComponent,
  useMemo,
  useState,
} from "react";
import {
  deleteDocumentMutation,
} from "utils/queries";
// components
import { Tab } from "@headlessui/react";
import { Button } from "components/Basic/Button/Button";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Container } from "components/Layout/Container";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { Download, FilePlus, Plus, Upload } from "react-feather";
// Types
import { Alert } from "components/Basic/Alert/Alert";
import { downloadFile } from "helpers";
import { Resource, TOffer, ApiError, ApiMutationResponse } from "types";
import { Api } from "utils/api";
import { ResourceTable } from "../ResourceTable";
import { DocumentUploadDialog } from "../dialogs/DocumentUploadDialog";
import { DocumentEditDialog } from "../dialogs/EditDocumentMetaDialog";
import { AxiosError } from "axios";
import { ContextMenuPropItem } from "components/Bespoke/ContextMenu/ContextMenu";


interface IProps {
  offer: TOffer;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonlyFields: any;
}

interface IGetDocumentProps {
  offerId: string;
  rowId: string;
  fileName: string;  
}

interface IGetAllDocumentProps {
  offerId: string;
  offerName: string;
}

export const EditOfferDocumentsTab: FunctionComponent<IProps> = ({ offer, readonlyFields }) => {
  const { data: session } = useSession();
  const qc = useQueryClient();
  const [isEditDocumentModalOpen, setEditDocumentModalOpen] = useState(false);
  const [isUploadDocumentModalOpen, setUploadDocumentModalOpen] = useState(false);
  const [isRemoveDocumentModalOpen, setRemoveDocumentModalOpen] = useState(false);
  const [currentResource, setCurrentResource] = useState<Resource>()

  const deleteDocumentMutationOptions = useMemo(
    () => deleteDocumentMutation(offer.id, qc, session?.accessToken),
    [offer.id, qc, session?.accessToken]
  );

  const deleteDocumentMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>, string>({
    ...deleteDocumentMutationOptions,
    onSuccess: () => {
      deleteDocumentMutationOptions.onSuccess();
      setRemoveDocumentModalOpen(false);
    },    
    //eslint-disable-next-line  @typescript-eslint/no-empty-function
    onError: () => {}
  });

  
  const [documentDownloadError, setDocumentDownloadError] = useState<ApiError>();
  const getDocumentMutator = useMutation<Blob,AxiosError<Blob>,IGetDocumentProps>({
    mutationFn: (props?: IGetDocumentProps) => {
      return Api.getDocument(props?.offerId ?? "", props?.rowId ?? "", session?.accessToken);
    },
    onSuccess: (data:Blob, props: IGetDocumentProps) => {
      const file = new File([data], props.fileName, {
        type: data.type,
      });

      downloadFile(file);
    },
    onError: async (error: AxiosError<Blob>) => {      
      setDocumentDownloadError(JSON.parse((await error.response?.data.text()) ?? "{}") as ApiError)
      setShowErrorDialog(true);
    },
  })

  const getDocumentAllMutator = useMutation<Blob,AxiosError<Blob>,IGetAllDocumentProps>({
    mutationFn: (props?:IGetAllDocumentProps) => { return Api.getDocuments(props?.offerId ?? "", session?.accessToken); },
    onSuccess: (data:Blob, props: IGetAllDocumentProps) => {  
      const file = new File([data], `${props.offerName} - Resources`, {
        type: data.type,
      });

      downloadFile(file);
    },
    onError: async (error: AxiosError<Blob>) => {
      setDocumentDownloadError(JSON.parse((await error.response?.data.text()) ?? "{}") as ApiError)
      setShowErrorDialog(true);
    }
  })


  const [showErrorDialog,setShowErrorDialog] = useState<boolean>(false);


  const ctxItems = useMemo<ContextMenuPropItem<Resource>[]>( ():ContextMenuPropItem<Resource>[] => {
    
    const downloadItem:ContextMenuPropItem<Resource> = {
      label: "Download",
      onClick: (e, row) => {
        e.preventDefault();
        if (!row?.id) return;

        getDocumentMutator.mutate({
          offerId: offer.id,
          rowId: row.id,
          fileName: row.file_name,
        })
        
      },
    }

    if (readonlyFields?.document ?? false) {
      return [downloadItem]
    } 
    
    return [
      {
        label: "Edit",
        onClick: (e, row) => {
          e.preventDefault();
          setCurrentResource(row);
          setEditDocumentModalOpen(true);                    
        },
      },
      {
        label: "Remove",
        onClick: (e, row) => {
          e.preventDefault();                     
          setCurrentResource(row);
          deleteDocumentMutator.reset();
          setRemoveDocumentModalOpen(true);
        },
      },
      downloadItem  
    ]
  },[offer.id, readonlyFields, deleteDocumentMutator, getDocumentMutator])

  return (
    <>
      <Tab.Panel>
        <Container>
          {offer.documents?.length ? (
            <>
              <ResourceTable
                className="mb-sm"
                pageSize={10}
                rows={offer.documents}
                contextMenuItems={ctxItems}
              />
              <Button
                type="button"
                onClick={() => setUploadDocumentModalOpen(true)}
                disabled={readonlyFields?.document ?? false}
                icon={Upload}
              >
                Add document
              </Button>
              <Button
                icon={Download}
                className="ml-sm"
                onClick={() => { getDocumentAllMutator.mutate({offerId: offer.id, offerName:offer.name}) }}
              >
                Download All
              </Button>
            </>
          ) : (
            <EmptyAction
              icon={FilePlus}
              message="You haven't added any documents to this offer"
              action={{
                label: "Add Document",
                onClick: () => setUploadDocumentModalOpen(true),
                icon: Plus,
              }}
            />
          )}
        </Container>
      </Tab.Panel>

      {/* Upload Document Modal */}
     <DocumentUploadDialog 
        open={isUploadDocumentModalOpen} 
        setOpen={setUploadDocumentModalOpen} 
        accessToken={session?.accessToken} 
        offer={offer}
      />

      {/* Edit Document Modal */}
      <DocumentEditDialog
        open={isEditDocumentModalOpen}
        setOpen={setEditDocumentModalOpen}
        accessToken={session?.accessToken}
        offer={offer}
        document={currentResource}
      />

      {/* Remove Document Modal */}
      <StyledDialog
        open={isRemoveDocumentModalOpen}
        onClose={() => setRemoveDocumentModalOpen(false)}
        unmount={false}
        title="Remove document"
        footer={
          <div className="flex gap-xs">
            <Button
              type="button"             
              onClick={async (e) => {
                e.preventDefault();
                if (currentResource ) {
                  deleteDocumentMutator.mutate( currentResource.id )
                }                
              }}
            >
              Remove document
            </Button>
            <Button
              theme="outline"
              type="button"
              onClick={() => {
                setRemoveDocumentModalOpen(false);                
              }}
            >
              Cancel
            </Button>

            <p className="text-[#FF0000]">{deleteDocumentMutator.error?.response?.data.message}</p>
          </div>
        }
      >
        <Alert
          theme="warning"
          title="Confirm removal"
          message={`Are you sure you want to remove "${currentResource?.title}"? This action cannot be undone.`}
        />
      </StyledDialog>

      {/* Error Dialog for Download */}
      <StyledDialog
        open={showErrorDialog}
        onClose={() => {
         setShowErrorDialog(false);
        }}
        title="Error Downloading"    
        footer = {
          <div className="flex gap-xs">
            <Button type="button" onClick={ () => { setShowErrorDialog(false); } } > Ok </Button>
          </div>
        }>
          <Alert theme="warning" message={documentDownloadError?.message}/>          
      </StyledDialog>
        
    </>
  );
};
