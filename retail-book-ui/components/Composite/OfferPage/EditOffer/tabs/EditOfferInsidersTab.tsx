import {FunctionComponent, useMemo, useState} from "react";
import {
    EInsiderEvent,
    EInsiderStatus,
    TInsider,
    TOffer
} from "types";
import {Tab} from "@headlessui/react";
import {Container} from "components/Layout/Container";
import {Column, StyledReactDataGrid} from "../../../../StyledReactDataGrid/StyledReactDataGrid";
import {Badge} from "../../../../Basic/Badge/Badge";
import {
    insiderConsentThemeSelector,
    insiderEventTextSelector,
    insiderEventThemeSelector
} from "../../../../../helpers";
import {SortColumn} from "react-data-grid";
import {useQuery} from "@tanstack/react-query";
import {useSession} from "next-auth/react";
import {getOfferInsidersQuery} from "../../../../../utils/queries";
import {format, parseISO} from "date-fns";
import {EmptyAction} from "../../../../Bespoke/EmptyAction/EmptyAction";
import {Plus, Shield} from "react-feather";
import {Button} from "../../../../Basic/Button/Button";
import {EditInsiderDialog} from "../dialogs/EditInsiderDialog";
import {AddInsiderDialog} from "../dialogs/AddInsiderDialog";


interface IProps {
  offer: TOffer;
}

interface Row {
    id: string;
    name: string;
    email: string;
    event: EInsiderEvent;
    consent: EInsiderStatus;
    response_time?: string;
    inside_time?: string;
    cleansed_time?: string;
}

const columns: Column<Row>[] = [
    {
        key: "name",
        name: "Name",
        sortable: true,
        resizable: true,
    },
    {
        key: "email",
        name: "Email",
        sortable: true,
        resizable: true
    },
    {
        key: "event",
        name: "Event",
        formatter: ({ row }) => (
            <Badge theme={insiderEventThemeSelector(row.event)}>{insiderEventTextSelector(row.event)}</Badge>
        ),
        sortable: true,
        resizable: true,
    },
    {
        key: "consent",
        name: "Consent",
        formatter: ({ row }) => (
            <Badge theme={insiderConsentThemeSelector(row.consent)}>{row.consent}</Badge>
        ),
        sortable: true,
        resizable: true,
    },
    {
        key: "response_time",
        name: "Responded",
        sortable: true,
        resizable: true,
    },
    {
        key: "inside_time",
        name: "Inside",
        sortable: true,
        resizable: true,
    },
    {
        key: "cleansed_time",
        name: "Cleansed",
        sortable: true,
        resizable: true,
    },
];

export const DATETIME_LOCAL_DATE_GRID_FORMAT = "dd/MM/yyyy HH:mm:ss";

const getRowFromInsider = (insider: TInsider): Row => {
    return {
        id: insider.id,
        name: insider.user_name,
        email: insider.user_email,
        event: insider.event,
        consent: insider.status,
        response_time: insider.response_time ? format(parseISO(insider.response_time), DATETIME_LOCAL_DATE_GRID_FORMAT) : undefined,
        inside_time: insider.inside_time ? format(parseISO(insider.inside_time), DATETIME_LOCAL_DATE_GRID_FORMAT) : undefined,
        cleansed_time: insider.cleansed_time ? format(parseISO(insider.cleansed_time), DATETIME_LOCAL_DATE_GRID_FORMAT) : undefined,
    };
};

const pageSize = 20;

export const EditOfferInsidersTab: FunctionComponent<IProps> = ({ offer }) => {
    const { data: session } = useSession();

    const isInsideInformation = offer.inside_information
    const [currentPage, setCurrentPage] = useState(1);
    const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);

    const [isEditInsidersModalOpen, setEditInsidersModalOpen] = useState(false);
    const [selectedInsiderId, setSelectedInsiderId] = useState("")

    const [isAddInsiderModalOpen, setAddInsiderModalOpen] = useState(false);

    const params = useMemo(() => {
        const sort =
            sortColumns.length > 0
                ? `${
                    sortColumns[0].columnKey
                }=${sortColumns[0].direction.toLocaleLowerCase()}`
                : undefined;

        return {
            limit: pageSize,
            offset: (currentPage - 1) * pageSize,
            sort,
        };
    }, [currentPage, sortColumns]);

    const { data: insidersData } = useQuery({
            ...getOfferInsidersQuery(params, offer.id, session?.accessToken)
        }
    );

    const totalItems = useMemo(
        () => insidersData?.pagination.count ?? 0,
        [insidersData?.pagination.count]
    );

    const rows = useMemo(
        () =>
            insidersData?.data?.map((insider) =>
                getRowFromInsider(insider)
            ) ?? [],
        [insidersData]
    );

    const isEditable = (event: EInsiderEvent): boolean => {
        return event !== EInsiderEvent.WALL_CROSSING
    }

    const onEditInsidersModalState = (modalState: boolean): void => {
        setEditInsidersModalOpen(modalState);
        if (!modalState) {
            // reset selected insider when dialog closes
            setSelectedInsiderId("")
        }
    }

    return (
        <Tab.Panel>
            <Container>
                { isInsideInformation ? (
                    (<div>
                        <StyledReactDataGrid
                            rows={rows}
                            columns={columns}
                            sortColumns={sortColumns}
                            onSortColumnsChange={setSortColumns}
                            contextMenuItems={[
                                {
                                    label: "Edit",
                                    isEnabled: (row) => isEditable(row.event),
                                    onClick: (e, row) => {
                                        e.preventDefault();
                                        setSelectedInsiderId(row.id);
                                        setEditInsidersModalOpen(true);
                                    },
                                },
                            ]}
                            paginationProps={{
                                onClick: (page) => setCurrentPage(page),
                                currentPage: currentPage,
                                totalItems: totalItems,
                                pageSize: pageSize,
                            }}
                        />
                        <Button
                            className={"mt-4"}
                            type="button"
                            onClick={() => setAddInsiderModalOpen(true)}
                            icon={Plus}
                        >
                            Add Insider
                        </Button>
                    </div>)
                )
                : ( <EmptyAction
                        icon={Shield}
                        message={"This offer is not flagged as having inside information"}
                    />)
                }
            </Container>

            {/* Edit Insider Modal */}
            <EditInsiderDialog
                offer={offer}
                insiderId={selectedInsiderId}
                isModalOpen={isEditInsidersModalOpen}
                modalOpenTrigger={onEditInsidersModalState}
                insiders={insidersData?.data ?? []}
                />


            {/* Add Insider Modal */}
            <AddInsiderDialog
                offer={offer}
                isModalOpen={isAddInsiderModalOpen}
                modalOpenTrigger={setAddInsiderModalOpen}
            />

        </Tab.Panel>


  );
}
