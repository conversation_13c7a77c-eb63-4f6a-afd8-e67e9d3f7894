import { Tab } from "@headlessui/react";
import { Button } from "components/Basic/Button/Button";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Container } from "components/Layout/Container";
import { FunctionComponent } from "react";
import { FilePlus, Plus } from "react-feather";
import { toast } from "react-toastify";
import { TOffer } from "types";
import { ResourceTable } from "../ResourceTable";

interface IProps {
  offer: TOffer;
}

export const EditOfferAgreementTab: FunctionComponent<IProps> = ({ offer }) => {
  return (
    <>
      <Tab.Panel>
        <Container>
          {offer.contracts?.length ? (
            <>
              <ResourceTable
                className="mb-sm"
                pageSize={10}
                rows={offer.contracts}
                contextMenuItems={[
                  {
                    label: "Edit",
                    onClick: (e) => {
                      e.preventDefault();
                      toast.warning("Awaiting contracts API!");
                    },
                  },
                  {
                    label: "Remove",
                    onClick: (e) => {
                      e.preventDefault();
                      toast.warning("Awaiting contracts API!");
                    },
                  },
                  {
                    label: "Download",
                    onClick: async (e) => {
                      e.preventDefault();
                      toast.warning("Awaiting contracts API!");
                    },
                  },
                ]}
              />
              <Button
                type="button"
                onClick={() => toast.warning("Awaiting contracts API!")}
              >
                Add agreement
              </Button>
            </>
          ) : (
            <EmptyAction
              icon={FilePlus}
              message="You haven't added any agreements to this offer"
              action={{
                label: "Add Agreement",
                onClick: () => toast.warning("Awaiting contracts API!"),
                icon: Plus,
              }}
            />
          )}
        </Container>
      </Tab.Panel>
    </>
  );
};
