import { Tab } from "@headlessui/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Alert } from "components/Basic/Alert/Alert";
import { Button } from "components/Basic/Button/Button";
import { Prose } from "components/Basic/Prose/Prose";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Container } from "components/Layout/Container";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { useSession } from "next-auth/react";
import React, { FunctionComponent, useMemo, useState } from "react";
import { toast } from "react-toastify";
import { ApiError, EOrderStatus, TOffer, TOrder } from "types";
import {
  acceptOrderMutation,
  getInvitesQuery,
  getOrderQuery,
  rejectOrderMutation,
  getOrderMetrics,
  getDailyOrderMetrics,
} from "utils/queries";
import { OrderBreakDownStats } from "../components/OrderBreakdownStats";
import { EditOfferOrderGrid } from "../grids/EditOfferOrdersGrid";
import { Api } from "utils/api";
import { Download } from "react-feather";
import { downloadFile } from "helpers";
import { AxiosError } from "axios";
import { toApiError } from "utils/dataConversion";
import { OrderMetricCharts } from "../charts/OrderMetricCharts";

interface IProps {
  offer: TOffer;
}

export const EditOfferOrdersTab: FunctionComponent<IProps> = ({ offer }) => {
  const { data: session } = useSession();
  const qc = useQueryClient();
  const [selectedOrder, setSelectedOrder] = useState<TOrder>();
  const [isAcceptOrderModalOpen, setAcceptOrderModalOpen] = useState(false);
  const [isRejectOrderModalOpen, setRejectOrderModalOpen] = useState(false);

  // React Query Queries

  const { data: invites } = useQuery(
    getInvitesQuery(offer.id, session?.accessToken)
  );

  const { data: order, isLoading } = useQuery({
    ...getOrderQuery(offer.id, session?.accessToken),
  });

  const { data: rawOrderMetrics } = useQuery({
    ...getOrderMetrics(offer.id, session?.accessToken),
  });
  const { data: rawOrderDailyMetrics } = useQuery({
    ...getDailyOrderMetrics(offer.id, session?.accessToken),
  });

  // React Query Mutations
  const acceptOrderMutator = useMutation(
    // eslint-disable-next-line  @typescript-eslint/no-empty-function
    {...acceptOrderMutation(offer.id, qc, session?.accessToken), onError: () => {} }
  );

  const rejectOrderMutator = useMutation(
    // eslint-disable-next-line  @typescript-eslint/no-empty-function
    {...rejectOrderMutation(offer.id, qc, session?.accessToken), onError: () => {} }
  );

  const orderStateChangeText = (
    action: string,
    order?: TOrder,
    currency?: string
  ) => {
    if (!order) return "No order selected";
    return `${action} ${order.order_type} order from ${order.intermediary} with ${order.totals.applications} applications and a value of ${order.totals.notional_value} ${currency}? This cannot be undone.`;
  };

  const handleAcceptOrder = async (order?: TOrder) => {
    acceptOrderMutator.reset();

    if (order?.status === EOrderStatus.PENDING) {
      setAcceptOrderModalOpen(true);
      setSelectedOrder(order);
    } else {
      toast.warning(`Order status must be ${EOrderStatus.PENDING}`);
    }
  };

  const handleRejectOrder = async (order?: TOrder) => {
    rejectOrderMutator.reset();

    if (order?.status === EOrderStatus.PENDING) {
      setRejectOrderModalOpen(true);
      setSelectedOrder(order);
    } else {
      toast.warning(`Order status must be ${EOrderStatus.PENDING}`);
    }
  };

  const [downloadOrderError, setDownloadOrderError] = useState<ApiError | undefined>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const downloadOrderMutation = useMutation<any, AxiosError<Blob>, TOrder>({
    mutationFn: async (order?: TOrder) => {
      return await Api.downloadOfferOrder(offer.id, order?.order_book_id, session?.accessToken);
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (data: any, order: TOrder) => {
      const file = new File([data], `rb_order_${order?.id}`, {
        type: data.type,
      });
      downloadFile(file);
    },
    onError: async (blob: AxiosError<Blob>) => {
      setDownloadOrderError(toApiError(await blob.response?.data.text()));
    }
  });

  const [downloadOrderAuditError, setDownloadOrderAuditError] = useState<ApiError | undefined>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const downloadOrderAuditMutation = useMutation<any, AxiosError<Blob>, TOrder>({
    mutationFn: async (order?: TOrder) => {
      return await Api.downloadOrderAudit(offer.id, order?.id, session?.accessToken);
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (data: any, order: TOrder) => {
      const file = new File([data], `rb_order_audit_${order?.id}`, {
        type: data.type,
      });
      downloadFile(file);
    },
    onError: async (blob: AxiosError<Blob>) => {
      setDownloadOrderAuditError(toApiError(await blob.response?.data.text()));
    }
  });

  const [downloadAllOrdersError, setDownloadAllOrdersError] = useState<ApiError | undefined>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const downloadAllOrdersMutation = useMutation<any, AxiosError<Blob>>({
    mutationFn: async () => {
      return await Api.downloadOfferOrders(offer.id, session?.accessToken);
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (data: any) => {
      const file = new File([data], `rb_orders_${offer.id}`, {
        type: data.type,
      });
      downloadFile(file);
    },
    onError: async (blob: AxiosError<Blob>) => {
      setDownloadAllOrdersError(toApiError(await blob.response?.data.text()));
    }
  });

  const [downloadAllAuditError, setDownloadAllAuditError] = useState<ApiError | undefined>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const downloadAllAuditMutation = useMutation<any, AxiosError<Blob>>({
    mutationFn: async () => {
      return await Api.downloadFullOrderAudit(offer.id, session?.accessToken);
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (data: any) => {
      const file = new File([data], `rb_audit_${offer.id}`, {
        type: data.type,
      });
      downloadFile(file);
    },
    onError: async (blob: AxiosError<Blob>) => {
      setDownloadAllAuditError(toApiError(await blob.response?.data.text()));
    }
  });


  const termsSigned = useMemo(() => {
    const invite = invites?.find(i => i.intermediary_display_name === selectedOrder?.intermediary);
    return invite?.terms?.accepted ?? false
  }, [selectedOrder, invites])

  
  const resetDownloadMutations = () => {
    downloadOrderMutation.reset();
    downloadOrderAuditMutation.reset();
    downloadAllAuditMutation.reset();
    downloadAllOrdersMutation.reset();
  }

  const downloadError = useMemo(() => {
    if (downloadOrderError) return downloadOrderError;
    if (downloadOrderAuditError) return downloadOrderAuditError;
    if (downloadAllOrdersError) return downloadAllOrdersError;
    if (downloadAllAuditError) return downloadAllAuditError;
    return undefined
  }, [downloadOrderError, downloadOrderAuditError, downloadAllOrdersError, downloadAllAuditError])


  if (isLoading) {
    return (
      <Tab.Panel>
      <div className="grow flex items-center justify-center">
        <Spinner message="Loading orders..." />
      </div>
      </Tab.Panel>
    );
  }
  
  return (
    <>
      <Tab.Panel>
        <Container as="div" className="flex flex-row flex-auto justify-center pb-5 pl-0">
          <OrderBreakDownStats currency={offer.currency} totals={order?.totals} stats={order?.breakdown} />
        </Container>
        <Container as="div" className="pb-sm space-x-1">
          <Button
            size="sm"
            type="button"
            onClick={() => downloadAllOrdersMutation.mutate()}
            icon={Download}
          >
            Download All Orders
          </Button>
          <Button size="sm" type="button" onClick={() => downloadAllAuditMutation.mutate()} icon={Download}>Download Audit for All Orders</Button>
        </Container>
        <Container>
          {(order?.orders ?? []).length ? (
            <EditOfferOrderGrid
              data={order?.orders}
              currency={offer.currency}
              offer={offer}
              token={session?.accessToken}
              onAccept={handleAcceptOrder}
              onReject={handleRejectOrder}
              onDownload={(order?: TOrder) => order && order?.order_book_id && downloadOrderMutation.mutate(order)}
              onDownloadAudit={(order?: TOrder) => order && downloadOrderAuditMutation.mutate(order)}
            />)
            : (
              <EmptyAction message="No current orders for this offer" />
            )}
        </Container>       
        <OrderMetricCharts dailyMetrics={rawOrderDailyMetrics} metrics={rawOrderMetrics} />
      </Tab.Panel>

      {/* Download error dialog */}
      <StyledDialog
        open={downloadOrderMutation.error !== null || downloadOrderAuditMutation.error !== null || downloadAllAuditMutation.error !== null || downloadAllOrdersMutation.error !== null}
        onClose={resetDownloadMutations}

        title="Downloading Error"
        footer={
          <Button type="button" onClick={resetDownloadMutations}>Ok</Button>
        }
      >
        <Alert theme="warning" message={downloadError?.message} />
      </StyledDialog>

      {/* Accept Order Modal */}
      <StyledDialog
        open={isAcceptOrderModalOpen}
        onClose={() => setAcceptOrderModalOpen(false)}
        title="
        "
        footer={
          <div className="flex gap-sm">
            <Button
              type="button"              
              disabled={!selectedOrder || !termsSigned}
              onClick={async (e) => {
                e.preventDefault();
                if (!selectedOrder) return;
                acceptOrderMutator.mutate(
                  selectedOrder.id, { onSuccess: () => { setAcceptOrderModalOpen(false) } }
                )
              }}
            >
              Accept
            </Button>
            <Button
              theme="outline"
              type="button"
              onClick={() => setAcceptOrderModalOpen(false)}
            >
              Cancel
            </Button>
                      
            {acceptOrderMutator.isError && (<Prose className="text-[#FF0000]">{(acceptOrderMutator.error as ApiError).message ?? "Unknown error"}</Prose>)}            
          </div>
        }
      >
        <Alert
          theme="warning"
          title="Accept order"
          message={orderStateChangeText("Accept", selectedOrder, offer.currency)}
        />

        {
          !termsSigned && <Alert theme="failure" message="Intermediary has not accepted the terms - order cannot be accepted." />
        }
      </StyledDialog>

      {/* Reject Order Modal */}
      <StyledDialog
        open={isRejectOrderModalOpen}
        onClose={() => setRejectOrderModalOpen(false)}
        title="Reject order"
        footer={
          <div className="flex gap-sm">
            <Button
              type="button"              
              disabled={!selectedOrder}
              onClick={async (e) => {
                e.preventDefault();
                if (!selectedOrder?.id) return;
                rejectOrderMutator.mutate(selectedOrder?.id,{ onSuccess: () => { setRejectOrderModalOpen(false) } })
              }}
            >
              Reject
            </Button>
            <Button
              theme="outline"
              type="button"
              onClick={() => setRejectOrderModalOpen(false)}
            >
              Cancel
            </Button>

            {rejectOrderMutator.isError && (<Prose className="text-[#FF0000]">{(rejectOrderMutator.error as ApiError).message ?? "Unknown error"}</Prose>)}            
          </div>
        }
      >
        <Alert
          theme="warning"
          title="Reject order"
          message={orderStateChangeText("Reject", selectedOrder, offer.currency)}
        />


      </StyledDialog>
    </>
  );
};
