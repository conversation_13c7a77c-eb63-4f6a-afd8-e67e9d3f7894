import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Badge } from "components/Basic/Badge/Badge";
import { Button } from "components/Basic/Button/Button";
import { Prose } from "components/Basic/Prose/Prose";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Container } from "components/Layout/Container";
import {
  Column,
  StyledReactDataGrid,
} from "components/StyledReactDataGrid/StyledReactDataGrid";
import { useSession } from "next-auth/react";
import { FunctionComponent, useCallback, useMemo, useState } from "react";
import { Plus, Send, User } from "react-feather";
import { IntermediaryResponse, InviteResponse, TOffer } from "types";
import {
  getInvitesQuery,
  getStaticIntermediariesQuery,
  sendInviteMutation,
} from "utils/queries";
import { NotifyIntermediariesDialog } from "../dialogs/NotifyIntermediariesDialog";
import { TermsAcceptance } from "../components/TermsAcceptance";
import { ChooseIntermediariesDialog } from "../dialogs/ChooseIntermediariesDialog"

interface IntermediaryRow {
  id: string;
  date?: Date;
  sendDate?: Date;
  name: string;
  status: string;
  acceptedTime?: Date;
  acceptedBy: string;
  acceptedByEmail: string;
  termsAccepted: boolean;
  receiveCommission: boolean;
}

export const GridDateCompare = (a?:Date,b?:Date) => {
  if (a === undefined && b === undefined) {
    return 0;
  }
  if (a === undefined && b !== undefined) {
    return 1;
  }
  if (b === undefined && a !== undefined) {
    return -1;
  }

  const lhs = a ?? new Date(); // Both a&b must be defined; but we add this
  const rhs = b ?? new Date(); //  otherwise typescript thinks it might be undefined

  if (lhs > rhs) {
    return 1;
  }
  if (lhs < rhs) {
    return -1;
  }
  return 0;
}

const intermediaryColumns: Column<IntermediaryRow>[] = [
 
  {
    key: "name",
    name: "Name",
    sortable: true,
    comparator: (a, b) => a.name.localeCompare(b.name),
   
  },
  {
    key:"termsAccepted",
    name: "Terms Accepted",
    sortable: false,
    
    formatter: ({ row }) => ( <TermsAcceptance accepted={row.termsAccepted} acceptedTime={row.acceptedTime}/> )      
  },
  {
    key:"acceptedBy",
    name: "Accepted By",
    sortable: false,
    formatter: ({ row }) => ( <a href={"mailto:" + row.acceptedByEmail}>{row.acceptedBy}</a> )      
  },
  {
    key: "receiveCommission",
    name: "Commission",
    sortable: false,
    
    formatter: ({row}) => (
      row.termsAccepted && (row.receiveCommission ? <Badge theme="success">Commission</Badge> : <Badge theme="red-orange">No Commission</Badge>)
    )
  },
  {
    key:"status",
    name: "Status",
    sortable: false,
    formatter: ({row}) => (
      <Badge theme={ row.status === "Draft" ? "warning-strong" : "info"}>{row.status === "Draft" ? "Draft" : "Sent"}</Badge>
    )
  },
  {
    key: "sendDate",
    name: "Invite Sent",
    sortable: true,
    comparator: (a,b) => {
      return GridDateCompare(a.sendDate, b.sendDate)
    },
    formatter: ({row}) => (<p> {row.sendDate?.toLocaleDateString() ?? ""} {row.sendDate?.toLocaleTimeString() ?? ""}</p>)  
  }
];

const InvitedIntermediariesTable = ({
  intermediaries,
}: {
  intermediaries: InviteResponse[];
}) => {
  const rows = useMemo(
    () =>
      intermediaries.map((intermediary) => {
        return {
          id: intermediary.intermediary_system_id,
          date: intermediary.time_of_invite ? new Date(intermediary.time_of_invite) : undefined,
          sendDate: intermediary.time_of_sending ? new Date(intermediary.time_of_sending) : undefined,
          name: intermediary.intermediary_display_name,
          termsAccepted: intermediary.terms?.accepted ?? false,          
          acceptedBy: intermediary.terms?.accepted_by ?? "",      
          acceptedByEmail: intermediary.terms?.accepted_by_email ?? "",      
          receiveCommission: intermediary.terms?.receive_commission ?? false,
          acceptedTime: intermediary.terms?.accepted_time ? new Date(intermediary.terms.accepted_time) : undefined,
          status: intermediary.status ?? "",
        };
      }),
    [intermediaries]
  );

  return (
    <StyledReactDataGrid
      className="mb-sm"
      rows={rows}
      columns={intermediaryColumns}
      contextMenuItems={[]}
    />
  );
};

interface IProps {
  offer: TOffer;
  description?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonlyFields: any;
}

export const EditOfferIntermediaryAccessForm: FunctionComponent<IProps> = ({
  offer,
  description = "Intermediaries will have the ability to view the offer and add orders.",
  readonlyFields,
}) => {
  const { data: session } = useSession();
  const qc = useQueryClient();
  
  const [isInviteIntermediaryModalOpen, setInviteIntermediaryModalOpen] =
    useState(false);
  const { data: invitesQueryResponse } = useQuery(
    getInvitesQuery(offer.id, session?.accessToken)
  );
  const { data: intermediaryOptions } = useQuery({
    ...getStaticIntermediariesQuery(session?.accessToken),
    select: useCallback(
      (data: IntermediaryResponse) =>
        data.map((intermediary) => ({
          id: intermediary.system_id,
          label: intermediary.display_name,
          value: intermediary,
        })),
      []
    ),
  });

  const [isNotifyUsersOpen, setNotifyUsersOpen] = useState(false);
  
  const sendIntermediaryInviteMutator = useMutation(
    sendInviteMutation(offer.id, qc, session?.accessToken)
  );

  const accessDisabled = useMemo( () => {
    return readonlyFields?.intermediary_access ?? false
  },[readonlyFields]);

  return (
    <>
      <Container>
        <Prose className="mb-md">
          <h2>Intermediaries</h2>
          {description && <p>{description}</p>}
        </Prose>
        {invitesQueryResponse && invitesQueryResponse.length > 0 ? (
          <>
            <InvitedIntermediariesTable
              intermediaries={invitesQueryResponse}
            />
            <div className="flex gap-sm">
              <Button
                loading={sendIntermediaryInviteMutator.isLoading}
                disabled={
                  accessDisabled ||
                  sendIntermediaryInviteMutator.isLoading ||
                  !intermediaryOptions ||
                  !intermediaryOptions.length
                }
                type="button"
                onClick={() => setInviteIntermediaryModalOpen(true)}
                icon={Plus}
              >
                Invite intermediaries
              </Button>
              <Button type="button" onClick={() => setNotifyUsersOpen(true)} icon={Send} disabled={accessDisabled}>
                Notify intermediaries of changes
              </Button>
            </div>
          </>
        ) : (
          <EmptyAction
            icon={User}
            message="You haven't invited any intermediaries to this offer"
            action={{
              label: "Invite Intermediaries",
              onClick: () => setInviteIntermediaryModalOpen(true),
              disabled: accessDisabled,
              icon: Plus,
            }}
          />
        )}
      </Container>

      {/*Notify Intermediary Modal */}
      <NotifyIntermediariesDialog  
        open={isNotifyUsersOpen} 
        setOpen={setNotifyUsersOpen} 
        accessToken={session?.accessToken}
        invitedIntermediaries={invitesQueryResponse}
        offer={offer}
      />
      {/* Invite Intermediary Modal */}
      <ChooseIntermediariesDialog
        description={description}
        open={isInviteIntermediaryModalOpen}
        isLoading={sendIntermediaryInviteMutator.isLoading}
        invites={invitesQueryResponse??[]}
        onInvite={(invites) => sendIntermediaryInviteMutator.mutateAsync(invites)}
        onClose={() => setInviteIntermediaryModalOpen(false)}
      />
    </>
  );
};
