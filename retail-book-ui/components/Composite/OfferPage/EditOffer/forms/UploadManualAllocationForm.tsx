import { Alert } from "components/Basic/Alert/Alert";
import { Button } from "components/Basic/Button/Button";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { DropUpload } from "components/Bespoke/DropUpload/DropUpload";
import { AllocationLineItemGrid } from "components/Grids/Allocation/AllocationLineItemGrid";
import { useState } from "react";
import { useSession } from "next-auth/react";
import { Download } from "react-feather";
import { FailedAllocationAndOfferBook } from "../dialogs/EditAllocationDialog";
import { X } from "react-feather";
import { NumericFormat } from "react-number-format";
import { Input } from "components/Basic/Input/Input";
import Decimal from "decimal.js";

interface UploadManualAllocationFormProps {
    onDrop: (files: File[], offerPrice?: Decimal) => void;
    onForceUpload: () => void;
    onDownloadTemplate: (offerPrice?: Decimal) => void;
    onDownloadTemplateError?: string;
    onDownloadAnnotated: (bookId: string) => void;
    isUploading?: boolean;
    isDownloading?: boolean;
    errorMessage?: string;
    step?: number,
    setStep: (n: number) => void;
    failedAllocationOfferBook?: FailedAllocationAndOfferBook;
    loadingProgressText: string|undefined;
    offerPrice?: Decimal;
}

export const UploadManualAllocationForm = ({
    onDrop,
    onForceUpload,
    onDownloadTemplate,
    onDownloadTemplateError,
    onDownloadAnnotated,
    isUploading,
    isDownloading,
    step,
    setStep,
    errorMessage,
    failedAllocationOfferBook,
    loadingProgressText,
    offerPrice
}: UploadManualAllocationFormProps) => {
    const { data: session } = useSession();
    const [uploadPrice, setUploadPrice] = useState(offerPrice);
    return (
        <>
            {isUploading ? (
                <div className="flex items-center justify-center">
                    <Spinner message={ loadingProgressText ? loadingProgressText : "Please wait while your file is uploaded..." } />
                </div>
            ) : (
                <form className="form">

                    {/* Step 1 */}
                    {step === 1 && (
                        <section className="form-section">
                            <legend className="form-legend">Spreadsheet Upload</legend>
                            {errorMessage && <Alert theme="failure" message={errorMessage} />}
                            <div>
                                <NumericFormat
                                    label="Offer Price"
                                    value={uploadPrice?.toNumber()}
                                    valueIsNumericString
                                    thousandSeparator
                                    customInput={Input}
                                    decimalScale={12}
                                    allowNegative={false}
                                    onValueChange={({ floatValue }) => {
                                        if (floatValue) {
                                            setUploadPrice( new Decimal(floatValue));
                                        } else {
                                            setUploadPrice(undefined);
                                        }
                                    }}
                                    readOnly={false}
                                />
                            </div>
                            <div className="flex space-between gap-md mb-md">
                                <Button
                                    type="button"
                                    icon={Download}
                                    disabled={!uploadPrice || isDownloading}
                                    loading={isDownloading}
                                    onClick={() => onDownloadTemplate(uploadPrice)}
                                >
                                    Download template
                                </Button>
                                {
                                    onDownloadTemplateError && <Alert theme="failure" message={onDownloadTemplateError}/>
                                }
                            </div>

                            <DropUpload maxFiles={1} onDrop={(files) => {onDrop(files, uploadPrice)}} />
                        </section>
                    )}


                    {/* Step (-)2 - Review the allocation errors (only applicable if present) */}
                    {step === -2 && (
                        <section className="form-section">
                            <legend className="form-legend">Review Allocation</legend>
                            {
                                errorMessage && <Alert theme="failure" message={errorMessage} />
                            }
                            <div className="overflow-auto">
                                <AllocationLineItemGrid 
                                    allocationBookId={failedAllocationOfferBook?.allocationBookId} 
                                    offerId={failedAllocationOfferBook?.offerId} 
                                    showErrors={true} 
                                    token={session?.accessToken} 
                                /></div>
                            <div className="form-actions">                                
                                <Button type="button" onClick={() => {
                                    setStep(1);
                                }}>Try again</Button>                                
                                <Button type="button" theme="outline" icon={Download} disabled={isDownloading} onClick={() => onDownloadAnnotated(failedAllocationOfferBook?.allocationBookId ?? "")}>Download Sheet</Button>
                                <Button type="button" theme="special" icon={X} onClick={() => {
                                    setStep(2);
                                }}>Ignore and upload</Button>
                            </div>
                        </section>
                    )}

                     {/* Step 2 - Are you sure you want to bypass validation? */}
                     {step === 2 && (
                        <section className="form-section">
                            {
                                <Alert theme="warning" message="Are you sure? One or more allocations have failures and this will replace any existing allocations." />
                            }
                            <div className="form-actions">
                                <Button type="button" onClick={() => {
                                    onForceUpload();
                                }}>Yes</Button>
                                <Button type="button" theme="outline" onClick={() => {
                                    setStep(-2);
                                }}>Go back</Button>
                            </div>
                        </section>
                    )}
                </form>
            )}
        </>
    )
}

