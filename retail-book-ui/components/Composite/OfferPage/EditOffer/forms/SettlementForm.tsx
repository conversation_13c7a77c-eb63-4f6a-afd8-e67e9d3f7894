import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { MarkdownEditor } from "components/Bespoke/MarkdownEditor/MarkdownEditor";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo } from "react";
import { Controller, FieldErrors, FieldNamesMarkedBoolean, useForm } from "react-hook-form";
import { Clearable, TOffer } from "types";
import { setOrClearIfDirty } from "../../model/OfferFieldModel";
import { Textarea } from "components/Basic/Textarea/Textarea";
import { format, parseISO } from "date-fns";
import { DATETIME_LOCAL_DATE_TIME_FORMAT, DATETIME_LOCAL_DATE_ONLY_FORMAT } from "../../../../../helpers";

const schema = {
  type: "object",
  properties: {},
  required: [],
};

interface SettlementFormData {
  ticker: string;
  isin: string;
  sedol: string;
  crest_id: string;
  security_name: string;
  settlement_details: string;
  settlement_date: string;
}

const formatDate = (input: string): string => {
  if (input == "") {
    return "";
  }
  return format(parseISO(input), DATETIME_LOCAL_DATE_TIME_FORMAT)
}

const getPartialOfferFromSettlementFormData = (
  formData: SettlementFormData,
  dirtyFields: Partial<Readonly<FieldNamesMarkedBoolean<SettlementFormData>>>
): Clearable<Partial<TOffer>> => {
  return {
    security_name: setOrClearIfDirty(dirtyFields.security_name, formData.security_name),
    ticker: setOrClearIfDirty(dirtyFields.ticker, formData.ticker),
    isin: setOrClearIfDirty(dirtyFields.isin, formData.isin),
    sedol: setOrClearIfDirty(dirtyFields.sedol, formData.sedol),
    crest_id: setOrClearIfDirty(dirtyFields.crest_id, formData.crest_id),
    settlement_details: setOrClearIfDirty(dirtyFields.settlement_details, formData.settlement_details),
    settlement_date: setOrClearIfDirty(dirtyFields.settlement_date, formatDate(formData.settlement_date)),
  };
};

const getSettlementFormDataFromPartialOffer = (
  offer: Partial<TOffer>
): SettlementFormData => ({
  security_name: offer.security_name ?? "",
  ticker: offer.ticker ?? "",
  isin: offer.isin ?? "",
  sedol: offer.sedol ?? "",
  crest_id: offer.crest_id ?? "",
  settlement_details: offer.settlement_details ?? "",
  settlement_date: offer.settlement_date ? format(parseISO(offer.settlement_date), DATETIME_LOCAL_DATE_ONLY_FORMAT) : "",
});

interface SettlementFormProps {
  offer: Partial<TOffer>;
  onSubmit: (data: Clearable<Partial<TOffer>>) => void;
  fieldErrors: FieldErrors<SettlementFormData>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonlyFields: any;
}

export const SettlementForm = ({
  offer,
  onSubmit,
  fieldErrors,
  readonlyFields,
}: SettlementFormProps) => {
  const defaultValues: SettlementFormData = useMemo(
    () => getSettlementFormDataFromPartialOffer(offer),
    [offer]
  );

  const { register, handleSubmit, formState, reset, setError, control, clearErrors } =
    useForm<SettlementFormData>({
      defaultValues,
      resolver: ajvResolver(schema),
    });

  const { errors, isDirty, isSubmitting, dirtyFields } = formState;

  useReactHookFormServerErrors<SettlementFormData>(fieldErrors, errors, setError, clearErrors);

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const isReadonly = (field: string) => {
    if ( readonlyFields ) {
      return readonlyFields[field] ?? false
    }
    return false
  }

  return (
    <form
      className="form"
      onSubmit={handleSubmit((formData) => 
        onSubmit(getPartialOfferFromSettlementFormData(formData, dirtyFields))
      )}
    >
      <section className="form-section">
        <fieldset className="form-fieldset">
          <Input
            label="Ticker"
            {...register("ticker")}
            error={errors.ticker?.message}
            readOnly={isReadonly("ticker")}
          />
          <Input
            label="ISIN"
            {...register("isin")}
            error={errors.isin?.message}
            readOnly={isReadonly("isin")}
          />
          <Input
            label="SEDOL"
            {...register("sedol")}
            error={errors.sedol?.message}
            readOnly={isReadonly("sedol")}
          />
          <Input
            label="Crest ID"
            {...register("crest_id")}
            error={errors.crest_id?.message}
            readOnly={isReadonly("crest_id")}
          />
          <Input
            label="Security Name"
            {...register("security_name")}
            error={errors.security_name?.message}
            readOnly={isReadonly("security_name")}
          />
          <Input
            {...register("settlement_date")}
            label="Settlement Date"
            type="date"
            readOnly={isReadonly("settlement_date")}
            onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
            onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
            max="9999-12-31T23:59"
            error={errors?.settlement_date?.message}
          />
        </fieldset>
      </section>

      <section className="form-section">
      { isReadonly("settlement_details") ? 
              <Textarea {...register("settlement_details")} label="Settlement Details" rows={10} readOnly={true} /> :

        <Controller
          name="settlement_details"
          control={control}
          render={({
            field: { onChange, value, name },
            fieldState: { error },
          }) => (
            <MarkdownEditor
              label="Settlement Details"
              id={name}
              value={value}
              onChange={(value) => onChange(value)}
              error={error?.message}
              disabled={isReadonly("settlement_details")}
            />
          )}
        />
          }
      </section>

      <div className="form-actions">
        <Button loading={isSubmitting} disabled={!isDirty || isSubmitting}>
          Save
        </Button>
        <Button
          type="button"
          theme="outline"
          onClick={() => reset(defaultValues)}
        >
          Cancel
        </Button>
      </div>
    </form>
  );
};
