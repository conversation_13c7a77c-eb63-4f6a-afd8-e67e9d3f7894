import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import { Input } from "components/Basic/Input/Input";
import { Select } from "components/Basic/Select/Select";
import { Textarea } from "components/Basic/Textarea/Textarea";
import { MarkdownEditor } from "components/Bespoke/MarkdownEditor/MarkdownEditor";
import { format, parseISO } from "date-fns";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo } from "react";
import { Controller, FieldErrors, FieldNamesMarkedBoolean, useForm } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { Clearable, EOfferType, TOffer } from "types";
import { setOrClearIfDirty, setOrClearIfDirtyDate, setOrClearIfDirtyDecimal } from "../../model/OfferFieldModel";
import { Alert } from "components/Basic/Alert/Alert";
import { DATETIME_LOCAL_DATE_FNS_FORMAT } from "../../../../../helpers";


const schema = {
  type: "object",
  properties: {
    name: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "Required" },
    }
  },
  required: [
    "name",    
  ],
};

export interface OverviewFormData {
  name: string;
  type: EOfferType;
  issued_by: string;
  offer_price: number | null;
  min_order_amount: number | null;
  raise_amount: number | null;
  registration_date: string;
  open_date: string;
  close_date: string;
  description: string;
  details: string;
  allocation_principles: string;
  shareholders_only: boolean | null;
  inside_information: boolean | null;
  currency: string;
  price_range_low: number | null;
  price_range_high: number | null;
  inside_time?: string;
}



const getPartialOfferFromOverviewFormData = (
  formData: OverviewFormData,
  dirtyFields: Partial<Readonly<FieldNamesMarkedBoolean<OverviewFormData>>>
): Clearable<Partial<TOffer>> => {
  return {
    name: setOrClearIfDirty(dirtyFields.name,formData.name),
    type: setOrClearIfDirty(dirtyFields.type,formData.type),
    issued_by: setOrClearIfDirty(dirtyFields.issued_by,formData.issued_by),
    offer_price: setOrClearIfDirtyDecimal(dirtyFields.offer_price, formData.offer_price),      
    min_order_amount: setOrClearIfDirtyDecimal(dirtyFields.min_order_amount, formData.min_order_amount),      
    raise_amount: setOrClearIfDirtyDecimal(dirtyFields.raise_amount, formData.raise_amount),      
    registration_date: setOrClearIfDirtyDate(dirtyFields.registration_date, formData.registration_date),     
    open_date: setOrClearIfDirtyDate(dirtyFields.open_date, formData.open_date),      
    close_date: setOrClearIfDirtyDate(dirtyFields.close_date, formData.close_date),      
    description: setOrClearIfDirty(dirtyFields.description, formData.description),
    details: setOrClearIfDirty(dirtyFields.details, formData.details),
    allocation_principles: setOrClearIfDirty(dirtyFields.allocation_principles, formData.allocation_principles),
    shareholders_only: setOrClearIfDirty(dirtyFields.shareholders_only, formData.shareholders_only),
    inside_information: setOrClearIfDirty(dirtyFields.inside_information, formData.inside_information),
    currency: setOrClearIfDirty(dirtyFields.currency, formData.currency),
    price_range_low: setOrClearIfDirtyDecimal(dirtyFields.price_range_low, formData.price_range_low),
    price_range_high: setOrClearIfDirtyDecimal(dirtyFields.price_range_high, formData.price_range_high),
  };
};

const getOverviewFormDataFromPartialOffer = (
  offer: Partial<TOffer>
): OverviewFormData => ({
  name: offer.name ?? "",
  type: offer.type ?? EOfferType.IPO,
  issued_by: offer.issued_by ?? "",
  offer_price: offer.offer_price ? offer.offer_price.toNumber() : null,
  min_order_amount: offer.min_order_amount
    ? offer.min_order_amount.toNumber()
    : null,
  raise_amount: offer.raise_amount ? offer.raise_amount.toNumber() : null,
  registration_date: offer.registration_date
    ? format(parseISO(offer.registration_date), DATETIME_LOCAL_DATE_FNS_FORMAT)
    : "",
  open_date: offer.open_date
    ? format(parseISO(offer.open_date), DATETIME_LOCAL_DATE_FNS_FORMAT)
    : "",
  close_date: offer.close_date
    ? format(parseISO(offer.close_date), DATETIME_LOCAL_DATE_FNS_FORMAT)
    : "",
  description: offer.description ?? "",
  details: offer.details ?? "",
  allocation_principles: offer.allocation_principles ?? "",
  shareholders_only: offer.shareholders_only ?? null,
  inside_information: offer.inside_information ?? null,
  currency: offer.currency ?? "",
  price_range_low: offer.price_range_low ? offer.price_range_low.toNumber() : null,
  price_range_high: offer.price_range_high ? offer.price_range_high.toNumber() : null,
});

interface OverviewFormProps {
  offer: Partial<TOffer>;  
  onSubmit: (data: Clearable<Partial<TOffer>>) => void;
  onCancel: () => void;
  fieldErrors: FieldErrors<OverviewFormData>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonlyFields: any;
  currencies?: string[];
  errorMessage?: string;
}

export const OverviewForm = ({
  offer,
  currencies,
  onSubmit,
  onCancel,
  fieldErrors,
  readonlyFields,
  errorMessage,
}: OverviewFormProps) => {
  const hasWallCrossInfo = offer.wall_cross_info_id != null && offer.wall_cross_info_id != ""

  const defaultValues: OverviewFormData = useMemo(
    () => getOverviewFormDataFromPartialOffer(offer),
    [offer]
  );


  const { register, handleSubmit, formState, reset, setError, control, getValues, clearErrors } =
    useForm<OverviewFormData>({
      defaultValues,
      resolver: ajvResolver(schema),
    });

  const { errors, isDirty, isSubmitting, dirtyFields } = formState;

  useReactHookFormServerErrors<OverviewFormData>(fieldErrors, errors, setError, clearErrors);

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const isReadonly = (field: string) => {
    if ( readonlyFields ) {
      return readonlyFields[field] ?? false
    }
    return true
  }

  const isReadonlyInsideInfo = () => {
    // logic to prevent you from flagging an offer as public (unchecking the inside info box) after a wall-cross has been created
    return isReadonly("inside_information") || (getValues("inside_information") === true && hasWallCrossInfo)
  }

  return (
    <form
      className="form"
      onSubmit={handleSubmit((formData) => {
        onSubmit( getPartialOfferFromOverviewFormData(formData, dirtyFields))
      }
      )}
    >
      <section className="form-section">
        <fieldset className="form-fieldset">

          <Input
            {...register("name")}
            label="Offer Name"
            error={errors.name?.message}
            readOnly={isReadonly("name")}
          />
          <Select
              {...register("type")}
              label="Type"
              disabled={isReadonly("type")}>
            {Object.entries(EOfferType).map(([key, value]) => (
              <option key={key}>{value}</option>
            ))}
          </Select>

          <Checkbox
              {...register("inside_information")}
              className={isReadonlyInsideInfo() ? "text-grey-step-1" : "font-bold"}
              label="Inside Information"
              disabled={isReadonlyInsideInfo()} />
          <div/>
          <Checkbox
              {...register("shareholders_only")}
              className={isReadonly("shareholders_only") ? "pb-8 text-grey-step-1" : "pb-8 font-bold"}
              label="Shareholders Only"
              disabled={isReadonly("shareholders_only")} />
          <div/>

          <Input
            {...register("issued_by")}
            label="Issuer"
            error={errors.issued_by?.message}
            readOnly={isReadonly("issued_by")}
          />
          <Select
            {...register("currency")}
              label="Currency"
              error={errors.currency?.message}
              disabled={isReadonly("currency")}>
              { (currencies??[offer.currency]).map( x => <option key={x}>{x}</option> ) }

          </Select>
          
          <div className="flex">
            <Controller
              name="price_range_low"
              control={control}
              render={({
                field: { onChange, value, ref },
                fieldState: { error },
              }) => (
                <NumericFormat
                  label="Price Range Low"
                  getInputRef={ref}
                  value={value}
                  valueIsNumericString
                  thousandSeparator
                  customInput={Input}
                  decimalScale={12}
                  allowNegative={false}
                  onValueChange={({ floatValue }) => {
                    onChange(floatValue ?? null);
                  }}
                  error={error?.message}
                  readOnly={isReadonly("price_range_low")}
                />
              )}
            />
            <span className="pr-sm"></span>
            <Controller
              name="price_range_high"
              control={control}
              render={({
                field: { onChange, value, ref },
                fieldState: { error },
              }) => (
                <NumericFormat
                  label="Price Range High"
                  getInputRef={ref}
                  value={value}
                  valueIsNumericString
                  thousandSeparator
                  customInput={Input}
                  decimalScale={12}
                  allowNegative={false}
                  onValueChange={({ floatValue }) => {
                    onChange(floatValue ?? null);
                  }}
                  error={error?.message}
                  readOnly={isReadonly("price_range_high")}
                />
              )}
            />
          </div>

          <Controller
            name="offer_price"
            control={control}
            render={({
              field: { onChange, value, ref },
              fieldState: { error },
            }) => (
              <NumericFormat
                label="Offer Price"
                getInputRef={ref}
                value={value}
                valueIsNumericString
                thousandSeparator
                customInput={Input}
                decimalScale={12}
                allowNegative={false}
                onValueChange={({ floatValue }) => {
                  onChange(floatValue ?? null);
                }}
                error={error?.message}
                readOnly={isReadonly("offer_price")}
              />
            )}
          />

          <Controller
            name="min_order_amount"
            control={control}
            render={({
              field: { onChange, value, ref },
              fieldState: { error },
            }) => (
              <NumericFormat
                label="Minimum Order"
                getInputRef={ref}
                value={value}
                valueIsNumericString
                thousandSeparator
                customInput={Input}
                decimalScale={0}
                allowNegative={false}
                onValueChange={({ floatValue }) => {
                  onChange(floatValue ?? null);
                }}
                error={error?.message}
                readOnly={isReadonly("min_order_amount")}
              />
            )}
          />

          <Controller
            name="raise_amount"
            control={control}
            render={({
              field: { onChange, value, ref },
              fieldState: { error },
            }) => (
              <NumericFormat
                label="Raise Amount"
                getInputRef={ref}
                value={value}
                valueIsNumericString
                thousandSeparator
                customInput={Input}
                decimalScale={0}
                allowNegative={false}
                onValueChange={({ floatValue }) => {
                  onChange(floatValue ?? null);
                }}
                error={error?.message}
                readOnly={isReadonly("raise_amount")}
              />
            )}
          />

          <Input
            {...register("registration_date")}
            label="Registration Date"
            type="datetime-local"
            readOnly={isReadonly("registration_date")}
            onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
            onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
            max="9999-12-31T23:59"
            error={errors?.registration_date?.message}
          />
          <Input
            {...register("open_date")}
            label="Open Date"
            type="datetime-local"
            readOnly={isReadonly("open_date")}
            onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
            onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
            max="9999-12-31T23:59"
            error={errors?.open_date?.message}
          />
          <Input
            {...register("close_date")}
            label="Close Date"
            type="datetime-local"
            readOnly={isReadonly("close_date")}
            onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
            onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
            max="9999-12-31T23:59"
            error={errors?.close_date?.message}
          />
          <div/>


        </fieldset>
      </section>

      <section className="form-section">
        <Textarea
          {...register("description")}
          label="Short Description"
          rows={10}
          error={errors.description?.message}
          readOnly={isReadonly("description")}
        />
      </section>

      <section className="form-section">
      { isReadonly("details") ? 
              <Textarea {...register("details")} label="Offer Details" rows={10} readOnly={true} /> :
            
        <Controller
          name="details"
          control={control}
          render={({
            field: { onChange, value, name },
            fieldState: { error },
          }) => (
            
            <MarkdownEditor
              id={name}
              label="Offer Details"
              value={value}
              onChange={(value) => onChange(value)}
              error={error?.message}
              disabled={isReadonly("details")}
            />
          )}
        />
          }
      </section>

      <section className="form-section">
        { isReadonly("allocation_principles") ? 
          <Textarea {...register("allocation_principles")} label="Allocation Principles" rows={10} readOnly={true} /> :
        <Controller
          name="allocation_principles"
          control={control}
          render={({
            field: { onChange, value, name },
            fieldState: { error },
          }) => (
            <MarkdownEditor
              label="Allocation Principles"
              id={name}
              value={value}
              onChange={(value) => onChange(value)}
              error={error?.message}
              disabled={isReadonly("allocation_principles")}
              aria-readonly={isReadonly("allocation_principles")}
            />
          )}
        />}
      </section>

      { errorMessage && <Alert theme="failure" message={errorMessage}/>}
      <div className="form-actions">
        <Button loading={isSubmitting} disabled={ isReadonly("details") || !isDirty || isSubmitting}>
          Save
        </Button>
        <Button
          type="button"
          theme="outline"
          onClick={() => { reset(defaultValues); onCancel(); }}
        >
          Cancel
        </Button>
      </div>
    </form>
  );
};
