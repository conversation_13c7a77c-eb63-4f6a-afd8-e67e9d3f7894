import { TOfferMetrics } from "types";
import { Container } from "components/Layout/Container";
import { Bar } from 'recharts';
import { RbBarChart } from "components/Charts/RbBarChart";
import { tOfferMetricsToRechartsFormatOrderValue, tOfferMetricsToIntermediarySet, getColourMap, getDateFormat, getDateTimeFormat } from "components/Charts/Utils";
import React, { useMemo } from "react";

interface OrderMetricChartsProps {
  dailyMetrics: TOfferMetrics | undefined,
  metrics: TOfferMetrics | undefined,
}

export const OrderMetricCharts = (props: OrderMetricChartsProps) => {

    const orderDailyMetrics = useMemo(() => { return tOfferMetricsToRechartsFormatOrderValue(props.dailyMetrics, getDateFormat); }, [props.dailyMetrics]);
    const orderMetrics = useMemo(() => { return tOfferMetricsToRechartsFormatOrderValue(props.metrics, getDateTimeFormat); }, [props.metrics]);

    //useMemo so the colours stay the same between renders...
    const metricsInters = useMemo(() => { return tOfferMetricsToIntermediarySet(props.dailyMetrics).map(int => int.name); }, [props.dailyMetrics])
    const dailyMetricsInters = useMemo(() => { return tOfferMetricsToIntermediarySet(props.metrics).map(int => int.name); }, [props.metrics])
    const intColorMap = useMemo(() => { return getColourMap(metricsInters.concat(dailyMetricsInters)) }, [metricsInters, dailyMetricsInters]);
    const barchartBars = useMemo(() => { return metricsInters.map( (intName) => (
        <Bar key={intName} dataKey={intName} name={intName} stackId="a" fill={intColorMap.get(intName)} />
    ))}, [metricsInters, intColorMap])
    const dailyBarchartBars = useMemo(() => { return dailyMetricsInters.map( (intName) => (
        <Bar key={intName} dataKey={intName} name={intName} stackId="a" fill={intColorMap.get(intName)} />
    ))}, [dailyMetricsInters, intColorMap])
  return (
    <Container as="div" className="pt-xl grid grid-cols-1 gap-md md:grid-cols-2 lg:grid-cols-2">
        {orderDailyMetrics.length > 0 && (<RbBarChart title="Order Value By Day" data={orderDailyMetrics} dataKey="date" bars={dailyBarchartBars} />)}
        {orderMetrics.length > 0 && (<RbBarChart title="Order Updates" data={orderMetrics} dataKey="date" bars={barchartBars} />)}
    </Container>
  );
};
