import { Bar } from 'recharts';
import { RbBarChart } from "components/Charts/RbBarChart";
import React, { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { ApiGetParams, TAllocation, TOffer } from "types";
import { getAllocationBookAggregatedQuery } from "utils/queries";

interface AllocationChartProps {
    offer: TOffer;
    allocation?: TAllocation;
    token?: string;
}

export const AllocationChart = ({offer, allocation, token}: AllocationChartProps) => {
    const params:ApiGetParams = { offset: 0, limit: 0 }; //all of them
    const query = useQuery({...getAllocationBookAggregatedQuery(offer?.id, allocation?.allocation_book_id, token, params)});
    
    const allocationMetrics = useMemo(() => {
        return (query.data?.data ?? []).map((allocation) => {
            return {name: allocation.intermediary, value: allocation.alloc_value?.toNumber()??0, qty: allocation.value?.toNumber()??0};
        });
    }, [query]);
    const barchartBars = [
      <Bar key="value" dataKey='value' name="Allocated Value" fill="#006300" />,
      <Bar key="qty" dataKey='qty' name="Order Value" fill="#040194" />
    ]

    if (allocationMetrics.length > 0) {
      return (
        <RbBarChart title="Order Value vs Allocation" data={allocationMetrics} dataKey="name" bars={barchartBars} xAxisHeight={100} barCategoryGap="70%" />
      );
    } else {
      return <></>
    }
};
