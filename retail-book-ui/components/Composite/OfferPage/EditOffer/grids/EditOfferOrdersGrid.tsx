import { Badge } from "components/Basic/Badge/Badge";
import { Row as BaseRow,Column, DynamicContextMenuPropItem, ExpandingStyledReactDataGrid, ERowType } from "components/ExpandingStyledReactDataGrid/ExpandingStyledReactDataGrid";
import { formatCurrency } from "currency";
import { format, parseISO } from "date-fns";
import Decimal from "decimal.js";
import { formatNumber, orderByDate, orderStatusThemeSelector, Zero } from "helpers";
import { FunctionComponent, ReactNode, useMemo } from "react";
import { EOrderStatus, EOrderType, IOrderBase, TOffer, TOrder } from "types";
import { OrderDetailCard } from "../components/OrderDetailCard";

// Row Data definition:
export interface Row extends BaseRow {
    
    dataIdx: number;
    orderId: string;
    // Main row information
    date: string;
    status: EOrderStatus;
    type: EOrderType;
    intermediary?: string;
    applications: string;
    value: string;
    existing_holding: string;

    // Detail information
    shs: IOrderBase|undefined;
    non_shs: IOrderBase|undefined;
    enteredBy: string;

    sort: {
        date: string;
        applications: Decimal | undefined;
        value: Decimal | undefined;
    };
}

// Convert our raw data into the row
const getRowFromOrder = (order: TOrder, currency: string, idx:number): Row => {
    return {
        dataIdx: idx,
        id: order.id,
        orderId: order.id,
        _row_type: ERowType.ROW,
        date: format(parseISO(order.order_date), "dd/MM/yyyy"),
        status: order.status,
        intermediary: order.intermediary,
        type: order.order_type,
        applications: formatNumber(order.totals.applications),
        value: formatCurrency(order.totals.notional_value ?? Zero, currency),
        existing_holding: formatNumber(order.totals.existing_holding ?? Zero),
        enteredBy: order.entered_by ?? "",
        shs: order.shareholding,
        non_shs: order.non_shareholding,
        sort: {
            date: order.order_date,
            applications: order.totals.applications,
            value: order.totals.notional_value
        }
    };
};

// For normal rows; the columns
const columns: Column<Row>[] = [   
    {
      key: "date",
      name: "Date",
      sortable: true,
      comparator: (a, b) => orderByDate(a.sort.date, b.sort.date, false),
      resizable: true,
    },
    {
      key: "intermediary",
      name: "Intermediary",
      sortable: true,
      comparator: (a, b) =>
        (a?.intermediary ?? "").localeCompare(b?.intermediary ?? "") ?? 0,
      formatter: ({row}) => {        
          return (<span>{row.intermediary}</span>)        
      },
      resizable: true,
    },    
    {
      key: "status",
      name: "Status",
      sortable: true,
      comparator: (a, b) => a.status.localeCompare(b.status ?? "") ?? 0,
      formatter: ({ row }) => {
        return ( <Badge theme={orderStatusThemeSelector(row.status ?? EOrderStatus.ACCEPTED)}>{row.status}</Badge>)
      },
      resizable: true,
    },
    {
      key: "type",
      name: "Type",
      sortable: true,
      comparator: (a, b) => a.type.localeCompare(b.type ?? "") ?? 0,
      resizable: true,
    },
    {
      key: "applications",
      name: "Applications",
      sortable: true,
      comparator: (a, b) =>
        a.sort.applications?.minus(b.sort.applications ?? Zero)?.toNumber() ?? 0,
      formatter: ({ row }) => (
        <span className="font-mono">{row.applications}</span>
      ),
      resizable: true,
      headerCellClass: "text-right",
      cellClass: "text-right",
    },
    {
      key: "value",
      name: "Total Value",
      sortable: true,
      comparator: (a, b) =>
        a.sort.value?.minus(b.sort?.value ?? Zero)?.toNumber() ?? 0,
      formatter: ({ row }) => <span className="font-mono">{row.value}</span>,
      resizable: true,
      headerCellClass: "text-right",
      cellClass: "text-right",
    },   
    {
      key:"existing_holding",
      name: "Existing Holding",
      sortable: true,
      comparator: (a,b) => a.sort.value?.minus(b.sort?.value ?? Zero)?.toNumber() ?? 0,
      formatter: ({row}) => <span className="font-mono">{row.existing_holding}</span>,
      resizable: true,
      headerCellClass: "text-right",
      cellClass: "text-right",
    }
  ];

interface IProps {
    data:TOrder[] | undefined;
    offer?: TOffer;
    token?: string;
    currency: string;

    onAccept: (order?:TOrder) => void;
    onReject: (order?:TOrder) => void;
    onDownload: (order?:TOrder) => void;
    onDownloadAudit: (order?:TOrder) => void;
}


export const EditOfferOrderGrid: FunctionComponent<IProps> = ({ data,currency,offer, token, onAccept, onReject, onDownload, onDownloadAudit }) => {

    const rows = useMemo( () => {
        return data?.map( (x, idx) => getRowFromOrder(x,currency, idx)) ?? []
    },[data,currency])

    const ctx:DynamicContextMenuPropItem<Row>[] = [
      {
        label: "Accept",
        onClick: (e, row) => {
          e.preventDefault();
          
          if (data) {
            onAccept(data[row.dataIdx])
          }
        },
        isEnabled: (row) => row.status === EOrderStatus.PENDING        
      },
      {
        label: "Reject",
        onClick: (e, row) => {
          e.preventDefault();
          if (data) {
            onReject(data[row.dataIdx])
          }
        },
        isEnabled: (row) => row.status === EOrderStatus.PENDING
      },
    ]

    const detailControl = useMemo( () => {
        const fn = (row:Row):ReactNode => {
            return <OrderDetailCard 
                      order={data?.[row.dataIdx]} 
                      offer={offer}
                      token={token}
                      currency={currency} 
                      onDownload={onDownload} 
                      onDownloadAudit={onDownloadAudit} 
                      onAccept={onAccept} 
                      onReject={onReject}
                      
                      />
        }
        fn.displayName = "OrderDetailCardFn"
        return fn
    },[data,currency,offer,onAccept,onDownload,onDownloadAudit,onReject,token])

    return (
        <ExpandingStyledReactDataGrid 
            columns={columns} 
            rows={rows} 
            detailControl={detailControl} expandedSize={300}
            contextMenuItems={ctx}
        />    
    )
}