import { Column, Row as BaseRow, ExpandingStyledReactDataGrid, ERowType } from "components/ExpandingStyledReactDataGrid/ExpandingStyledReactDataGrid";
import Decimal from "decimal.js";
import { Zero, formatNumber } from "helpers";
import { FunctionComponent, ReactNode, useMemo, useState } from "react";
import { AllocationDetailCard } from "../components/AllocationDetailCard";
import {  getAllocationBookAggregatedQuery } from "utils/queries";
import { ApiGetParams, TAggregatedAllocation, TAllocation, TOffer } from "types";
import { useQuery } from "@tanstack/react-query";
import { formatCurrency } from "currency";
import { Percent } from "../components/Percent";

interface IProps {
    offer: TOffer;
    allocation?: TAllocation;
    token?: string;
};

interface Row extends BaseRow {
    intermediary: string;
    intermediary_system_id: string;
    quantity: Decimal;
    value: Decimal;
    allocated: Decimal;
    allocated_value: Decimal;
    applications: Decimal;
    allocated_orders: Decimal;
    number_of_orders: Decimal;
};


const convertToRow = (x:TAggregatedAllocation):Row => {
    return { 
        id: x.intermediary,
        _row_type: ERowType.ROW,
        intermediary: x.intermediary,
        quantity: x.qty ?? Zero,
        value: x.value ?? Zero,
        applications: x.applications ?? Zero,
        allocated: x.alloc_quantity ?? Zero,
        allocated_value: x.alloc_value ?? Zero,
        allocated_orders : x.allocated_orders ?? Zero,
        number_of_orders: x.number_of_orders ?? Zero,
        intermediary_system_id: x.intermediary_system_id
     };        
}

const pageSize = 10;



export const AllocationByIntermediaryGrid: FunctionComponent<IProps> = ({offer, allocation, token } ) => {
    const [currentPage, setCurrentPage] = useState(1);
    const params:ApiGetParams = { offset: 0, limit: 20 };

    const query = useQuery({...getAllocationBookAggregatedQuery(offer?.id, allocation?.allocation_book_id, token, params)});
    
    const rows = useMemo( () => {
        return query.data?.data?.map(convertToRow) ?? []
    }, [query.data?.data])

    const totalItems = useMemo(
        () => query.data?.pagination.count ?? 0,
        [query.data?.pagination.count]
    );

    const columns:Column<Row>[] = useMemo( () => { return [
        {
            key:"intermediary",
            name:"Intermediary",
            sortable: false,
            resizable: true
        },{
            key:"applications",
            name:"Applications",
            sortable: false,
            resizable: true,
            formatter: ({row}) => {
                return (<span className="font-mono">{formatNumber(row.applications)}</span>)
            }
        },{
            key: "number_of_orders",
            name:"No of Orders",
            sortable: false,
            resizable: true,
            formatter: ({row}) => {
                return (<span className="font-mono">{formatNumber(row.number_of_orders)}</span>)
            }
        },{       
            key:"quantity",
            name:"Quantity",
            sortable: false,
            resizable: true,
            formatter: ({row}) => {        
                return (<span className="font-mono">{formatNumber(row.quantity)}</span>)        
            },
        },{
            key:"value",
            name:"Value",
            sortable: false,
            resizable: true,
            formatter: ({row}) => { return (<span className="font-mono">{formatCurrency(row.value, offer.currency)}</span>)} 
        },{
            key:"allocated_orders",
            name:"Allocated Orders",
            sortable: false,
            resizable: true,
            formatter: ({row}) => {return(<span className="font-mono">{formatNumber(row.allocated_orders)} <Percent reference={row.number_of_orders} value={row.allocated_orders}/></span>)}    
        },{
            key:"allocated",
            name:"Allocated Quantity",
            sortable: false,
            resizable: true,
            formatter: ({row}) => { return (<span className="font-mono">{formatNumber(row.allocated)} <Percent reference={row.quantity} value={row.allocated}/></span>)},
            headerCellClass: "text-right",
            cellClass: "text-right",
        },{
            key:"allocated_value",
            name:"Allocated Value",
            sortable: false,
            resizable: true,
            formatter:({row}) => {return (<span className="font-mono">{formatCurrency(row.allocated_value, offer.currency)} <Percent reference={row.value} value={row.allocated_value}/></span>)}
        }    
    ]}, [offer]);


    const detailControl = useMemo( () => {
        const fn = (row:Row):ReactNode => {
            return (<div className="pb-3 pt-3"><AllocationDetailCard offer={offer} allocation={allocation} token={token} intermediary={row.intermediary_system_id}/></div>)
        };
        fn.displayName = "AllocationDetailCardFn"
        return fn
    },[allocation, offer, token])

    return <ExpandingStyledReactDataGrid columns={columns} 
                                         rows={rows} 
                                         detailControl={detailControl} 
                                         expandedSize={400}
                                         paginationProps={{
                                            onClick: (page) => setCurrentPage(page),
                                            currentPage: currentPage,
                                            totalItems: totalItems,
                                            pageSize: pageSize ?? 5,
                                        }} />
}