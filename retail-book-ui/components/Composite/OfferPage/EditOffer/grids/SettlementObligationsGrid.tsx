import { Column, StyledReactDataGrid } from "components/StyledReactDataGrid/StyledReactDataGrid";
import Decimal from "decimal.js";
import { Zero, formatNumber } from "helpers";
import { formatCurrency } from "currency";
import { FunctionComponent, useMemo } from "react";
import { TSettlementObligation } from "types";
import { format, parseISO } from "date-fns";
import { DATETIME_LOCAL_DATE_ONLY_FORMAT, DATETIME_LOCAL_DATE_FNS_FORMAT } from "../../../../../helpers";
import { Badge } from "components/Basic/Badge/Badge";

interface IProps {
    data: TSettlementObligation[]
};

interface Row {
    id: string;
    settlement_book_id: string;
    offer_id: string;
    created: string;
    updated: string;
    quantity: Decimal;
    open_quantity: Decimal;
    cash_amount: Decimal;
    open_cash_amount: Decimal;
    counterparty_system_id: string;
    counterparty_display_name: string;
    settlement_reference: string;
    security_isin: string;
    security_name: string;
    settlement_date: string;
    actual_settlement_date: string;
    price: Decimal;
    delivery_type: string;
    currency: string;
};


const convertToRow = (x:TSettlementObligation):Row => {
    return { 
        id: x.id,
        settlement_book_id: x.settlement_book_id,
        offer_id: x.offer_id,
        created: x.created ?? "",
        updated: x.updated ?? "",
        quantity: x.quantity ?? Zero,
        open_quantity: x.open_quantity ?? Zero,
        cash_amount: x.cash_amount ?? Zero,
        open_cash_amount: x.open_cash_amount ?? Zero,
        counterparty_system_id: x.counterparty_system_id ?? "",
        counterparty_display_name: x.counterparty_display_name ?? "",
        settlement_reference: x.settlement_reference ?? "",
        security_isin: x.security_isin ?? "",
        security_name: x.security_name ?? "",
        settlement_date: x.settlement_date ?? "",
        actual_settlement_date: x.actual_settlement_date ?? "",
        price: x.price ?? Zero,
        delivery_type: x.delivery_type ?? "",
        currency: x.currency ?? ""
     };        
}

const columns:Column<Row>[] = [
    {
        key:"counterparty_display_name",
        name:"Counterparty",
        sortable: true,
        resizable: true,
        comparator: (a, b) => a.counterparty_display_name.localeCompare(b.counterparty_display_name)
    },{
        key:"settlement_reference",
        name:"Settlement Reference",
        sortable: true,
        resizable: true,
        comparator: (a, b) => a.settlement_reference.localeCompare(b.settlement_reference)
    },{
        key:"delivery_type",
        name:"Delivery Type",
        sortable: true,
        resizable: true,
        comparator: (a, b) => a.delivery_type.localeCompare(b.delivery_type),
        formatter: ({row}) => {
            const theme = () => {
                if (row.delivery_type == "FOP") {
                    return "warning";
                }
                if (row.delivery_type == "DVP") {
                    return "success";
                }
                return "info";
            }
            return <Badge theme={theme()}>{row.delivery_type}</Badge>
        }
    },{
        key:"settlement_date",
        name:"Settlement Date",
        sortable: false,
        resizable: true,
        formatter: ({row}) => {
            return (<span>{row.settlement_date ? format(parseISO(row.settlement_date), DATETIME_LOCAL_DATE_ONLY_FORMAT) : ""}</span>)
        }
    },{
        key:"open_quantity",
        name:"Open Quantity",
        sortable: false,
        resizable: true,
        formatter: ({row}) => {
            return (<span>{formatNumber(row.open_quantity)}</span>)
        },
        headerCellClass: "text-right",
        cellClass: "text-right",
    },{
        key:"open_cash_amount",
        name:"Open Cash Amount",
        sortable: false,
        resizable: true,
        formatter: ({row}) => {
            return (<span>{formatCurrency(row.cash_amount, row.currency)}</span>)
        },
        headerCellClass: "text-right",
        cellClass: "text-right",
    },{
        key:"actual_settlement_date",
        name:"Actual Settlement Date",
        sortable: false,
        resizable: true,
        formatter: ({row}) => {
            if (!row.actual_settlement_date || row.actual_settlement_date == "") {
                return <Badge theme="warning">Open</Badge>
            }
            return <Badge theme="success">{format(parseISO(row.actual_settlement_date), DATETIME_LOCAL_DATE_FNS_FORMAT)}</Badge>
        }
    }
];

export const SettlementObligationsGrid: FunctionComponent<IProps> = ({data} ) => {
    
    const rows = useMemo( () => {
        return data?.map(convertToRow) ?? []
    }, [data])

    if (rows.length <= 0) {
        return <></>
    }
    return <StyledReactDataGrid columns={columns} rows={rows} />
}