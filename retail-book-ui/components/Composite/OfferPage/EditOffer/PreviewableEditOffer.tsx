import { StandaloneLink } from "components/Basic/StandaloneLink/StandaloneLink";
import { Container } from "components/Layout/Container";
import { FunctionComponent, useState } from "react";
import { ArrowLeft } from "react-feather";
import { TOffer } from "types";
import { ViewOffer } from "../ViewOffer/ViewOffer";
import { EditOffer } from "./EditOffer";


interface IProps {
    offer: TOffer;
}

export const PreviewableEditOffer: FunctionComponent<IProps> = ({ offer }) => {

    const [preview, setPreview] = useState(false)


    return (
        <>
            <Container className="pt-xs-sm flex justify-between items-center">
                <StandaloneLink
                    href="/"
                    icon={ArrowLeft}
                    iconPosition="before"
                    className="mb-md-lg"
                >
                    Offers
                </StandaloneLink>

                {
                    preview ?
                        <StandaloneLink className="mb-md-lg" icon={ArrowLeft} iconPosition="before" onClick={() => setPreview(false)}>Edit</StandaloneLink> :
                        <StandaloneLink className="mb-md-lg" onClick={() => setPreview(true)}>Preview</StandaloneLink>
                }

            </Container>

            {
                preview ? <ViewOffer offer={offer} preview /> : <EditOffer offer={offer} />
            }

        </>
    )
}