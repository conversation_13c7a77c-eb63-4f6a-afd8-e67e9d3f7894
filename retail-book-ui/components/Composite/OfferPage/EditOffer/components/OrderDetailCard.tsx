import { Tab } from "@headlessui/react";
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { Table } from "components/Basic/Table/Table";
import { Container } from "components/Layout/Container";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { formatCurrency } from "currency";
import { formatLocalFromIso, formatNumber, Zero } from "helpers";
import { FunctionComponent, useMemo } from "react";
import { Download } from "react-feather";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { EOrderStatus, EOrderType, TOffer, TOrder } from "types";
import { OrderHistoryTable } from "components/Bespoke/OrderHistoryTable/OrderHistoryTable";
import { useQuery } from "@tanstack/react-query";
import { getIntermediaryOrderHistoryQuery } from "utils/queries";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";

interface IProps {
    order?: TOrder;
    currency?: string;
    offer?: TOffer;
    token?: string;
}


// Order Detail Breakdown
const OrderDetailBreakdown: FunctionComponent<IProps> = ({ order, currency }) => {
    return (
        <Table className="place-self-start">
            <Table.Head>
                <Table.Row>
                    <Table.HeaderCell><span></span></Table.HeaderCell>
                    <Table.HeaderCell>Applications</Table.HeaderCell>
                    <Table.HeaderCell>Value</Table.HeaderCell>
                    {order?.shareholding && order?.shareholding.applications?.greaterThan(Zero) &&
                        (<Table.HeaderCell>Existing Holding</Table.HeaderCell>)}
                </Table.Row>
            </Table.Head>
            <Table.Body>
                {order?.non_shareholding &&
                    (<Table.Row>
                        <Table.Cell heading="">Non Shareholders</Table.Cell>
                        <Table.Cell heading=""><span className="font-mono">{formatNumber(order?.non_shareholding.applications ?? Zero)}</span></Table.Cell>
                        <Table.Cell heading=""><span className="font-mono">{formatCurrency(order?.non_shareholding.notional_value ?? Zero, currency ?? "GBP")}</span></Table.Cell>

                        {order?.shareholding && order?.shareholding.applications?.greaterThan(Zero) && (<Table.Cell heading=""><span className="font-mono">-</span></Table.Cell>)}
                    </Table.Row>)}

                {order?.shareholding &&
                    (<Table.Row>
                        <Table.Cell heading="">Shareholders</Table.Cell>
                        <Table.Cell heading=""><span className="font-mono">{formatNumber(order?.shareholding.applications ?? Zero)}</span></Table.Cell>
                        <Table.Cell heading=""><span className="font-mono">{formatCurrency(order?.shareholding.notional_value ?? Zero, currency ?? "GBP")}</span> </Table.Cell>
                        <Table.Cell heading=""><span className="font-mono">{formatNumber(order?.shareholding.existing_holding ?? Zero)}</span></Table.Cell>
                    </Table.Row>)}
            </Table.Body>
        </Table>
    )
}

// Order Detail Details

const OrderDetailDetails: FunctionComponent<IProps> = ({ order }) => {
    return (
        <table className="border-1 border-black" cellPadding={5}>
            <tbody>
                <tr>
                    <td>Entered By</td>
                    <td>{order?.entered_by ?? ""}</td>
                </tr>
                <tr>
                    <td>Entered On</td>
                    <td><Input label="" type="datetime-local" readOnly={true} value={formatLocalFromIso(order?.order_date, "")}></Input></td>
                </tr>
                <tr>
                    <td>Updated On</td>
                    <td><Input label="" type="datetime-local" readOnly={true} value={formatLocalFromIso(order?.update_date, "")}></Input></td>
                </tr>
            </tbody>
        </table>
    )
}

// Order History

const OrderHistory: FunctionComponent<IProps> = ({ order, offer, token }) => {
    const orderHistoryQueryOptions = useMemo(() => getIntermediaryOrderHistoryQuery(order?.intermediary_system_id, order?.offer_id, token), [order, token]);
    const { data: orderHistory } = useQuery(orderHistoryQueryOptions);
  
    
    return (
       <div className="pt-2">{ (orderHistory && offer && orderHistory?.length > 0) ? (<OrderHistoryTable orderHistory={orderHistory} offer={offer} paging />) : (<EmptyAction message="No History Available"/>) }</div>
    )
}


// Order Detail Card

interface OrderDetailProps extends IProps {
    onAccept: (order?: TOrder) => void;
    onReject: (order?: TOrder) => void;
    onDownload: (order?: TOrder) => void;
    onDownloadAudit: (order?: TOrder) => void;
}


export const OrderDetailCard: FunctionComponent<OrderDetailProps> = ({ order, currency, onAccept, onReject, onDownload, onDownloadAudit, offer, token}) => {

    const acceptRejectEnabled = useMemo(() => {
        return (order?.status === EOrderStatus.PENDING)
    }, [order])

    return (
        <Container className="bg-white h-full w-full flex flex-col" >
            <Tab.Group>
                <Container className="pt-0">
                    <Tab.List className="tab-list">
                        <StyledTab>Details</StyledTab>
                        <StyledTab>Order Breakdown</StyledTab>
                        <StyledTab>History</StyledTab>
                    </Tab.List>
                    <Tab.Panels className="tab-panel tab-panel--bordered p-0">
                        <Tab.Panel className="bg-white">
                            <Container className="bg-white h-full w-full pt-5">
                                <OrderDetailDetails order={order} />
                            </Container>
                        </Tab.Panel>
                        <Tab.Panel className="h-full">
                            <Container className="bg-white h-full">
                                <OrderDetailBreakdown order={order} currency={currency} />
                            </Container>
                        </Tab.Panel>
                        <Tab.Panel className="h-full">
                             <Container className="bg-white h-full">
                                <OrderHistory offer={offer} order={order} token={token}/>
                            </Container>
                        </Tab.Panel>
                    </Tab.Panels>
                </Container>
            </Tab.Group>
            <Container className="space-x-1 mt-auto mb-2">
                <Button size="sm" theme="outline" type="button" disabled={!acceptRejectEnabled} onClick={() => onAccept(order)}>Accept</Button>
                <Button size="sm" theme="outline" type="button" disabled={!acceptRejectEnabled} onClick={() => onReject(order)}>Reject</Button>
                <Button size="sm" theme="outline" type="button" icon={Download} onClick={() => onDownload(order)} disabled={!order?.order_book_id}>Download</Button>
                <Button size="sm" theme="outline" type="button" icon={Download} onClick={() => onDownloadAudit(order)}> Download Audit </Button>
            </Container>
        </Container>
    )
}
