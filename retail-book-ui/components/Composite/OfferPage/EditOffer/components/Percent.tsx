import Decimal from "decimal.js";
import { Zero } from "helpers";
import { FunctionComponent, useMemo } from "react";

interface PercentProps {
    reference?: Decimal;
    value?: Decimal;
};

export const Percent: FunctionComponent<PercentProps> = ({reference, value}) => {
    const pc = useMemo( () => {
        if (!reference || !value) return undefined;
        if (reference.eq(Zero)) return undefined;

        return value.div(reference).mul(100).trunc().toNumber();
    },[reference,value])
    

    const style = useMemo( () => {
        if (!pc) return;

        if (pc > 100) {
            return `rgb(255,0,0)`;
        } else {

            const red = 255-(255*pc)/100;
            const green = (139*pc)/100;
        
            return `rgb(${red.toString()},${green.toString()},0)`;
        }
    },[pc]);

    return pc ? <span className="text-xs" style={{color: `${style}`}}>{pc}% </span> : <></>;
};