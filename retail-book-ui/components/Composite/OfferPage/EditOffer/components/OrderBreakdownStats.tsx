import { Container } from "components/Layout/Container"
import { StyledReactDataGrid } from "components/StyledReactDataGrid/StyledReactDataGrid"
import { formatCurrency } from "currency"
import { formatNumber, Zero } from "helpers"
import { FunctionComponent, useMemo } from "react"
import { Column } from "react-data-grid"
import { CornerDownRight } from "react-feather"
import { IOrderBase, TOrderBreakdown } from "types"

interface Row {
    id:string
    group1:string
    group2:string
   
    applications:string
    value:string
    existing:string
}

interface IProps {
    currency: string
    stats?: TOrderBreakdown
    totals?:IOrderBase
}


const columns : Column<Row>[] = [
    {
        name: "Detail",
        key: "group1",
        formatter: ({ row }) => {
            if (row.group1 === "" && row.group2 != "") {
                return (<span className="flex justify-end content-center"><CornerDownRight/></span>)
            } else {
                return (<span>{row.group1}</span>)
            }
        },
        width:70,
        resizable:true
    },
    {
        name: "",
        key: "group2",
        formatter: ({ row }) => {
            if (row.group1 === "" && row.group2 === "") {
                return (<span className="flex justify-end content-center"><CornerDownRight/></span>)
            } else {
                return (<span>{row.group2}</span>)
            }
        },
        width: 120,
        resizable:true
    },  
    {
        name: "",
        key: "group3",
        width: 160,
        resizable:true
    },   
    {
        name: "Applications",
        key: "applications"
    },
    {
        name: "Value",
        key: "value",
        formatter: ({ row }) => <span className="font-mono">{row.value}</span>
    },
    {
        name: "Existing Holding",
        key: "existing"
    }]


const createItem  = (currency:string, total?: IOrderBase) => {
    return {
        applications: formatNumber(total?.applications),
        value: formatCurrency(total?.notional_value ?? Zero,currency),
        existing: formatNumber(total?.existing_holding)
    }
}

const createHeaderItem = (id:string, groupName:string, currency: string, total?:IOrderBase) => {
    return {
        id: id, group1: groupName, group2: "", group3: "", ...createItem(currency, total)
    }
}

const createGroupItem = (id:string, groupName:string, currency: string, total?:IOrderBase) => {
    return {
        id: id, group1: "", group2: groupName, group3: "", ...createItem(currency, total)
    }
}

const createLineItem = (id: string, lineName: string, currency: string, total?:IOrderBase) => {
    return {
        id: id, group1: "", group2: "", group3: lineName, ...createItem(currency, total)
    }
}

const validLineItem = (total ?: IOrderBase) => {
    return total && (total.applications ?? Zero).greaterThan(Zero)
}

export const OrderBreakDownStats:FunctionComponent<IProps> = ({currency,stats,totals}) => {

    const rows = useMemo( () => {
        const rows:Row[] = []

        rows.push(createHeaderItem("0", "Total", currency, totals))

        if (stats?.accepted && validLineItem(stats?.accepted.total)) {
            rows.push(createGroupItem("1","Accepted",currency, stats?.accepted.total))

            if (validLineItem(stats.accepted.shareholding)) {
                rows.push(createLineItem("2","Shareholders",currency, stats.accepted.shareholding))                
            }

            if (validLineItem(stats.accepted.non_shareholding)) {
                rows.push(createLineItem("3","Non Shareholders",currency, stats.accepted.non_shareholding))                
            }            
        }

        if (stats?.pending && validLineItem(stats?.pending.total)) {
            rows.push(createGroupItem("4","Pending",currency, stats?.pending.total))

            if (validLineItem(stats.pending.shareholding)) {
                rows.push(createLineItem("5","Shareholders",currency, stats.pending.shareholding))                
            }

            if (validLineItem(stats.pending.non_shareholding)) {
                rows.push(createLineItem("6","Non Shareholders",currency, stats.pending.non_shareholding))                
            }            
        }
        return rows
    }, [currency, stats, totals])
    
    return (
        <Container className="flex flex-col justify-center content-center pl-0 pr-0">
            {/* TOP Level */}
            <Container className="rounded-lg pr-0">
                <StyledReactDataGrid 
                    columns={columns}    
                    rows={rows}
                />
            </Container>   
        </Container>
    )

}