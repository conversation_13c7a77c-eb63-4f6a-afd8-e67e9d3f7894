import { Badge } from "components/Basic/Badge/Badge";
import { FunctionComponent } from "react";

interface IProps {
    accepted: boolean,    
    acceptedTime?: Date
}

export const TermsAcceptance: FunctionComponent<IProps> = ({accepted, acceptedTime}) => {
    return (<div>
        { !accepted && <Badge theme="warning-strong">Not Accepted</Badge> }
        { accepted && (
            <Badge  theme="success"><span className="flex">{acceptedTime?.toLocaleDateString()} {acceptedTime?.toLocaleTimeString()??"Accepted"} </span></Badge> 
        )}
    </div>)
}