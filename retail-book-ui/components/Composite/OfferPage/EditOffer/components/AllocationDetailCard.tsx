import { AllocationLineItemGrid } from "components/Grids/Allocation/AllocationLineItemGrid";
import { Container } from "components/Layout/Container";
import { FunctionComponent } from "react";
import { TAllocation, TOffer } from "types";


interface IProps {
    offer?: TOffer,
    allocation?: TAllocation,
    intermediary?: string;
    token?: string
};


const detailColumns = ["ref","applications","existing","value","qty","allocQty","allocValue","taxWrapper","commissionDue","validationError"]

export const AllocationDetailCard:FunctionComponent<IProps> = ({offer,allocation,token,intermediary}) => {
    return <Container className="bg-white h-full w-full flex flex-col overflow-auto" > 
        <AllocationLineItemGrid             
            colNames={detailColumns} 
            offerId={offer?.id}
            allocationBookId={allocation?.allocation_book_id}
            showErrors={false}
            token={token}
            filter={[`intermediary=${intermediary}:eq`]}
            
        />
    </Container>
}