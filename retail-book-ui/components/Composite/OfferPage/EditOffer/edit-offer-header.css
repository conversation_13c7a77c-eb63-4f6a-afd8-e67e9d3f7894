.edit-offer-header {
  @apply mb-md;

  &__main {
    @apply flex flex-col mb-md justify-between gap-md-lg;
  }

  &__title {
    @apply h1 font-brand;
  }

  &__sidebar {
    @apply flex flex-col justify-between gap-md-lg;
  }

  &__operations {
    @apply flex gap-sm;
  }

  &__checklist-description {
    @apply mt-2xs mb-sm;
  }

  &__checklist {
    @apply columns-2 gap-md-lg;
  }

  &__checklist-item {
    @apply flex mb-sm overflow-auto;
  }

  &__checklist-icon {
    @apply shrink-0 pointer-events-none mr-2xs h-7 w-7;
  }

  &__checklist-icon--valid {
    @apply text-utility-green-step-0;
  }
  &__checklist-icon--invalid {
    @apply text-utility-red-step-0;
  }

  &__checklist-icon--warning {
    @apply text-utility-yellow-step-0;
  }

  @media screen(lg) {
    @apply grid grid-cols-3 gap-lg;

    &__main {
      @apply col-span-1 mb-0;
    }

    &__sidebar {
      @apply col-span-2 border-l border-l-grey-step-3 pl-md-lg;
    }
  }
}
