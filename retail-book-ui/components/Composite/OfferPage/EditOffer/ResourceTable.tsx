import { ContextMenuProps } from "components/Bespoke/ContextMenu/ContextMenu";
import {
  Column,
  StyledReactDataGrid,
  StyledReactDataGridProps,
} from "components/StyledReactDataGrid/StyledReactDataGrid";
import { formatFileSize } from "helpers";
import { Resource } from "types";

interface Row {
  id: string;
  title: string;
  file_name: string;
  type: string;
  mime_type: string;
  size: number;
  url: string;
}

const columns: Column<Row>[] = [
  {
    key: "title",
    name: "Title",
    sortable: true,
    comparator: (a, b) => a.title.localeCompare(b.title),
    resizable: true,
  },
  {
    key: "type",
    name: "Resource Type",
    sortable: true,
    comparator: (a, b) => a.type.localeCompare(b.type),
    resizable: true,
  },
  {
    key: "mime_type",
    name: "File Type",
    sortable: true,
    comparator: (a, b) => a.type.localeCompare(b.type),
    resizable: true,
    formatter: ({ row }) => row.mime_type?.split("/")[1],
  },
  {
    key: "size",
    name: "File Size",
    sortable: true,
    comparator: (a, b) => a.size - b.size,
    resizable: true,
    formatter: ({ row }) => formatFileSize(row.size),
  },
];

interface ResourceTableProps
  extends Omit<StyledReactDataGridProps<Row>, "columns"> {
  rows: Resource[];
  contextMenuItems: ContextMenuProps<Resource>["items"];
}

export const ResourceTable = ({
  contextMenuItems,
  ...props
}: ResourceTableProps) => (
  <StyledReactDataGrid
    {...props}
    columns={columns}
    contextMenuItems={contextMenuItems}
  />
);
