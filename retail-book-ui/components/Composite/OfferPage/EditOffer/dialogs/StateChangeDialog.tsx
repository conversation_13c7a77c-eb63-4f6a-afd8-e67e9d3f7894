import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { Alert } from "components/Basic/Alert/Alert";
import { Button } from "components/Basic/Button/Button";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { useSession } from "next-auth/react";
import { FunctionComponent, useEffect, useState } from "react";
import { ApiError, ApiMutationResponse, StatusEvent, TOffer, TOfferUIStateOperation } from "types";
import { parseApiError } from "utils/misc";
import { updateOfferStatusMutation } from "utils/queries";
import { Input } from "components/Basic/Input/Input";
import { formatIsoFromLocal } from "helpers";

interface IProps {
    open: boolean;
    onClose: () => void;
    operation?: TOfferUIStateOperation|null;
    offer: TOffer;
};


const toHumanFieldNames = (fn:string): string => {
    switch (fn) {
        case "open_date": {
            return "Open Date";
        }
        case "close_date": {
            return "Close Date";
        }
    }
    return fn;
}

//const toApiDate = (stringDate:string) => {
//    return stringDate + ":00.000Z"
//}

export const StateChangeDialog: FunctionComponent<IProps> = ({ open, onClose, operation, offer }) => {
    const { data: session } = useSession();
    const qc = useQueryClient();

    const [setOpenTime,SetSetOpenTime] = useState(operation?.options?.set_open_time ?? false);
    const [setCloseTime,SetSetCloseTime] = useState(operation?.options?.set_close_time ?? false);
    const [cleanseDate,SetCleanseDate] = useState("");
    const [beforeExpected,setBeforeExpected] = useState(false)
 
    useEffect( () => {
        SetSetOpenTime(operation?.options?.set_open_time ?? false);
        SetSetCloseTime(operation?.options?.set_close_time ?? false);
        if (operation?.options?.set_cleansing_time) {
            SetCleanseDate(new Date().toLocaleString('sv').slice(0,10) + "T" + new Date().toLocaleString('sv').slice(11, 16))
        }
    },[operation,open]);

    useEffect( () => {
        try {
            const expected = new Date(offer.open_date);
            if (expected > new Date()) {
                setBeforeExpected(true);
            } else {
                setBeforeExpected(false);
            }
        } catch (e ) {
            console.log(e);
        }

    },[offer])

    const updateStatusMutator = useMutation<ApiMutationResponse,AxiosError<ApiError>,StatusEvent>({
        ...updateOfferStatusMutation(offer.id, qc, session?.accessToken),
        onSuccess: () => { 
          qc.invalidateQueries(["offer", offer.id]);
          qc.invalidateQueries(["ui", "offer", offer.id]);
          onClose();
        },
        // eslint-disable-next-line  @typescript-eslint/no-empty-function
        onError: () => {},
      });

    return (

    <StyledDialog
        open={open}
        onClose={onClose}
        title="Confirm status update"
        footer={
            <div className="flex gap-xs">
                <Button type="button" disabled={updateStatusMutator.isLoading} onClick={ () => {
    
                    updateStatusMutator.mutate({
                        event: operation?.label ?? "",
                        set_open_time: setOpenTime,
                        set_close_time: setCloseTime,
                        cleansing_time: operation?.options?.set_cleansing_time ? formatIsoFromLocal(cleanseDate) : undefined
                    });
                }}>Update status</Button>
                <Button
                    theme="outline"
                    type="button"
                    onClick={() => {
                        updateStatusMutator.reset();
                        onClose();
                    }}
                >
                    Cancel
                </Button>                
            </div>
        }
    >
        <div className="flex flex-col space-y-2">
            <p className="body">
                {operation && operation.confirm_text
                    ? operation.confirm_text
                    : "Are you sure you are ready to change the offer state?"}
            </p>

            {
                operation?.label === "Open Applications" &&  beforeExpected && (
                    <Alert theme="warning" message="Offer hasn't yet reached expected open time." />
                )
            }
            
            <Checkbox { ...(operation?.options?.set_open_time ? {} : {className:"hidden"}) } label="Update open time?" checked={setOpenTime} onChange={ (x) => SetSetOpenTime(x.currentTarget.checked)}  />
            <Checkbox { ...(operation?.options?.set_close_time ? {}: {className:"hidden"}) } label="Update close time?" checked={setCloseTime} onChange={ (x) => SetSetCloseTime(x.currentTarget.checked) } />
            
            <div className="mt-lg">
                <Input
                { ...(operation?.options?.set_cleansing_time ? {}: {className:"hidden"}) }
                label="Cleanse Date"
                type="datetime-local"
                onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
                onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
                value={cleanseDate}
                max="9999-12-31T23:59"
                onChange={ (x) => SetCleanseDate(x.currentTarget.value)}
            />
            </div>

            {
                updateStatusMutator.error && <Alert theme="failure" message={parseApiError(updateStatusMutator.error.response?.data, toHumanFieldNames)} />
            }
        </div>
    </StyledDialog>)
}