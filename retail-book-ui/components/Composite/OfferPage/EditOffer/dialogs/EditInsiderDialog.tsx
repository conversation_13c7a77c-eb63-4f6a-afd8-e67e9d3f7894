import {FunctionComponent, useEffect, useMemo, useState} from "react";
import {useSession} from "next-auth/react";
import {
    AmendOfferInsiderVariables,
    ApiError,
    ApiMutationResponse,
    EInsiderEvent,
    TInsider,
    TOffer
} from "../../../../../types";
import {Button} from "../../../../Basic/Button/Button";
import {formatIsoFromLocal} from "../../../../../helpers";
import {AxiosError} from "axios";
import {StyledCombobox, StyledComboboxOption} from "../../../../StyledHeadlessUI/StyledCombobox";
import {Input} from "../../../../Basic/Input/Input";
import {Alert} from "../../../../Basic/Alert/Alert";
import {StyledDialog} from "../../../../StyledHeadlessUI/StyledDialog";
import {useMutation, useQueryClient} from "@tanstack/react-query";
import {amendOfferInsiderInsideTimeMutation} from "../../../../../utils/queries";

interface IProps {
    offer: TOffer;
    insiderId: string;
    isModalOpen: boolean;
    modalOpenTrigger: (open: boolean) => void;
    insiders: TInsider[];
}

export const EditInsiderDialog: FunctionComponent<IProps> = ({ offer, insiderId, isModalOpen, modalOpenTrigger, insiders }) => {
    const {data: session} = useSession();
    const qc = useQueryClient();

    const isEditable = (event: EInsiderEvent): boolean => {
        return event !== EInsiderEvent.WALL_CROSSING
    }

    const insiderOptions = useMemo( () => {
        const data = insiders ?? []

        return data.filter((insider) => isEditable(insider.event))
            .map((insider) => ({label: `${insider.user_name} (${insider.user_email})`, value: insider, id: insider.id}))

    }, [insiders])

    const [selectedInsideTime, setSelectedInsideTime] = useState("")
    const [selectedInsiders, setSelectedInsiders] = useState<
        StyledComboboxOption<TInsider>[]
    >([]);

    const insiderOptionsFiltered = useMemo(
        () =>
            insiderOptions.filter((option) => {
                if (selectedInsiders.find((selected) => selected.id === option.id)) {
                    return false;
                }
                return true;
            }),
        [insiderOptions, selectedInsiders]
    );

    useEffect( () => {
        if (insiderId) {
            setSelectedInsiders(insiderOptions.filter((option) => option.id === insiderId))
        }
    }, [insiderOptions, insiderId]);


    const [errorsFromEdit, setErrorsFromEdit] = useState<string[]>([]);

    const amendOfferInsiderInsideTimeMutationOptions = useMemo( () => {
        return amendOfferInsiderInsideTimeMutation(session?.accessToken, offer.id, qc)
    },[session?.accessToken, offer.id, qc])

    const amendOfferInsiderInsideTimeMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>, AmendOfferInsiderVariables> (
        {...amendOfferInsiderInsideTimeMutationOptions,
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            onError: () => {}}
    );

    const resetClose = useMemo(() => {
        return () => {
            modalOpenTrigger(false);
            setSelectedInsiders([]);
            setSelectedInsideTime("")
            setErrorsFromEdit([]);
            amendOfferInsiderInsideTimeMutator.reset();
        }
    }, [modalOpenTrigger, amendOfferInsiderInsideTimeMutator]);

    return (<StyledDialog
        open={isModalOpen}
        onClose={() => resetClose()}
        title="Edit inside times"
        footer={
            <div className="flex gap-xs">
                <Button
                    type="button"
                    disabled={selectedInsiders.length < 1}
                    loading={amendOfferInsiderInsideTimeMutator.isLoading}
                    onClick={async (e) => {
                        e.preventDefault();

                        const errors:string[] = []

                        if (!selectedInsiders) return

                        if (selectedInsideTime === "") {
                            errors.push('Valid inside time is required')
                        }
                        else {


                            for (let c = 0; c < selectedInsiders.length; ++c) {
                                const opt = selectedInsiders[c]

                                try {
                                    await amendOfferInsiderInsideTimeMutator.mutateAsync({
                                        id: opt.value.id,
                                        insideTime: formatIsoFromLocal(selectedInsideTime) ?? ""
                                    })
                                } catch (err) {
                                    errors.push(`Could not update ${opt.value.user_name} (${opt.value.user_email}) - ${(err as AxiosError<ApiError>).response?.data.message ?? "unknown"}`)
                                }
                            }
                        }
                        if (errors.length == 0) {
                            resetClose()
                        }

                        setErrorsFromEdit(errors)
                    }}
                >
                    Update ({selectedInsiders.length})
                </Button>
                <Button
                    theme="outline"
                    type="button"
                    onClick={() => {
                        resetClose()
                    }}
                >
                    Cancel
                </Button>
            </div>
        }>
        <div className="w-screen max-w-screen-sm">
            <p className="body mb-sm">Select one or more insiders to edit</p>
            <StyledCombobox
                options={insiderOptionsFiltered}
                onChange={(options: StyledComboboxOption<TInsider>[]) =>
                    setSelectedInsiders(options)
                }
                onRemove={(option: StyledComboboxOption<TInsider>) =>
                    setSelectedInsiders((existing) =>
                        existing.filter((o) => o.id !== option.id)
                    )
                }
                value={selectedInsiders}
                multiple
                placeholder="Search team members"
                label="Search team members"
                showLabel={false}
            />

            <Input
                className={"mb-sm mt-sm"}
                type="datetime-local"
                label="Inside Time"
                value={selectedInsideTime}
                onChange={ (x) => setSelectedInsideTime(x.currentTarget.value)}
                onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
                onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
            />


            <div className="pt-2"/>

            {errorsFromEdit.map( (x, idx) => <Alert key={`err_${idx}`} theme="failure" className="pt-1" message={x}/>)}
        </div>
    </StyledDialog>);
}