import { Tab } from "@headlessui/react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { StyledTab } from "components/StyledHeadlessUI/StyledTab";
import { downloadFile } from "helpers";
import { useSession } from "next-auth/react";

import { FunctionComponent, useCallback, useMemo, useState, useEffect } from "react";
import { ApiError, ApiMutationResponse, TAllocation, TOffer, ApiAllocationUploadResponse } from "types";
import { Api } from "utils/api";
import { toApiError } from "utils/dataConversion";
import { downloadAllocationAnnotatedMutator, uploadAllocationMutation, resubmitAllocationMutation, getTask } from "utils/queries";
import { UploadManualAllocationForm } from "../forms/UploadManualAllocationForm";
import { Alert } from "components/Basic/Alert/Alert";
import Decimal from "decimal.js";

interface IProps {
    offer: TOffer;
    allocation: TAllocation;
    isModalOpen: boolean;
    modalOpenTrigger: (open: boolean) => void;
}

export interface FailedAllocationAndOfferBook {
    allocationBookId: string;
    offerId: string;
}

export const EditAllocationDialog: FunctionComponent<IProps> = ({ offer, isModalOpen, modalOpenTrigger }) => {
    const { data: session } = useSession();
    const qc = useQueryClient();
    const [openTab, setOpenTab] = useState(0);
    const [bulkAllocationStep, setBulkAllocationStep] = useState(1);
    const [failedAllocationOfferBook, setFailedAllocationOfferBook] = useState<FailedAllocationAndOfferBook>();
    const [annotatedDownloadError, setAnnotatedDownloadError] = useState<ApiError>();
    const [uploadTaskId, setUploadTaskId] = useState<string>();
    const [taskError, setTaskError] = useState<string>();
    const [loadingProgressText, setLoadingProgressText] = useState<string>();

    const [downloadTemplateError,setDownloadTemplateError] = useState<string>();

    const downloadTemplate = useCallback(async (offerPrice?: Decimal) => {
        if (!offerPrice) {
            setDownloadTemplateError("Please enter a valid price");
            return
        }
        try {
            const data = await Api.downloadPopulatedAllocationTemplate(offer?.id, offerPrice, session?.accessToken);

            const file = new File([data],"RetailBook - allocation upload template",{ type: data.type });

            downloadFile(file);
            setDownloadTemplateError(undefined);
        } catch (e) {
            const blob = (e as AxiosError<Blob>);
            const apiError =  toApiError(await blob.response?.data.text());
            
            if (apiError?.message) {
                setDownloadTemplateError(apiError.message);
            }            
        }
    }, [offer?.id, session?.accessToken]);

    const downloadAnnotatedMutatorOption = useMemo(
        () => downloadAllocationAnnotatedMutator(session?.accessToken),
        [session?.accessToken]
    );

    const onSuccess = useCallback(() => {
        modalOpenTrigger(false);
        setBulkAllocationStep(1);
        qc.invalidateQueries(["allocations", offer.id]);
        qc.invalidateQueries(["ui", "offer", offer.id])
    }, [modalOpenTrigger,offer.id,qc]);
    const onError = (resp: ApiAllocationUploadResponse) => {
        const fieldErrors = resp.field_errors
        if (fieldErrors && fieldErrors?.id && fieldErrors?.offer_id) {
            // A hacky way of determining if an error condition was set through field errors.
            console.log("allocation upload failed - setting fieldErrors", fieldErrors);
            if (fieldErrors.root) {
                setTaskError(fieldErrors.root);
            }
            setFailedAllocationOfferBook({
                allocationBookId: fieldErrors.id,
                offerId: fieldErrors.offer_id,
            });
            setBulkAllocationStep(-2);
        } else if (resp.message) {
            setTaskError(resp.message);
            if (resp.cause?.message) {
                setTaskError(resp.message + " - " + resp.cause?.message);
            }
        }
    }
    
    const uploadAllocationMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>, FormData>({
        ...uploadAllocationMutation(offer.id, session?.accessToken),
        onSuccess: (data) => {setUploadTaskId(data.id)},
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        onError: (e) => {
            setTaskError(e.response?.data.message ?? e.message)
        }
    });

    const { data: task } = useQuery(
        getTask(uploadTaskId??"", session?.accessToken)
    );

    useEffect( () => {
        if (task) {
            if (task.status == "success") {
                setLoadingProgressText(undefined);
                setUploadTaskId(undefined);
                onSuccess();
            } else if (task.status == "failed") {
                setLoadingProgressText(undefined)
                setUploadTaskId(undefined);
                setTaskError(task.message);
                const apiError = task.response as ApiAllocationUploadResponse;
                if (apiError) {
                    onError(apiError);
                }
            } else {
                if (task?.message) {
                    setLoadingProgressText(task.message);
                }
            }
        }
    }, [task, onSuccess]);

    const resubmitAllocationMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>, string>({
        ...resubmitAllocationMutation(offer.id, session?.accessToken),
        onSuccess: onSuccess,
        onError: (err) => {
            if (err.response) {
                onError(err.response?.data.detail as ApiAllocationUploadResponse);
            }
        }
    });

    const downloadAnnotatedMutator = useMutation({
        ...downloadAnnotatedMutatorOption,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSuccess: (data: any) => {
            // Download the file.
            const file = new File([data], `rb_allocation_${offer?.id}`, {
                type: data.type,
            });
            downloadFile(file);

            // Return back to the first step.
            setBulkAllocationStep(1);
        },
        onError: async (blob: AxiosError<Blob>) => {
            setAnnotatedDownloadError(toApiError(await blob.response?.data.text()));
        },
    });

    const uploadSheet = useCallback(
        (files: File[], offerPrice?: Decimal) => {
            const formData = new FormData();
            formData.append("file", files[0], files[0].name);
            formData.append("offer_price", offerPrice?.toString() ?? "");
            uploadAllocationMutator.mutate(formData);
        },
        [uploadAllocationMutator]
    );
    
    const forceUploadSheet = useCallback(
        () => {
            setTaskError(undefined);
            resubmitAllocationMutator.mutate(failedAllocationOfferBook?.allocationBookId??"");
        },
        [resubmitAllocationMutator, failedAllocationOfferBook?.allocationBookId]
    );

    const downloadAnnotated = (allocationBookId: string) => {
        downloadAnnotatedMutator.mutate({ offerId: offer.id, allocationBookId: allocationBookId })
    };

    const customClose = useMemo(() => {
        return () => {
            setOpenTab(0);
            modalOpenTrigger(false);
            setBulkAllocationStep(1);
            setDownloadTemplateError(undefined);
            setAnnotatedDownloadError(undefined);
            setUploadTaskId(undefined);
            setTaskError(undefined);

            uploadAllocationMutator.reset();
            resubmitAllocationMutator.reset();
            downloadAnnotatedMutator.reset();
        }
    }, [modalOpenTrigger, setBulkAllocationStep, uploadAllocationMutator, resubmitAllocationMutator, downloadAnnotatedMutator]);

    return (
        <>
            <StyledDialog open={isModalOpen}
                fullWidth
                onClose={customClose}
                unmount={false}
                title={`Update Allocation`}>

                <Tab.Group defaultIndex={0}>
                    <Tab.List className="tab-list tab-list--bordered">
                        <StyledTab disabled={openTab != 0}>
                            Upload list
                        </StyledTab>
                        {/* TODO: straight line and other tabs go here. */}
                    </Tab.List>

                    {/* Upload list tab */}
                    <Tab.Panels className="pt-lg">
                        <Tab.Panel>
                            <UploadManualAllocationForm
                                isUploading={uploadAllocationMutator.isLoading || !!uploadTaskId}
                                isDownloading={downloadAnnotatedMutator.isLoading}
                                onDrop={uploadSheet}
                                onForceUpload={forceUploadSheet}
                                onDownloadTemplate={downloadTemplate}
                                onDownloadTemplateError={downloadTemplateError}
                                onDownloadAnnotated={downloadAnnotated}
                                setStep={setBulkAllocationStep}
                                step={bulkAllocationStep}
                                failedAllocationOfferBook={failedAllocationOfferBook}
                                errorMessage={taskError}
                                loadingProgressText={loadingProgressText}
                                offerPrice={offer.offer_price}
                            />
                        </Tab.Panel>
                    </Tab.Panels>
                </Tab.Group>

                <StyledDialog open={!!annotatedDownloadError} title="Spreadsheet Download Error" onClose={() => setAnnotatedDownloadError(undefined)}>
                    <Alert theme="failure" message={annotatedDownloadError?.message} />
                </StyledDialog>

            </StyledDialog>
        </>
    )
}
