import { ajvResolver } from "@hookform/resolvers/ajv";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { Prose } from "components/Basic/Prose/Prose";
import { Select } from "components/Basic/Select/Select";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { DropUpload } from "components/Bespoke/DropUpload/DropUpload";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { Dispatch, FunctionComponent, SetStateAction, useEffect, useMemo, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { ApiError, ApiMutationResponse, TOffer, UploadResource } from "types";
import { getDocumentTypesQuery, uploadDocumentMutation } from "utils/queries";

interface IProps {
    open: boolean
    setOpen: Dispatch<SetStateAction<boolean>>
    accessToken?: string
    offer : TOffer
}

interface DocumentUploadFormData {
    file?: File;    
    file_name: string;
    title: string;
    type: string;
}

const schema = {
    type: "object",
    properties: {
      title: {
        type: "string",
        minLength: 1,
        errorMessage: { minLength: "title field is required" },
      },
      type: {
        type: "string",
        minLength: 1,
        errorMessage: { minLength: "type field is required" },
      },
    },
    required: ["title", "type"],
};

const defaultValues: DocumentUploadFormData = {
    title: "",
    type: "",
    file_name: "",
}

const truncate = (str:string) => {
  if (str.length > 50) {
    return str.substring(0, 50) + "..."
  }
  return str
}

export const DocumentUploadDialog:FunctionComponent<IProps> = ({ open, setOpen, accessToken, offer}) => {
    const qc = useQueryClient();
  
    const [errorText,setErrorText] = useState("")

    const { register, formState, setValue, control, reset, handleSubmit, setFocus } =
        useForm<DocumentUploadFormData>({
            defaultValues,
            resolver: ajvResolver(schema),
    });

    const selectedFile = useWatch({ control: control, name: "file"})
    
    useEffect( () => {
      setValue("file_name", selectedFile?.name ?? "")
    }, [setValue, selectedFile] )

    const { data: documentTypes } = useQuery(
        getDocumentTypesQuery(accessToken)
      );


    // React Query Mutations
    const uploadDocumentMutationOptions = useMemo(
        () => uploadDocumentMutation(offer.id, qc, accessToken),
        [offer.id, qc, accessToken]
    );

    const uploadDocumentMutator = useMutation<ApiMutationResponse,AxiosError<ApiError>,Partial<UploadResource>>({
        ...uploadDocumentMutationOptions,
        onSuccess: () => {
          CloseDialog()
        },
        onError: (err) => {
          setFlowState(1);
          setErrorText(truncate(err.response?.data.message ?? "Server error"));
        }
    });


    const [ flowState, setFlowState ] = useState(0);
    
    const dropHidden = useMemo( () => {
      return flowState === 1
    },[flowState])
    
    useEffect(() => {
      if (dropHidden) {
        setFocus("title");
      }
    }, [dropHidden, setFocus]);

    const CloseDialog = () => {
      setErrorText("")
      setOpen(false)
      setFlowState(0)
      reset()    
    }
  
    return (
        <StyledDialog
            open={open}
            onClose={CloseDialog}
            unmount={false}
            title={"Upload document"}
            footer={

            <div className="flex gap-xs">            
            { dropHidden && !uploadDocumentMutator.isLoading && (
               <Button
                  type="button"
                  disabled={!formState.isDirty || formState.isSubmitting || !formState.isValid}
                  loading={formState.isSubmitting}
                  onClick = { handleSubmit( (formData) => {
                    const file = formData.file;
                    uploadDocumentMutator.mutate({ file: file, title: formData.title, type: formData.type },uploadDocumentMutationOptions);
                    setFlowState(2);
                  })}> Upload </Button>
            ) }
            
            { !uploadDocumentMutator.isLoading && (
              <Button
                theme="outline"
              type="button"
              onClick={CloseDialog}                
            >
              Cancel
            </Button> )}

            <Prose className="text-red text-ellipsis">{errorText}</Prose>
          </div>
        }
      >
        <div className="w-screen max-w-screen-sm">
            { !uploadDocumentMutator.isLoading && flowState !== 2 && open && (

            <form className="form">

              <div className={ dropHidden ? "hidden" : "" }>
                <DropUpload onDrop={ (f) => { 
                  setValue("file", f[0]);
                  setFlowState(1);
                }} />
              </div>
              
              <div className={ !dropHidden ? "hidden" : ""}>
                <Input disabled label="File name" {...register("file_name")} />
                  <section className="form-section">
                    <fieldset className="form-fieldset">
                      <Input
                        label="Title"
                        {...register("title")}
                        error={formState.errors.title?.message}
                      />
                      <Select
                        label="Select a type"
                        {...register("type")}
                        error={formState.errors.type?.message}
                      >
                      {documentTypes?.sort((x,y) => x.label.localeCompare(y.label))?.map((type) => (
                          <option key={type.label} value={type.value}>
                           {type.label}
                          </option>
                      ))}
                      </Select>
                      
                    </fieldset>
                  </section>
                </div>
            </form>

            )}

            { uploadDocumentMutator.isLoading && (
              <div className="flex items-center justify-center">                      
              { flowState === 2 && <Spinner message="Uploading..." /> }
            </div>
            )}
          
        </div>
      </StyledDialog>


    )
}