import { useMutation } from "@tanstack/react-query";
import {  AxiosError } from "axios";
import { Button } from "components/Basic/Button/Button";
import { Textarea } from "components/Basic/Textarea/Textarea";
import { StyledCombobox, StyledComboboxOption } from "components/StyledHeadlessUI/StyledCombobox";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { Dispatch, FunctionComponent, SetStateAction, useMemo, useState } from "react";
import { ApiMutationResponse, InviteResponse, TOffer, TSendNotificationArgs, ApiError } from "types";
import { Api } from "utils/api";


interface IProps {
    open: boolean
    
    setOpen: Dispatch<SetStateAction<boolean>>    
    offer: TOffer
    accessToken?: string
    invitedIntermediaries?: InviteResponse[]
}

export const NotifyIntermediariesDialog:FunctionComponent<IProps> = ({open, setOpen, offer, invitedIntermediaries, accessToken}) => {
    
    const [selectedIntermediaries, setSelectedIntermediaries] = useState< StyledComboboxOption<InviteResponse>[]>([])
    const [notificationMessage, setNotificationMessage] = useState("")

    const sendNotificationMutator = useMutation<ApiMutationResponse, AxiosError<ApiError>, TSendNotificationArgs>(
        {
            mutationFn: (args?: TSendNotificationArgs) => {
                return Api.sendNotifications(args?.offerId ?? "", args?.message ?? "", args?.intermediarySystemIds ?? [], accessToken);
            },            
            onSuccess: () => {
                setOpen(false);
                setSelectedIntermediaries([]);
            },
            // eslint-disable-next-line  @typescript-eslint/no-empty-function
            onError: () => {}
        }
    );


    const intermediaryOptions = useMemo<StyledComboboxOption<InviteResponse>[]>(
        () => {
            return (invitedIntermediaries ?? []).map( (x) => {
                return {
                    id: x.intermediary_system_id,
                    label: x.intermediary_display_name,
                    value: x
                }
            }
            )
        },
        [invitedIntermediaries]
    )

    const intermediaryOptionsFiltered = useMemo(
        () =>
          (intermediaryOptions ?? []).filter(
            (option) =>
              !selectedIntermediaries.find((selected) => option.id === selected.id)
          ),
        [intermediaryOptions, selectedIntermediaries]
    );

    return (<StyledDialog
        open={open}
        onClose={() => {setSelectedIntermediaries([]); setOpen(false); sendNotificationMutator.reset();}}
        title="Notify of changes to this offer"
        footer={
            <div className="flex gap-xs">
                <Button
                    type="button"
                    disabled={!notificationMessage || !selectedIntermediaries.length}
                    onClick={ () => {
                        sendNotificationMutator.mutate({
                            offerId: offer.id,
                            message: notificationMessage,
                            intermediarySystemIds: selectedIntermediaries.map((opt) => opt.value.intermediary_system_id)
                        })
                    }}                                        
                >
                    Send
                </Button>
                <Button
                    disabled={!intermediaryOptionsFiltered.length}
                    type="button"
                    onClick={() => {
                        setSelectedIntermediaries((selected) => [
                            ...selected,
                            ...intermediaryOptionsFiltered,
                        ]);
                    }}
                >
                    Select all
                </Button>
                <Button
                    theme="outline"
                    type="button"
                    onClick={() => {
                        setOpen(false);
                        setSelectedIntermediaries([]);
                        sendNotificationMutator.reset();
                    }}
                >
                    Cancel
                </Button>
            </div>
        }
    >
        <div className="w-screen max-w-screen-sm">
            <p className="body mb-sm">
                These intermediaries will be sent a notification of your message.
            </p>
            <div className="form-section">
                <div>
                    <StyledCombobox
                        options={intermediaryOptionsFiltered}
                        onChange={(options: StyledComboboxOption<InviteResponse>[]) =>
                            setSelectedIntermediaries(options)
                        }
                        onRemove={(opt) =>
                            setSelectedIntermediaries((existing) =>
                                existing.filter((o) => o.id !== opt.id)
                            )
                        }
                        value={selectedIntermediaries}
                        multiple
                        placeholder="Search intermediaries"
                        label="Send notification to"
                    />
                </div>
                <Textarea
                    label="Message"
                    rows={4}
                    onChange={(event) => setNotificationMessage(event.target.value)}
                />
                <p className="text-[#FF0000]">{sendNotificationMutator.error?.response?.data.message}</p>
            </div>
        </div>
    </StyledDialog>)
}