import { useQuery } from "@tanstack/react-query";
import { Button } from "components/Basic/Button/Button";
import { StyledCombobox, StyledComboboxOption} from "components/StyledHeadlessUI/StyledCombobox";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { useSession } from "next-auth/react";
import { FunctionComponent, useCallback, useMemo, useState } from "react";
import { IntermediaryResponse, ApiMutationResponse, Invite } from "types";
import {getStaticIntermediariesQuery} from "utils/queries";

interface Intermediary {
  system_id: string;
  display_name: string;
}

interface IProps {
  description?: string;
  open: boolean;
  isLoading: boolean;
  invites: Invite[];
  onInvite: (invites: Invite[]) => Promise<ApiMutationResponse>
  onClose: () => void;
}

export const ChooseIntermediariesDialog: FunctionComponent<IProps> = ({
  description,
  open,
  isLoading,
  invites,
  onInvite,
  onClose,
}) => {
  const { data: session } = useSession();
  
  const [selectedIntermediaries, setSelectedIntermediaries] = useState<StyledComboboxOption<Intermediary>[]>([]);
  const { data: intermediaryOptions } = useQuery({
    ...getStaticIntermediariesQuery(session?.accessToken),
    select: useCallback(
      (data: IntermediaryResponse) =>
        data.map((intermediary) => ({
          id: intermediary.system_id,
          label: intermediary.display_name,
          value: intermediary,
        })),
      []
    ),
  });

  const intermediaryOptionsFiltered = useMemo(
    () =>
      (intermediaryOptions ?? []).filter((option) => {
        if (
          selectedIntermediaries.find((selected) => option.id === selected.id)
        ) {
          return false;
        }
        if (
            invites.find(
            (i) => option.id === i.intermediary_system_id
          )
        ) {
          return false;
        }
        return true;
      }),
    [intermediaryOptions, invites, selectedIntermediaries]
  );

  return (<StyledDialog
        open={open}
        onClose={onClose}
        title="Invite intermediaries"
        footer={
          <div className="flex gap-xs">
            <Button
              type="button"
              disabled={!selectedIntermediaries.length}
              loading={isLoading}
              onClick={async (e) => {
                e.preventDefault();
                await onInvite(
                  selectedIntermediaries?.map((opt) => ({
                    intermediary_system_id: opt.value.system_id,
                    intermediary_display_name: opt.value.display_name,
                  }))
                );
                onClose();
                setSelectedIntermediaries([]);
              }}
            >
              Invite ({selectedIntermediaries.length})
            </Button>
            <Button
              type="button"
              disabled={
                isLoading ||
                !intermediaryOptionsFiltered.length
              }
              onClick={() => {
                setSelectedIntermediaries((selected) => [
                  ...selected,
                  ...intermediaryOptionsFiltered,
                ]);
              }}
            >
              Select all
            </Button>
            <Button
              theme="outline"
              type="button"
              onClick={() => {
                onClose();
                setSelectedIntermediaries([]);
              }}
            >
              Cancel
            </Button>
          </div>
        }
      >
        <div className="w-screen max-w-screen-sm">
          {description && <p className="body mb-sm">{description}</p>}
          <StyledCombobox
            options={intermediaryOptionsFiltered}
            onChange={(options: StyledComboboxOption<Intermediary>[]) =>
              setSelectedIntermediaries(options)
            }
            onRemove={(opt) =>
              setSelectedIntermediaries((existing) =>
                existing.filter((o) => o.id !== opt.id)
              )
            }
            value={selectedIntermediaries}
            multiple
            placeholder="Search intermediaries"
            label="Search intermediaries"
            showLabel={false}
          />
        </div>
      </StyledDialog>
  );
};
