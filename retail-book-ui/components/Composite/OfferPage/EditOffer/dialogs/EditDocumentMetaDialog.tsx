import { ajvResolver } from "@hookform/resolvers/ajv";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { Select } from "components/Basic/Select/Select";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { useFieldErrors } from "hooks/useFieldErrors";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { Dispatch, FunctionComponent, SetStateAction, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { ApiError, ApiMutationResponse, Resource, TOffer } from "types";
import { getDocumentTypesQuery, updateDocumentMetaMutation } from "utils/queries";

interface IProps {
    open: boolean
    setOpen: Dispatch<SetStateAction<boolean>>
    accessToken?: string
    offer: TOffer
    document?: Resource
}

interface DocumentEditFormData {    
    title: string;
    type: string;
    file_name: string;
}

const schema = {
    type: "object",
    properties: {
        title: {
            type: "string",
            minLength: 1,
            errorMessage: { minLength: "title field is required" },
        },
        type: {
            type: "string",
            minLength: 1,
            errorMessage: { minLength: "type field is required" },
        },
    },
    required: ["title", "type"],
};

export const DocumentEditDialog: FunctionComponent<IProps> = ({
    open,
    setOpen,
    accessToken,
    offer,
    document

}) => {
    const qc = useQueryClient();

    const { data: documentTypes } = useQuery(
        getDocumentTypesQuery(accessToken)
    );

    const defaultValues: DocumentEditFormData = {
        title: document?.title ?? "",
        type: document?.type ?? "",
        file_name: ""
    }

    const { register, formState, setValue, setError, reset, handleSubmit, clearErrors } =
        useForm<DocumentEditFormData>({
            defaultValues,
            resolver: ajvResolver(schema),
        });

    const updateDocumentMetaMutationOptions = useMemo(
        () =>
            updateDocumentMetaMutation(
                offer.id,
                document?.id ?? "",
                qc,
                accessToken
            ),
        [offer.id, document, qc, accessToken]
    );

    const updateDocumentMetaMutator = useMutation<
        ApiMutationResponse,
        AxiosError<ApiError>,
        Partial<Resource>
    >({
        ...updateDocumentMetaMutationOptions,
        onSuccess: () => {                    
            CloseDialog();
        },
        //eslint-disable-next-line  @typescript-eslint/no-empty-function
        onError: () => {}
    });

    const CloseDialog = () => {
        setOpen(false)      
        reset()
    }

    useEffect(() => {
        reset()
        setValue("title", document?.title ?? "")
        setValue("type", document?.type ?? "")
    },[document, reset, setValue])

    const fieldErrors = useFieldErrors(updateDocumentMetaMutator.error);
    const { errors } = formState;
    useReactHookFormServerErrors<DocumentEditFormData>(fieldErrors, errors, setError, clearErrors);

    return (
        <StyledDialog
            open={open}
            onClose={CloseDialog}
            unmount={false}
            title={"Edit Document"}
            footer={
                <div className="flex gap-xs">
                    
                        <Button
                            type="button"
                            disabled={!formState.isDirty || formState.isSubmitting || !formState.isValid}
                            loading={formState.isSubmitting}
                            onClick={ handleSubmit((formData) => updateDocumentMetaMutator.mutate({ title: formData.title, type: formData.type }, updateDocumentMetaMutationOptions)) }> Edit </Button>                    

                        <Button
                            theme="outline"
                            type="button"
                            onClick={CloseDialog}
                        >
                            Cancel
                        </Button>

                        <p className="pt-1 text-[#FF0000]">{updateDocumentMetaMutator.error?.response?.data.message}</p>
                </div>
            }
        >
            <div className="w-screen max-w-screen-sm">
                <form className="form">
                    <Input disabled label="File name" value={document?.file_name ?? ""} error={formState.errors.file_name?.message} />
                    <section className="form-section">
                        <fieldset className="form-fieldset">
                            <Input
                                label="Title"
                                {...register("title")}                                
                                error={formState.errors.title?.message}
                            />
                            <Select
                                label="Select a type"
                                {...register("type")}
                                error={formState.errors.type?.message}
                            >
                                {documentTypes?.sort((x,y) => x.label.localeCompare(y.label))?.map((type) => (
                                    <option key={type.label} value={type.value}>
                                        {type.label}
                                    </option>
                                ))}
                            </Select>
                        </fieldset>
                    </section>

                </form>

            </div>
        </StyledDialog>)
}