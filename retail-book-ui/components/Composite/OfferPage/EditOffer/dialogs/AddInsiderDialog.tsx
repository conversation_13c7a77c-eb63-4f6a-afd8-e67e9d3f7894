import {FunctionComponent, useMemo, useState} from "react";
import {useSession} from "next-auth/react";
import {
    ApiError,
    ApiMutationResponse,
    EInsiderEvent, EInsiderStatus,
    TNewInsider,
    TOffer
} from "../../../../../types";
import {Button} from "../../../../Basic/Button/Button";
import {formatIsoFromLocal} from "../../../../../helpers";
import {AxiosError} from "axios";
import {Input} from "../../../../Basic/Input/Input";
import {StyledDialog} from "../../../../StyledHeadlessUI/StyledDialog";
import {useMutation, useQueryClient} from "@tanstack/react-query";
import {createOfferInsiderMutation} from "../../../../../utils/queries";
import {useForm} from "react-hook-form";
import {Alert} from "../../../../Basic/Alert/Alert";
import {ajvResolver} from "@hookform/resolvers/ajv";

interface IProps {
    offer: TOffer;
    isModalOpen: boolean;
    modalOpenTrigger: (open: boolean) => void;
}

interface NewInsiderFormData {
    user_name: string;
    user_email: string;
    inside_time: string;
}

const defaultValues: NewInsiderFormData = {
    user_name: "",
    user_email: "",
    inside_time: "",
}

const schema = {
    type: "object",
    properties: {
        user_name: {
            type: "string",
            minLength: 1,
            errorMessage: { minLength: "Required" },
        },
        user_email: {
            type: "string",
            minLength: 1,
            errorMessage: { minLength: "Required" },
        },
        inside_time: {
            type: "string",
            minLength: 1,
            errorMessage: { minLength: "Required" },
        }
    },
    required: [
        "user_name",
        "user_email",
        "inside_time"
    ],
};

export const AddInsiderDialog: FunctionComponent<IProps> = ({ offer, isModalOpen, modalOpenTrigger }) => {
    const {data: session} = useSession();
    const qc = useQueryClient();

    const [errorFromAdd, setErrorFromAdd] = useState("");

    const { register, formState, reset, handleSubmit } =
    useForm<NewInsiderFormData>({
        defaultValues,
        resolver: ajvResolver(schema)
    });

    const resetClose = useMemo(() => {
        return () => {
            modalOpenTrigger(false);
            reset();
            setErrorFromAdd("");
        }
    }, [modalOpenTrigger, reset, setErrorFromAdd]);

    const { mutate:mutation } = useMutation<ApiMutationResponse, AxiosError<ApiError>, TNewInsider>(
        {...createOfferInsiderMutation(qc, offer.id, session?.accessToken),
            onSuccess: () => {
                qc.invalidateQueries(["insiders", offer.id]);
                resetClose()
            },
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            onError: (err) => {
                setErrorFromAdd(`Could not add insider - ${(err as AxiosError<ApiError>).response?.data.message ?? "unknown"}`)
            }
        }
    );

    const handleInsiderCreation = (data: NewInsiderFormData) => {
        mutation({
            ...data,
            inside_time: formatIsoFromLocal(data.inside_time) ?? "",
            event: EInsiderEvent.MANUAL_ADD,
            status: EInsiderStatus.AUTO,
        });
    };

    const { errors, isDirty, isSubmitting } = formState;



    return (<StyledDialog
        open={isModalOpen}
        onClose={() => resetClose()}
        title="Add insider"
        footer={
            <div className="flex gap-xs">
                <Button
                    type="button"
                    disabled={!isDirty || isSubmitting}
                    loading={isSubmitting}
                    onClick={handleSubmit((formData) => {
                        handleInsiderCreation(formData)
                    })}
                >
                    Add
                </Button>
                <Button
                    theme="outline"
                    type="button"
                    onClick={() => {
                        resetClose()
                    }}
                >
                    Cancel
                </Button>
            </div>
        }>
        <div className="w-screen max-w-screen-sm">
            <p className="body mb-sm">Record an inside event for an off-platform user</p>
            <form className="form">
                <Input
                    {...register("user_name")}
                    label="Name"
                    error={errors.user_name?.message}
                />
                <Input
                    {...register("user_email")}
                    label="Email"
                    error={errors.user_email?.message}
                />
                <Input
                    {...register("inside_time")}
                    className={"mb-sm"}
                    type="datetime-local"
                    label="Inside Time"
                    onInvalid={e => (e.target as HTMLInputElement).setCustomValidity('Invalid date')}
                    onInput={e => (e.target as HTMLInputElement).setCustomValidity('')}
                    max="9999-12-31T23:59"
                    error={errors?.inside_time?.message}
                />
            </form>

            {errorFromAdd ? <Alert key={`err_add_insider`} theme="failure" className="pt-1" message={errorFromAdd}/> : ""}
        </div>
    </StyledDialog>);
}