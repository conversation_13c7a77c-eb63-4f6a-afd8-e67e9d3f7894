import { Button } from "components/Basic/Button/Button";
import { Definition } from "components/Basic/Definition/Definition";
import { ElementType, useMemo, useState } from "react";
import { TOffer, TOfferHeadingFieldMessagePair, TOfferUIStateField, TOfferUIStateOperation } from "types";
import { OfferHeaderAlert } from "../OfferHeaderAlert";
import { AlertTriangle, ChevronLeft, ChevronRight, Pause } from "react-feather";
import { StateChangeDialog } from "./dialogs/StateChangeDialog";


// @TODO this is a bit of a brittle way to style buttons but they're driven by the API
// Would need a "primary/secondary" action flag on the operations or something to do it more robustly
const getOperationButtonTheme = (label: string) => {
  return ["Back", "Hold", "Abandon"].includes(label) ? "outline" : undefined;
};


const getOperationIcon = (label: string):ElementType => {
  switch(label) {
    case "Continue": {
      return ChevronLeft;
    }
    case "Back": {
      return ChevronLeft;
    }
    case "Hold": {
      return Pause;
    }
    case "Abandon": {
      return AlertTriangle;
    }
  }
  return ChevronRight;
}

const getOperationIconPosition = (label:string) => {
  switch(label) {
    case "Back": return "before";
    case "Continue": return "before";
    case "Hold": return "before";
    case "Abandon": return "before";
  }
  return "after"
}

interface OfferHeaderProps {
  offer: TOffer;
  operations: TOfferUIStateOperation[];
  errors: TOfferUIStateField[];
}

export const EditOfferHeader = ({
  offer,
  operations,
  errors,
}: OfferHeaderProps) => {
  const [operation, setOperation] = useState<TOfferUIStateOperation | null>(
    null
  );
  const [stateChangeOpen, setStateChangeOpen] = useState<boolean>(false);

  const errorPairs = useMemo( () => {
    const errorPairs: TOfferHeadingFieldMessagePair[] = [];
    if (errors && errors.length > 0) {
      for (const error of errors) {
        if (error.error) {
          errorPairs.push({ field: error.field, isError: true, message: error.error })
        }
        // Some fields like document can have a list for error and a list for warning
        if (error.warning) {
          errorPairs.push({ field: error.field, isError: false, message: error.warning})
        }
      }
    }
    return errorPairs
  }, [errors])
  
  return (
    <>
      <header className="edit-offer-header">
        <div className="edit-offer-header__main">
          <h1 className="edit-offer-header__title">{offer.name}</h1>
        </div>

        <div className="edit-offer-header__sidebar">
          <div>
            <Definition
              term="Status"
              description={offer.status}
              className="edit-offer-header__status"
            />

            {  }

            {errorPairs.length > 0 && (
              <OfferHeaderAlert
                headingMessage="To progress this offer to the next stage please ensure that 
              the below issues are resolved."
                fieldMessages={errorPairs}
              />
            )}
          </div>

          {operations && operations.length > 0 && (
            <div className="edit-offer-header__operations">
              {operations.map((op) => (
                <Button
                  key={op.label}
                  disabled={!op.enabled}
                  type="button"
                  theme={getOperationButtonTheme(op.label)}
                  icon={getOperationIcon(op.label)}
                  iconPosition={getOperationIconPosition(op.label)}
                  onClick={() => {  setOperation(op); setStateChangeOpen(true); } }
                >
                  {op.label}
                </Button>
              ))}
            </div>
          )}

          
        </div>
      </header>

      <StateChangeDialog open={stateChangeOpen} onClose={ () => {setStateChangeOpen(false);} } operation={operation} offer={offer} />      
    </>
  );
};
