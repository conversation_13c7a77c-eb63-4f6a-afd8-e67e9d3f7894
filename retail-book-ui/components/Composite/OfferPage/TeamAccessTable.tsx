import {
  Column,
  StyledReactDataGrid,
} from "components/StyledReactDataGrid/StyledReactDataGrid";
import { useMemo } from "react";
import { TUser } from "types";

interface UserRow {
  id: string;
  name: string;
  timestamp: string,
}

const columns: Column<UserRow>[] = [
  {
    key: "name",
    name: "Name",
    sortable: true,
    comparator: (a, b) => a.name.localeCompare(b.name),
  },
  {
    key: "timestamp",
    name: "Date Added",
    sortable: false
  },
];

function getPrettyTime(timestamp: string): string {
  if (timestamp == undefined) {
      return ""
  }

  const date = new Date(Number(timestamp))
  return date.toLocaleDateString() + " " + date.toLocaleTimeString() 
}

export const TeamAccessTable = ({
  users,
  onRemove,
}: {
  users: TUser[];
  onRemove: (row: UserRow) => void;
}) => {
  const rows = useMemo(
    () =>
      users.map((user) => {
        return {
          id: user.id,
          name: user.name,
          timestamp: getPrettyTime(user.role_timestamps)
        };
      }),
    [users]
  );

  return (
    <StyledReactDataGrid
      className="mb-sm"
      rows={rows}
      columns={columns}
      contextMenuItems={[
        {
          label: "Remove",
          onClick: (e, row) => {
            e.preventDefault();
            onRemove(row);
          },
        },
      ]}
    />
  );
};
