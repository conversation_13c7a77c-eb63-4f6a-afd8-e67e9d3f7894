import Decimal from "decimal.js"
import { TOfferUIStateField } from "types"
import {formatIsoFromLocal} from "../../../../helpers";

enum ETabName {
    OVERVIEW = "Overview",

    DOCUMENT = "Documents",
    SETTLEMENT_DETAILS = "Settlements Details",
    ORDERS = "Orders",
    ALLOCATIONS = "Allocations",
    SETTLEMENTS = "Settlements",

    UNKNOWN = "Unknown"
}

export interface OfferTabPanelProps {
    tabName?: string;
    tabPanel: JSX.Element;
    disabled?: boolean;
}

export type TOfferTabFrequency = {
    Overview: number,
    Document: number,
    Settlement: number
}

type TOfferField = {
    label: string,
    tab: ETabName
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const fieldMap: any = {
    open_date: { label: "Open Date", tab: ETabName.OVERVIEW },
    close_date: { label:  "Close Date", tab: ETabName.OVERVIEW },
    document: { label: "Documents", tab: ETabName.DOCUMENT },
    name: { label: "Name", tab: ETabName.OVERVIEW },
    type: { label: "Type", tab: ETabName.OVERVIEW },
    issued_by: {label : "Issuer", tab: ETabName.OVERVIEW },
    offer_price: { label: "Offer Price", tab: ETabName.OVERVIEW },
    min_order_amount: { label:"Minimum Order", tab: ETabName.OVERVIEW },
    raise_amount: { label:"Raise Amount", tab: ETabName.OVERVIEW },
    registration_date: { label:"Registration Date", tab: ETabName.OVERVIEW },
    details: { label:"Offer Details", tab: ETabName.OVERVIEW },
    description: { label:"Short Description", tab: ETabName.OVERVIEW },
    allocation_principles: { label:"Allocation Principles", tab: ETabName.OVERVIEW },
    ticker: { label:"Ticker", tab: ETabName.SETTLEMENT_DETAILS },
    isin: { label:"ISIN", tab: ETabName.SETTLEMENT_DETAILS },
    sedol: { label:"SEDOL", tab: ETabName.SETTLEMENT_DETAILS },
    crest_id: { label:"Crest_ID", tab: ETabName.SETTLEMENT_DETAILS },
    settlement_details: { label:"Settlement Details", tab: ETabName.SETTLEMENT_DETAILS },
    orders: { label:"Orders", tab: ETabName.ORDERS },
    allocations: { label:"Allocations", tab: ETabName.ALLOCATIONS },
    price_range_low: { label: "Price Range Low", tab: ETabName.OVERVIEW },
    price_range_high: { label: "Offer Price High", tab: ETabName.OVERVIEW },
    settlement_date: { label: "Settlement Date", tab: ETabName.SETTLEMENT_DETAILS },
  }
  
  
export const OfferFieldLabel = (fieldname:string):string => {
    return (fieldMap[fieldname] as TOfferField)?.label ?? fieldname
}

export type TTabFrequencyTable = {
    Overview: number;
    Document: number;
    SettlementDetails: number;
    Orders: number;
    Allocations: number;
    Settlements: number;
}

const TabFrequencyReducer = (freqTable: TTabFrequencyTable, tab: ETabName)=> {            
    switch(tab) {
        case ETabName.OVERVIEW:
            freqTable.Overview += 1;
            break;
        case ETabName.DOCUMENT:
            freqTable.Document += 1;
            break;
        case ETabName.SETTLEMENT_DETAILS:
            freqTable.SettlementDetails += 1;
            break;
        case ETabName.ORDERS:
            freqTable.Orders += 1;
            break;
        case ETabName.ALLOCATIONS:
            freqTable.Allocations += 1;
            break;
        case ETabName.SETTLEMENTS:
            freqTable.Allocations += 1;
            break;
    }
    return freqTable
};

const EmptyFreqTable = ():TTabFrequencyTable => { return {Overview: 0, Document: 0, SettlementDetails: 0, Orders: 0, Allocations: 0, Settlements: 0}}


export const ErrorCountByTab = (errors : TOfferUIStateField[]) => {        
    return errors.filter(x => !!x.error).map( x => (fieldMap[x.field] as TOfferField)?.tab ?? ETabName.UNKNOWN).reduce(TabFrequencyReducer,EmptyFreqTable())       
}

export const WarningCountByTab = (errors : TOfferUIStateField[]) => {    
    return errors.filter(x => !!x.warning).map( x => (fieldMap[x.field] as TOfferField)?.tab ?? ETabName.UNKNOWN).reduce(TabFrequencyReducer,EmptyFreqTable())       
}

export function setOrClearIfDirty<T>(dirty?:boolean, arg?:T): T|null|undefined {
    return dirty ? (arg ? arg : null) : undefined
}
  
export function setOrClearIfDirtyDecimal(dirty?:boolean, arg?:number|null) : Decimal|null|undefined {
    const val = setOrClearIfDirty(dirty, arg)
    if (val === null || val === undefined) {
      return val
    }
    return new Decimal(val)
}
  
export function setOrClearIfDirtyDate(dirty?:boolean, arg?:string): string| null |undefined {
    const val = setOrClearIfDirty(dirty, arg)
    return formatIsoFromLocal(val)
}
