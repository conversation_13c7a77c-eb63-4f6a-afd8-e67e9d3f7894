import { Alert } from "components/Basic/Alert/Alert";
import { AlertCircle, AlertTriangle } from "react-feather";
import { TOfferHeadingFieldMessagePair } from "types";
import { OfferFieldLabel } from "./model/OfferFieldModel";

interface OfferHeaderAlertProps {
    headingMessage: string;
    fieldMessages?: TOfferHeadingFieldMessagePair[];
    isBasic?: boolean;
    descriptionTextSize?: EOfferHeaderAlertDescriptionTextSize;
}

export enum EOfferHeaderAlertDescriptionTextSize {
    Small = "text-sm",
    Normal = "text-base"
}

export const OfferHeaderAlert = ({
    headingMessage,
    fieldMessages,
    isBasic = true,
    descriptionTextSize = EOfferHeaderAlertDescriptionTextSize.Small,
}: OfferHeaderAlertProps) => {
    return (
        <>
            {!isBasic ? 
                <Alert
                theme="failure"
                title=""
                message={headingMessage}
                />
                    :
                <p className={`edit-offer-header__checklist-description ${descriptionTextSize}`}>
                    {headingMessage}
                </p>
            }

            {fieldMessages &&
                <ul className="edit-offer-header__checklist">
                    {fieldMessages.map(({ field, isError, message = "" }) => (
                        <li
                            key={field + isError + message}
                            className="edit-offer-header__checklist-item"
                        >
                            {
                                isError ?
                                    <AlertCircle aria-hidden="true" className="edit-offer-header__checklist-icon edit-offer-header__checklist-icon--invalid"/> :
                                    <AlertTriangle aria-hidden="true" className="edit-offer-header__checklist-icon edit-offer-header__checklist-icon--warning" />
                            }
                            

                            {OfferFieldLabel(field)} {message != "" ? `- ${message}` : ""}
                        </li>
                    ))}
                </ul>
            }
        </>
    );
};