import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { Stat, StatProps } from "components/Bespoke/Stat/Stat";
import { OrderAggregateFormFields } from "components/Composite/OrderAggregateForm/OrderAggregateFormFields";
import { Container } from "components/Layout/Container";
import { formatCurrency } from "currency";
import Decimal from "decimal.js";
import { formatNumber, GetChangeType, Zero } from "helpers";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo } from "react";
import { FieldErrors, useForm } from "react-hook-form";
import { EOfferType, TOffer, TOrder } from "types";


const schema = {
  type: "object",
  properties: {},
  required: [],
};

export interface AggregateOrderFormData {
  non_shareholding: {
    applications: number | null;
    notional_value: number | null;
  };
  shareholding: {
    applications: number | null;
    notional_value: number | null;
    existing_holding: number | null;
  };
}

interface OrderAggregateFormProps {
  className?: string;
  offer: TOffer;
  order?: TOrder;
  onConfirm: (data: AggregateOrderFormData) => void;
  fieldErrors: FieldErrors<AggregateOrderFormData>;
  errorMessage: string | undefined;
}


export const OrderAggregateForm = ({
  offer,
  order,
  onConfirm,
  fieldErrors,
  errorMessage
}: OrderAggregateFormProps) => {

  const defaultValues = useMemo(
    () => ({
      shareholding: {
        applications: order?.shareholding?.applications?.toNumber() ?? null,
        existing_holding:
          order?.shareholding?.existing_holding?.toNumber() ?? null,
        notional_value: order?.shareholding?.notional_value?.toNumber() ?? null,
      },
      non_shareholding: {
        applications: order?.non_shareholding?.applications?.toNumber() ?? null,
        notional_value:
          order?.non_shareholding?.notional_value?.toNumber() ?? null,
      },
    }),
    [order]
  );

  const form = useForm<AggregateOrderFormData>({
    defaultValues,
    resolver: ajvResolver(schema),
  });

  const { reset, watch, formState, setError, clearErrors } = form;

  const shareholderValue = watch("shareholding.notional_value");
  const nonShareholderValue = watch("non_shareholding.notional_value");

  const shareholderApplications = watch("shareholding.applications");
  const nonShareholderApplications = watch("non_shareholding.applications");
  const existingHolding = watch("shareholding.existing_holding");

  const valueStat: StatProps = useMemo(() => {
    let total = Zero;

    const shValue = shareholderValue ? new Decimal(shareholderValue) : Zero;
    const nonShValue = nonShareholderValue
      ? new Decimal(nonShareholderValue)
      : Zero;

    if (!!shValue && shValue.gte(0)) total = Decimal.add(total, shValue);
    if (!!nonShValue && nonShValue.gte(0))
      total = Decimal.add(total, nonShValue);

    let previousValue = Zero;
    previousValue = Decimal.add(
      previousValue,
      order?.shareholding?.notional_value ?? Zero
    );
    previousValue = Decimal.add(
      previousValue,
      order?.non_shareholding?.notional_value ?? Zero
    );

    return {
      title: "Total Value",
      value: formatCurrency(total, offer.currency),
      change: formatCurrency(
        Decimal.abs(Decimal.sub(total, previousValue)),
        offer.currency
      ),
      changeType: GetChangeType(total, previousValue),
    };
  }, [order, shareholderValue, nonShareholderValue, offer.currency]);

  const applicationsStat: StatProps = useMemo(() => {
    let total: Decimal = Zero;

    const shApplications = shareholderApplications
      ? new Decimal(shareholderApplications)
      : Zero;
    const nonShApplications = nonShareholderApplications
      ? new Decimal(nonShareholderApplications)
      : Zero;

    if (!!shApplications && shApplications.gte(0))
      total = Decimal.add(total, shApplications);
    if (!!nonShApplications && nonShApplications.gte(0))
      total = Decimal.add(total, nonShApplications);

    let previousApplications: Decimal = Zero;
    previousApplications = Decimal.add(
      previousApplications,
      order?.shareholding?.applications ?? Zero
    );
    previousApplications = Decimal.add(
      previousApplications,
      order?.non_shareholding?.applications ?? Zero
    );

    return {
      title: "Total Applications",
      value: formatNumber(total),
      change: formatNumber(
        Decimal.abs(Decimal.sub(total, previousApplications))
      ),
      changeType: GetChangeType(total, previousApplications),
    };
  }, [order, shareholderApplications, nonShareholderApplications]);

  const existingHoldingStat: StatProps | undefined = useMemo(() => {
    const eh = existingHolding ? new Decimal(existingHolding) : Zero;
    if (eh.eq(Zero)) return;

    let previousTotal: Decimal = Zero;
    previousTotal = Decimal.add(
      previousTotal,
      order?.shareholding?.existing_holding ?? Zero
    );

    return {
      title: "Total Existing Holding",
      value: formatNumber(eh),
      change: formatNumber(Decimal.abs(Decimal.sub(eh, previousTotal))),
      changeType: GetChangeType(eh, previousTotal),
    };
  }, [order, existingHolding]);

  const stats = useMemo(() => {
    const stats = [];
    if (applicationsStat) stats.push(applicationsStat);
    if (valueStat) stats.push(valueStat);
    if (existingHoldingStat) stats.push(existingHoldingStat);
    return stats;
  }, [applicationsStat, valueStat, existingHoldingStat]);

  useReactHookFormServerErrors(fieldErrors, formState.errors, setError, clearErrors);

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const existingEnabled = useMemo( () => {
    switch(offer.type) {
      case EOfferType.IPO:
      case EOfferType.RETAIL_BOND:
          return false
    }
    return true
  },[offer])

  return (
    <Container narrow>
      <dl className="mb-sm flex flex-wrap gap-sm">
        {stats.map((stat, index) => (
          <Stat key={index} {...stat} />
        ))}
      </dl>

      <form
        className="form"
        onSubmit={form.handleSubmit((data) => {
          onConfirm(data)

        })}
      >
        <>
         {existingEnabled && (
            <section className="form-section">
              <OrderAggregateFormFields
                type="shareholding"
                form={form}
                currencyCode={offer.currency}
              />
            </section>
          )}
          {!offer.shareholders_only && (
            <section className="form-section">
              <OrderAggregateFormFields
                type="non_shareholding"
                form={form}
                currencyCode={offer.currency}
                showLegend={offer.type === EOfferType.FOLLOW_ON}
              />
            </section>
          )}
          
          <div className="form-actions">
            <Button
              type="button"
              disabled={!formState.isDirty}
              onClick={() => form.handleSubmit((data) => {
                onConfirm(data)
              })()
              }
            >
              Confirm Orders
            </Button>
            <p className="text-[#FF0000]">{errorMessage}</p>
          </div>
        </>

      </form>
    </Container>
  );
};
