import { Input } from "components/Basic/Input/Input";
import { formatCurrency } from "currency";
import Decimal from "decimal.js";
import { Zero } from "helpers";
import { useMemo } from "react";
import { Controller, UseFormReturn } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { AggregateOrderFormData } from "./OrderAggregateForm";

interface OrderAggregateFormFieldsProps {
  form: UseFormReturn<AggregateOrderFormData>;
  type: "shareholding" | "non_shareholding";
  currencyCode?: string;
  showLegend?: boolean;
}

export const OrderAggregateFormFields = ({
  type,
  form,
  currencyCode = "GBP",
  showLegend = true,
}: OrderAggregateFormFieldsProps) => {
  const { watch, control } = form;

  const value = watch(`${type}.notional_value`);
  const applications = watch(`${type}.applications`);

  const average = useMemo(() => {
    let amount = Zero;

    if (value && applications) {
      amount = Decimal.div(new Decimal(value), new Decimal(applications));
    }

    return formatCurrency(amount, currencyCode);
  }, [value, applications, currencyCode]);

  return (
    <>
      {showLegend && (
        <legend className="form-legend">
          {type === "shareholding" ? "Existing Shareholders" : "New Shareholders"}
        </legend>
      )}

      {type === "shareholding" && (
        <Controller
          name={`${type}.existing_holding`}
          control={control}
          render={({
            field: { onChange, value, ref },
            fieldState: { error },
          }) => (
            <NumericFormat
              label="Existing Holding"
              placeholder="Shares"
              getInputRef={ref}
              value={value}
              valueIsNumericString
              thousandSeparator
              customInput={Input}
              decimalScale={0}
              allowNegative={false}
              onValueChange={({ floatValue }) => {
                onChange(floatValue ?? null);
              }}
              error={error?.message}
            />
          )}
        />
      )}
      <div className="form-fieldset">
        <Controller
          name={`${type}.applications`}
          control={control}
          render={({
            field: { onChange, value, ref },
            fieldState: { error },
          }) => (
            <NumericFormat
              label="Applications"
              getInputRef={ref}
              value={value}
              valueIsNumericString
              thousandSeparator
              customInput={Input}
              decimalScale={0}
              allowNegative={false}
              onValueChange={({ floatValue }) => {
                onChange(floatValue ?? null);
              }}
              error={error?.message}
            />
          )}
        />

        <Controller
          name={`${type}.notional_value`}
          control={control}
          render={({
            field: { onChange, value, ref },
            fieldState: { error },
          }) => (
            <NumericFormat
              label={`Value (${currencyCode})`}
              getInputRef={ref}
              value={value}
              valueIsNumericString
              thousandSeparator
              customInput={Input}
              decimalScale={2}
              allowNegative={false}
              onValueChange={({ floatValue }) => {
                onChange(floatValue ?? null);
              }}
              error={error?.message}
            />
          )}
        />
      </div>
      <p>
        Average value per application: <strong>{average}</strong>
      </p>
    </>
  );
};
