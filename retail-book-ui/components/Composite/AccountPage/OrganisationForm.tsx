import {  TOrganisation } from "types";
import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo } from "react";
import { FieldErrors, useForm } from "react-hook-form";

const schema = {
  type: "object",
  properties: {
    name: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "name field is required" },
    },
  },
  required: ["name"],
};

export interface OrganisationFormData {
  system_id: string;
  system_role: string;
  name: string;
  crest_account_name: string;
  crest_participant_id: string;
  account_type: string;
  account_name: string;
  bank_name: string;
  swift_bic: string;
  account_number: string;
  sort_code: string;
  address1: string;
  address2: string;
  address3: string;
  address4: string;
  address_city: string;
  address_postal_code: string;
  address_country: string;
  settlement_reference: string;
}

interface OrganisationFormProps {
  organisation: TOrganisation;
  onSubmit: (data: OrganisationFormData) => void;
  fieldErrors?: FieldErrors<OrganisationFormData>;
}

export const OrganisationForm = ({
  organisation,
  onSubmit,
  fieldErrors,
}: OrganisationFormProps) => {
  const defaultValues: OrganisationFormData = useMemo(
    () => ({
      name: organisation.name ?? "",
      crest_account_name: organisation.crest_account_name ?? "",
      crest_participant_id: organisation.crest_participant_id ?? "",
      account_type: organisation.account_type ?? "",
      account_name: organisation.account_name ?? "",
      bank_name: organisation.account_bank_name ?? "",
      swift_bic: organisation.account_swift_bic ?? "",
      account_number: organisation.account_number ?? "",
      sort_code: organisation.account_sort_code ?? "",
      address1: organisation.address1 ?? "",
      address2: organisation.address2 ?? "",
      address3: organisation.address3 ?? "",
      address4: organisation.address4 ?? "",
      address_city: organisation.address_city ?? "",
      address_postal_code: organisation.address_postal_code ?? "",
      address_country: organisation.address_country ?? "",
      system_id: organisation.system_id ?? "",
      system_role: organisation.system_role ?? "",
      settlement_reference: organisation.settlement_reference ?? "",
    }),
    [organisation]
  );

  const { register, handleSubmit, formState, reset, setError, clearErrors } =
    useForm<OrganisationFormData>({
      defaultValues,
      resolver: ajvResolver(schema),
    });

  const { errors, isDirty, isSubmitting } = formState;

  useReactHookFormServerErrors<OrganisationFormData>(fieldErrors, errors, setError, clearErrors);

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  return (
    <form
      className="form"
      onSubmit={handleSubmit((formData) => onSubmit(formData))}
    >
      <section className="form-section">
        <fieldset className="form-fieldset">
          <legend>Org Details</legend>
          <Input
            {...register("name")}
            label="Full Name"
            error={errors.name?.message}
          />
        </fieldset>
        <fieldset className="form-fieldset">
          <legend>Crest Details</legend>
          <Input
            {...register("crest_account_name")}
            label="Crest Account Name"
            error={errors.crest_account_name?.message}
          />
          <Input
            {...register("crest_participant_id")}
            label="Crest Participant Id"
            error={errors.crest_participant_id?.message}
          />
        </fieldset>
        {organisation.system_role.toUpperCase() == "BANK" && (
          <fieldset className="form-fieldset">
            <legend>Settlement Details</legend>
            <Input
              {...register("settlement_reference")}
              label="Settlement Reference"
              error={errors.settlement_reference?.message}
            />
          </fieldset> 
        )}
        <fieldset className="form-fieldset">
          <legend>Account Details</legend>
          <Input
            label="Account Type"
            value={organisation.account_type ?? ""}
            disabled
          />
          <Input
            {...register("account_name")}
            label="Account Name"
            error={errors.account_name?.message}
          />
          <Input
            {...register("bank_name")}
            label="Bank Name"
            error={errors.bank_name?.message}
          />
          <Input
            {...register("swift_bic")}
            label="SWIFT BIC"
            error={errors.swift_bic?.message}
          />
          <Input
            {...register("account_number")}
            label="Account Number"
            error={errors.account_number?.message}
          />
          <Input
            {...register("sort_code")}
            label="Sort Code"
            error={errors.sort_code?.message}
          />
        </fieldset>
        <fieldset className="form-fieldset">
          <legend>Address</legend>
          <Input
            {...register("address1")}
            label="Address Line 1"
            error={errors.address1?.message}
          />
          <Input
            {...register("address2")}
            label="Address Line 2"
            error={errors.address2?.message}
          />
          <Input
            {...register("address3")}
            label="Address Line 3"
            error={errors.address3?.message}
          />
          <Input
            {...register("address4")}
            label="Address Line 4"
            error={errors.address4?.message}
          />
          <Input
            {...register("address_postal_code")}
            label="Postal Code"
            error={errors.address_postal_code?.message}
          />
          <Input
            {...register("address_country")}
            label="Country"
            error={errors.address_country?.message}
          />
          <Input
            {...register("address_city")}
            label="City"
            error={errors.address_city?.message}
          />
        </fieldset>
        <fieldset className="form-fieldset">
          <legend>System Info</legend>
          <Input
            label="System Id"
            value={organisation.system_id ?? ""}
            disabled
          />
          <Input
            label="System Role"
            value={organisation.system_role ?? ""}
            disabled
          />
        </fieldset>
      </section>

      <div className="form-actions">
        <Button loading={isSubmitting} disabled={!isDirty || isSubmitting}>
          Save
        </Button>
        <Button
          type="button"
          theme="outline"
          onClick={() => reset(defaultValues)}
        >
          Cancel
        </Button>
      </div>
    </form>
  );
};
