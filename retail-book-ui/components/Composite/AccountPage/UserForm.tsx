import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { Input } from "components/Basic/Input/Input";
import { Prose } from "components/Basic/Prose/Prose";
import { useReactHookFormServerErrors } from "hooks/useReactHookFormServerErrors";
import { useEffect, useMemo } from "react";
import { FieldErrors, useForm } from "react-hook-form";
import { TUser } from "types";

const schema = {
  type: "object",
  properties: {
    name: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "name field is required" },
    },
    email: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "email field is required" },
    },
  },
  required: ["name", "email"],
};

export interface UserFormData {
  name: string;
  email: string;
}

interface UserFormProps {
  user?: TUser;
  onSubmit: (data: UserFormData) => void;
  fieldErrors?: FieldErrors<UserFormData>;
}

export const UserForm = ({ user, onSubmit, fieldErrors }: UserFormProps) => {
  const defaultValues: UserFormData = useMemo(
    () => ({
      name: user?.name ?? "",
      email: user?.email ?? "",
    }),
    [user]
  );

  const { register, handleSubmit, formState, reset, setError, clearErrors } =
    useForm<UserFormData>({
      defaultValues,
      resolver: ajvResolver(schema),
    });

  const { errors, isDirty, isSubmitting } = formState;

  useReactHookFormServerErrors<UserFormData>(fieldErrors, errors, setError, clearErrors);

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  return (
    <form
      className="form"
      onSubmit={handleSubmit((formData) => onSubmit(formData))}
    >
      <section className="form-section">
        <Input
          {...register("name")}
          label="Username"
          error={errors.name?.message}
          disabled
        />
        <Input
          {...register("email")}
          label="Email Address"
          error={errors.email?.message}
          disabled
        />
        <Prose>Please contact RetailBook support if you wish to update your username or email address</Prose>
      </section>

      { /* For the moment, since we've got Username, and Email Address and we can't change it - there's no point in having save/cancel */}
      <div className="form-actions">
        <Button loading={isSubmitting} disabled={!isDirty || isSubmitting} className="hidden">
          Save
        </Button>
        <Button
          type="button"
          className="hidden"
          theme="outline"
          onClick={() => reset(defaultValues)}
        >
          Cancel
        </Button>
      </div>
    </form>
  );
};
