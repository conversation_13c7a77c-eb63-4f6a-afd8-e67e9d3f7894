import { useQueryClient, useMutation } from "@tanstack/react-query";
import { Button } from "components/Basic/Button/Button";
import { Alert } from "components/Basic/Alert/Alert";
import { Input } from "components/Basic/Input/Input";
import { useMemo, useState, FunctionComponent } from "react";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { useSession } from "next-auth/react";
import { TSettlementInstruction } from "types";
import {deleteSettlementInstructionMutation } from "utils/queries";

interface DeleteInstructionDialogProps {
  open: boolean;
  onClose: () => void;
  selectedInstruction: TSettlementInstruction | undefined;
}

const DeleteInstructionDialog: FunctionComponent<DeleteInstructionDialogProps> = ({onClose, open, selectedInstruction}) => {
  const qc = useQueryClient();
  const { data: session } = useSession();

  const [deleteValue, setDeleteValue] = useState<string>();

  const deleteInstructionMutationOptions = useMemo(
    () => deleteSettlementInstructionMutation(qc, session?.accessToken),
    [qc, session?.accessToken]
  );

  const deleteInstructionMutator = useMutation({
    ...deleteInstructionMutationOptions,
    onSuccess: () => {
      deleteInstructionMutationOptions.onSuccess();
      closeForm();
    },
  });

  const closeForm = () => {
    deleteInstructionMutator.reset();
    setDeleteValue(undefined);
    onClose();
  }

  return (
    <StyledDialog
      open={open}
      onClose={closeForm}
      unmount={false}
      title="Remove settlement instruction"
      footer={
        <div className="flex gap-xs">
          <Button
            disabled={
              deleteValue !== "DELETE" || deleteInstructionMutator.isLoading
            }
            loading={deleteInstructionMutator.isLoading}
            type="button"
            onClick={() =>
              deleteInstructionMutator.mutate(selectedInstruction?.id ?? "")
            }
          >
            Delete
          </Button>
          <Button
            theme="outline"
            type="button"
            onClick={closeForm}
          >
            Cancel
          </Button>
        </div>
      }
    >
      <div className="w-screen max-w-screen-sm">
        <Alert
          className="mb-md"
          theme="warning"
          message={`Are you sure you want to remove this settlement instruction for ${selectedInstruction?.intermediary_display_name}? This action cannot be undone.`}
        />
        <Input
          label="Type 'DELETE' to remove this instruction"
          value={deleteValue}
          onChange={(e) => setDeleteValue(e.target.value)}
        />
      </div>
    </StyledDialog>
  );
};

export default DeleteInstructionDialog;
