import { useQuery, useQ<PERSON>yClient, useMutation } from "@tanstack/react-query";
import { ajvResolver } from "@hookform/resolvers/ajv";
import { Button } from "components/Basic/Button/Button";
import { useForm } from "react-hook-form";
import { Alert } from "components/Basic/Alert/Alert";
import { Input } from "components/Basic/Input/Input";
import { useMemo, useState, useCallback, useEffect } from "react";
import { StyledDialog } from "components/StyledHeadlessUI/StyledDialog";
import { useSession } from "next-auth/react";
import { AxiosError } from "axios";
import { TSettlementInstruction, IntermediaryResponse, ApiError, ApiMutationResponse } from "types";
import { StyledComboboxSingle } from "components/StyledHeadlessUI/StyledComboboxSingle";
import { StyledComboboxOption } from "components/StyledHeadlessUI/StyledCombobox";
import {
  createSettlementInstructionMutation,
  updateSettlementInstructionMutation,
  getStaticIntermediariesQuery,
  getSettlementInstructionsQuery
} from "utils/queries";
import { Prose } from "components/Basic/Prose/Prose";

interface Intermediary {
  system_id: string;
  display_name: string;
}

interface InstructionFormData {
  intermediary_system_id: string;
  settlement_reference: string;
}

const schema = {
  type: "object",
  properties: {
    intermediary_system_id: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "intermediary field is required" },
    },
    settlement_reference: {
      type: "string",
      minLength: 1,
      errorMessage: { minLength: "settlement reference field is required" },
    },
  },
  required: ["intermediary_system_id", "settlement_reference"],
};

interface EditInstructionDialogProps {
  open: boolean;
  onClose: () => void;
  selectedInstruction: TSettlementInstruction | undefined;
}

const EditInstructionDialog = (props: EditInstructionDialogProps) => {
  const qc = useQueryClient();
  const { data: session } = useSession();

  const [selectedIntermediary, setSelectedIntermediary] = useState<StyledComboboxOption<Intermediary>[]>([]);

  useEffect(() => {
    const startingInstruction = props.selectedInstruction ?
      [{
        id: props.selectedInstruction.intermediary_system_id,
        label: props.selectedInstruction?.intermediary_display_name,
        value: {system_id: props.selectedInstruction.intermediary_system_id, display_name: props.selectedInstruction?.intermediary_display_name}
      }]
    : [];
    setSelectedIntermediary(startingInstruction);
  }, [props.selectedInstruction]);

  const { data: intermediaryOptions } = useQuery({
    ...getStaticIntermediariesQuery(session?.accessToken),
    select: useCallback(
      (data: IntermediaryResponse) =>
        data.map((intermediary) => ({
          id: intermediary.system_id,
          label: intermediary.display_name,
          value: intermediary
        })),
      []
    ),
  });

  const { data: instructions, isLoading: isLoadingInstructions } = useQuery({
    ...getSettlementInstructionsQuery({ limit: 0, offset: 0 }, session?.accessToken),
  });
  const instructionSet = useMemo(() => {
    return Array.from(new Set<string>((instructions?.data ?? []).map((i) => i.intermediary_system_id)))
  }, [instructions]);
  const intermediaryOptionsFiltered = useMemo(
    () =>
      (intermediaryOptions ?? []).filter((option) => {
        if (selectedIntermediary.find((selected) => option.id === selected.id)) {
          return false;
        }
        //hide intermediaries that already have instructions
        if (instructionSet.find((existing) => option.id === existing)) {
          return false;
        }
        return true;
      }),
    [intermediaryOptions, selectedIntermediary, instructionSet]
  );

  const createInstructionMutationOptions = useMemo(
    () => createSettlementInstructionMutation(qc, session?.accessToken),
    [qc, session?.accessToken]
  );
  const createInstructionMutator = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    Partial<TSettlementInstruction>
  >({
    ...createInstructionMutationOptions,
    onSuccess: () => {
      createInstructionMutationOptions.onSuccess();
      closeForm();
    },
    onError: () => { /* This function is intentionally left blank */ }
  });

  const updateInstructionMutationOptions = useMemo(
    () =>
      updateSettlementInstructionMutation(
        props?.selectedInstruction?.id ?? "",
        qc,
        session?.accessToken
      ),
    [props?.selectedInstruction, qc, session?.accessToken]
  );
  const updateInstructionMutator = useMutation<
    ApiMutationResponse,
    AxiosError<ApiError>,
    Partial<TSettlementInstruction>
  >({
    ...updateInstructionMutationOptions,
    onSuccess: () => {
      updateInstructionMutationOptions.onSuccess();
      closeForm();
    },
  });

  const defaultValues: InstructionFormData = useMemo(
    () => ({
      intermediary_system_id: props.selectedInstruction?.intermediary_system_id ?? "",
      settlement_reference: props.selectedInstruction?.settlement_reference ?? "",
    }),
    [props.selectedInstruction]
  );

  const { register, handleSubmit, formState, reset, setValue } =
  useForm<InstructionFormData>({
    defaultValues,
    resolver: ajvResolver(schema),
  });

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const { errors, isDirty, isSubmitting } = formState;

  const getErrorMessage = function(): string {
    if (errors.root) {
      return errors.root.message ?? ""
    }
    if (updateInstructionMutator.error?.response?.status == 409 && updateInstructionMutator.error?.response?.data.detail?.message) {
      return updateInstructionMutator.error?.response?.data.detail?.message
    }
    if (createInstructionMutator.error?.response?.status == 409 && createInstructionMutator.error?.response?.data.detail?.message) {
      return createInstructionMutator.error?.response?.data.detail?.message
    }
    return createInstructionMutator.error?.response?.data.message ?? updateInstructionMutator.error?.response?.data.message ?? ""
  }

  const closeForm = () => {
    updateInstructionMutator.reset();
    createInstructionMutator.reset();
    setSelectedIntermediary([]);
    reset();
    props.onClose();
  }

  return (
    <StyledDialog
      open={props.open}
      onClose={closeForm}
      unmount={false}
      title={`${props.selectedInstruction ? "Edit" : "Add"} settlement instruction`}
      footer={
        <div className="flex gap-xs">
          {props.selectedInstruction ? (
            <Button
              type="button"
              disabled={!isDirty || isSubmitting}
              loading={isSubmitting}
              onClick={handleSubmit((formData) =>                  
                {updateInstructionMutator.reset();
                updateInstructionMutator.mutate(formData);}
              )}
            >
              Update
            </Button>
          ) : (
            <Button
              type="button"
              disabled={!isDirty || isSubmitting}
              loading={isSubmitting}
              onClick={() => {
                  handleSubmit((formData) => {
                      createInstructionMutator.reset();
                      createInstructionMutator.mutate(formData);
                    }
                  )()
                }
              }
            >
              Add
            </Button>
          )}
          <Button
            theme="outline"
            type="button"
            onClick={() => closeForm()}
          >
            Cancel
          </Button>          
        </div>
      }
    >
      <div className="w-screen max-w-screen-sm">
        <div className={"pb-2 " + ( (createInstructionMutator.error || updateInstructionMutator) ? "": "hidden")}>
          { (createInstructionMutator.error || updateInstructionMutator.error) && (
              <Alert theme="failure" message={getErrorMessage()}/>
          )
          }          
        </div>
        <form className="form">
          <section className="form-section">
            {isLoadingInstructions && (
              <Prose>Loading intermediaries</Prose>
            )}
            {!isLoadingInstructions && (
              <StyledComboboxSingle
                options={intermediaryOptionsFiltered}
                onChange={(options: StyledComboboxOption<Intermediary>[]) => {
                  setValue("intermediary_system_id", options[0].id);
                  setSelectedIntermediary(options);
                  }
                }
                onRemove={(opt) =>
                  setSelectedIntermediary((existing) =>
                    existing.filter((o) => o.id !== opt.id)
                  )
                }
                value={selectedIntermediary}
                multiple
                placeholder={selectedIntermediary.length > 0 ? "" : "Search intermediaries"}
                label="Intermediary"
                showLabel={true}
                error={errors.intermediary_system_id?.message}
              />
            )}
            <Input
              label="Settlement Reference"
              {...register("settlement_reference")}
              error={errors.settlement_reference?.message}
            />
          </section>
        </form>
      </div>
    </StyledDialog>
  );
};

export default EditInstructionDialog;
