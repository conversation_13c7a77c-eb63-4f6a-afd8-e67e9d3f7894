import { Tab } from "@headlessui/react";
import { Container } from "components/Layout/Container";
import { FunctionComponent } from "react";
import ObligationsGrid from "components/Composite/SettlementsPage/Grids/ObligationsGrid";
// import { TSettlementInstruction } from "types";

const ObligationsTab: FunctionComponent = () => {

  // const [isEditInstructionOpen, setEditInstructionOpen] = useState(false);
  // const [isDeleteInstructionOpen, setDeleteInstructionOpen] = useState(false);
  // const [selectedInstruction, setSelectedInstruction] = useState<TSettlementInstruction>();

  return (
    <Tab.Panel>
      <Container>

        <ObligationsGrid pageSize={20} />

      </Container>
    </Tab.Panel>
  );
};

export default ObligationsTab;
