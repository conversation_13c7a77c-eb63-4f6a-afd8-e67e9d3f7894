import { Tab } from "@headlessui/react";
import { Button } from "components/Basic/Button/Button";
import { Container } from "components/Layout/Container";
import { useState, FunctionComponent } from "react";
import { Plus } from "react-feather";
import EditInstructionDialog from "components/Composite/SettlementsPage/Dialogs/EditInstructionDialog";
import DeleteInstructionDialog from "components/Composite/SettlementsPage/Dialogs/DeleteInstructionDialog";
import InstructionsGrid from "components/Composite/SettlementsPage/Grids/InstructionsGrid";
import { TSettlementInstruction } from "types";

const InstructionsTab: FunctionComponent = () => {

  const [isEditInstructionOpen, setEditInstructionOpen] = useState(false);
  const [isDeleteInstructionOpen, setDeleteInstructionOpen] = useState(false);
  const [selectedInstruction, setSelectedInstruction] = useState<TSettlementInstruction>();

  return (
    <Tab.Panel>
      <Container>
        <Button
          className="mb-md"
          type="button"
          icon={Plus}
          onClick={() => setEditInstructionOpen(true)}
        >
          Add an instruction
        </Button>

        <InstructionsGrid pageSize={20} editRow={(id) => {setSelectedInstruction(id); setEditInstructionOpen(true);}} deleteRow={(id) => {setSelectedInstruction(id); setDeleteInstructionOpen(true);}} />

      </Container>

      <EditInstructionDialog selectedInstruction={selectedInstruction} open={isEditInstructionOpen} onClose={() => {setEditInstructionOpen(false); setSelectedInstruction(undefined); }} />

      <DeleteInstructionDialog selectedInstruction={selectedInstruction} open={isDeleteInstructionOpen} onClose={() => {setDeleteInstructionOpen(false); setSelectedInstruction(undefined); }} />
    </Tab.Panel>
  );
};

export default InstructionsTab;
