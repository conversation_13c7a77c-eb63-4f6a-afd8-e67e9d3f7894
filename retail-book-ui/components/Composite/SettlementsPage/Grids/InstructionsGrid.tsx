import { useQuery } from "@tanstack/react-query";
import { useMemo, useState } from "react";
import { Column, StyledReactDataGrid } from "components/StyledReactDataGrid/StyledReactDataGrid";
import { useSession } from "next-auth/react";
import { SortColumn } from "react-data-grid";
import { TSettlementInstruction } from "types";
import { getSettlementInstructionsQuery } from "utils/queries";
import { Spinner } from "components/Basic/Spinner/Spinner";
import { format, parseISO } from "date-fns";

interface Row {
  id: string;
  timestamp: string;
  intermediary_display_name: string;
  intermediary_system_id: string;
  settlement_reference: string;
  instruction: TSettlementInstruction;
}

const columns: Column<Row>[] = [
  {
    key: "intermediary_display_name",
    name: "Intermediary",
    sortable: true,
    resizable: true,
  },
  {
    key: "settlement_reference",
    name: "Settlement Reference",
    sortable: true,
    resizable: true,
  },
  {
    key: "timestamp",
    name: "Timestamp",
    sortable: true,
    resizable: true,
    formatter: ({ row }) => {
      return (format(parseISO(row.timestamp), "dd/MM/yyyy HH:mm"))
  },
  },
];

const getRowFromInstruction = (instruction: TSettlementInstruction): Row => {
  return {
    id: instruction.id,
    timestamp: instruction.timestamp,
    settlement_reference: instruction.settlement_reference,
    intermediary_display_name: instruction.intermediary_display_name,
    intermediary_system_id: instruction.intermediary_system_id,
    instruction: instruction,
  };
};

interface InstructionsGridProps {
  pageSize: number;
  editRow: (instruction: TSettlementInstruction) => void;
  deleteRow: (instruction: TSettlementInstruction) => void;
}

const InstructionsGrid = (props: InstructionsGridProps) => {
  const { data: session } = useSession();

  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);

  const params = useMemo(() => {
    const sort =
      sortColumns.length > 0
        ? `${
            sortColumns[0].columnKey
          }=${sortColumns[0].direction.toLocaleLowerCase()}`
        : undefined;

    return {
      limit: props.pageSize,
      offset: (currentPage - 1) * props.pageSize,
      sort,
    };
  }, [currentPage, sortColumns, props.pageSize]);

  const { data: instructions, isLoading: isLoadingInstructions } = useQuery({
    ...getSettlementInstructionsQuery(params, session?.accessToken),
  });

  const totalItems = useMemo(
    () => instructions?.pagination.count ?? 0,
    [instructions?.pagination.count]
  );

  const rows = useMemo(
    () =>
      instructions?.data?.map((instruction) =>
        getRowFromInstruction(instruction)
      ) ?? [],
    [instructions]
  );

  return (
    <>
      {isLoadingInstructions && (
        <div className="flex items-center justify-center">
          <Spinner size="lg" message="Loading instructions..." />
        </div>
      )}
      {!isLoadingInstructions && (
        <StyledReactDataGrid
          rows={rows ?? []}
          columns={columns}
          contextMenuItems={[
            {
              label: "Edit",
              onClick: (e, row) => {
                e.preventDefault();
                props.editRow(row.instruction);
              },
            },
            {
              label: "Delete",
              onClick: (e, row) => {
                e.preventDefault();
                props.deleteRow(row.instruction);
              },
            },
          ]}
          sortColumns={sortColumns}
          onSortColumnsChange={setSortColumns}
          paginationProps={{
            onClick: (page) => setCurrentPage(page),
            currentPage: currentPage,
            totalItems: totalItems,
            pageSize: props.pageSize,
          }}
        />
      )}
    </>
  );
};

export default InstructionsGrid;
