import { Container } from "components/Layout/Container";
import { ExtractProps } from "types";
import { StyledCombobox } from "./StyledCombobox";
import { Input } from "components/Basic/Input/Input";

//Typescript and headlessui combobox don't play along well, simply removing the "multiple" argument to it
//and passing value as a non-array type causes all sorts of typing issues, see: https://github.com/tailwindlabs/headlessui/issues/2438
//I did consider using StyledListbox, but it doesn't support auto-complete which for a large list of intermediaries is required.

export const StyledComboboxSingle = ({
  ...props
}: ExtractProps<typeof StyledCombobox>) => {

  const valueIsSelected = (props.value) && props.value.length > 0

  //In the future I would like to add a cancel button to this so a value can be changed once selected, but for now it's fine.
  return (
    <>
      {!valueIsSelected && (
        <StyledCombobox {...props} />
      )}
      {valueIsSelected && (
        <Container className="pl-0 pr-0 ">
          <Input label={props.label} value={props.value[0].label} disabled={true} />
          {props.error && <span className="input__error">{props.error}</span>}
        </Container>
      )}
    </>
  );
};
