/* HeadlessUI/Tab */
.tab {
  @apply inline-flex items-center gap-2xs py-xs text-duke-blue-step-0;
  @apply transition-colors hover:text-pink-step-0;

  &:after {
    height: 3px;
    @apply absolute top-full w-full bg-pink-step-0;
  }

  &--selected {
    @apply relative border-pink-step-0 font-bold;

    &:after {
      content: "";
    }
  }

  &--disabled {
    @apply pointer-events-none text-grey-step-2;
  }

  &--vertical {
    @apply px-xs py-2xs;

    &:after {
      width: 3px;
      @apply top-0 right-full h-full;
    }
  }
}

.tab-list {
  @apply flex gap-lg w-full;

  &--bordered {
    @apply border-grey-step-4 border-b;
    border-bottom-width: 3px;
  }

  &--vertical {
    @apply flex-col;
  }
}

.tab-panel {
  @apply pt-md-lg pb-lg-xl grow;

  &--bordered {
    @apply bg-grey-step-5 border-grey-step-4 border-t;
    border-top-width: 3px;
  }
}
