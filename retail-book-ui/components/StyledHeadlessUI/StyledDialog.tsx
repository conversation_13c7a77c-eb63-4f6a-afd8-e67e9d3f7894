import { Dialog, Transition } from "@headlessui/react";
import classNames from "classnames";
import { Fragment, ReactNode } from "react";
import { X } from "react-feather";
import { ExtractProps } from "types";

interface StyledDialogProps {
  open: boolean;
  title: string;
  description?: string;
  children: ReactNode;
  fullWidth?: boolean;
  footer?: ReactNode;
  bodyClassName?: string;
  type?: "modal" | "flyout";
}

export const StyledDialog = ({
  className,
  bodyClassName,
  open,
  title,
  description,
  children,
  fullWidth = false,
  footer,
  type = "modal",
  ...props
}: ExtractProps<typeof Dialog> & StyledDialogProps) => {
  return (
    <Transition show={open} as={Fragment}>
      <Dialog
        as="div"
        className={classNames("dialog", className, {
          [`dialog--${type}`]: type,
          [`dialog--full`]: fullWidth,
        })}
        {...props}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="dialog__overlay" />
        </Transition.Child>

        <div className="dialog__content">
          <div className="dialog__container">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom={
                type === "flyout" ? "translate-x-full" : "opacity-0 scale-95"
              }
              enterTo={
                type === "flyout" ? "translate-x-0" : "opacity-100 scale-100"
              }
              leave="ease-in duration-200"
              leaveFrom={
                type === "flyout" ? "translate-x-0" : "opacity-100 scale-100"
              }
              leaveTo={
                type === "flyout" ? "translate-x-full" : "opacity-0 scale-95"
              }
            >
              <Dialog.Panel className="dialog__panel">
                <header className="dialog__header">
                  <div>
                    <Dialog.Title as="h2" className="dialog__title">
                      {title}
                    </Dialog.Title>
                    <Dialog.Description className="dialog__description">
                      {description}
                    </Dialog.Description>
                  </div>
                  <button
                    onClick={() => props.onClose(false)}
                    className="dialog__close"
                  >
                    <X className="dialog__close-icon" aria-hidden="true" />
                  </button>
                </header>
                <div className={ bodyClassName ? bodyClassName : "dialog__body"}>{children}</div>
                {footer && <div className="dialog__footer">{footer}</div>}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
