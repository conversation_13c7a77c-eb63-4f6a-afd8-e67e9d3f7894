import { Combobox, Transition } from "@headlessui/react";
import classNames from "classnames";
import { Fragment, SyntheticEvent, useMemo, useState } from "react";
import { Check, ChevronDown, X } from "react-feather";
import { ExtractProps } from "types";

export interface StyledComboboxOption<T = string> {
  id: string;
  label: string;
  value: T;
}

interface StyledComboboxProps<T> {
  label: string;
  showLabel?: boolean;
  placeholder?: string;
  options: StyledComboboxOption<T>[];
  onRemove: (opt: StyledComboboxOption<T>) => void;
  value: StyledComboboxOption<T>[];
  error?: string;
}

const Chip = ({
  label,
  onRemove,
}: {
  label: string;
  onRemove: (e: SyntheticEvent) => void;
}) => (
  <span className="chip">
    {label}
    <button className="chip__button" onClick={onRemove}>
      <X className="chip__button-icon" />
    </button>
  </span>
);

export const StyledCombobox = <T,>({
  className,
  options,
  label,
  showLabel = true,
  placeholder,
  value,
  onRemove,
  error,
  ...props
}: ExtractProps<typeof Combobox> & StyledComboboxProps<T>) => {
  const [query, setQuery] = useState("");
  const filteredOptions = useMemo(
    () =>
      options.filter((option) => {
        // @TODO replace with better filtering, i.e fuse.js
        return option.label.toLowerCase().includes(query.toLowerCase());
      }),
    [query, options]
  );

  return (
    <>
      {value?.length > 0 && (
        <div className="mb-sm flex flex-wrap gap-2xs">
          {value.map((opt: StyledComboboxOption<T>) => {
            return (
              <Chip
                key={opt.id}
                label={opt.label}
                onRemove={() => onRemove(opt)}
              />
            );
          })}
        </div>
      )}

      <Combobox
        as="div"
        className={classNames("combobox", className)}
        value={value}
        {...props}
      >
        <div className="input">
          <label
            className={classNames("input__label", { "sr-only": !showLabel })}
            htmlFor=""
          >
            {label}
          </label>
          <div className="input__input-wrapper">
            <Combobox.Input
              onChange={(event) => setQuery(event.target.value)}
              autoComplete="off"
              className="combobox__input"
              placeholder={placeholder}
            />
            <Combobox.Button className="combobox__button">
              <ChevronDown
                className="combobox__button-icon"
                aria-hidden="true"
              />
            </Combobox.Button>
          </div>
        </div>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Combobox.Options className="combobox__options">
            {!filteredOptions.length ? (
              <span className="combobox__option">Nothing found</span>
            ) : (
              filteredOptions.map((option, i) => (
                <Combobox.Option
                  key={`${option.label}-${i}`}
                  className={({ active }) =>
                    classNames("combobox__option", {
                      "combobox__option--active": active,
                    })
                  }
                  value={option}
                >
                  {({ selected }) => (
                    <>
                      <span
                        className={classNames(`combobox__option-label`, {
                          "combobox__option-label--selected": selected,
                        })}
                      >
                        {option.label}
                      </span>
                      {selected && (
                        <Check
                          className="combobox__option-icon"
                          aria-hidden="true"
                        />
                      )}
                    </>
                  )}
                </Combobox.Option>
              ))
            )}
          </Combobox.Options>
        </Transition>
        {error && <span className="input__error">{error}</span>}
      </Combobox>
    </>
  );
};
