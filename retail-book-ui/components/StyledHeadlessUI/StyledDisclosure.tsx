import { Disclosure } from "@headlessui/react";
import classNames from "classnames";
import { ChevronUp } from "react-feather";
import { ExtractProps } from "types";

interface StyledDisclosureProps {
  title: string;
}

export const StyledDisclosure = ({
  className,
  children,
  title,
  ...props
}: ExtractProps<typeof Disclosure> & StyledDisclosureProps) => {
  return (
    <Disclosure
      as="div"
      className={classNames("disclosure", className)}
      {...props}
    >
      {({ open }) => (
        <>
          <Disclosure.Button className="disclosure__button">
            <span className="disclosure__title">{title}</span>
            <ChevronUp
              className={classNames("disclosure__icon", {
                "disclosure__icon--open": open,
              })}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="disclosure__panel">
            {children}
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
};
