import { Tab } from "@headlessui/react";
import classNames from "classnames";
import React, { Fragment, ReactNode } from "react";
import { ExtractProps } from "types";

interface StyledTabProps {
  children: ReactNode;
  disabled?: boolean;
  vertical?: boolean;
}

export const StyledTab = ({
  children,
  vertical,
  disabled,
  className,
  ...props
}: ExtractProps<typeof Tab> & StyledTabProps) => (
  <Tab as={Fragment} {...props}>
    {({ selected }) => (
      <button
        className={classNames("tab", className, {
          "tab--selected": selected,
          "tab--disabled": disabled,
          "tab--vertical": vertical,
        })}
      >
        {children}
      </button>
    )}
  </Tab>
);
