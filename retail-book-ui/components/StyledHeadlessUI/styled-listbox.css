/* HeadlessUI/Listbox */
.listbox {
  @apply relative inline-block w-full text-grey-step-0;

  &__button {
    @apply w-full cursor-default flex items-center justify-between bg-white text-base p-2xs border border-grey-step-1;
    border-radius: 1px;
  }

  &__button-label {
    @apply block truncate;
  }

  &__button-icon {
    @apply ml-2xs pointer-events-none h-5 w-5;
  }

  &__options {
    @apply dropdown -mt-px absolute z-10 top-full right-0;
    @apply w-full;
  }

  &__option {
    @apply w-full cursor-default flex items-center justify-between p-xs select-none transition-colors;
  }

  &__option--active {
    @apply bg-pink-step-2;
  }

  &__option-icon {
    @apply ml-2xs h-5 w-5;
  }

  &__option-label {
    @apply block truncate;
  }

  &__option-label--selected {
    @apply font-bold;
  }
}
