/* HeadlessUI/Listbox */
.combobox {
  @apply relative inline-block w-full text-grey-step-0;

  &__input {
    @apply grow border-none outline-none;
  }

  &__input:focus {
    border: none;
    box-shadow: none;
  }

  &__button {
    @apply flex items-center justify-center p-2xs;
  }

  &__button-icon {
    @apply h-5 w-5;
  }

  &__options {
    @apply dropdown -mt-px absolute z-10 top-full right-0;
    @apply w-full;
  }

  &__option {
    @apply w-full cursor-default flex items-center justify-between p-xs select-none transition-colors;
  }

  &__option--active {
    @apply bg-pink-step-2;
  }

  &__option-icon {
    @apply ml-2xs h-5 w-5;
  }

  &__option-label {
    @apply block truncate;
  }

  &__option-label--selected {
    @apply font-bold;
  }
}

.chip {
  @apply px-sm py-3xs text-sm inline-flex items-center justify-center rounded-full text-center  gap-3xs;
  @apply bg-bluetiful-step-3 text-grey-step-0;

  &[disabled] {
    @apply bg-grey-step-4 border-grey-step-4 text-grey-step-1 pointer-events-none;
  }

  &__button {
    @apply text-bluetiful-step--2 transition-colors translate-x-1/4;
    @apply hover:text-pink-step-0;
  }

  &__button-icon {
    @apply text-current;
    height: 1.25em;
    width: 1.25em;
  }
}
