/* HeadlessUI/Dialog */
.dialog {
  @apply relative z-50;

  &__overlay {
    @apply fixed inset-0 bg-black bg-opacity-50;
  }

  &__content {
    @apply fixed inset-0 overflow-y-scroll;
  }

  &__panel {
    @apply h-screen bg-white flex flex-col;
  }

  &__header {
    @apply p-sm flex items-start gap-sm justify-between bg-duke-blue-step-0;
  }

  &__title {
    @apply h4 text-white;
    @apply leading-6;
  }

  &__close {
    @apply text-white;
    @apply transition-colors hover:text-pink-step-0;
  }

  &__close-icon {
    @apply h-6 w-6;
  }

  &__description {
    @apply text-white;
  }

  &__body {
    @apply p-xl;
  }
  
  &__footer {
    @apply p-sm border-t border-grey-step-4;
  }

  &--flyout &__container {
    @apply fixed top-0 right-0 w-full max-w-md;
  }

  &--flyout &__panel {
    @apply shadow-xl;
  }

  &--flyout &__body {
    @apply grow overflow-auto;
  }

  &--full &__panel {
    @apply w-full;
  }

  @media screen(md) {
    &--modal &__container {
      @apply flex p-xs-md min-h-full items-center justify-center;
    }

    &--modal &__header {
      @apply rounded-t;
    }

    &--modal &__panel {
      @apply h-auto max-w-screen-xl shadow-xl rounded;
    }
  }
}
