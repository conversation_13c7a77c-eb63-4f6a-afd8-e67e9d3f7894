import { Listbox, Transition } from "@headlessui/react";
import classNames from "classnames";
import { Fragment } from "react";
import { Check, ChevronDown } from "react-feather";
import { ExtractProps } from "types";

export interface StyledListboxOption {
  value: unknown;
  label: string;
}

interface StyledListboxProps {
  value?: StyledListboxOption | StyledListboxOption[];
  defaultValue?: StyledListboxOption | StyledListboxOption[];
  label: string;
  options: StyledListboxOption[];
}

export const StyledListbox = ({
  className,
  options,
  value,
  defaultValue,
  label,
  ...props
}: ExtractProps<typeof Listbox> & StyledListboxProps) => {
  return (
    <Listbox
      value={value}
      defaultValue={defaultValue}
      as="div"
      className={classNames("listbox", className)}
      {...props}
    >
      <Listbox.Button className="listbox__button">
        <span className="listbox__button-label">{label}</span>
        <ChevronDown className="listbox__button-icon" aria-hidden="true" />
      </Listbox.Button>
      <Transition
        as={Fragment}
        leave="transition ease-in duration-100"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <Listbox.Options className="listbox__options">
          {options.map((option) => (
            <Listbox.Option
              key={`${option?.label}-${option.value}`}
              className={({ active }) =>
                classNames("listbox__option", {
                  "listbox__option--active": active,
                })
              }
              value={option}
            >
              {({ selected }) => (
                <>
                  <span
                    className={classNames(`listbox__option-label`, {
                      "listbox__option-label--selected": selected,
                    })}
                  >
                    {option.label ?? option.value}
                  </span>
                  {selected && (
                    <Check
                      className="listbox__option-icon"
                      aria-hidden="true"
                    />
                  )}
                </>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </Transition>
    </Listbox>
  );
};
