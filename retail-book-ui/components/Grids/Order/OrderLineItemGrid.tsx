import { useQuery } from "@tanstack/react-query";
import { Badge } from "components/Basic/Badge/Badge";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import { Container } from "components/Layout/Container";
import { StyledReactDataGrid, Column } from "components/StyledReactDataGrid/StyledReactDataGrid";
import { formatCurrency } from "currency";
import { formatNumber } from "helpers";
import { FunctionComponent, useCallback, useMemo, useState } from "react";
import { SortColumn } from "react-data-grid";
import { TDetailedOrder } from "types";
import { SelectThreeWayBoolValue } from "utils/misc";
import { viewItemisedOrderQuery } from "utils/queries";


interface IProps {
    offerId?: string;
    orderId?: string;
    currency: string;
    token?: string;
    showExisting: boolean;
    showErrors?:boolean;
    pageSize?:number;
}

interface Row {
    id: string;
    ref: string;
    existing: string;
    value: string;    
    taxWrapper?: boolean;
    validationError: string;
}

const columns: Column<Row>[] = [   
    {
      key: "ref",
      name: "Client Order Ref",
      sortable: true,
      resizable: true,
    },
    {
        key: "existing",
        name: "Existing Holding",
        sortable: true,
        resizable: true,
    },
    {
        key: "value",
        name: "Value",
        sortable: true,
        resizable: true,
        minWidth: 150,
    },    
    {
        key: "taxWrapper",
        name: "Tax Wrapper?",
        sortable: true,
        resizable: true,
        formatter: ({row}) => (<Badge theme={ SelectThreeWayBoolValue(row.taxWrapper,"success","failure",undefined) }>{ SelectThreeWayBoolValue(row.taxWrapper,"Yes","No","N/A")}</Badge>)         
    }
];

const errorColumn: Column<Row> = 
    {
        key:"validationError",
        name: "Validation Error",
        sortable: true,
        resizable: true
    };

export const OrderLineItemGrid : FunctionComponent<IProps> = ({offerId, orderId, token, currency, showExisting, showErrors, pageSize:_pageSize}) => {
    const [pageSize] = useState( () => _pageSize ? _pageSize : 5)
    
    const [filterErrorsOnly, setFilterErrorsOnly] = useState(() => showErrors ? !!showErrors : false);

    const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    
    const params = useMemo(() => {
        const filter = filterErrorsOnly ? ["validationFailed=true:eq"] : undefined;

        const sort =
            sortColumns.length > 0
                ? `${sortColumns[0].columnKey
                }=${sortColumns[0].direction.toLocaleLowerCase()}`
                : undefined;

        return {
            limit: pageSize,
            offset: (currentPage - 1) * pageSize,
            sort,
            filter,
        };
    }, [currentPage, sortColumns, filterErrorsOnly,pageSize]);

    const rawData = useQuery(viewItemisedOrderQuery(offerId, orderId, token, params))
    
    const totalItems = useMemo(
        () => rawData?.data?.pagination.count ?? 0,
        [rawData?.data?.pagination.count]
    );

    const toRow = useCallback( 
        (order:TDetailedOrder): Row => {
            return {
                id: order.id,
                ref: order.client_order_ref ?? "",
                existing: order.existing_holding ? formatNumber(order.existing_holding) : "",
                value: order.notional_value ? formatCurrency(order.notional_value, currency) : "",                
                taxWrapper: order.tax_wrapper,
                validationError: order.validation_error ?? "",
            }
        }, [currency]);
    
    const rows = useMemo( () => {
        if (!rawData.isLoading) 
            return rawData?.data?.data.map(toRow) ?? []
        return []
    },[rawData,toRow])

    const actualColumns = useMemo( () => {
        const cols = columns.filter(() => true);
        if (showErrors) {
            cols.unshift(errorColumn)
        }

        if (showExisting) return cols 

        return cols.filter( x => x.key != "existing" )
    },[showExisting,showErrors]);

    return (<Container>
               {/* <Container>
                <div className="flex flex-wrap justify-end mb-sm gap-sm">
                    <Filter label="Items" value={pageSize} options={allPageSizes} size="sm" onChange={setPageSize}/>
                </div>                    
    </Container> */}
                {
                    !!showErrors && (
                        <div className="flex flex-wrap justify-end mb-sm gap-sm">
                            <Checkbox label="Errors Only" checked={filterErrorsOnly} onChange={(e) => setFilterErrorsOnly(e.target.checked) }/>
                        </div>
                    )
                }
                <StyledReactDataGrid columns={actualColumns}
                                     rows={rows}
                                     sortColumns={sortColumns}
                                     onSortColumnsChange={setSortColumns}
                                     paginationProps={{
                                        onClick: (page) => setCurrentPage(page),
                                        currentPage: currentPage,
                                        totalItems: totalItems,
                                        pageSize: pageSize,
                    }} />
            </Container>)
}