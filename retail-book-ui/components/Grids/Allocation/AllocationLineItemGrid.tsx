import { useQuery } from "@tanstack/react-query";
import { Badge } from "components/Basic/Badge/Badge";
import { Percent } from "components/Composite/OfferPage/EditOffer/components/Percent";
import { Container } from "components/Layout/Container";
import { StyledReactDataGrid, Column } from "components/StyledReactDataGrid/StyledReactDataGrid";
import Decimal from "decimal.js";
import { Zero, formatNumber } from "helpers";
import { FunctionComponent,  useMemo, useState } from "react";
import { SortColumn } from "react-data-grid";
import {  TDetailedAllocation } from "types";
import { getAllocationBookQuery } from "utils/queries";


interface IProps {
    offerId?: string;
    allocationBookId?: string;
    
    showErrors: boolean;
    token?: string;
    pageSize?: number;

    colNames?: string[];    
    filter?: string[];
};

interface Row {
    id: string;
    ref?: string;
    intermediary?: string;
    existing?: Decimal;
    value?: Decimal;
    commissionDue?: boolean;
    taxWrapper?: boolean;
    validationError: string;
    applications?: Decimal;
    qty?: Decimal;
    allocQty?:Decimal;
    allocValue?:Decimal;    
};

const columns: Column<Row>[] = [
    {
        key: "validationError",
        name: "Warning",
        sortable: true,
        resizable: true
    },
    {
        key: "ref",
        name: "Cli Order Ref",
        sortable: true,
        resizable: true,
    },
    {
        key: "intermediary",
        name: "Intermediary",
        sortable: true,
        resizable: true,
    },
    {
        key: "applications",
        name: "Applications",
        sortable: true,
        resizable: true,
        formatter: ({row}) => {
            return (<span>{formatNumber(row.applications)}</span>)
        }
    },
    {
        key: "existing",
        name: "Existing Holding",
        sortable: true,
        resizable: true,
        formatter: ({row}) => {
            return (<span>{formatNumber(row.existing)}</span>)
        }
    },
    {
        key: "value",
        name: "Value",
        sortable: true,
        resizable: true,
        minWidth: 150,
        formatter: ({row}) => {
            return (<span>{formatNumber(row.value)}</span>)
        }
    },
    {
        key: "qty",
        name: "Qty",
        sortable: true,
        resizable: true,        
        formatter: ({row}) => {
            return (<span>{formatNumber(row.qty)}</span>)
        }
    },
    {
        key: "allocQty",
        name: "Alloc Qty",
        sortable: true,
        resizable: true,
        formatter: ({row}) => {
            return (<span>{formatNumber(row.allocQty)} <Percent reference={row.qty} value={row.allocQty}/></span>)
        }
    },
    {
        key: "allocValue",
        name: "Alloc Value",
        sortable: true,
        resizable: true,
        formatter: ({row}) => {
            return (<span>{formatNumber(row.allocValue)} <Percent reference={row.value} value={row.allocValue}/></span>)
        }
    },
    {
        key: "taxWrapper",
        name: "Tax Wrapper?",
        sortable: true,
        resizable: true,
        formatter: ({ row }) => {
            return (<span>{row.taxWrapper ? (<Badge theme={"success"}>Yes</Badge>) : <Badge theme={"warning"}>No</Badge>}</span>)
        }
    },
    {
        key: "commissionDue",
        name: "Comm due?",
        sortable: true,
        resizable: true,
        formatter: ({ row }) => {
            return (<span>{row.commissionDue ? (<Badge theme={"success"}>Yes</Badge>) : <Badge theme={"warning"}>No</Badge>}</span>)
        }
    }
];

const standardColumn = ["validationError","ref","intermediary","existing","value","commissionDue","taxWrapper"]

const toRow = (alloc: TDetailedAllocation): Row => {
        const row = {
            id: alloc.client_order_ref ?? "",
            ref: alloc.client_order_ref ?? "",
            existing: alloc.existing_holding ?? Zero,
            value: alloc.notional_value ?? Zero,
            commissionDue: alloc.commission_due,
            taxWrapper: alloc.tax_wrapper,
            validationError: alloc.validation_error ?? "",
            intermediary: alloc.intermediary ?? "",
            qty: alloc.qty ?? Zero,
            allocQty: alloc.shares_allocated??Zero,
            allocValue: alloc.allocation_value?? Zero,
            applications: alloc.applications ?? Zero,
        }
        return row;
    };

export const AllocationLineItemGrid: FunctionComponent<IProps> = ({ offerId, allocationBookId, token, showErrors, pageSize: _pageSize, filter, colNames=standardColumn }) => {
    const [pageSize] = useState(() => _pageSize ? _pageSize : 5)

    const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);
    const [currentPage, setCurrentPage] = useState(1);

    const params = useMemo(() => {
        const flter = showErrors ? ["validationFailed=true:eq"] : filter;

        const sort =
            sortColumns.length > 0
                ? `${sortColumns[0].columnKey
                }=${sortColumns[0].direction.toLocaleLowerCase()}`
                : undefined;

        return {
            limit: pageSize,
            offset: (currentPage - 1) * pageSize,
            sort,
            filter:flter,
        };
    }, [currentPage, sortColumns, showErrors, pageSize, filter]);

    const rawData = useQuery(getAllocationBookQuery(offerId ?? "", allocationBookId ?? "", token, params));

    const totalItems = useMemo(
        () => rawData?.data?.pagination.count ?? 0,
        [rawData?.data?.pagination.count]
    );

    const rows = useMemo(() => {
        if (!rawData.isLoading) {
            return rawData?.data?.data.map(toRow) ?? [];
        }
            
        return []
    }, [rawData])

    const selectedColumns = useMemo( () => {
        return colNames.map( c => {return columns.find( y => y.key == c) ?? columns[0]})  ?? [];        
    },[colNames]);

    return (
        <Container>
            <StyledReactDataGrid columns={selectedColumns}
                rows={rows}
                sortColumns={sortColumns}
                onSortColumnsChange={setSortColumns}
                paginationProps={{
                    onClick: (page) => setCurrentPage(page),
                    currentPage: currentPage,
                    totalItems: totalItems,
                    pageSize: pageSize ?? 5,
                }} />
        </Container>
    )
}