import {AggregateOrderFormData} from "components/Composite/OrderAggregateForm/OrderAggregateForm";
import Decimal from "decimal.js";
import {EInsiderEvent, EInsiderStatus, EOrderStatus, EWallCrossStatus} from "types";
import {TAggregateOrder} from "./types";
import {format, parse, parseISO} from "date-fns";

export const Zero = new Decimal(0);

export const DATETIME_LOCAL_DATE_FNS_FORMAT = "yyyy-MM-dd'T'HH:mm";
export const DATETIME_LOCAL_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
export const DATETIME_LOCAL_DATE_ONLY_FORMAT = "yyyy-MM-dd";

export const GetChangeType = (
  value: Decimal,
  previous: Decimal
): "increase" | "decrease" | undefined => {
  return value.eq(previous)
    ? undefined
    : value.gte(previous)
    ? "increase"
    : "decrease";
};

export const IsUndefinedOrNull = (x: unknown): boolean => {
  return x === undefined || x === null;
};

export const countChars = (x: string, chr: string): number => {
  let cnt = 0;
  for (let c = 0; c < x.length; c++) {
    if (x.charAt(c) === chr) {
      cnt++;
    }
  }
  return cnt;
};

export const formatNumberString = (amount: string) => {
  const hasPoint = amount.indexOf(".") !== -1;

  let result = "";
  let foundPoint = !hasPoint;
  let digitCount = 0;
  for (let c = amount.length - 1; c >= 0; c--) {
    if (digitCount === 3) {
      result = "," + result;
      digitCount = 0;
    }

    const ch = amount.charAt(c);

    if (ch === ".") {
      foundPoint = true;
      result = "." + result;
    } else {
      result = ch + result;
      if (foundPoint) {
        digitCount++;
      }
    }
  }

  return result;
};

export const formatNumberOrBlank = (amount: Decimal | undefined): string => {
  if (amount === undefined) return "";
  return formatNumberString(amount.toString());
};

export const formatNumberOrUndefined = (
  amount: Decimal | undefined
): string => {
  if (amount === undefined) return "";
  return formatNumberString(amount.toString());
};

export const formatNumber = (amount: Decimal | undefined) => {
  if (amount === undefined) return "0";

  return formatNumberString(amount.toString());
};

export const formatStatus = (status: EOrderStatus) => {
  if (status == EOrderStatus.REPLACEPENDING) {
    return "Replace Pending"
  }

  return status
}

export const formatIsoFromLocal = (val: string |null |undefined) => {
  if (val === null || val === undefined || (typeof(val) === 'string' && val === "")) {
    return undefined
  }
  return parse(val, DATETIME_LOCAL_DATE_FNS_FORMAT, new Date()).toISOString()
}

export const formatLocalFromIso = (val: string | null | undefined, defaultVal: string | undefined) => {
  if (val === null || val === undefined) {
    return defaultVal
  }
  return format(parseISO(val), DATETIME_LOCAL_DATE_FNS_FORMAT)
}


const kilobyte = 1024;
const megabyte = 1048576;
const gigabyte = megabyte*1024;

export const formatFileSize = (size: number | undefined) => {
  if (!size || size<0) return "--";

  if (size < kilobyte) {
    return `${size} Bytes`
  } 

  if (size < megabyte) {
    return `${(size/kilobyte).toFixed(2)} KiB`;
  }
  
  if (size < gigabyte)
  {
    return `${(size/megabyte).toFixed(2)} MiB`; 
  }

  return `${(size/gigabyte).toFixed(2)} GiB`;
};

export const asDecimal = (
  input: Decimal | string | undefined
): Decimal | undefined | null => {
  if (input instanceof Decimal) {
    return input;
  }

  if (input === undefined || input === null) {
    return input;
  }

  if (input === "") {
    return null;
  }

  return new Decimal(input.replaceAll(",", ""));
};

export const asDateInputString = (
    input: Date | undefined
): string | undefined => {
  if (input instanceof Date) {
    return input.toLocaleString('sv').slice(0,10) + "T" + input.toLocaleString('sv').slice(11, 16)
  }
  if (input === undefined || input === null) {
    return undefined;
  }
};

export const createOrderFromAggregateFormData = (
  formData: AggregateOrderFormData
): TAggregateOrder => {
  const aggregateOrder: Partial<TAggregateOrder> = {};

  // Note the form data holds all the values in strings
  //  this is mostly because the setting of the initial value in the form doesn't allow us
  //  to format the string (there is most likely a better solution to this problem).
  //  Potentially there's some efficiency to be gained by holding the decimal and not converting
  //  to / from the decimal when calculating totals.

  if (
    formData.non_shareholding?.applications ||
    formData.non_shareholding?.notional_value
  ) {
    aggregateOrder["non_shareholding"] = {
      applications: formData.non_shareholding.applications
        ? new Decimal(formData.non_shareholding.applications)
        : undefined,
      notional_value: formData.non_shareholding?.notional_value
        ? new Decimal(formData.non_shareholding?.notional_value)
        : undefined,
    };
  }

  if (
    formData.shareholding?.applications ||
    formData.shareholding?.notional_value ||
    formData.shareholding?.existing_holding
  ) {
    aggregateOrder["shareholding"] = {
      applications: formData.shareholding.applications
        ? new Decimal(formData.shareholding.applications)
        : undefined,
      notional_value: formData.shareholding.notional_value
        ? new Decimal(formData.shareholding.notional_value)
        : undefined,
      existing_holding: formData.shareholding.existing_holding
        ? new Decimal(formData.shareholding.existing_holding)
        : undefined,
    };
  }

  return aggregateOrder as TAggregateOrder;
};

export const orderByDate = (dateA: string|undefined, dateB: string|undefined, desc: boolean) => {
  //Handles empty dates by always placing them at the bottom.
  if (!dateA) return 1;
  if (!dateB) return -1;
  if (desc) {
    return new Date(dateA) > new Date(dateB) ? 1 : -1;
  }
  return new Date(dateA) < new Date(dateB) ? 1 : -1;
};

export const orderStatusThemeSelector = (status: EOrderStatus) => {
  switch (status) {
    case EOrderStatus.ACCEPTED:
      return "success";
    case EOrderStatus.DELETED:
      return "failure";
    case EOrderStatus.PENDING:
      return "warning";
    case EOrderStatus.REJECT:
      return "failure-strong";
    case EOrderStatus.SUPERSEDED:
      return "warning";
    case EOrderStatus.UNCONFIRMED:
      return "warning";
  }
};

export const wallCrossStatusThemeSelector = (status: EWallCrossStatus) => {
  switch (status) {
    case EWallCrossStatus.PENDING:
      return "warning";
    case EWallCrossStatus.ACCEPTED:
      return "success";
    case EWallCrossStatus.REJECTED:
      return "failure";
  }
  return undefined
};

export const insiderConsentThemeSelector = (status: EInsiderStatus) => {
  switch (status) {
    case EInsiderStatus.ACCEPTED:
      return "success";
    case EInsiderStatus.DECLINED:
      return "failure";
  }
  return undefined
};

export const insiderEventThemeSelector = (status: EInsiderEvent) => {
  switch (status) {
    case EInsiderEvent.WALL_CROSSING:
      return "light-bluetiful";
    case EInsiderEvent.TEAM_ACCESS:
      return "success";
  }
  return undefined
};

export const insiderEventTextSelector = (status: EInsiderEvent) => {
  switch (status) {
    case EInsiderEvent.WALL_CROSSING:
      return "Wall-Crossing";
    case EInsiderEvent.TEAM_ACCESS:
      return "Team Access";
  }
  return status
};

export const downloadFile = (file: File) => {
  const url = window.URL.createObjectURL(file);
  const anchor = document.createElement("a");
  anchor.href = url;
  anchor.download = file.name;
  document.body.appendChild(anchor);
  anchor.click();
  document.body.removeChild(anchor);
  URL.revokeObjectURL(url);
};
