/* @link https://utopia.fyi/type/calculator?c=320,16,1.125,1440,18,1.2,6,2,&s=0.75|0.5|0.25,1.5|2|3|4|6,s-l */

:root {
  --fluid-min-width: 320;
  --fluid-max-width: 1440;

  --fluid-screen: 100vw;
  --fluid-bp: calc(
    (var(--fluid-screen) - var(--fluid-min-width) / 16 * 1rem) /
      (var(--fluid-max-width) - var(--fluid-min-width))
  );
}

@media screen and (min-width: 1440px) {
  :root {
    --fluid-screen: calc(var(--fluid-max-width) * 1px);
  }
}

:root {
  --f--2-min: 12.64;
  --f--2-max: 12.5;
  --step--2: calc(
    ((var(--f--2-min) / 16) * 1rem) + (var(--f--2-max) - var(--f--2-min)) *
      var(--fluid-bp)
  );

  --f--1-min: 14.22;
  --f--1-max: 15;
  --step--1: calc(
    ((var(--f--1-min) / 16) * 1rem) + (var(--f--1-max) - var(--f--1-min)) *
      var(--fluid-bp)
  );

  --f-0-min: 16;
  --f-0-max: 18;
  --step-0: calc(
    ((var(--f-0-min) / 16) * 1rem) + (var(--f-0-max) - var(--f-0-min)) *
      var(--fluid-bp)
  );

  --f-1-min: 18;
  --f-1-max: 21.6;
  --step-1: calc(
    ((var(--f-1-min) / 16) * 1rem) + (var(--f-1-max) - var(--f-1-min)) *
      var(--fluid-bp)
  );

  --f-2-min: 20.25;
  --f-2-max: 25.92;
  --step-2: calc(
    ((var(--f-2-min) / 16) * 1rem) + (var(--f-2-max) - var(--f-2-min)) *
      var(--fluid-bp)
  );

  --f-3-min: 22.78;
  --f-3-max: 31.1;
  --step-3: calc(
    ((var(--f-3-min) / 16) * 1rem) + (var(--f-3-max) - var(--f-3-min)) *
      var(--fluid-bp)
  );

  --f-4-min: 25.63;
  --f-4-max: 37.32;
  --step-4: calc(
    ((var(--f-4-min) / 16) * 1rem) + (var(--f-4-max) - var(--f-4-min)) *
      var(--fluid-bp)
  );

  --f-5-min: 28.83;
  --f-5-max: 44.79;
  --step-5: calc(
    ((var(--f-5-min) / 16) * 1rem) + (var(--f-5-max) - var(--f-5-min)) *
      var(--fluid-bp)
  );

  --f-6-min: 32.44;
  --f-6-max: 53.75;
  --step-6: calc(
    ((var(--f-6-min) / 16) * 1rem) + (var(--f-6-max) - var(--f-6-min)) *
      var(--fluid-bp)
  );
}

/* @link https://utopia.fyi/space/calculator?c=320,16,1.125,1440,18,1.2,6,2,&s=0.75|0.5|0.25,1.5|2|3|4|6,xs-m */
:root {
  --fluid-min-width: 320;
  --fluid-max-width: 1440;

  --fluid-screen: 100vw;
  --fluid-bp: calc(
    (var(--fluid-screen) - var(--fluid-min-width) / 16 * 1rem) /
      (var(--fluid-max-width) - var(--fluid-min-width))
  );
}

@media screen and (min-width: 1440px) {
  :root {
    --fluid-screen: calc(var(--fluid-max-width) * 1px);
  }
}

:root {
  --fc-3xs-min: (var(--fc-s-min) * 0.25);
  --fc-3xs-max: (var(--fc-s-max) * 0.25);

  --fc-2xs-min: (var(--fc-s-min) * 0.5);
  --fc-2xs-max: (var(--fc-s-max) * 0.5);

  --fc-xs-min: (var(--fc-s-min) * 0.75);
  --fc-xs-max: (var(--fc-s-max) * 0.75);

  --fc-s-min: (var(--f-0-min, 16));
  --fc-s-max: (var(--f-0-max, 18));

  --fc-m-min: (var(--fc-s-min) * 1.5);
  --fc-m-max: (var(--fc-s-max) * 1.5);

  --fc-l-min: (var(--fc-s-min) * 2);
  --fc-l-max: (var(--fc-s-max) * 2);

  --fc-xl-min: (var(--fc-s-min) * 3);
  --fc-xl-max: (var(--fc-s-max) * 3);

  --fc-2xl-min: (var(--fc-s-min) * 4);
  --fc-2xl-max: (var(--fc-s-max) * 4);

  --fc-3xl-min: (var(--fc-s-min) * 6);
  --fc-3xl-max: (var(--fc-s-max) * 6);

  /* T-shirt sizes */
  --space-3xs: calc(
    ((var(--fc-3xs-min) / 16) * 1rem) + (var(--fc-3xs-max) - var(--fc-3xs-min)) *
      var(--fluid-bp)
  );
  --space-2xs: calc(
    ((var(--fc-2xs-min) / 16) * 1rem) + (var(--fc-2xs-max) - var(--fc-2xs-min)) *
      var(--fluid-bp)
  );
  --space-xs: calc(
    ((var(--fc-xs-min) / 16) * 1rem) + (var(--fc-xs-max) - var(--fc-xs-min)) *
      var(--fluid-bp)
  );
  --space-s: calc(
    ((var(--fc-s-min) / 16) * 1rem) + (var(--fc-s-max) - var(--fc-s-min)) *
      var(--fluid-bp)
  );
  --space-m: calc(
    ((var(--fc-m-min) / 16) * 1rem) + (var(--fc-m-max) - var(--fc-m-min)) *
      var(--fluid-bp)
  );
  --space-l: calc(
    ((var(--fc-l-min) / 16) * 1rem) + (var(--fc-l-max) - var(--fc-l-min)) *
      var(--fluid-bp)
  );
  --space-xl: calc(
    ((var(--fc-xl-min) / 16) * 1rem) + (var(--fc-xl-max) - var(--fc-xl-min)) *
      var(--fluid-bp)
  );
  --space-2xl: calc(
    ((var(--fc-2xl-min) / 16) * 1rem) + (var(--fc-2xl-max) - var(--fc-2xl-min)) *
      var(--fluid-bp)
  );
  --space-3xl: calc(
    ((var(--fc-3xl-min) / 16) * 1rem) + (var(--fc-3xl-max) - var(--fc-3xl-min)) *
      var(--fluid-bp)
  );

  /* One-up pairs */
  --space-3xs-2xs: calc(
    ((var(--fc-3xs-min) / 16) * 1rem) + (var(--fc-2xs-max) - var(--fc-3xs-min)) *
      var(--fluid-bp)
  );
  --space-2xs-xs: calc(
    ((var(--fc-2xs-min) / 16) * 1rem) + (var(--fc-xs-max) - var(--fc-2xs-min)) *
      var(--fluid-bp)
  );
  --space-xs-s: calc(
    ((var(--fc-xs-min) / 16) * 1rem) + (var(--fc-s-max) - var(--fc-xs-min)) *
      var(--fluid-bp)
  );
  --space-s-m: calc(
    ((var(--fc-s-min) / 16) * 1rem) + (var(--fc-m-max) - var(--fc-s-min)) *
      var(--fluid-bp)
  );
  --space-m-l: calc(
    ((var(--fc-m-min) / 16) * 1rem) + (var(--fc-l-max) - var(--fc-m-min)) *
      var(--fluid-bp)
  );
  --space-l-xl: calc(
    ((var(--fc-l-min) / 16) * 1rem) + (var(--fc-xl-max) - var(--fc-l-min)) *
      var(--fluid-bp)
  );
  --space-xl-2xl: calc(
    ((var(--fc-xl-min) / 16) * 1rem) + (var(--fc-2xl-max) - var(--fc-xl-min)) *
      var(--fluid-bp)
  );
  --space-2xl-3xl: calc(
    ((var(--fc-2xl-min) / 16) * 1rem) + (var(--fc-3xl-max) - var(--fc-2xl-min)) *
      var(--fluid-bp)
  );

  /* Custom pairs */
  --space-xs-m: calc(
    ((var(--fc-xs-min) / 16) * 1rem) + (var(--fc-m-max) - var(--fc-xs-min)) *
      var(--fluid-bp)
  );
}

/* react-toastify */
:root {
  --toastify-color-info: theme(colors.bluetiful.step-2);
  --toastify-color-success: theme(colors.utility-green.step-1);
  --toastify-color-warning: theme(colors.utility-yellow.step-1);
  --toastify-color-error: theme(colors.utility-red.step-1);

  --toastify-text-color-info: theme(colors.bluetiful.step--1);
  --toastify-text-color-success: theme(colors.utility-green.step-0);
  --toastify-text-color-warning: theme(colors.utility-yellow.step-0);
  --toastify-text-color-error: theme(colors.utility-red.step-0);

  --toastify-font-family: theme(fontFamily.sans);
}

.Toastify__toast {
  @apply rounded-none shadow;

  &--info .Toastify__close-button {
    @apply text-bluetiful-step--1;
  }
  &--success .Toastify__close-button {
    @apply text-utility-green-step-0;
  }
  &--warning .Toastify__close-button {
    @apply text-utility-yellow-step-0;
  }
  &--error .Toastify__close-button {
    @apply text-utility-red-step-0;
  }
}

.Toastify__progress-bar {
  height: 4px;
}

/** colored notifications share the same progress bar color **/
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info {
  @apply bg-bluetiful-step--1;
}
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success {
  @apply bg-utility-green-step-0;
}
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning {
  @apply bg-utility-yellow-step-0;
}
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error {
  @apply bg-utility-red-step-0;
}

/* react-tooltip */
:root {
  --rt-color-white: theme(colors.white);
  --rt-color-dark: theme(colors.black);

  --rt-color-info: theme(colors.bluetiful.step-2);
  --rt-color-success: theme(colors.utility-green.step-1);
  --rt-color-warning: theme(colors.utility-yellow.step-1);
  --rt-color-error: theme(colors.utility-red.step-1);

  --rt-opacity: 0.9;
}

/* custom */
:root {
  --brand-slant-angle: 36.575deg;
}

:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid theme(colors.utility-yellow.step-0);
  outline-offset: 2px;
}

body {
  @apply body;
}
