.bg-book {
  @apply bg-right-top bg-no-repeat bg-fixed;

  background-image: url("/BookGraphicGrey.svg");
  background-size: cover;
}

.dropdown {
  @apply max-h-72 min-w-fit overflow-auto bg-white text-black text-base border border-grey-step-1 shadow-lg;
}

.link {
  @apply text-duke-blue-step-0 underline underline-offset-4 transition-colors;
  @apply visited:text-bluetiful-step--1;
  @apply focus:text-duke-blue-step-0;
  @apply hover:text-pink-step-0;
}

.form {
  @apply flex flex-col gap-lg;

  &-section {
    @apply flex flex-col gap-sm;
  }

  &-title {
    @apply h2 mb-3xs;
  }

  &-legend {
    @apply h3;
  }

  &-fieldset {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-sm;
  }

  &-actions {
    @apply flex flex-wrap gap-sm items-center;
  }
}

/* Typography */
/* Placeholder until font has been purchased, this version of FF Mark has been lifted from https://www.fontshop.com/families/ff-mark and should not be used for production */
@font-face {
  font-family: "FF Mark";
  src: url("/fonts/FFMarkHeavy.woff") format("woff");
  font-weight: bold;
  font-style: normal;
}
/*  */

.h0 {
  @apply font-bold text-5xl leading-tight;
}
.h1 {
  @apply font-bold text-4xl leading-tight;
}
.h2 {
  @apply font-bold text-2xl leading-tight;
}
.h3 {
  @apply font-bold text-xl leading-snug;
}
.h4 {
  @apply font-bold text-lg leading-snug;
}
.h5 {
  @apply font-bold text-base leading-normal;
}
.h6 {
  @apply font-bold text-sm leading-normal;
}

.lead {
  @apply font-sans font-normal text-lg leading-snug;
}

.body {
  @apply font-sans font-normal text-base leading-normal;
}
