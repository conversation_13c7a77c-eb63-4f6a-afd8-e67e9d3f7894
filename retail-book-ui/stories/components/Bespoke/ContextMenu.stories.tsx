import { ComponentMeta, ComponentStory } from "@storybook/react";
import { ContextMenu as ContextMenuComponent } from "components/Bespoke/ContextMenu/ContextMenu";

export default {
  title: "Components/Bespoke/ContextMenu",
  component: ContextMenuComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    items: Array(5)
      .fill(1)
      .map((n) => ({
        label: `Item ${n}`,
        onClick: () => console.log(`Clicked item ${n}`),
      })),
  },
} as ComponentMeta<typeof ContextMenuComponent>;

const Template: ComponentStory<typeof ContextMenuComponent> = (args) => (
  <ContextMenuComponent {...args} />
);

export const ContextMenu = Template.bind({});
