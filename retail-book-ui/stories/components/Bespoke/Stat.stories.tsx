import { faker } from "@faker-js/faker";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Stat as StatComponent } from "components/Bespoke/Stat/Stat";

export default {
  title: "Components/Bespoke/Stat",
  component: StatComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    title: "Total Value",
    value: faker.finance.amount(1000, 100000, 2, "£", true),
    change: `${faker.datatype.number(100)}%`,
    changeType: "decrease",
  },
} as ComponentMeta<typeof StatComponent>;

const Template: ComponentStory<typeof StatComponent> = (args) => (
  <StatComponent {...args} />
);

export const Stat = Template.bind({});
