import { faker } from "@faker-js/faker";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { PageHeader as PageHeaderComponent } from "components/Bespoke/PageHeader/PageHeader";

export default {
  title: "Components/Bespoke/PageHeader",
  component: PageHeaderComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    title: "Page title",
    standfirst: faker.lorem.sentences(2),
    crumbs: [
      {
        name: "Link one",
        href: "#",
      },
      {
        name: "Link two",
        href: "#",
      },
    ],
  },
} as ComponentMeta<typeof PageHeaderComponent>;

const Template: ComponentStory<typeof PageHeaderComponent> = (args) => (
  <PageHeaderComponent {...args} />
);

export const PageHeader = Template.bind({});
