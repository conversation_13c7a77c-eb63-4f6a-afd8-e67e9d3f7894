import { ComponentMeta, ComponentStory } from "@storybook/react";
import { OrderHistoryTable as OrderHistoryTableComponent } from "components/Bespoke/OrderHistoryTable/OrderHistoryTable";
import { generateOffer } from "data/offer";
import { generateOrder, createOrderHistoryEntry } from "data/order";

const offer = generateOffer();
const order1 = generateOrder(offer);
const order2 = generateOrder(offer);
const order3 = generateOrder(offer);

export default {
  title: "Components/Bespoke/OrderHistoryTable",
  component: OrderHistoryTableComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    offer,
    orderHistory: [
      createOrderHistoryEntry(order1),
      createOrderHistoryEntry(order2, order1),
      createOrderHistoryEntry(order3, order2),
    ],
  },
} as ComponentMeta<typeof OrderHistoryTableComponent>;

const Template: ComponentStory<typeof OrderHistoryTableComponent> = (args) => (
  <OrderHistoryTableComponent {...args} />
);

export const OrderHistoryTable = Template.bind({});
