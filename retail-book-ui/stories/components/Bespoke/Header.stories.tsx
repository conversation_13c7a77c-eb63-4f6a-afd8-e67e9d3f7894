import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Header as HeaderComponent } from "components/Bespoke/Header/Header";

export default {
  title: "Components/Bespoke/Header",
  component: HeaderComponent,
  parameters: {
    layout: "fullscreen",
  },
  args: {
    currentPath: "/",
    hasNotification: false,
  },
} as ComponentMeta<typeof HeaderComponent>;

const Template: ComponentStory<typeof HeaderComponent> = (args) => (
  <HeaderComponent {...args} />
);

export const LoggedOut = Template.bind({});
// export const LoggedIn = Template.bind({});
