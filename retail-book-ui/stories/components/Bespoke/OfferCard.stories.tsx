import { ComponentMeta, ComponentStory } from "@storybook/react";
import { OfferCard as OfferCardComponent } from "components/Bespoke/OfferCard/OfferCard";
import { generateOffer, getOfferSummary } from "data/offer";
// import { generateOrder } from "data/order";
import { EOfferStatus } from "types";

const offer = generateOffer();
// const order = generateOrder(offer);

export default {
  title: "Components/Bespoke/OfferCard",
  component: OfferCardComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    offer,
  },
} as ComponentMeta<typeof OfferCardComponent>;

const Template: ComponentStory<typeof OfferCardComponent> = (args) => (
  <div className="w-80">
    <OfferCardComponent {...args} />
  </div>
);

export const Default = Template.bind({});

export const PreLaunch = Template.bind({});
PreLaunch.args = {
  offer: getOfferSummary(generateOffer(undefined, EOfferStatus.PRE_LAUNCH)),
};
export const BooksOpen = Template.bind({});
BooksOpen.args = {
  offer: getOfferSummary(
    generateOffer(undefined, EOfferStatus.APPLICATIONS_OPEN)
  ),
};
export const BooksClosed = Template.bind({});
BooksClosed.args = {
  offer: getOfferSummary(
    generateOffer(undefined, EOfferStatus.APPLICATIONS_CLOSED)
  ),
};
// export const WithOrder = Template.bind({});
// WithOrder.args = {
//   order,
// };
