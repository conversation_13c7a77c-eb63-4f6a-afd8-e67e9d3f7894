import { ComponentMeta, ComponentStory } from '@storybook/react';
import {
  Filter as FilterComponent,
  FilterOption,
} from 'components/Bespoke/Filter/Filter';
import { useState } from 'react';

const options: FilterOption[] = [
  { label: 'Option 1', value: 'option-1' },
  { label: 'Option 2', value: 'option-2' },
  { label: 'Option 3', value: 'option-3' },
];

export default {
  title: 'Components/Bespoke/Filter',
  component: FilterComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    label: 'Option',
    labelPlural: 'Options',
    multiple: false,
    options,
  },
  argTypes: {
    size: {
      options: [undefined, 'sm'],
      control: 'radio',
    },
  },
} as ComponentMeta<typeof FilterComponent>;

const Template: ComponentStory<typeof FilterComponent> = (args) => {
  const [selected, setSelected] = useState(options[0]);
  const [selectedMultiple, setSelectedMultiple] = useState([options[0]]);

  return (
    <FilterComponent
      {...args}
      value={args.multiple ? selectedMultiple : selected}
      onChange={args.multiple ? setSelectedMultiple : setSelected}
    />
  );
};

export const Filter = Template.bind({});
