import { faker } from "@faker-js/faker";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { DownloadCard as DownloadCardComponent } from "components/Bespoke/DownloadCard/DownloadCard";
import { EContractStatus } from "types";

export default {
  title: "Components/Bespoke/DownloadCard",
  component: DownloadCardComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    title: faker.commerce.productName(),
    type: faker.system.commonFileExt(),
    size: faker.datatype.number({ min: 1, max: 10, precision: 0.01 }),
    status: EContractStatus.SIGNED,
  },
  argTypes: {
    status: {
      control: "radio",
      options: [EContractStatus.SIGNED, EContractStatus.UNSIGNED],
    },
  },
} as ComponentMeta<typeof DownloadCardComponent>;

const Template: ComponentStory<typeof DownloadCardComponent> = (args) => (
  <DownloadCardComponent {...args} />
);

export const DownloadCard = Template.bind({});
