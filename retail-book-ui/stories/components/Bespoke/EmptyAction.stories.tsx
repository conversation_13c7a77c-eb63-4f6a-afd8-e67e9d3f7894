import { faker } from "@faker-js/faker";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { ArrowRight, UploadCloud } from "react-feather";

export default {
  title: "Components/Bespoke/EmptyAction",
  component: EmptyAction,
  parameters: {
    layout: "centered",
  },
  args: {
    message: faker.lorem.sentences(2),
  },
} as ComponentMeta<typeof EmptyAction>;

const Template: ComponentStory<typeof EmptyAction> = (args) => (
  <EmptyAction {...args} />
);

export const Default = Template.bind({});
export const WithAction = Template.bind({});
WithAction.args = {
  action: {
    onClick: () => console.log("click!"),
    label: "Action",
    icon: ArrowRight,
  },
};
export const WithCustomIcon = Template.bind({});
WithCustomIcon.args = {
  icon: UploadCloud,
  iconClassName: "text-pink-step-1",
  action: {
    onClick: () => console.log("click!"),
    label: "Action",
    icon: ArrowRight,
  },
};
