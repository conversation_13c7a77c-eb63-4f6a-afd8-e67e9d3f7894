import { ComponentMeta, ComponentStory } from "@storybook/react";
import { OrderCard as OrderCardComponent } from "components/Bespoke/OrderCard/OrderCard";
import { generateOffer } from "data/offer";
import { generateOrder } from "data/order";
import { EOrderType } from "types";

const offer = generateOffer();

export default {
  title: "Components/Bespoke/OrderCard",
  component: OrderCardComponent,
  parameters: {
    layout: "centered",
  },
} as ComponentMeta<typeof OrderCardComponent>;

const Template: ComponentStory<typeof OrderCardComponent> = (args) => (
  <OrderCardComponent {...args} />
);

export const Summary = Template.bind({});
Summary.args = {
  order: generateOrder(offer, EOrderType.AGGREGATE),
  offer,
};
export const Itemised = Template.bind({});
Itemised.args = {
  order: generateOrder(generateOffer(), EOrderType.DETAILED),
  offer,
};
