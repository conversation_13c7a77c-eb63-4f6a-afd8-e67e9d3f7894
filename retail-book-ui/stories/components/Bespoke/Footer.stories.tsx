import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Footer as FooterComponent } from "components/Bespoke/Footer/Footer";

export default {
  title: "Components/Bespoke/Footer",
  component: FooterComponent,
  parameters: {
    layout: "fullscreen",
  },
  args: {
    currentPath: "/",
  },
} as ComponentMeta<typeof FooterComponent>;

const Template: ComponentStory<typeof FooterComponent> = (args) => (
  <FooterComponent {...args} />
);

export const Footer = Template.bind({});
