import { ComponentMeta, ComponentStory } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ViewOfferHeader as OfferHeaderComponent } from "components/Composite/OfferPage/ViewOffer/ViewOfferHeader";
import { Container } from "components/Layout/Container";
import { generateOffer } from "data/offer";
const queryClient = new QueryClient();
export default {
  title: "Components/Bespoke/OfferHeader",
  component: OfferHeaderComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    offer: generateOffer(),
  },
} as ComponentMeta<typeof OfferHeaderComponent>;

const Template: ComponentStory<typeof OfferHeaderComponent> = (args) => (
  <QueryClientProvider client={queryClient}>
    <Container>
      <OfferHeaderComponent {...args} />
    </Container>
  </QueryClientProvider>
);

export const OfferHeader = Template.bind({});
