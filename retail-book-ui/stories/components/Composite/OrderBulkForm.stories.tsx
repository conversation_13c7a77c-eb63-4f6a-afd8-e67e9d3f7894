import { ComponentMeta, ComponentStory } from "@storybook/react";
import { OrderBulkForm } from "components/Composite/OrderBulkForm/OrderBulkForm";
import { Container } from "components/Layout/Container";
import { generateOffer } from "data/offer";
import { generateOrder } from "data/order";
import { useState } from "react";
import { EOrderType } from "types";

export default {
  title: "Components/Composite/OrderBulkForm",
  component: OrderBulkForm,
  parameters: {
    layout: "centered",
  },
  args: {
    onSubmit: undefined,
  },
} as ComponentMeta<typeof OrderBulkForm>;

const Template: ComponentStory<typeof OrderBulkForm> = (args) => {
  const [step, setStep] = useState(1);
  return (
    <Container>
      <OrderBulkForm {...args} step={step} setStep={setStep} />
    </Container>
  );
};

const offer = generateOffer();
const order = generateOrder(offer, EOrderType.DETAILED);

export const Add = Template.bind({});
Add.args = { offer };

export const Edit = Template.bind({});
Edit.args = { offer, previousOrder: order };
