import { ComponentMeta, ComponentStory } from "@storybook/react";
import { OrderAggregateForm } from "components/Composite/OrderAggregateForm/OrderAggregateForm";
import { Container } from "components/Layout/Container";
import { generateOffer } from "data/offer";
import { generateOrder } from "data/order";
import { useState } from "react";

export default {
  title: "Components/Composite/OrderAggregateForm",
  component: OrderAggregateForm,
  parameters: {
    layout: "centered",
  },
  args: {
    onSubmit: undefined,
  },
} as ComponentMeta<typeof OrderAggregateForm>;

const Template: ComponentStory<typeof OrderAggregateForm> = (args) => {
 
  return (
    <Container>
      <OrderAggregateForm {...args} />
    </Container>
  );
};

const offer = generateOffer();
const order = generateOrder(offer);

export const Add = Template.bind({});
Add.args = { offer };

export const Edit = Template.bind({});
Edit.args = { offer, order };
