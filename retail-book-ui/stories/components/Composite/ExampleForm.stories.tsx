import { faker } from "@faker-js/faker";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Checkbox } from "components/Basic/Checkbox/Checkbox";
import { Input } from "components/Basic/Input/Input";
import { Textarea } from "components/Basic/Textarea/Textarea";
import { Select } from "components/Basic/Select/Select";
import { Container } from "components/Layout/Container";

export default {
  title: "Components/Composite/ExampleForm",
  parameters: {
    layout: "padded",
  },
} as ComponentMeta<any>;

const Template: ComponentStory<any> = () => {
  return (
    <Container narrow>
      <form className="form">
        <section className="form-section">
          <h2 className="form-title">Form Title</h2>
          <fieldset className="form-fieldset">
            <Input placeholder="Placeholder" label="Input Label" />
            <Input label="Input Label" />
          </fieldset>
        </section>
        <section className="form-section">
          <h2 className="form-title">Form Title</h2>
          <legend className="form-legend">Form Legend</legend>
          <Textarea label="Textarea Label" rows={4} />

          <fieldset className="form-fieldset">
            <Input label="Input Label" />
            <Input label="Input Label" />
          </fieldset>

          <Select label="Select Label">
            <option>Option 1</option>
            <option>Option 2</option>
            <option>Option 3</option>
          </Select>

          <fieldset className="form-fieldset">
            <Select label="Select Label">
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </Select>
            <Select label="Select Label">
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </Select>
          </fieldset>
        </section>
        <section className="form-section">
          <legend className="form-legend">Form Legend</legend>
          <Checkbox
            label="Checkbox Label"
            description={faker.lorem.sentence()}
          />
          <Checkbox
            label="Checkbox Label"
            description={faker.lorem.sentence()}
          />
          <Checkbox
            label="Checkbox Label"
            description={faker.lorem.sentence()}
          />
        </section>
        <section className="form-section">
          <legend className="form-legend">Form Legend</legend>
          <Checkbox
            label="Checkbox Label"
            description={faker.lorem.sentence()}
            type="radio"
          />
          <Checkbox
            label="Checkbox Label"
            description={faker.lorem.sentence()}
            type="radio"
          />
          <Checkbox
            label="Checkbox Label"
            description={faker.lorem.sentence()}
            type="radio"
          />
        </section>
      </form>
    </Container>
  );
};

export const ExampleForm = Template.bind({});
