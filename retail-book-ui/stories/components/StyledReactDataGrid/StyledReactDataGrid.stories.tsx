import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Badge } from "components/Basic/Badge/Badge";
import { Filter } from "components/Bespoke/Filter/Filter";
import { Container } from "components/Layout/Container";
import {
  Column,
  StyledReactDataGrid as StyledReactDataGridComponent,
} from "components/StyledReactDataGrid/StyledReactDataGrid";
import { formatCurrency } from "currency";
import { generateOffer } from "data/offer";
import { createOrderHistoryEntry, generateOrder } from "data/order";
import { format, parseISO } from "date-fns";
import Decimal from "decimal.js";
import {
  formatNumber,
  orderByDate,
  orderStatusThemeSelector,
  Zero,
} from "helpers";
import { EOrderStatus, EOrderType, TOrder } from "types";

const offer = generateOffer();

interface Row {
  id: string;
  date: string;
  intermediary: string | undefined;
  status: EOrderStatus;
  type: EOrderType;
  applications: string;
  value: string;
  sort: {
    date: string;
    applications: Decimal | undefined;
    value: Decimal | undefined;
  };
}

const getRowFromOrder = (order: TOrder): Row => {
  return {
    id: order.id,
    date: format(parseISO(order.order_date), "dd/MM/yyyy HH:mm"),
    intermediary: order.intermediary,
    status: order.status,
    type: order.order_type,
    applications: formatNumber(order.totals.applications),
    value: formatCurrency(order.totals.notional_value ?? Zero, offer.currency),
    sort: {
      date: order.order_date,
      applications: order.totals.applications,
      value: order.totals.notional_value,
    },
  };
};

const rows: Row[] = Array(100)
  .fill(1)
  .map(() => {
    const order = generateOrder(offer);
    const order2 = generateOrder(offer);
    const order3 = generateOrder(offer);

    const h1 = createOrderHistoryEntry(order);
    const h2 = createOrderHistoryEntry(order2, order);
    const h3 = createOrderHistoryEntry(order3, order2);

    const children = [
      { ...getRowFromOrder(h1.order), id: `${order.id}-1`, parentId: order.id },
      { ...getRowFromOrder(h2.order), id: `${order.id}-2`, parentId: order.id },
      { ...getRowFromOrder(h3.order), id: `${order.id}-3`, parentId: order.id },
    ];

    return {
      ...getRowFromOrder(order),
      children,
    };
  });

const columns: Column<Row>[] = [
  {
    key: "date",
    name: "Date",
    sortable: true,
    comparator: (a, b) => orderByDate(a.sort.date, b.sort.date, false),
    resizable: true,
  },
  {
    key: "intermediary",
    name: "Intermediary",
    sortable: true,
    comparator: (a, b) =>
      (a?.intermediary ?? "").localeCompare(b?.intermediary ?? ""),
    resizable: true,
  },
  {
    key: "status",
    name: "Status",
    sortable: true,
    comparator: (a, b) => a.status.localeCompare(b.status),
    formatter: ({ row }) => (
      <Badge theme={orderStatusThemeSelector(row.status)}>{row.status}</Badge>
    ),
    resizable: true,
  },
  {
    key: "type",
    name: "Type",
    sortable: true,
    comparator: (a, b) => a.type.localeCompare(b.type),
    resizable: true,
  },
  {
    key: "applications",
    name: "Applications",
    sortable: true,
    comparator: (a, b) =>
      (a.sort.applications ?? Zero)
        .minus(b.sort.applications ?? Zero)
        .toNumber(),
    formatter: ({ row }) => (
      <span className="font-mono">{row.applications}</span>
    ),
    resizable: true,
    headerCellClass: "text-right",
    cellClass: "text-right",
  },
  {
    key: "value",
    name: "Value",
    sortable: true,
    comparator: (a, b) =>
      (a.sort.value ?? Zero).minus(b.sort.value ?? Zero).toNumber(),
    formatter: ({ row }) => <span className="font-mono">{row.value}</span>,
    resizable: true,
    headerCellClass: "text-right",
    cellClass: "text-right",
  },
];

export default {
  title: "Components/StyledReactDataGrid/StyledReactDataGrid",
  parameters: {
    layout: "padded",
  },
} as ComponentMeta<typeof StyledReactDataGridComponent>;

const Template: ComponentStory<typeof StyledReactDataGridComponent> = () => {
  return (
    <Container>
      <div className="flex justify-end gap-sm">
        <Filter
          size="sm"
          value={[]}
          label={"Status"}
          labelPlural={"Statuses"}
          options={[
            { value: "1", label: "Option 1" },
            { value: "2", label: "Option 2" },
          ]}
        />
        <Filter
          size="sm"
          value={[]}
          label={"Intermediary"}
          labelPlural={"Intermediaries"}
          options={[
            { value: "1", label: "Option 1" },
            { value: "2", label: "Option 2" },
          ]}
        />
        <Filter
          size="sm"
          value={[]}
          label={"Type"}
          labelPlural={"Types"}
          options={[
            { value: "1", label: "Option 1" },
            { value: "2", label: "Option 2" },
          ]}
        />
      </div>
      <StyledReactDataGridComponent
        selectColumn
        expandColumn
        contextMenuItems={[
          { label: "Item One", onClick: (_e, row) => console.log(row) },
          { label: "Item Two", onClick: (_e, row) => console.log(row) },
          { label: "Item Three", onClick: (_e, row) => console.log(row) },
        ]}
        columns={columns}
        rows={rows}
        pageSize={10}
      />
    </Container>
  );
};

export const StyledReactDataGrid = Template.bind({});
