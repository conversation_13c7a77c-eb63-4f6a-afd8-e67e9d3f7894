import { ComponentMeta, ComponentStory } from "@storybook/react";
import { But<PERSON> } from "components/Basic/Button/Button";
import { Check, X, AlertCircle, Info } from "react-feather";
import { toast, ToastContainer } from "react-toastify";

export default {
  title: "Components/Vendor/Toastify",
  parameters: {
    layout: "centered",
  },
} as ComponentMeta<any>;

const Template: ComponentStory<any> = () => {
  return (
    <>
      <div className="flex gap-sm items-center">
        <Button onClick={() => toast.success("Success toast!")}>Success</Button>
        <Button onClick={() => toast.error("Error toast!")}>Error</Button>
        <Button onClick={() => toast.warning("Warning toast!")}>Warning</Button>
        <Button onClick={() => toast.info("Info toast!")}>Info</Button>
      </div>
      <ToastContainer
        position="bottom-right"
        theme="colored"
        hideProgressBar
        icon={({ type }) => {
          return type === "success" ? (
            <Check aria-hidden="true" className="h-5 w-5" />
          ) : type === "error" ? (
            <X aria-hidden="true" className="h-5 w-5" />
          ) : type === "warning" ? (
            <AlertCircle aria-hidden="true" className="h-5 w-5" />
          ) : (
            <Info aria-hidden="true" className="h-5 w-5" />
          );
        }}
      />
    </>
  );
};

export const Toastify = Template.bind({});
