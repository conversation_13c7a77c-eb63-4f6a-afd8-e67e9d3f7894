import { faker } from "@faker-js/faker";
import { Tab } from "@headlessui/react";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import classNames from "classnames";
import { Badge } from "components/Basic/Badge/Badge";
import { EmptyAction } from "components/Bespoke/EmptyAction/EmptyAction";
import { Container } from "components/Layout/Container";
import { StyledTab as StyledTabComponent } from "components/StyledHeadlessUI/StyledTab";
import { AlertCircle } from "react-feather";

export default {
  title: "Components/StyledHeadlessUI/StyledTab",
  component: StyledTabComponent,
  parameters: {
    layout: "padded",
  },
  args: {
    vertical: false,
  },
} as ComponentMeta<typeof StyledTabComponent>;

const Template: ComponentStory<typeof StyledTabComponent> = (args) => (
  <Container>
    <Tab.Group
      as="div"
      vertical={args.vertical}
      className={classNames({
        "grid grid-cols-12 gap-md": args.vertical,
      })}
    >
      <Tab.List
        className={classNames("tab-list", {
          "tab-list--vertical col-span-4": args.vertical,
          "tab-list--bordered": !args.vertical,
        })}
      >
        <StyledTabComponent {...args}>Tab 1 Title</StyledTabComponent>
        <StyledTabComponent {...args}>
          <span>Tab with error</span>
          <AlertCircle
            className="h-5 w-5 text-utility-red-step-0"
            aria-hidden="true"
          />
        </StyledTabComponent>
        <StyledTabComponent {...args}>
          <span>Tab with badge</span>
          <Badge theme="info">3</Badge>
        </StyledTabComponent>
      </Tab.List>
      <Tab.Panels
        className={classNames({
          "tab-panel": !args.vertical,
          "col-span-8": args.vertical,
        })}
      >
        <Tab.Panel>
          <EmptyAction message="Tab 1" />
        </Tab.Panel>
        <Tab.Panel>
          <EmptyAction message="Tab 2" />
        </Tab.Panel>
        <Tab.Panel>
          <EmptyAction message="Tab 3" />
        </Tab.Panel>
      </Tab.Panels>
    </Tab.Group>
  </Container>
);

export const StyledTab = Template.bind({});
