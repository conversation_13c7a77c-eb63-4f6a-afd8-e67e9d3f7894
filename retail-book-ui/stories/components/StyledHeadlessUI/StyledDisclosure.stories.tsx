import { faker } from '@faker-js/faker';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import { StyledDisclosure as StyledDisclosureComponent } from 'components/StyledHeadlessUI/StyledDisclosure';

export default {
  title: 'Components/StyledHeadlessUI/StyledDisclosure',
  component: StyledDisclosureComponent,
  parameters: {
    layout: 'centered',
  },
} as ComponentMeta<typeof StyledDisclosureComponent>;

const Template: ComponentStory<typeof StyledDisclosureComponent> = () => (
  <StyledDisclosureComponent title="Disclosure title" className="w-96">
    <p>{faker.lorem.paragraph()}</p>
  </StyledDisclosureComponent>
);

export const StyledDisclosure = Template.bind({});
