import { ComponentMeta, ComponentStory } from "@storybook/react";
import {
  StyledListbox as StyledListboxComponent,
  StyledListboxOption,
} from "components/StyledHeadlessUI/StyledListbox";
import { useState } from "react";

export default {
  title: "Components/StyledHeadlessUI/StyledListbox",
  component: StyledListboxComponent,
  parameters: {
    layout: "centered",
  },
} as ComponentMeta<typeof StyledListboxComponent>;

const options: StyledListboxOption[] = [
  { label: "Option 1", value: "option-1" },
  { label: "Option 2", value: "option-2" },
  { label: "Option 3", value: "option-3" },
];

const Template: ComponentStory<typeof StyledListboxComponent> = () => {
  const [selected, setSelected] = useState(options[0]);

  return (
    <StyledListboxComponent
      value={selected}
      onChange={setSelected}
      options={options}
      label={selected.label}
    />
  );
};

export const StyledListbox = Template.bind({});
