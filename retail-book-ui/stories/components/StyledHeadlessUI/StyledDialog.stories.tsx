import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Button } from "components/Basic/Button/Button";
import { Prose } from "components/Basic/Prose/Prose";
import { StyledDialog as StyledDialogComponent } from "components/StyledHeadlessUI/StyledDialog";
import { useState } from "react";

export default {
  title: "Components/StyledHeadlessUI/StyledDialog",
  component: StyledDialogComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    title: "Example Dialog",
    description: "The description of the example dialog",
    footer: <Button>An action</Button>,
    type: "modal",
    fullWidth: false,
  },
  argTypes: {
    type: {
      control: "radio",
      options: ["modal", "flyout"],
    },
    fullWidth: {
      control: "boolean",
    },
  },
} as ComponentMeta<typeof StyledDialogComponent>;

const Template: ComponentStory<typeof StyledDialogComponent> = (args) => {
  const [open, setOpen] = useState(true);

  return (
    <>
      <Button onClick={() => setOpen(true)}>Show Dialog</Button>
      <StyledDialogComponent
        {...args}
        open={open}
        onClose={() => setOpen(false)}
      >
        <Prose>
          <p>
            Lorem, ipsum dolor sit amet consectetur adipisicing elit.
            Praesentium vitae cumque sequi hic minus! Quo quod iste, error saepe
            officiis tenetur molestiae inventore minus fuga vero sint? Repellat,
            eligendi at.
          </p>
        </Prose>
      </StyledDialogComponent>
    </>
  );
};

export const StyledDialog = Template.bind({});
