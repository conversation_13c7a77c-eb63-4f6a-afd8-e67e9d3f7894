import { ComponentMeta, ComponentStory } from "@storybook/react";
import {
  StyledCombobox as StyledComboboxComponent,
  StyledComboboxOption,
} from "components/StyledHeadlessUI/StyledCombobox";
import { useState } from "react";

export default {
  title: "Components/StyledHeadlessUI/StyledCombobox",
  component: StyledComboboxComponent,
  parameters: {
    layout: "centered",
  },
} as ComponentMeta<typeof StyledComboboxComponent>;

const options: StyledComboboxOption[] = [
  { id: "Option-1", label: "Option 1", value: "option-1" },
  { id: "Option-2", label: "Option 2", value: "option-2" },
  { id: "Option-3", label: "Option 3", value: "option-3" },
];

const Template: ComponentStory<typeof StyledComboboxComponent> = () => {
  const [selected, setSelected] = useState<StyledComboboxOption[]>([]);

  return (
    <StyledComboboxComponent
      value={selected}
      onChange={(options: StyledComboboxOption[]) => setSelected(options)}
      options={options}
      multiple
      onRemove={(opt) =>
        setSelected((existing) => existing.filter((o) => o.id !== opt.id))
      }
      label="Choose options"
    />
  );
};

export const StyledCombobox = Template.bind({});
