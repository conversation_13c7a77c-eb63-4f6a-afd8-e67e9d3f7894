import { ComponentMeta, ComponentStory } from '@storybook/react';
import { StandaloneLink as StandaloneLinkComponent } from 'components/Basic/StandaloneLink/StandaloneLink';

export default {
  title: 'Components/Basic/StandaloneLink',
  component: StandaloneLinkComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    href: '#',
  },
} as ComponentMeta<typeof StandaloneLinkComponent>;

const Template: ComponentStory<typeof StandaloneLinkComponent> = (args) => (
  <StandaloneLinkComponent {...args}>Link Text</StandaloneLinkComponent>
);

export const StandaloneLink = Template.bind({});
