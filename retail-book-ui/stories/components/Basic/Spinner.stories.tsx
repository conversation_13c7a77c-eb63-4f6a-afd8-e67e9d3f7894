import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Spinner as SpinnerComponent } from 'components/Basic/Spinner/Spinner';

export default {
  title: 'Components/Basic/Spinner',
  component: SpinnerComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    message: 'Loading, please wait...',
  },
} as ComponentMeta<typeof SpinnerComponent>;

const Template: ComponentStory<typeof SpinnerComponent> = (args) => (
  <SpinnerComponent {...args}>Spinner</SpinnerComponent>
);

export const Spinner = Template.bind({});
