import { faker } from '@faker-js/faker';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import { TimeDefinition as TimeDefinitionComponent } from 'components/Basic/Definition/TimeDefinition';
import { addHours } from 'date-fns';
import { Calendar, File } from 'react-feather';

export default {
  title: 'Components/Basic/TimeDefinition',
  component: TimeDefinitionComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    icon: Calendar,
    iconClassName: 'text-pink-step-0',
    term: 'Date',
    date: addHours(new Date(), 3),
    showCountdown: true,
    showTime: true,
  },
} as ComponentMeta<typeof TimeDefinitionComponent>;

const Template: ComponentStory<typeof TimeDefinitionComponent> = (args) => (
  <TimeDefinitionComponent {...args} />
);

export const TimeDefinition = Template.bind({});
