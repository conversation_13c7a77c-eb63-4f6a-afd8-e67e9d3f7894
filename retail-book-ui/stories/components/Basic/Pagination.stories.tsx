import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Pagination as PaginationComponent } from 'components/Basic/Pagination/Pagination';

export default {
  title: 'Components/Basic/Pagination',
  component: PaginationComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    currentPage: 1,
    pageSize: 10,
    totalItems: 123,
    prevLink: '#',
    nextLink: '#',
    pages: [
      {
        number: 1,
        href: '#',
      },
      {
        number: 2,
        href: '#',
      },
      {
        number: 3,
        href: '#',
      },
    ],
  },
} as ComponentMeta<typeof PaginationComponent>;

const Template: ComponentStory<typeof PaginationComponent> = (args) => (
  <PaginationComponent {...args} />
);

export const Pagination = Template.bind({});
