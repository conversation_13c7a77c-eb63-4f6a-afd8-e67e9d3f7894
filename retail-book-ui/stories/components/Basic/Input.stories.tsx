import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Input as InputComponent } from "components/Basic/Input/Input";

export default {
  title: "Components/Basic/Input",
  component: InputComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    name: "input",
    label: "Input Label",
    placeholder: "Placeholder...",
    disabled: false,
    showLabel: true,
  },
  argTypes: {
    disabled: {
      control: { type: "boolean" },
    },
  },
} as ComponentMeta<typeof InputComponent>;

const Template: ComponentStory<typeof InputComponent> = (args) => (
  <InputComponent {...args} />
);

export const Input = Template.bind({});
