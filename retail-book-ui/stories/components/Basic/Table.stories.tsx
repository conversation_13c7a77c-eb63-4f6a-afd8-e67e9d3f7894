import { faker } from '@faker-js/faker';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Table as TableComponent } from 'components/Basic/Table/Table';
import { Container } from 'components/Layout/Container';

export default {
  title: 'Components/Basic/Table',
  component: TableComponent,
  parameters: {
    layout: 'centered',
  },
} as ComponentMeta<typeof TableComponent>;

const rows = Array(5)
  .fill(1)
  .map(() => ({
    uuid: faker.datatype.uuid(),
    datetime: faker.datatype.datetime().toLocaleDateString(),
    float: faker.datatype.float(),
    hexadecimal: faker.datatype.hexadecimal(),
    number: faker.datatype.number(),
    string: faker.datatype.string(),
    paragraph: faker.lorem.paragraph(),
  }));

const Template: ComponentStory<typeof TableComponent> = (args) => (
  <Container>
    <TableComponent {...args}>
      <TableComponent.Head>
        <TableComponent.Row>
          {Object.keys(rows[0]).map((key, index) => (
            <TableComponent.HeaderCell key={index}>
              {key}
            </TableComponent.HeaderCell>
          ))}
        </TableComponent.Row>
      </TableComponent.Head>
      <TableComponent.Body>
        {rows.map((row: any, index) => {
          return (
            <TableComponent.Row key={index}>
              {Object.keys(row).map((key: keyof typeof row, index) => (
                <TableComponent.Cell key={index} heading={key as string}>
                  {row[key]}
                </TableComponent.Cell>
              ))}
            </TableComponent.Row>
          );
        })}
      </TableComponent.Body>
      <TableComponent.Caption>This is the table caption</TableComponent.Caption>
    </TableComponent>
  </Container>
);

export const Table = Template.bind({});
Table.args = {
  compact: false,
};
