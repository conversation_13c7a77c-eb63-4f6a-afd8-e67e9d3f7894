import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Breadcrumb as BreadcrumbComponent } from 'components/Basic/Breadcrumb/Breadcrumb';

export default {
  title: 'Components/Basic/Breadcrumb',
  component: BreadcrumbComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    currentPath: '/2',
    crumbs: [
      {
        name: 'Page name 1',
        href: '/1',
      },
      {
        name: 'Page name 2',
        href: '/2',
      },
    ],
  },
} as ComponentMeta<typeof BreadcrumbComponent>;

const Template: ComponentStory<typeof BreadcrumbComponent> = (args) => (
  <BreadcrumbComponent {...args} />
);

export const Breadcrumb = Template.bind({});
