import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Checkbox as CheckboxComponent } from 'components/Basic/Checkbox/Checkbox';

export default {
  title: 'Components/Basic/Checkbox',
  component: CheckboxComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    name: 'input',
    label: 'Checkbox Input',
    showLabel: true,
    disabled: false,
  },
  argTypes: {
    disabled: {
      control: { type: 'boolean' },
    },
  },
} as ComponentMeta<typeof CheckboxComponent>;

const Template: ComponentStory<typeof CheckboxComponent> = (args) => (
  <CheckboxComponent {...args} />
);

export const Checkbox = Template.bind({});
