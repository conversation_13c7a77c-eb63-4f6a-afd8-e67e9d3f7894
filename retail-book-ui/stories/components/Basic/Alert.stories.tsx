import { faker } from "@faker-js/faker";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Alert as AlertComponent } from "components/Basic/Alert/Alert";

export default {
  title: "Components/Basic/Alert",
  component: AlertComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    date: new Date(),
    title: faker.lorem.word(),
    message: faker.lorem.paragraph(),
    action: {
      label: "Action",
      onClick: () => console.log("Alert action clicked!"),
    },
  },
} as ComponentMeta<typeof AlertComponent>;

const Template: ComponentStory<typeof AlertComponent> = (args) => (
  <div className="w-96">
    <AlertComponent {...args} />
  </div>
);

export const Alert = Template.bind({});
