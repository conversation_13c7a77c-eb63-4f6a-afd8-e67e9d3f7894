import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Select as SelectComponent } from 'components/Basic/Select/Select';

export default {
  title: 'Components/Basic/Select',
  component: SelectComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    name: 'select',
    label: 'Select',
    disabled: false,
    showLabel: true,
    valid: false,
  },
  argTypes: {
    disabled: {
      control: { type: 'boolean' },
    },
  },
} as ComponentMeta<typeof SelectComponent>;

const Template: ComponentStory<typeof SelectComponent> = (args) => (
  <SelectComponent {...args}>
    <option>Option Label 1</option>
    <option>Option Label 2</option>
    <option>Option Label 3</option>
    <option>Option Label 4</option>
  </SelectComponent>
);

export const Select = Template.bind({});
