import { faker } from '@faker-js/faker';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Definition as DefinitionComponent } from 'components/Basic/Definition/Definition';
import { File } from 'react-feather';

export default {
  title: 'Components/Basic/Definition',
  component: DefinitionComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    icon: File,
    term: faker.lorem.word(),
    description: faker.lorem.words(3),
  },
} as ComponentMeta<typeof DefinitionComponent>;

const Template: ComponentStory<typeof DefinitionComponent> = (args) => (
  <DefinitionComponent {...args} />
);

export const Definition = Template.bind({});
