import { ComponentMeta, ComponentStory } from '@storybook/react';
import { LoadingBar as LoadingBarComponent } from 'components/Basic/LoadingBar/LoadingBar';

export default {
  title: 'Components/Basic/LoadingBar',
  component: LoadingBarComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    progress: 75,
    message: 'Loading, please wait...',
  },
} as ComponentMeta<typeof LoadingBarComponent>;

const Template: ComponentStory<typeof LoadingBarComponent> = (args) => (
  <LoadingBarComponent {...args}>LoadingBar</LoadingBarComponent>
);

export const LoadingBar = Template.bind({});
