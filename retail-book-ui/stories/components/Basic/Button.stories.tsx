import { ComponentMeta, ComponentStory } from "@storybook/react";
import { Button as ButtonComponent } from "components/Basic/Button/Button";
import { ChevronRight, Download, MoreVertical } from "react-feather";

export default {
  title: "Components/Basic/Button",
  component: ButtonComponent,
  parameters: {
    layout: "centered",
  },
  args: {
    disabled: false,
    loading: false,
    icon: ChevronRight,
    children: "Button",
  },
  argTypes: {
    theme: {
      options: [
        "Default",
        "Outline",
        "Light",
        "Outline Light",
        "Special",
        "Link",
      ],
      mapping: {
        Default: undefined,
        Outline: "outline",
        Light: "light",
        "Outline Light": "outline-light",
        Special: "special",
        Link: "link",
      },
    },
    size: {
      options: ["Default", "Small", "Large"],
      mapping: {
        Default: undefined,
        Small: "sm",
        Large: "lg",
      },
    },
  },
} as ComponentMeta<typeof ButtonComponent>;

const Template: ComponentStory<typeof ButtonComponent> = (args) => (
  <ButtonComponent {...args}>{args.children}</ButtonComponent>
);

export const Default = Template.bind({});
export const LargeIcon = Template.bind({});
LargeIcon.args = {
  icon: Download,
};
export const IconOnly = Template.bind({});
IconOnly.args = {
  icon: MoreVertical,
  children: undefined,
};
