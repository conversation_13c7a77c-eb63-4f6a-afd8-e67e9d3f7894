import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Textarea as TextareaComponent } from 'components/Basic/Textarea/Textarea';

export default {
  title: 'Components/Basic/Textarea',
  component: TextareaComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    name: 'input',
    label: 'Textarea Label',
    placeholder: 'Placeholder...',
    disabled: false,
    showLabel: true,
    valid: false,
  },
  argTypes: {
    disabled: {
      control: { type: 'boolean' },
    },
  },
} as ComponentMeta<typeof TextareaComponent>;

const Template: ComponentStory<typeof TextareaComponent> = (args) => (
  <TextareaComponent {...args} />
);

export const Textarea = Template.bind({});
