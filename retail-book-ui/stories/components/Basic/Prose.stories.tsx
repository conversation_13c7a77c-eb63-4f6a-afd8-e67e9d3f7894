import { faker } from '@faker-js/faker';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Prose as ProseComponent } from 'components/Basic/Prose/Prose';
import { Container } from 'components/Layout/Container';

export default {
  title: 'Components/Basic/Prose',
  component: ProseComponent,
} as ComponentMeta<typeof ProseComponent>;

const Template: ComponentStory<typeof ProseComponent> = () => (
  <Container>
    <ProseComponent>
      <h1>h1. {faker.lorem.sentences(2)}</h1>
      <p className="lead">Lead text: {faker.lorem.sentences(5)}</p>
      <p>{faker.lorem.sentences(3)}</p>
      <ul>
        <li>{faker.lorem.sentences(1)}</li>
        <li>{faker.lorem.sentences(1)}</li>
        <li>{faker.lorem.sentences(1)}</li>
      </ul>

      <h2>h2. {faker.lorem.sentences(3)}</h2>
      <p>{faker.lorem.sentences(3)}</p>
      <p>{faker.lorem.sentences(3)}</p>
      <ol>
        <li>{faker.lorem.sentences(1)}</li>
        <li>{faker.lorem.sentences(1)}</li>
        <li>{faker.lorem.sentences(1)}</li>
      </ol>

      <h3>h3. {faker.lorem.sentences(3)}</h3>
      <p>{faker.lorem.sentences(3)}</p>
      <p>{faker.lorem.sentences(3)}</p>

      <h4>h4. {faker.lorem.sentences(3)}</h4>
      <p>{faker.lorem.sentences(3)}</p>
      <p>{faker.lorem.sentences(3)}</p>

      <h5>h5. {faker.lorem.sentences(3)}</h5>
      <p>{faker.lorem.sentences(3)}</p>
      <p>{faker.lorem.sentences(3)}</p>

      <h6>h6. {faker.lorem.sentences(3)}</h6>
      <p>{faker.lorem.sentences(3)}</p>
      <p>{faker.lorem.sentences(3)}</p>
    </ProseComponent>
  </Container>
);

export const Prose = Template.bind({});
