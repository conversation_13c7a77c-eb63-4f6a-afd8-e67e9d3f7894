import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Delta as DeltaComponent } from 'components/Basic/Delta/Delta';

export default {
  title: 'Components/Basic/Delta',
  component: DeltaComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    value: 100,
    changeType: 'increase',
  },
} as ComponentMeta<typeof DeltaComponent>;

const Template: ComponentStory<typeof DeltaComponent> = (args) => (
  <DeltaComponent {...args} />
);

export const Delta = Template.bind({});
