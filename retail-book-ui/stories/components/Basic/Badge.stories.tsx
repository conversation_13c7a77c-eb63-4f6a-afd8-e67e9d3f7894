import { ComponentMeta, ComponentStory } from '@storybook/react';
import { Badge as BadgeComponent } from 'components/Basic/Badge/Badge';

export default {
  title: 'Components/Basic/Badge',
  component: BadgeComponent,
  parameters: {
    layout: 'centered',
  },
} as ComponentMeta<typeof BadgeComponent>;

const Template: ComponentStory<typeof BadgeComponent> = (args) => (
  <BadgeComponent {...args}>Badge</BadgeComponent>
);

export const Badge = Template.bind({});
