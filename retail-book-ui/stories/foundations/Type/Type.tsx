import tailwindConfig from "tailwind.config";
import resolveConfig from "tailwindcss/resolveConfig";

const fullConfig = resolveConfig(tailwindConfig);
const fontSize: any = fullConfig.theme?.fontSize ?? [];

export const Type = () => {
  return (
    <ul>
      {Object.keys(fontSize).map((fontSize: string) => (
        <li key={fontSize} className={`mb-md text-${fontSize}`}>
          <strong>Text Size:</strong> {fontSize}
        </li>
      ))}
    </ul>
  );
};
