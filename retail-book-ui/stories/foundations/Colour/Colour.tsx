import tailwindConfig from "tailwind.config";
import resolveConfig from "tailwindcss/resolveConfig";

const fullConfig = resolveConfig(tailwindConfig);
const colours: any = fullConfig.theme?.colors ?? [];

export const Colour = () => {
  return (
    <>
      {Object.keys(colours)
        .filter((colour) => typeof colours[colour] === "object")
        .map((colour) => (
          <section key={colour} className={`mb-lg`}>
            <h1 className="font-medium mb-sm">
              Colour Name: <strong>{colour}</strong>
            </h1>
            <ul className="flex space-x-sm">
              {Object.keys(colours[colour]).map((shade) => (
                <li key={shade} className="text-center">
                  <div
                    className={`flex items-center justify-center h-24 w-24 rounded-full bg-${colour}-${shade}`}
                  >
                    <span>{colours[colour][shade]}</span>
                  </div>
                  <p className="text-sm">{shade}</p>
                </li>
              ))}
            </ul>
          </section>
        ))}
    </>
  );
};
