import { useEffect, useState } from "react";

export function useDebouncedBoolean(value: boolean, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState(false);

  useEffect(
    () => {
      // if its false set it immediately and bail
      if (value === false) return setDebouncedValue(false);

      // Update debounced value after delay
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    },
    [value, delay] // Only re-call effect if value or delay changes
  );

  return debouncedValue;
}