import { useMemo } from "react";
import { EUserRole, TUser } from "types";

export interface NavigationItem {
  label: string;
  href: string;
}

export const useNavigation = (user?: TUser) => {
  const navigation: NavigationItem[] = useMemo(() => {
    let items = [
      {
        label: "Offers",
        href: "/",
      },
    ];

    if (user) {
      items = [
        ...items,
        {
          label: "Orders",
          href: "/orders",
        },
      ];
    }

    if (user && user?.organisational_role.toLowerCase() === EUserRole.ISSUER) {
      items = [
        ...items,
        {
          label: "Settlements",
          href: "/settlements",
        },
      ];
    }

    if (user?.gatekeeper && user?.organisational_role.toLowerCase() === EUserRole.INTERMEDIARY)
      items = [
        ...items,
        {
          label: "Wall-Crossings",
          href: "/wallcrossings",
        },
      ];

    return items;
  }, [user]);

  const userNavigation: NavigationItem[] = useMemo(() => {
    if (user && user.role === "Manager") {
      return [
        { label: "Profile", href: "/profile" },
        { label: "Organisation", href: "/organisation"},        
        { label: "Team", href: "/team" },
        { label: "Logout", href: "/auth/signout" },
      ];
    } else if (user) {
      return [
        { label: "Profile", href: "/profile" },
        { label: "Organisation", href: "/organisation"},
        { label: "Logout", href: "/auth/signout" },
      ];
    } else return [{ label: "Login", href: "/auth/signin" }];
  }, [user]);

  const footerNavigation: NavigationItem[] = useMemo(
    () => [
      { label: "FAQs", href: "/faqs" },
      { label: "Privacy", href: "https://documents.prod.retailbook.com/content/rb-external-privacy-notice.pdf" },
      { label: "Terms of use", href: "https://documents.prod.retailbook.com/content/rb-terms-of-use.pdf" },
      { label: "Cookies notice", href: "https://documents.prod.retailbook.com/content/rb-cookies-notice.pdf" },
      { label: "Order Execution Policy", href: "https://cdn.prod.website-files.com/63bed5b900a95d9dd2541ec9/66ec238744ebf7c3119f5d83_Order%20Execution%20Policy%20(August%202024).pdf" },
    ],
    []
  );

  return { navigation, userNavigation, footerNavigation };
};
