import { useEffect } from "react";
import { FieldErrors, FieldValues, UseFormClearErrors } from "react-hook-form";

export const useReactHookFormServerErrors = <T extends FieldValues>(
  errors: FieldErrors<T> | undefined,
  formErrors: FieldErrors<T> | undefined,
  setError: (
    fieldName: keyof T,
    error: { type: string; message: string }
  ) => void,
  clearErrors: UseFormClearErrors<any>
) => {
  useEffect(() => {   
    if (!errors || !setError || !clearErrors) return;

    if (formErrors) {
      Object.keys(formErrors).forEach((key) => {
        clearErrors(key)
      });
    }

    Object.keys(errors).forEach((key) => {  
      // Check if we need to re-set these errors or not; if we don't do this we get infinite loops
      if (!formErrors || errors[key]?.type !== formErrors[key]?.type || errors[key]?.message !== formErrors[key]?.message) {    
        setError(key as keyof T, { type: "server", message: errors[key as keyof T]?.message as string,});
      }
    });
  }, [errors, setError, formErrors, clearErrors]);
};
