import { FieldErrors } from "react-hook-form";
import { ApiError } from "types";
import { AxiosError } from "axios";

export const useFieldErrors = (error: AxiosError<ApiError> | null) => {
  if (!error) return {};

  const fieldErrors: FieldErrors = {
    root: {
      summary: {
        type: "server",
        message: error.response?.data.message,
      },
    },
  };

  const fields = error.response?.data.detail?.field_errors ?? {};

  Object.keys(fields).forEach((key) => {
    fieldErrors[key] = { type: "server", message: fields[key][0] };
  });

  return fieldErrors;
};
