import { ApiError, Resource, TOffer } from "types";
import { Api } from "./api";
import { downloadFile } from "helpers";

export const downloadResource = async (file?:Resource, offer?:TOffer, accessToken?:string) => {
    //  Trigger download...
    if (!file?.id) return;
    if (!offer) return;
    
    const data = await Api.getDocument(
      offer.id,
      file.id,
      accessToken
    );

    const f = new File([data], file.file_name, {
      type: data.type,
    });

    downloadFile(f);
}

export const selectApiError = (err?:ApiError) => {
  return err?.detail?.field_errors?.root[0] ?? err?.message;
}

export const parseApiError = (err? :ApiError, fieldNameMapper? : (x:string)=> string ) => {
  const message = selectApiError(err)

  return (
    <>
      <p>{message}</p>
      <ul>
      {
        Object.entries(err?.detail?.field_errors ?? {}).filter(([k,v]) => k!="root").map( ([k,v]) => {
          return (<li>{ fieldNameMapper ? fieldNameMapper(k) : k}: {v}</li>)
        })
      }
      </ul>

    </>
  )
}


export const SelectThreeWayBoolValue = <T,>(input:boolean|undefined|null, truthy: T, falsy: T, nully: T) : T => {
  if (input === undefined || input === null) {
    return nully;
  }
  if (!!input) {
    return truthy;
  }
  return falsy;
};