// Convert the information returned from the server into the correct type, primarily to create

import Decimal from "decimal.js";
import { Zero } from "helpers";
import {
  ApiError,
  ApiItemisedUploadResponse,
  IAllocationBase,
  IOrderBase,
  TAggregatedAllocation,
  TAllocation,
  TDetailedAllocation,
  TDetailedOrder,
  TOffer,
  TOfferSummary,
  TOrder,
  TOrderBreakdown,
  TOrderBreakdownL2,
  TOrderHistory,
  TOrderLineItem,
  TOfferMetricsSnapshot,
  TOfferMetricsTotals,
  TIntermediaryOfferMetric,
  TSettlementObligation,
  TSettlementBook,
} from "types";

export const toApiError = (str: string | undefined): ApiError | undefined => {
  if (str) {
    return JSON.parse(str) as ApiError;
  } else {
    return undefined;
  }
}

export const encodeDecimal = (
  d: Decimal | undefined | null
): string | undefined | null => {
  if (d === null) {
    return null;
  }
  return d ? d.toString() : undefined;
};

export const toItemisedUploadResponse = (
  x: ApiItemisedUploadResponse
): ApiItemisedUploadResponse => ({
  ...x,
  totals: x.totals ? toIOrderBase(x.totals) : undefined,
});

export const toOrderLineItem = (x: TOrderLineItem): TOrderLineItem => ({
  ...x,
  notional_value: new Decimal(x.notional_value),
  existing_holding: x.existing_holding
    ? new Decimal(x.existing_holding)
    : undefined,
});

export const toTOfferSummary = (summary: TOfferSummary): TOfferSummary => {
  return {
    ...summary,
    offer_price: summary.offer_price ? new Decimal(summary.offer_price) : undefined,
    price_range_low: summary.price_range_low ? new Decimal(summary.price_range_low) : undefined,
    price_range_high: summary.price_range_high ? new Decimal(summary.price_range_high) : undefined,
    totals: summary.totals ? toIOrderBase(summary.totals) : undefined,
  };
};

export const toTOffer = (offer: TOffer): TOffer => {
  return {
    ...offer,
    min_order_amount: offer.min_order_amount
      ? new Decimal(offer.min_order_amount)
      : undefined,
    offer_price: offer.offer_price ? new Decimal(offer.offer_price) : undefined,
    price_range_low: offer.price_range_low ? new Decimal(offer.price_range_low) : undefined,
    price_range_high: offer.price_range_high ? new Decimal(offer.price_range_high) : undefined,
    raise_amount: offer.raise_amount
      ? new Decimal(offer.raise_amount)
      : undefined,
  };
};

export const toTDetailedAllocation = (allocation: TDetailedAllocation): TDetailedAllocation => {
  return {
    ...allocation,
    notional_value: allocation.notional_value ? new Decimal(allocation.notional_value) : undefined,
    existing_holding: allocation.existing_holding ? new Decimal(allocation.existing_holding) : undefined,
    qty: allocation.qty ? new Decimal(allocation.qty) : undefined,
    allocation_value: allocation.allocation_value ? new Decimal(allocation.allocation_value) : undefined,
    shares_allocated: allocation.shares_allocated ? new Decimal(allocation.shares_allocated) : undefined,
    applications: allocation.applications ? new Decimal(allocation.applications) : undefined
  };
}

export const toTAggregatedAllocation = (allocation: TAggregatedAllocation) : TAggregatedAllocation => {
  
  return {
    ...allocation,
    allocated_orders: allocation.allocated_orders ? new Decimal(allocation.allocated_orders) : undefined,
    number_of_orders: allocation.number_of_orders ? new Decimal(allocation.number_of_orders) : undefined,
    alloc_quantity: allocation.alloc_quantity ? new Decimal(allocation.alloc_quantity): undefined,
    alloc_value: allocation.alloc_value ? new Decimal(allocation.alloc_value) : undefined,
    applications: allocation.applications ? new Decimal(allocation.applications) : undefined,
    qty: allocation.qty ? new Decimal(allocation.qty) : undefined,
    value: allocation.value ? new Decimal(allocation.value) : undefined,
  }
}

export const toTAllocation = (allocation: TAllocation): TAllocation => {
  return {
    ...allocation,
    totals: toIAllocationBase(allocation.totals),
    shareholdings: allocation.shareholdings ? toIAllocationBaseOptional(allocation.shareholdings) : undefined,
    non_shareholdings: allocation.non_shareholdings ? toIAllocationBaseOptional(allocation.non_shareholdings): undefined,
  };
}

export const toIAllocationBaseOptional = (x: IAllocationBase): IAllocationBase | undefined => {
  const allocationBase = toIAllocationBase(x);
  if (allocationBase.applications?.eq(Zero) && 
  allocationBase.notional_value?.eq(Zero) && 
  allocationBase.order_quantity?.eq(Zero) &&
  allocationBase.num_orders?.eq(Zero) &&
  allocationBase.allocated_orders?.eq(Zero)  &&
  allocationBase.unallocated_orders?.eq(Zero) &&
  allocationBase.allocation_quantity?.eq(Zero) &&
  allocationBase.allocation_value?.eq(Zero)) {
    return undefined;
  }
  return allocationBase;
}

export const toIAllocationBase = (x: IAllocationBase): IAllocationBase => {
  return {
    applications: x.applications ? new Decimal(x.applications) : undefined,
    notional_value: x.notional_value ? new Decimal(x.notional_value) : undefined,
    order_quantity: x.order_quantity ? new Decimal(x.order_quantity) : undefined,
    num_orders: x.num_orders ? new Decimal(x.num_orders) : undefined,
    allocated_orders: x.allocated_orders ? new Decimal(x.allocated_orders) : undefined,
    unallocated_orders: x.unallocated_orders ? new Decimal(x.unallocated_orders) : undefined,
    allocation_value: x.allocation_value ? new Decimal(x.allocation_value) : undefined,
    allocation_quantity: x.allocation_quantity ? new Decimal(x.allocation_quantity) : undefined,
  };
}

export const toTOrder = (order: TOrder): TOrder => {
  return {
    ...order,
    totals: toIOrderBase(order.totals),
    non_shareholding: order.non_shareholding
      ? toIOrderBaseOptional(order.non_shareholding)
      : undefined,
    shareholding: order.shareholding
      ? toIOrderBaseOptional(order.shareholding)
      : undefined,
  };
};

export const toTOfferMetricsSnapshot = (snapshot: TOfferMetricsSnapshot): TOfferMetricsSnapshot => {
  return {
    ...snapshot,
    intermediaries: snapshot.intermediaries?.map(toTIntermediaryOfferMetric) ?? [],
    number_of_offers: snapshot.number_of_offers ? new Decimal(snapshot.number_of_offers) : new Decimal(0),
  };
};

export const toTOfferMetricsTotals = (totals: TOfferMetricsTotals): TOfferMetricsTotals => {
  return {
    ...totals,
    offers: totals.offers ? new Decimal(totals.offers) : new Decimal(0),
    currency: totals.currency?.map(toTIntermediaryOfferMetric) ?? [],
    intermediary_currency: totals.intermediary_currency?.map(toTIntermediaryOfferMetric) ?? [],
  };
};

export const toTIntermediaryOfferMetric = (metric: TIntermediaryOfferMetric): TIntermediaryOfferMetric => {
  return {
    ...metric,
    order_value: metric?.order_value ? new Decimal(metric.order_value) : undefined,
    applications: metric?.applications ? new Decimal(metric.applications) : undefined,
    allocation_value: metric?.allocation_value ? new Decimal(metric.allocation_value) : undefined,
  };
};

export const toIOrderBaseOptional = (x: IOrderBase): IOrderBase | undefined => {
  const orderBase = toIOrderBase(x);
  if (orderBase.applications?.eq(Zero) && orderBase.notional_value?.eq(Zero)) {
    return undefined;
  }
  return orderBase;
};

export const toIOrderBase = (x: IOrderBase): IOrderBase => {
  return {
    applications: x.applications ? new Decimal(x.applications) : undefined,
    existing_holding: x.existing_holding
      ? new Decimal(x.existing_holding)
      : undefined,
    notional_value: x.notional_value
      ? new Decimal(x.notional_value)
      : undefined,
  };
};

export const toTOrderBreakdownL2 = (x: TOrderBreakdownL2): TOrderBreakdownL2 => {
  return {
    total: x.total ? toIOrderBaseOptional(x.total) : x.total,
    shareholding: x.shareholding ? toIOrderBaseOptional(x.shareholding) : x.shareholding,
    non_shareholding: x.non_shareholding ? toIOrderBaseOptional(x.non_shareholding) : x.non_shareholding
  }
}
export const toTOrderBreakdown = (x?: TOrderBreakdown): TOrderBreakdown | undefined => {
  if (!x) {
    return x
  }

  return {
    accepted: toTOrderBreakdownL2(x.accepted),
    pending: toTOrderBreakdownL2(x.pending)
  }
}

export const toTOrderHistory = (x: TOrderHistory): TOrderHistory => {
  const rval: TOrderHistory = {
    delta: toIOrderBase(x.delta),
    order: toTOrder(x.order),
  };

  return rval;
};

export const toTDetailedOrder = (x: TDetailedOrder): TDetailedOrder => {
  const rval: TDetailedOrder = {
    ...x,
    notional_value: x.notional_value ? new Decimal(x.notional_value) : undefined,
    existing_holding: x.existing_holding ? new Decimal(x.existing_holding) : undefined,
  }
  return rval;
}

export const toTSettlementObligation = (x: TSettlementObligation): TSettlementObligation => {
  const rval: TSettlementObligation = {
    ...x,
    cash_amount: x.cash_amount ? new Decimal(x.cash_amount) : undefined,
    open_cash_amount: x.open_cash_amount ? new Decimal(x.open_cash_amount) : undefined,
    quantity: x.quantity ? new Decimal(x.quantity) : undefined,
    open_quantity: x.open_quantity ? new Decimal(x.open_quantity) : undefined,
    price: x.price ? new Decimal(x.price) : undefined,
  };

  return rval;
};


export const toTSettlementBook = (x: TSettlementBook): TSettlementBook => {
  const rval: TSettlementBook = {
    ...x,
  };

  return rval;
};
