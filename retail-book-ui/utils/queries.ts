import { MutationOptions, QueryClient } from "@tanstack/react-query";

import { toast } from "react-toastify";
import {
  AmendOfferInsiderVariables,
  ApiGetParams,
  Clearable,
  DownloadAllocationAnnotatedVariables,
  Invite,
  Resource,
  StatusEvent,
  TAggregateOrder,
  TDetailedAllocation,
  TDetailedOrder,
  TDocumentType,
  TInsider,
  TNewOffer,
  TOffer,
  TOrder,
  TOrderHistory,
  TOrderTotals,
  TOrganisation,
  TPaginatedData,
  TUser,
  UpdateUserNotificationStatusVariables,
  UpdateAllUserNotificationsStatusVariables,
  UploadResource,
  DownloadAllocationAnnotatedByOfferAllocVariables,
  TAggregatedAllocation,
  TWallCrossInfo,
  TWallCrossing, TNewInsider,
  TOfferMetrics,
  TSettlementInstruction,
  TSettlementObligation,
  TSettlementBook,
} from "types";
import { Api } from "utils/api";
import {
  toIOrderBase,
  toTAggregatedAllocation,
  toTDetailedAllocation,
  toTDetailedOrder,
  toTOfferSummary,
  toTOrder,
  toTOrderBreakdown,
  toTOrderHistory,
  toTOfferMetricsSnapshot,
  toTOfferMetricsTotals,
  toTSettlementObligation,
  toTSettlementBook,
} from "./dataConversion";
import { AxiosProgressEvent, all } from "axios";

/**
 *
 * NOTE: select functions are declared statically as they must be a stable function reference for memoisation to work.
 *
 */

// Static //////////////////////////////////////////
const getDocumentTypesQuerySelect = (data: TDocumentType[]) =>
  data.map((type: TDocumentType) => ({
    value: type.value,
    label: type.display_name,
  }));

export const getDocumentTypesQuery = (token?: string) => ({
  queryKey: ["documentTypes"],
  queryFn: () => Api.getDocumentTypes(token),
  enabled: !!token,
  select: getDocumentTypesQuerySelect,
});

export const getStaticIntermediariesQuery = (token: string | undefined) => ({
  queryKey: ["intermediaries"],
  queryFn: () => Api.getIntermediaries(token),
  enabled: !!token,
});

export const getUsersQuery = (offerId: string, token?: string) => ({
  queryKey: ["users", "offer", offerId],
  queryFn: () => Api.getOfferUsers(offerId, token),
  enabled: !!token && !!offerId,
});

export const getCurrencies = (token?: string) => ({
  queryKey: ["static", "currencies"],
  queryFn: () => Api.getCurrencies(token),
  enabled: !!token,
  staleTime: Infinity
})

// Task //////////////////////////////////////////////////

export const getTask = (taskId: string, token?: string) => ({
  queryKey: ["task", taskId],
  queryFn: () => Api.getTask(taskId, token),
  enabled: !!token && !!taskId && taskId != "",
  staleTime: 1000,
  refetchInterval: 3000
});

// Organisation //////////////////////////////////////////
export const getOrganisation = (token?: string) => ({
  queryKey: ["organisation"],
  queryFn: () => Api.getOrganisation(token),
  enabled: !!token,
  staleTime: 5 * 30 * 1000,
});

export const updateOrganisationMutation = (
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (org: Partial<TOrganisation>) =>
    Api.updateOrganisation(org, token),
  onSuccess: () => {
    qc.invalidateQueries(["organisation"]);
  },
});

// Wall Crossing //////////////////////////////////////////
export const getOfferWallcross = (offerId: string, token?: string, wallcrossId?: string) => ({
  queryKey: ["wallcross", offerId, wallcrossId],
  queryFn: () => Api.getOfferWallCrossInfo(offerId, token),
  enabled: !!token && !!wallcrossId
});

export const getOfferWallCrossInvitesQuery = (offerId: string, token?: string, wallcrossId?: string) => ({
  queryKey: ["offer-wallcross-invites", offerId, wallcrossId],
  queryFn: () => Api.getOfferWallCrossInvites(offerId, token),
  refetchInterval: defaultRefetchInterval,
  enabled: !!token && !!wallcrossId
});

export const getUserWallCrossInvitesQuery = (params: ApiGetParams, userId?: string, token?: string) => ({
  queryKey: ["wallcrossings", params, userId],
  queryFn: () => Api.getAllWallCrossInvites(userId, params, token),
  refetchInterval: defaultRefetchInterval,
  enabled: !!token && !!userId,
  select: getUserWallCrossInvitesQuerySelect,
});

export const getWallcrossInvite = (inviteId: string, token?: string) => ({
  queryKey: ["offer-wallcross-invite", inviteId],
  queryFn: () => Api.getWallCrossInfo(inviteId, token),
  enabled: !!token && !!inviteId
});

export const acceptWallcrossInviteMutation = (inviteId: string, qc: QueryClient, token?: string) => ({
  mutationFn: () => Api.acceptWallCrossInvite(inviteId, token),
  onSuccess: () => {
    qc.invalidateQueries(["offer-wallcross-invite", inviteId]);
  },
});

export const rejectWallcrossInviteMutation = (inviteId: string, qc: QueryClient, token?: string) => ({
  mutationFn: () => Api.rejectWallCrossInvite(inviteId, token),
  onSuccess: () => {
    qc.invalidateQueries(["offer-wallcross-invite", inviteId]);
  },
});

const getUserWallCrossInvitesQuerySelect = (data: TPaginatedData<TWallCrossing>) => ({
  data: data.data,
  pagination: data.pagination,
});

export const sendWallCrossInviteMutation = (offerId: string, qc: QueryClient, token?: string, wallcrossId?: string) => ({
  mutationFn: (invites: Invite[]) => Api.sendWallCrossInvite(offerId, invites, token),
  onSuccess: () => {
    qc.invalidateQueries(["offer-wallcross-invites", offerId, wallcrossId]);
  },
});

export const updateWallcrossMutation = (offerId: string, qc: QueryClient, token?: string) => ({
  mutationFn: (wallcrossInfo: Partial<TWallCrossInfo>) =>
    Api.updateWallcrossInfo(offerId, wallcrossInfo, token),
  onSuccess: () => {
    qc.invalidateQueries(["wallcross", offerId]);
    qc.invalidateQueries(["offer", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

export const deleteWallcrossMutation = (offerId: string, qc: QueryClient, token?: string) => ({
  mutationFn: () => Api.deleteWallcrossInfo(offerId, token),
  onSuccess: () => {
    qc.invalidateQueries(["wallcross", offerId]);
    qc.invalidateQueries(["offer", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

export const uploadWallcrossDocumentMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (data: Partial<UploadResource>) => {
    const form = new FormData();
    if (data.file) {
      form.append("file", data.file, data.file.name)
    }

    return Api.uploadWallcrossDocument(offerId, form, data.title ?? "", data.type ?? "", token)
  },
  onSuccess: () => {
    qc.invalidateQueries(["wallcross", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

// Insiders ///////////////////////////////////////
export const getOfferInsidersQuery = (params?: ApiGetParams, offerId?: string, token?: string) => ({
  queryKey: ["insiders", offerId, params],
  queryFn: () => Api.getOfferInsiders(offerId, params, token),
  refetchInterval: defaultRefetchInterval,
  enabled: !!token && !!offerId,
  select: getOfferInsidersQuerySelect,
});

const getOfferInsidersQuerySelect = (data: TPaginatedData<TInsider>) => ({
  data: data.data,
  pagination: data.pagination,
});

export const createOfferInsiderMutation = (
    qc: QueryClient,
    offerId: string,
    token: string | undefined
) => ({
  mutationFn: (newOfferInsider: TNewInsider) => Api.createOfferInsider(offerId, newOfferInsider, token),
});

export const amendOfferInsiderInsideTimeMutation = (
    token: string | undefined,
    offerId: string | undefined,
    qc: QueryClient
) => ({
  mutationFn: (variables: AmendOfferInsiderVariables) =>
      Api.amendOfferInsiderInsideTime(offerId, variables.id, variables.insideTime, token),
  onSuccess: () => {
    qc.invalidateQueries(["insiders", offerId]);
  },
});

// Metrics //////////////////////////////////////////
export const getMonthlyOfferMetrics = (params?: ApiGetParams, token?: string) => ({
  queryKey: ["monthlyoffermetrics", params],
  queryFn: () => Api.getMonthlyOfferMetrics(params, token),
  enabled: !!token,
  select: offerMetricsSelect,
  //deliberately don't refresh or have a stale time as this might be an expensive operation
});

// Users //////////////////////////////////////////
const getTeamMembersQuerySelect = (data: TPaginatedData<TUser>) => ({
  data: data.data,
  pagination: data.pagination,
});

export const getTeamMembersQuery = (params: ApiGetParams, token?: string | undefined) => ({
  queryKey: ["users", params],
  queryFn: () => Api.getUsers(params, token),
  enabled: !!token,
  select: getTeamMembersQuerySelect
});

export const getUserQuery = (id?: string, token?: string) => ({
  queryKey: ["user", id],
  queryFn: () => Api.getUser(id, token),
  enabled: !!token,
  staleTime: 5 * 30 * 1000
});

export const getUserNotificationsQuery = (params: ApiGetParams, id?: string, token?: string) => ({
  queryKey: ["user", id, "notifications", params],
  queryFn: () => Api.getUserNotifications(params, id, token),
  enabled: !!token,
  refetchInterval: defaultRefetchInterval
});

export const updateUserNotificationStatusMutation = (qc: QueryClient, id?: string, token?: string) => ({
  mutationFn: (variables: UpdateUserNotificationStatusVariables) => Api.updateUserNotificationStatus(variables.userId, variables.id, variables.status, token),
  onSuccess: () => {
    setTimeout(() => { qc.invalidateQueries(["user", id, "notifications"]) }, 100)
  }
});

export const updateAllUserNotificationsStatusMutation = (qc: QueryClient, id?: string, token?: string) => ({
  mutationFn: (variables: UpdateAllUserNotificationsStatusVariables) => Api.updateAllUserNotificationsStatus(variables.userId, variables.status, token),
  onSuccess: () => {
    setTimeout(() => { qc.invalidateQueries(["user", id, "notifications"]) }, 500)
  }
});

export const createUserMutation = (qc: QueryClient, token?: string) => ({
  mutationFn: (newUser: Partial<TUser>) => Api.createUser(newUser, token),
  onSuccess: () => {
    qc.invalidateQueries(["users"]);
  },
});

export const updateUserMutation = (
  intId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (newUser: Partial<TUser>) =>
    Api.updateUser(intId, newUser, token),
  onSuccess: () => {
    qc.invalidateQueries(["users"]);
  },
});

export const deleteUserMutation = (qc: QueryClient, token?: string) => ({
  mutationFn: (intId: string) => Api.deleteUser(intId, token),
  onSuccess: () => {
    qc.invalidateQueries(["users"]);
    toast.success("User deleted successfully");
  },
});

export const sendInviteMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (invites: Invite[]) => Api.sendInvite(offerId, invites, token),
  onSuccess: () => {
    qc.invalidateQueries(["invites"]);
  },
});

export const resendInviteMutation = (
  token: string | undefined
): MutationOptions<void, unknown, string> => ({
  mutationFn: async (userId) => {
    await Api.resendInvite(userId, token);
  },
  onSuccess: () => {
    toast.success("Invite resent successfully");
  },
});

export const sendUserInviteMutation = (
  token: string | undefined,
  offerId: string | undefined,
  retail: boolean,
  readonly: boolean,
  qc: QueryClient
) => ({
  mutationFn: (userId: string) =>
    Api.sendUserInvite(offerId, userId, readonly, retail, token),
  onSuccess: () => {
    qc.invalidateQueries(["users", "offer", offerId]);
  },
});

export const sendRemoveUserMutation = (
  token: string | undefined,
  offerId: string | undefined,
  retail: boolean,
  qc: QueryClient
) => ({
  mutationFn: (userId: string) => Api.sendRemoveUser(offerId, userId, retail, token),
  onSuccess: () => {
    qc.invalidateQueries(["users", "offer", offerId]);
  },
});


// Allocations /////////////////////////////////////
const getAllocationBookSelect = (data: TPaginatedData<TDetailedAllocation>) => ({
  data: data.data?.map(toTDetailedAllocation) ?? [],
  pagination: data.pagination,
});

const getAllocationBookAggregatedSelect = (data: TPaginatedData<TAggregatedAllocation>) => ({
  pagination: data.pagination,
  data: data.data?.map(toTAggregatedAllocation)??[]
})

export const getOfferAllocationQuery = (offerId: string, token?: string | undefined) => ({
  queryKey: ["allocations", offerId],
  queryFn: () => Api.getOfferAllocation(offerId, token),
  enabled: !!token,
  refetchInterval: defaultRefetchInterval,
});

export const getAllocationBookQuery = (offerId: string, allocationBookId?: string, token?: string | undefined, params?: ApiGetParams) => ({
  queryKey: ["allocationBook", allocationBookId, params],
  queryFn: () => {
    return Api.getAllocationBook(offerId, allocationBookId ?? "", token, params);
  },
  enabled: !!token && !!allocationBookId,
  select: getAllocationBookSelect,
});

export const getAllocationBookAggregatedQuery = (offerId: string, allocationBookId?: string, token?: string |undefined, params?: ApiGetParams) => ({
  queryKey: ["allocationAggregatedBook", allocationBookId, params],
  queryFn: () => {
    return Api.getAllocationBookAggregated(offerId, allocationBookId??"", token, params);
  },
  enabled: !!token && !!allocationBookId,
  select: getAllocationBookAggregatedSelect,
});

// Offers //////////////////////////////////////////
const getOffersQuerySelect = (data: TPaginatedData<TOffer>) => ({
  data: data.data?.map(toTOfferSummary),
  pagination: data.pagination,
});

const defaultRefetchInterval = 10000;
export const getOffersQuery = (params: ApiGetParams, token?: string) => ({
  queryKey: ["offers", params],
  queryFn: () => Api.getOffers(params, token),
  refetchInterval: defaultRefetchInterval,
  enabled: !!token,
  select: getOffersQuerySelect,
});

export const getOfferQuery = (offerId: string, token?: string) => ({
  queryKey: ["offer", offerId],
  queryFn: () => Api.getOffer(offerId, token),
  enabled: !!token && !!offerId,
  refetchInterval: defaultRefetchInterval,
});

export const getOfferUIState = (offerId: string, token?: string) => ({
  queryKey: ["ui", "offer", offerId],
  queryFn: () => Api.getOfferUIState(offerId, token),
  enabled: !!token && !!offerId,
});

export const createOfferMutation = (
  qc: QueryClient,
  token: string | undefined
) => ({
  mutationFn: (newOffer: TNewOffer) => Api.createOffer(newOffer, token),
});

export const editOfferMutation = (
  qc: QueryClient,
  offerId: string,
  token?: string
) => ({
  mutationFn: (offer: Clearable<Partial<TOffer>>) => Api.editOffer(offerId, offer, token),
  onSuccess: () => {
    qc.invalidateQueries(["offer", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

export const updateOfferStatusMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (status: StatusEvent) =>
    Api.updateOfferStatus(offerId, status, token),
  onSuccess: () => {
    qc.invalidateQueries(["offer", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

// Allocations //////////////////////////////////////////
export const getOfferAllocation = (offerId: string, token?: string) => ({
  queryKey: ["offer", offerId],
  queryFn: () => Api.getOfferAllocation(offerId, token),
  enabled: !!token && !!offerId,
});

// Documents //////////////////////////////////////////
const getLogoQuerySelect = (data: BlobPart) =>
  window.URL.createObjectURL(new Blob([data]));

export const getLogoQuery = (
  offerId: string,
  resourceId?: string,
  token?: string
) => ({
  queryKey: ["document", offerId, "logo"],
  queryFn: () => Api.getDocument(offerId, resourceId as string, token),
  enabled: !!token && !!resourceId,
  select: getLogoQuerySelect,
});

export const uploadDocumentMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (data: Partial<UploadResource>) => {
    const form = new FormData();
    if (data.file) {
      form.append("file", data.file, data.file.name)
    }

    return Api.uploadDocument(offerId, form, data.title ?? "", data.type ?? "", token)
  },
  onSuccess: () => {
    qc.invalidateQueries(["offer", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

export const updateDocumentMetaMutation = (
  offerId: string,
  resourceId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (data: Partial<Resource>) =>
    Api.updateDocumentMeta(offerId, resourceId, data, token),
  onSuccess: () => {
    qc.invalidateQueries(["offer", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

export const deleteDocumentMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (resourceId: string) =>
    Api.deleteDocument(offerId, resourceId, token),
  onSuccess: () => {
    qc.invalidateQueries(["offer", offerId]);
    qc.invalidateQueries(["ui", "offer", offerId]);
  },
});

//gets the invites for this user
export const getInvitesQuery = (offerId: string, token?: string) => ({
  queryKey: ["invites"],
  queryFn: () => Api.getInvites(offerId, token),
  refetchInterval: defaultRefetchInterval,
  enabled: !!token && !!offerId,
});

// Settlements //////////////////////////////////////////

export const getSettlementInstructionsQuery = (params: ApiGetParams, token?: string) => ({
  queryKey: ["settlement", "instruction", params],
  queryFn: () => Api.getSettlementInstructions(params, token),
  enabled: !!token,
  refetchInterval: defaultRefetchInterval
});

export const getSettlementInstructionQuery = (id: string, token?: string) => ({
  queryKey: ["settlement", "instruction", id],
  queryFn: () => Api.getSettlementInstruction(id, token),
  enabled: !!token,
  refetchInterval: defaultRefetchInterval
});

export const createSettlementInstructionMutation = (qc: QueryClient, token?: string) => ({
  mutationFn: (newInstruction: Partial<TSettlementInstruction>) => Api.createSettlementInstruction(newInstruction, token),
  onSuccess: () => {
    qc.invalidateQueries(["settlement", "instruction"]);
  },
});

export const updateSettlementInstructionMutation = (
  intId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (instruction: Partial<TSettlementInstruction>) =>
    Api.updateSettlementInstruction(intId, instruction, token),
  onSuccess: () => {
    qc.invalidateQueries(["settlement", "instruction"]);
  },
});

export const deleteSettlementInstructionMutation = (qc: QueryClient, token?: string) => ({
  mutationFn: (intId: string) => Api.deleteSettlementInstruction(intId, token),
  onSuccess: () => {
    qc.invalidateQueries(["settlement", "instruction"]);
    toast.success("Instruction deleted successfully");
  },
});

export const generateSettlementObligationsMutation = (qc: QueryClient, offerId: string, token?: string, onSuccessHook?: () => void) => ({
  mutationFn: () => {
    return Api.generateSettlementObligations(offerId, token)
  },
  onSuccess: () => {
    qc.invalidateQueries(["ui", "offer", offerId]);
    qc.invalidateQueries(["settlement", "obligation", offerId]);
    qc.invalidateQueries(["settlement", "book", offerId]);
    if (onSuccessHook) {
      onSuccessHook();
    }
  },
});

const getOfferSettlementObligationsSelect = (data: TSettlementObligation[]) => ({
  data: data?.map((obligation) => toTSettlementObligation(obligation))??[]
})

export const getOfferSettlementObligationsQuery = (offerId: string, hasSettlements: boolean, token?: string | undefined) => ({
  queryKey: ["settlement", "obligation", offerId],
  queryFn: () => {
    return Api.getOfferSettlementObligations(offerId, token);
  },
  enabled: !!token && !!offerId && !!hasSettlements,
  select: getOfferSettlementObligationsSelect,
});

const getSettlementObligationsSelect = (data: TPaginatedData<TSettlementObligation>) => ({
  data: data.data?.map(toTSettlementObligation),
  pagination: data.pagination
})

export const getSettlementObligationsQuery = (params: ApiGetParams, token?: string) => ({
  queryKey: ["settlement", "obligation", params],
  queryFn: () => Api.getSettlementObligations(params, token),
  enabled: !!token,
  refetchInterval: defaultRefetchInterval,
  select: getSettlementObligationsSelect,
});

const getOfferSettlementBookSelect = (data: TSettlementBook) => ({
  data: toTSettlementBook(data)
})

export const getOfferSettlementBookQuery = (offerId: string, hasSettlements: boolean, token?: string | undefined) => ({
  queryKey: ["settlement", "book", offerId],
  queryFn: () => {
    return Api.getOfferSettlementBook(offerId, token);
  },
  enabled: !!token && !!offerId && !!hasSettlements,
  select: getOfferSettlementBookSelect,
});

// Orders //////////////////////////////////////////

const getOrderQuerySelect = (data: TOrderTotals) => ({
  orders: data.orders?.map(toTOrder) ?? [],
  totals: toIOrderBase(data.totals),
  breakdown: toTOrderBreakdown(data.breakdown)
});

export const getOrderQuery = (offerId: string, token?: string) => ({
  queryKey: ["orders", offerId],
  queryFn: () => Api.getOrder(offerId, token),
  enabled: !!token && !!offerId,
  select: getOrderQuerySelect,
  staleTime: 10 * 1000, // 10s
  refetchInterval: defaultRefetchInterval
});

const offerMetricsSelect = (data: TOfferMetrics): TOfferMetrics => ({
  data: data.data?.map(toTOfferMetricsSnapshot) ?? [],
  totals: data.totals ? toTOfferMetricsTotals(data.totals) : undefined,
});

export const getOrderMetrics = (offerId: string, token?: string) => ({
  queryKey: ["ordermetrics", offerId],
  queryFn: () => Api.getOrderMetrics(offerId, token),
  enabled: !!token && !!offerId,
  select: offerMetricsSelect,
  staleTime: 10 * 1000, // 10s
  refetchInterval: defaultRefetchInterval
});

export const getDailyOrderMetrics = (offerId: string, token?: string) => ({
  queryKey: ["dailyordermetrics", offerId],
  queryFn: () => Api.getDailyOrderMetrics(offerId, token),
  enabled: !!token && !!offerId,
  select: offerMetricsSelect,
  staleTime: 10 * 1000, // 10s
  refetchInterval: defaultRefetchInterval
});

export const getLatestAllocationMetrics = (offerId: string, token?: string) => ({
  queryKey: ["latestallocationmetrics", offerId],
  queryFn: () => Api.getLatestAllocationMetrics(offerId, token),
  enabled: !!token && !!offerId,
  select: offerMetricsSelect,
  staleTime: 10 * 1000, // 10s
  refetchInterval: defaultRefetchInterval
});

const getOrdersQuerySelect = (data: TPaginatedData<TOrder>) => ({
  data: data.data?.map(toTOrder),
  pagination: data.pagination,
});

export const getOrdersQuery = (params: ApiGetParams, token?: string) => ({
  queryKey: ["orders", params],
  queryFn: () => Api.getOrders(params, token),
  enabled: !!token,
  select: getOrdersQuerySelect,
  refetchInterval: defaultRefetchInterval
});

const getOrderHistoryQuerySelect = (data: TOrderHistory[] | null) =>
  data ? data.map(toTOrderHistory) : [];

const getOrderDetailSelect = (data: TPaginatedData<TDetailedOrder>) => ({
  data: data.data?.map(toTDetailedOrder) ?? [],
  pagination: data.pagination,
});


export const getIntermediaryOrderHistoryQuery = (systemId?: string, offerId?: string, token?: string) => ({
  queryKey: ["orderIntermediaryHistory", systemId, offerId],
  queryFn: () => Api.getIntermediaryOrderHistory(systemId ?? "", offerId ?? "", token),
  enabled: !!token && systemId !== undefined && offerId !== undefined,
  select: getOrderHistoryQuerySelect,
  staleTime: 10 * 1000, // 10s
  refetchInterval: defaultRefetchInterval
})

export const getOrderHistoryQuery = (offerId: string, token?: string) => ({
  queryKey: ["orderHistory", offerId],
  queryFn: () => Api.getOrderHistory(offerId, token),
  enabled: !!token && !!offerId,
  select: getOrderHistoryQuerySelect,
  staleTime: 10 * 1000, // 10s
  refetchInterval: defaultRefetchInterval
});

export const getOfferStatesQuery = (token?: string) => ({
  queryKey: ["static", "offer", "states"],
  queryFn: () => Api.getStates(token),
  enabled: !!token,
})

export const downloadAllocationAnnotatedMutator = (
  token?: string
) => ({
  mutationFn: (variables: DownloadAllocationAnnotatedVariables) =>
    Api.downloadAllocationBookAnnotatedXLSX(variables.offerId, variables.allocationBookId, token),
});

export const downloadAllocationAnnotatedByOfferAllocationMutator = (
  token?: string
) => ({
  mutationFn: (variables: DownloadAllocationAnnotatedByOfferAllocVariables) =>
    Api.downloadAllocationBookAnnotatedByOfferAllocXLSX(variables.offerId, variables.offerAllocationId, token),
});

export const createAggregateOrderMutation = (
  offerId: string,
  token?: string
) => ({
  mutationFn: (order: TAggregateOrder) =>
    Api.createAggregateOrder(offerId, order, false, token),
});

export const validateAggregateOrderMutation = (offerId: string, token?: string) => ({
  mutationFn: (order: TAggregateOrder) =>
    Api.createAggregateOrder(offerId, order, true, token)
});

export const uploadAllocationMutation = (
  offerId: string,
  token?: string
) => ({
  mutationFn: (formData: FormData) =>
    Api.uploadAllocation(offerId, formData, token),
});

export const resubmitAllocationMutation = (
  offerId: string,
  token?: string
) => ({
  mutationFn: (allocationBookId: string) =>
    Api.resubmitAllocation(offerId, allocationBookId, token),
});

export const uploadItemisedOrderMutation = (
  offerId: string,
  token?: string,
  onUploadProgress?: (arg0: AxiosProgressEvent) => void
) => ({
  mutationFn: (formData: FormData) =>
    Api.uploadItemisedOrder(offerId, formData, token, onUploadProgress),
});

export const viewItemisedOrderQuery = (offerId?: string, orderId?: string, token?: string, params?: ApiGetParams) => ({
  queryKey: ["orderDetail", offerId, orderId, params],
  queryFn: () => Api.viewItemisedOrders(offerId ?? "", orderId ?? "", token, params),
  enabled: !!token && !!offerId && !!orderId,
  select: getOrderDetailSelect,
})

export const confirmItemisedOrderMutation = (
  offerId: string,
  token?: string
) => ({
  mutationFn: (orderId: string) =>
    Api.confirmItemisedOrder(offerId, orderId, token),
});

export const acceptOrderMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (orderId: string) => Api.acceptOrder(offerId, orderId, token),
  onSuccess: () => {
    qc.invalidateQueries(["orders"]);
    qc.invalidateQueries(["orderHistory"]);
    qc.invalidateQueries(["ui","offer",offerId]);
  },
});
export const rejectOrderMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (orderId: string) => Api.rejectOrder(offerId, orderId, token),
  onSuccess: () => {
    qc.invalidateQueries(["orders"]);
    qc.invalidateQueries(["orderHistory"]);
    qc.invalidateQueries(["ui","offer",offerId]);
  },
});

export const deleteOrderSummaryMutation = (
  offerId: string,
  qc: QueryClient,
  token?: string
) => ({
  mutationFn: (orderId: string) =>
    Api.deleteOrderSummary(offerId, orderId, token),
  onSuccess: () => {
    qc.invalidateQueries(["orders"]);
    qc.invalidateQueries(["orderHistory"]);
  },
});
