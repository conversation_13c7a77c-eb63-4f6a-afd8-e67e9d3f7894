import { countChars, formatNumberString } from "helpers";
import React from "react";

//
const generateNewString = (
  current: string,
  start: number | null,
  end: number | null,
  data: string
) => {
  // We put data into current split by start,end
  return (
    current.substring(0, start ?? 0) +
    data +
    current.substring(end ?? current.length)
  );
};

const onBeforeInputPostiveInteger = (
  e: React.CompositionEvent<HTMLInputElement>
) => {
  const target = e.target as HTMLInputElement;

  const checkValue = generateNewString(
    target.value,
    target.selectionStart,
    target.selectionEnd,
    e.data
  ).replaceAll(",", "");
  if (checkValue !== "" && !checkValue.match("^(0|([1-9][0-9]*))$")) {
    e.preventDefault();
  }
};

const onBeforeInputFixedDecimal = (dp: number) => {
  const regex = "^(0|([1-9][0-9]*))(\\.[0-9]{0," + dp + "})?$";

  return (e: React.CompositionEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;

    const checkValue = generateNewString(
      target.value,
      target.selectionStart,
      target.selectionEnd,
      e.data
    ).replaceAll(",", "");
    if (!checkValue.match(regex)) {
      e.preventDefault();
    }
  };
};

const onInputFormatNumber = (e: React.CompositionEvent<HTMLInputElement>) => {
  const target = e.target as HTMLInputElement;
  const selectionStart = target.selectionStart;
  const selectionEnd = target.selectionEnd;

  const preComma = countChars(
    target.value.substring(0, selectionEnd ?? 0),
    ","
  );
  target.value = formatNumberString(target.value.replaceAll(",", ""));

  // Calculate how many comma's we've inserted, and shift the selection by those
  const incr = countChars(target.value.substring(0, selectionEnd ?? 0), ",");

  // This doesn't always work correctly.
  target.setSelectionRange(
    (selectionStart ?? 0) + (incr - preComma),
    (selectionEnd ?? 0) + (incr - preComma)
  );
};

export const positiveIntegerOnly = () => {
  return {
    onBeforeInput: onBeforeInputPostiveInteger,
    onInput: onInputFormatNumber,
  };
};

export const positiveFixedDecimal = (dp: number) => {
  return {
    onBeforeInput: onBeforeInputFixedDecimal(dp),
    onInput: onInputFormatNumber,
  };
};
