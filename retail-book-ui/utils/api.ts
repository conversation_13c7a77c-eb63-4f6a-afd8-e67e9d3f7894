import axios, { AxiosError, AxiosProgressEvent } from "axios";
import { TermsFormData } from "components/Composite/OfferPage/ViewOffer/forms/TermsForm";
import { stringify } from "qs";
import { toast } from "react-toastify";
import {
    ApiError,
    ApiGetParams,
    ApiMutationResponse,
    Clearable,
    WallCrossInviteResponse,
    IntermediaryResponse,
    Invite,
    InviteResponse,
    Notification,
    Resource,
    StatusEvent,
    TAggregateOrder,
    TAggregatedAllocation,
    TAllocation,
    TDetailedAllocation,
    TDocumentType,
    TNewOffer,
    TOffer,
    TOfferState,
    TOfferUIState,
    TOrder,
    TOrderHistory,
    TOrderTotals,
    TOrganisation,
    TPaginatedData,
    TTask,
    TTerms,
    TUser,
    TWallCrossInfo,
    TWallCrossing, TInsider, TNewInsider,
    TOfferMetrics,
    TSettlementInstruction,
    TSettlementObligation,
    TSettlementBook,
} from "types";
import { encodeDecimal } from "./dataConversion";
import Decimal from "decimal.js";

const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_RETAILBOOK_API_URL,
  paramsSerializer: {
    serialize: (params) =>
      stringify(params, { arrayFormat: "repeat", encode: false }),
  },
});

const Helpers = {
  // this allows onError to work, because fetch() doesn't throw an error on 4xx or 5xx responses (axios does)
  handleFailure: (attemptedAction = "") => {
    const message = attemptedAction
      ? `Failed to ${attemptedAction}`
      : "Network error. Please refresh the page and try again.";
    toast.error(message);
    throw new Error(message);
  },
  getHeaders: (token: string | undefined) => ({
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  }),
};

export const Api = {
  // static
  getDocumentTypes: (token?: string) =>
    instance
      .get<TDocumentType[]>("/static/document/type", {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getIntermediaries: (token?: string) =>
    instance
      .get<IntermediaryResponse>("/static/intermediary", {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  // Offers
  getOffers: (params?: ApiGetParams, token?: string) =>
    instance
      .get<TPaginatedData<TOffer>>("/offer", {
        headers: Helpers.getHeaders(token),
        params,
      })
      .then((res) => res.data),

  getOfferUIState: (id: string, token?: string) =>
    instance
      .get<TOfferUIState>(`/ui/offer/${id}`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getOffer: (id: string, token?: string) =>
    instance
      .get<TOffer>(`/offer/${id}`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getOfferTerms: (id: string, token?: string) =>
    instance
      .get<TTerms>(`/offer/${id}/terms`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  updateOfferTerms: (id: string, formData: TermsFormData, token?: string) =>
    instance
      .post<ApiMutationResponse>(`/offer/${id}/terms`, formData, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  createOffer: (offer: TNewOffer, token?: string) =>
    instance
      .post<ApiMutationResponse>("/offer", offer, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  editOffer: async (
    offerId: string,
    offer: Clearable<Partial<TOffer>>,
    token?: string
  ) => {
    const data = {
      ...offer,
      // Decimal conversion to string      
      min_order_amount: encodeDecimal(offer.min_order_amount),
      offer_price: encodeDecimal(offer.offer_price),
      raise_amount: encodeDecimal(offer.raise_amount),
      price_range_low: encodeDecimal(offer.price_range_low),
      price_range_high: encodeDecimal(offer.price_range_high),
    };

    return instance
      .patch<ApiMutationResponse>(`/offer/${offerId}`, data, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data);
  },

  updateOfferStatus: (
    offerId: string,
    offerStatus: StatusEvent,
    token?: string
  ) =>
    instance
      .put(`/offer/${offerId}/status`, offerStatus, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getOfferUsers: (offerId: string, token?: string) =>
    instance
      .get<TUser[]>(`/offer/${offerId}/user`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  // Documents
  getDocumentMeta: (offerId: string, resourceId: string, id_type: string, token?: string) =>
    instance.get<Resource>(`/offer/${offerId}/document/${resourceId}/meta`, {
      headers: Helpers.getHeaders(token),
      params: { did_type: id_type }
    }).then((res) => res.data),

  getDocument: (offerId: string, resourceId: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/document/${resourceId}`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),

  getDocuments: (offerId: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/document`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
        params: {
          format: "zip",
        },
      })
      .then((res) => res.data),

  uploadDocument: async (offerId: string, formData: FormData, title: string, type: string, token?: string) =>
    instance
      .post<ApiMutationResponse>(`/offer/${offerId}/document`, formData, {
        headers: {
          // don't use helper here, as it will override content-type, which needs to be multipart/form-data
          Authorization: `Bearer ${token}`,
        },
        params: {
          title, type
        },
      })
      .then((res) => res.data),

  updateDocumentMeta: (
    offerId: string,
    resourceId: string,
    data: Partial<Resource>,
    token?: string
  ) =>
    instance
      .patch<ApiMutationResponse>(
        `/offer/${offerId}/document/${resourceId}/meta`,
        data,
        { headers: Helpers.getHeaders(token) }
      )
      .then((res) => res.data),

  deleteDocument: (offerId: string, resourceId: string, token?: string) =>
    instance
      .delete(`/offer/${offerId}/document/${resourceId}`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  // Task

  getTask: async (
    taskId: string,
    token: string | undefined
  ): Promise<TTask> => instance.get(`/task/${taskId}`, { headers: Helpers.getHeaders(token) }).then((res) => res.data),

  // Wall Crossing

  getOfferWallCrossInfo: async (
    offerId: string,
    token: string | undefined
  ): Promise<TWallCrossInfo> => instance.get(`/offer/${offerId}/wallcross`, { headers: Helpers.getHeaders(token) }).then((res) => res.data),

  updateWallcrossInfo: (offerId: string, wci: Partial<TWallCrossInfo>, token?: string) =>
    instance
      .patch(`/offer/${offerId}/wallcross`, wci, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  deleteWallcrossInfo: (offerId: string, token?: string) =>
    instance
      .delete(`/offer/${offerId}/wallcross`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getOfferWallCrossInvites: (offerId: string, token?: string) =>
    instance
      .get<WallCrossInviteResponse[]>(`/offer/${offerId}/wallcross/invite`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getWallCrossInfo: async (
    wallCrossInviteId: string,
    token: string | undefined
  ): Promise<TWallCrossing> => instance.get(`/wallcross/invite/${wallCrossInviteId}`, { headers: Helpers.getHeaders(token) }).then((res) => res.data),
      
  acceptWallCrossInvite: (wallCrossInviteId: string, token?: string) =>
    instance
      .put(`/wallcross/invite/${wallCrossInviteId}/accept`, {}, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  rejectWallCrossInvite: (wallCrossInviteId: string, token?: string) =>
    instance
      .put(`/wallcross/invite/${wallCrossInviteId}/reject`, {}, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getAllWallCrossInvites: (userId?: string, params?: ApiGetParams, token?: string) =>
    instance
      .get<TPaginatedData<TWallCrossing>>(`/user/${userId}/wallcross/invite`, {
        headers: Helpers.getHeaders(token),
        params,
      })
      .then((res) => res.data),

  sendWallCrossInvite: (offerId: string, invites: Invite[], token?: string) =>
    instance
      .post(`/offer/${offerId}/wallcross/invite`, invites, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  uploadWallcrossDocument: async (offerId: string, formData: FormData, title: string, type: string, token?: string) =>
    instance
      .post<ApiMutationResponse>(`/offer/${offerId}/wallcross/document`, formData, {
        headers: {
          // don't use helper here, as it will override content-type, which needs to be multipart/form-data
          Authorization: `Bearer ${token}`,
        },
        params: {
          title, type
        },
      })
      .then((res) => res.data),

  getOfferWallcrossDocument: (offerId: string, resourceId: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/wallcross/document/${resourceId}`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),

  getWallcrossInviteDocument: (wallCrossInviteId: string, resourceId: string, token?: string) =>
    instance
      .get(`/wallcross/invite/${wallCrossInviteId}/document/${resourceId}`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),

  // Insiders
  getOfferInsiders: (offerId?: string, params?: ApiGetParams, token?: string) =>
    instance
        .get<TPaginatedData<TInsider>>(`/offer/${offerId}/insider`, {
            headers: Helpers.getHeaders(token),
            params,
        })
        .then((res) => res.data),

  createOfferInsider: (offerId: string, offerInsider: TNewInsider, token?: string) =>
    instance
        .post<ApiMutationResponse>(`/offer/${offerId}/insider`, offerInsider, {
            headers: Helpers.getHeaders(token),
        })
        .then((res) => res.data),

  amendOfferInsiderInsideTime: async (
        offerId: string | undefined,
        insiderId: string | undefined,
        insideTime: string | undefined,
        token: string | undefined
    ) => {
      return instance.patch<ApiMutationResponse>(`/offer/${offerId}/insider/${insiderId}`, {inside_time: insideTime}, { headers: Helpers.getHeaders(token) }).then((res) => res.data)
    },


  // Organisation
  getOrganisation: async (
    token: string | undefined
  ): Promise<TOrganisation> => instance.get(`/organisation`, { headers: Helpers.getHeaders(token) }).then((res) => res.data),

  updateOrganisation: (org: Partial<TOrganisation>, token?: string) =>
    instance
      .patch(`/organisation`, org, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  // Users
  createUser: (newUser: Partial<TUser>, token?: string) =>
    instance
      .post("/user", newUser, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getUser: async (
    id: string | undefined,
    token: string | undefined
  ): Promise<TUser> => instance.get(`/user/${id}?id_type=ext`, { headers: Helpers.getHeaders(token) }).then((res) => res.data),

  updateUser: (intId: string, user: Partial<TUser>, token?: string) =>
    instance
      .patch(`/user/${intId}?id_type=int`, user, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  deleteUser: (intId: string, token?: string) =>
    instance
      .delete(`/user/${intId}?id_type=int`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getUsers: async (params?: ApiGetParams, token?: string) =>
    instance
      .get<TPaginatedData<TUser>>("/user", {
        headers: Helpers.getHeaders(token),
        params,
      }).then((res) => res.data),

  getUserNotifications: async (
    params: ApiGetParams,
    id: string | undefined,
    token: string | undefined
  ): Promise<TPaginatedData<Notification>> => instance.get<TPaginatedData<Notification>>(`/user/${id}/notification?id_type=ext`, { headers: Helpers.getHeaders(token), params }).then((res) => res.data),

  updateUserNotificationStatus: async (
    userId: string,
    id: string,
    status: string,
    token?: string) => {
    instance.patch(`/user/${userId}/notification/${id}`, { action: status }, { headers: Helpers.getHeaders(token) }).then((res) => res.data)
  },

  updateAllUserNotificationsStatus: async (
    userId: string,
    status: string,
    token?: string) => {
    instance.patch(`/user/${userId}/notification`, { action: status }, { headers: Helpers.getHeaders(token) }).then((res) => res.data)
  },

  sendInvite: async (
    offerId: string | undefined,
    invites: Invite[],
    token: string | undefined
  ): Promise<ApiMutationResponse> => {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_RETAILBOOK_API_URL}/offer/${offerId}/invite`,
      {
        method: "POST",
        headers: Helpers.getHeaders(token),
        body: JSON.stringify(invites),
      }
    );
    if (!res.ok) Helpers.handleFailure("send invite");

    return res.json();
  },

  resendInvite: async (
    id: string | undefined,
    token: string | undefined
  ): Promise<ApiMutationResponse> => {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_RETAILBOOK_API_URL}/user/${id}/invite?id_type=int`,
      {
        method: "PUT",
        headers: Helpers.getHeaders(token),
      }
    );

    if (!res.ok) Helpers.handleFailure("resend invite");

    return res.json();
  },

  getInvites: (offerId: string, token?: string) =>
    instance
      .get<InviteResponse[]>(`/offer/${offerId}/invite`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  sendUserInvite: async (
    offerId: string | undefined,
    userId: string | undefined,
    readonly: boolean | undefined,
    retail: boolean | undefined,
    token: string | undefined
  ) => {
    let role = "offer_collaborator";
    if (retail) {
      if (readonly) {
        role = "offer_retail_readonly";
      } else {
        role = "offer_retail_collaborator";
      }
    }

    return instance.put<ApiMutationResponse>(`/offer/${offerId}/user/${userId}/${role}/grant`, {}, { headers: Helpers.getHeaders(token) }).then((res) => res.data)
  },

  sendRemoveUser: async (
    offerId: string | undefined,
    userId: string | undefined,
    retail: boolean | undefined,
    token: string | undefined
  ): Promise<ApiMutationResponse> => {
    let role = "offer_collaborator";
    if (retail) {
      role = "offer_retail_collaborator";
    }

    return instance.put<ApiMutationResponse>(`/offer/${offerId}/user/${userId}/${role}/revoke`, {}, { headers: Helpers.getHeaders(token) }).then((res) => res.data)
  },

  sendNotifications: (
    id: string,
    message: string,
    intermediaries: string[],
    token?: string
  ) =>
    instance.post<ApiMutationResponse>(
      `/offer/${id}/notification`,
      {
        message,
        intermediaries,
      },
      {
        headers: Helpers.getHeaders(token),
      }
    ).then((d) => d.data),

  // Allocations
  getOfferAllocation: (offerId: string, token?: string) =>
    instance
      .get<TAllocation>(`/offer/${offerId}/allocation`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getAllocationBook: (offerId: string, allocationBookId: string, token?: string, params?: ApiGetParams) =>
    instance
      .get<TPaginatedData<TDetailedAllocation>>(`/offer/${offerId}/allocation/${allocationBookId}`, {
        headers: Helpers.getHeaders(token),
        params,
      })
      .then((res) => res.data),

  getAllocationBookAggregated: (offerId: string, allocationBookId: string, token?: string, params?: ApiGetParams) =>
  instance
    .get<TPaginatedData<TAggregatedAllocation>>(`/offer/${offerId}/allocation/${allocationBookId}`, {
      headers: Helpers.getHeaders(token),
      params: { ...params, view: "aggregate" },
    })
    .then((res) => res.data),

  downloadAllocationBookAnnotatedXLSX: (offerId: string, allocationBookId: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/allocation/${allocationBookId}/allocationbook/detail`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),

  downloadAllocationBookAnnotatedByOfferAllocXLSX: (offerId: string, offerAllocationId: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/allocation/${offerAllocationId}/offerallocation/detail`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),

  downloadAllocationTemplate: (token?: string) =>
    instance
      .get("/offer/allocation/detail/template", {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),

  downloadPopulatedAllocationTemplate: (offerId: string, offerPrice: Decimal, token?: string) =>
    instance
      .get(`/offer/${offerId}/allocation/detail/template?offer_price=${offerPrice}`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),

  clearOfferAllocation: (offerId: string, offerAllocationId: string, token?: string) =>
    instance
      .delete(`/offer/${offerId}/allocation/${offerAllocationId}`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  // Settlements
  getSettlementInstructions: (params: ApiGetParams, token?: string) =>
    instance
      .get<TPaginatedData<TSettlementInstruction>>("/settlement/instruction", {
        headers: Helpers.getHeaders(token),
        params,
      })
      .then((res) => res.data),

  getSettlementInstruction: (id: string, token?: string) =>
    instance
      .get<TSettlementInstruction>(`/settlement/instruction/${id}`, {
        headers: Helpers.getHeaders(token)
      })
      .then((res) => res.data),

  createSettlementInstruction: (newSettlementInstruction: Partial<TSettlementInstruction>, token?: string) =>
    instance
      .post("/settlement/instruction", newSettlementInstruction, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  updateSettlementInstruction: (intId: string, user: Partial<TSettlementInstruction>, token?: string) =>
    instance
      .patch(`/settlement/instruction/${intId}?id_type=int`, user, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  deleteSettlementInstruction: (intId: string, token?: string) =>
    instance
      .delete(`/settlement/instruction/${intId}?id_type=int`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  generateSettlementObligations: (offerId: string, token?: string) =>
    instance
      .post(`/offer/${offerId}/settlement`, {}, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getOfferSettlementObligations: (offerId: string, token?: string) =>
    instance
      .get<TSettlementObligation[]>(`/offer/${offerId}/settlement/obligations`, {
        headers: Helpers.getHeaders(token)
      })
      .then((res) => res.data),

  getOfferSettlementBook: (offerId: string, token?: string) =>
    instance
      .get<TSettlementBook>(`/offer/${offerId}/settlement/book`, {
        headers: Helpers.getHeaders(token)
      })
      .then((res) => res.data),

  getSettlementObligations: (params: ApiGetParams, token?: string) =>
    instance
      .get<TPaginatedData<TSettlementObligation>>("/settlement/obligations", {
        headers: Helpers.getHeaders(token),
        params,
      })
      .then((res) => res.data),

  // Orders
  downloadOrderTemplate: (token?: string) =>
    instance
      .get("/offer/order/detail/template", {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
      })
      .then((res) => res.data),


  downloadOrders: (token?: string) =>
    instance
      .get("/order", {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
        params: {
          format: "zip",
        },
      })
      .then((res) => res.data),

  getOrders: (params: ApiGetParams, token?: string) =>
    instance
      .get<TPaginatedData<TOrder>>("/order", {
        headers: Helpers.getHeaders(token),
        params,
      })
      .then((res) => res.data),

  getOrder: (offerId: string, token?: string) =>
    instance
      .get<TOrderTotals>(`/offer/${offerId}/order`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getOrderMetrics: (offerId: string, token?: string) =>
    instance
      .get<TOfferMetrics>(`/offer/${offerId}/order/metrics`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getDailyOrderMetrics: (offerId: string, token?: string) =>
    instance
      .get<TOfferMetrics>(`/offer/${offerId}/order/metrics/daily`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  getLatestAllocationMetrics: (offerId: string, token?: string) =>
    instance
      .get<TOfferMetrics>(`/offer/${offerId}/allocation/metrics/latest`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  downloadOfferOrders: (offerId: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/order/full`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
        params: {
          format: "csv",
        },
      })
      .then((res) => res.data),

  downloadOfferOrder: (offerId: string, orderBookId?: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/order/${orderBookId}/full`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
        params: {
          format: "csv",
        },
      })
      .then((res) => res.data),

  downloadOfferOrderXLSX: (offerId: string, orderBookId?: string, showErrors?: boolean, token?: string) =>
    instance
      .get(`/offer/${offerId}/order/${orderBookId}/full`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
        params: {
          format: "xlsx",
          show_errors: showErrors ? showErrors : false,
        },
      })
      .then((res) => res.data),

  downloadOrderAudit: (offerId: string, orderSummaryId?: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/order/${orderSummaryId}/audit`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
        params: {
          format: "csv",
        },
      })
      .then((res) => res.data),

  downloadFullOrderAudit: (offerId: string, token?: string) =>
    instance
      .get(`/offer/${offerId}/order/audit`, {
        headers: Helpers.getHeaders(token),
        responseType: "blob",
        params: {
          format: "csv",
        },
      })
      .then((res) => res.data),

  getIntermediaryOrderHistory: (systemId: string, offerId: string, token?: string) =>
    instance.get<TOrderHistory[] | null>(`/offer/${offerId}/order/history?system_id=${systemId}`, {
      headers: Helpers.getHeaders(token),
    }).then((res) => res.data),


  getOrderHistory: (offerId: string, token?: string) =>
    instance
      .get<TOrderHistory[] | null>(`/offer/${offerId}/order/history`, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),


  createAggregateOrder: async (
    offerId: string,
    data: TAggregateOrder,
    validateOnly: boolean = false,
    token?: string,
  ) =>
    instance
      .put(`/offer/${offerId}/order?validate_only=${validateOnly}`, data, {
        headers: Helpers.getHeaders(token),

      })
      // if validate_only=true specified we echo back the request data so it can be re-used for creating with validate_only=false
      .then((res) => validateOnly ? data : res.data),

  uploadAllocation: (offerId: string, data: FormData, token?: string) =>
    instance
      .post(`/offer/${offerId}/allocation`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((res) => res.data),


  resubmitAllocation: (offerId: string, allocationBookId: string, token?: string) =>
    instance
      .post(`/offer/${offerId}/allocation/${allocationBookId}?no_validation=true`, undefined, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((res) => res.data),

  uploadItemisedOrder: (offerId: string, data: FormData, token?: string, onUploadProgress?: (arg0: AxiosProgressEvent) => void) =>
    instance
      .post(`/offer/${offerId}/order/detail`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        onUploadProgress: onUploadProgress,
      })
      .then((res) => res.data),

  confirmItemisedOrder: (offerId: string, orderId: string, token?: string) =>
    instance
      .put(`/offer/${offerId}/order/${orderId}/confirm`, undefined, {
        headers: Helpers.getHeaders(token),
      })
      .then((res) => res.data),

  acceptOrder: async (
    offerId: string | undefined,
    orderId: string | undefined,
    token: string | undefined
  ): Promise<ApiMutationResponse> => {

    return instance.put(`/offer/${offerId}/order/${orderId}/accept`, undefined, {
      headers: Helpers.getHeaders(token),
    }).then((res) => res.data).catch((error: AxiosError<ApiError, any>) => {
      throw error.response?.data
    })
  },

  rejectOrder: async (
    offerId: string | undefined,
    orderId: string | undefined,
    token: string | undefined
  ): Promise<ApiMutationResponse> => {

    return instance.put(`/offer/${offerId}/order/${orderId}/reject`, undefined, {
      headers: Helpers.getHeaders(token),
    }).then((res) => res.data).catch((error: AxiosError<ApiError, any>) => {
      throw error.response?.data
    })
  },

  deleteOrderSummary: async (
    offerId: string | undefined,
    orderId: string | undefined,
    token: string | undefined
  ): Promise<ApiMutationResponse> => {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_RETAILBOOK_API_URL}/offer/${offerId}/order/${orderId}`,
      {
        method: "DELETE",
        headers: Helpers.getHeaders(token),
      }
    );

    return await res.json();
  },

  getMonthlyOfferMetrics: (params?: ApiGetParams, token?: string) =>
    instance
      .get<TOfferMetrics>(`/metrics/offer/monthly`, {
        headers: Helpers.getHeaders(token),
        params
      })
      .then((res) => res.data),

  viewItemisedOrders: (offerId: string, orderId: string, token?: string, params?: ApiGetParams) =>
    instance
      .get(`/offer/${offerId}/order/${orderId}/detail`, { headers: Helpers.getHeaders(token), params })
      .then((res) => res.data),

  getCurrencies: (token?: string) => instance.get("/static/currency", { headers: Helpers.getHeaders(token) }).then((res) => res.data),

  getStates: (token?: string) => instance.get<TOfferState[]>("/static/offer/state", { headers: Helpers.getHeaders(token) }).then((res) => res.data),
};
