import NextAuth from "next-auth";
//everytime you update the session atructure you need to update session type
declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    role: "intermediary" | "bank";
    accessToken: string;
    user: {
      ext_id: string;
      error: string;
    };
  }
}
