# RetailBook B2B App

This project is primarily a [Storybook](https://storybook.js.org/) installation containing the components for the _RetailBook_ app. It is also a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

### Third party libraries

Below are a list of packages which are intended to be used for production:

#### Language

- [Typescript](https://www.typescriptlang.org/)

#### Components

- [Headless UI](https://headlessui.com/)
- [React Toastify](https://fkhadra.github.io/react-toastify/introduction)

#### Utility

- [classnames](https://github.com/JedWatson/classnames)
- [date-fns](https://date-fns.org/)
- [svgr](https://react-svgr.com/)

#### Styling

- [Tailwind](https://tailwindcss.com/)
- [Tailwind Plugin: Forms](https://github.com/tailwindlabs/tailwindcss-forms/)
- [Utopia](https://utopia.fyi/)
- [React Feather Icons](https://github.com/feathericons/react-feather/)

#### Forms

- [React Hook Form](https://react-hook-form.com/)
- [React Number Format](https://s-yadav.github.io/react-number-format/)
- [EasyMDE](https://github.com/RIP21/react-simplemde-editor)

#### State Management

- [React Query](https://tanstack.com/query/v4/docs/overview)

#### Testing

- [Playwright](https://playwright.dev/docs/intro)

#### Linting

- [ESLint](https://eslint.org/docs/latest/developer-guide/)
- [Prettier](https://prettier.io/docs/en/)  
  <br />
  <br />

# Getting Started

Clone this repository:

```bash
$ git clone https://github.com/RETAIL-BOOK-LIMITED/retail-book-ui.git
```

Install the dependencies

```bash
$ cd retail-book-ui

$ nvm use

$ yarn
```

Run the development server:

```bash
$ yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `pages/index.tsx`. The page auto-updates as you edit the file.

[API routes](https://nextjs.org/docs/api-routes/introduction) can be accessed on [http://localhost:3000/api/hello](http://localhost:3000/api/hello). This endpoint can be edited in `pages/api/hello.ts`.

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/api-routes/introduction) instead of React pages.

# Auth

Auth is handled by [next-auth](https://next-auth.js.org/). The auth flow is configured to use Azure B2C.

<br />

## Creating a new user

You will need to contact the project owner (or anyone with access) to request a new user be added to Azure B2C. Once that is done, you will need to go to the app, get redirected to the login page, and reset your password. You will then be able to login with your new credentials.

## Types of users: +bank +retail

There are 2 organizational types of users: bank and retail. They are differentiated by the email address format that they use (`e.g. <EMAIL>`)

The +bank user is used to create and manage offers and the +retail user is used to review offers and create orders on those offers.

## Role: editor or manager

There are 2 roles for each organizational type: editor and manager. The manager role has access to manage team members.

## Configuration

The auth configuration is located in `pages/api/auth/[...nextauth].ts`. This file is where you can configure the auth flow, the auth providers, and the session object.
[nextauth config docs](https://next-auth.js.org/configuration/initialization)

## Page level auth

At the moment, all pages are protected. This is accomplished merely by the existence of the middleware.js file being created. If you want granular control of pages, you can manually list them in middleware.js:

```js
export { default } from "next-auth/middleware";

export const config = { matcher: ["/dashboard"] };
```

https://next-auth.js.org/tutorials/securing-pages-and-api-routes

</br>

# Pages

New route can just be created by creating a new file in the `pages/` directory. The file name will be the route name. For example, `pages/offer.tsx` will be accessible at `localhost:3000/offer`.

[Next JS Routing](https://nextjs.org/docs/routing/introduction)

<br />

# Modals

Some buttons/flows open a modal for the user to input information or simply confirm a selection. These modals are created using `<StyledDialog/>`s. To manage the open/close state of the modal, we create `useState` variables. An example:

```
const [isUploadModalOpen, setUploadModalOpen] = useState(false);
```

Typically, we add the `<StyledDialog/>` beneath all other components on the same page.

</br>

# Types

All types live in `types.ts`. Note, some types/enums are reflective of the backend's response types (for example, `EOfferStatus`, `EOfferType`). If something gets changed on the backend, make sure to update the corresponding types.

The only other type is in `types/auth.d.ts`. This file is used to overwrite the session type that next-auth uses. Anytime you want to modify what will be provided on the session object, be sure to update this file to match those changes.

<br />

# Helpers

`helpers.ts` contains helper functions that extract repeated logic for tasks such as formatting numbers (`formatNumber`), currency(`formatGBP`, `currencyFormatter` etc.), and parsing order data from form data (`createOrderFromAggregateFormData`).

</br>

# API Endpoints

Functions made to call PeelHunt's API endpoints can be found in `utils/api.ts`. To call one of these functions elsewhere in the code, `import { Api } from "utils/api"` and call `await Api.desiredFunction(args)`. Everyone of these functions needs at least the auth token, and potentially other parameters which are laid out in the function definitions.

</br>

# React Query

## Queries

[Query Docs](https://react-query-v3.tanstack.com/guides/queries)

We use react queries to fetch and cache data for easy access within our pages. They are also found in `utils/queries.ts`. A query function needs 3 elements: the query key (`queryKey`), query function (`queryFn`), and an `enabled` field, which disables the query from running if a certain condition is false. In our case, that condition is `!!token`. The query key is typically an array with the first item being a string that describes the type of object being queried (e.g. "document", "offer", "user). As your query gets more specialized, you can nest your query keys simply by adding more items to the array.

The query function itself is straightforward and calls a function to an API endpoint

#### How to invoke queries + isLoading

To use a query in a file, wrap the query you wish to call along with its arguments inside a `useQuery` hook. An example of this would be

```
 const { data: offer, isLoading: offerIsLoading } = useQuery(
    getOfferQuery(query?.id as string, accessToken)
 );
```

We use the `data` and `isLoading` properties from the returned object to populate our `offer` variable and `offerIsLoading`.
`isLoading` is a boolean property that denotes whether or not the query is still loading. We use this in places where we want disable certain functionality until the query data is fully ready.

## Mutations

[Mutation Docs](https://react-query-v3.tanstack.com/guides/mutations)

Mutations handle any user mutation of data (creating, editing, and deleting data). Mutations are grouped with queries that work with the same type of data structure in `utils/queries.ts`. A mutation function has two main parts we work with:


1. The mutation function (mutationFn)
2. handling of success (onSuccess)

You can technically add other properties but we stick to using these two. The mutation function just calls the API function for the mutation being done, similar to the nature of query functions.

The onSuccess handles the invalidation of the data query of the mutated object, and displays a success toast. Some mutations have calls to set the state in order to affect the frontend (such as closing modals)

#### How to invoke mutations + isLoading

Similar to queries, we initialize our mutations by wrapping them in a `useMutation` hook with the neccesarry arguments. Here is an example:

```
const sendInviteMutator = useMutation(
    sendInviteMutation(session?.accessToken, offer?.id, qc)
);
```

Once the mutator has been initialized, we invoke the mutation function by calling `sendInviteMutator.mutate(invites)`. Note how you pass in the `mutationFn`'s arguments inside the `.mutate()` call.

`isLoading` is similar to how it works for queries, a boolean that tells us whether or not the mutation is done loading.

</br>

# Playwright E2E Testing

Playwright is a Node.js library that we are using to do our testing. It's nice because we can both script our tests and automate our browsers (so you can see the test running in real time. The `playwright.config.ts` file picks which browsers to test on: Chromium Browser, Firefox Browser, Webkit Browser, Mobile Chrome, Mobile Safari, Microsoft Edge, and Google Chrome.

Playwright also comes with a code generator called codegen which will record every click in the browser as a command in a subsequent terminal. This makes writing tests alot easier!

## Testing Authentication

To begin using playwright, you will need to record your authentication credentials, or else the API calls will not succeed.

Type this command in your terminal:

```bash
npx playwright codegen --save-storage=auth-bank.json localhost:3000
```

A browser will open and you can login to your test user's bank account then quit. Now, you will have a `auth-bank.json` file in your project.

Repeat this command and process for your test intermediary account and create a `auth-intermediary.json` file:
 
```bash
npx playwright codegen --save-storage=auth-intermediary.json localhost:3000
```

These json files will allow you to run your playwright tests without having to login each time. You can load them into the beginning of your test files and you can load them into codegen with this command:

```bash
npx playwright codegen --load-storage=auth.json localhost:3000
```

</br>

## Where to write the tests

Tests are placed in files within the `/tests` directory. If you need a file or image in your test, you can place it within the `fixtures` folder of the test directory. Currently there is only one file in the `fixtures` folder and was left as a goodbye present-hope you like it!
 
</br>

## How to run the tests

To run the tests use this command:

```bash
yarn e2e:test
```
 
To only run tests in a single folder use:

```bash
npx playwright test <path to files>
```

To run test by name use:

```bash
npx playwright test -g "<name of your test>"
```

To see tests in the browser as they are being run:

```bash
npx playwright test --headed
```

To use the codegen tool:

```bash
npx playwright codegen
```

</br>

## Automated Test accounts

### Bank

<EMAIL>

### Intermediary:

<EMAIL>

</br>

# Notes on UI/styling development

## Utopia & Tailwind

[Utopia](https://utopia.fyi/) has been configured for the space and type scale for the site. The values are taken from the generator on the website and located in base.css - they should not be changed. Utopia provides T-shirt sizing values which are then interpolated across screen sizes. This should reduce the need for as many media queries and allow developers to 'eyeball' things to a far higher degree of accuracy as all values will at least be be on the right scale, and there are far fewer to choose from.

These values have been configured in tailwind. For font sizes these overwrite all tailwind values to ensure nothing outside of the scale is used, however for practicality the spacing sizes are adative as realistically some elements such as images and icons may need to be more explicitly controlled. You should always use a utopia value in preference over an explicit tailwind rem value if possible to preserve alignment and scaling.

If in doubt please consult the style guide or contact Clearleft.

## NextJS Images

NextJS `<Image />` components will presumably be used for production, however this prevents the static export of the site, so native `<img />` elements have been used for development. It should be apparent where these will need to be updated. The sotrybook addon for NextJS should also allow storybook to continue to function correctly.

## SVGs & Icons

[SVGR](https://react-svgr.com/) Has been used to load SVGs as react components. Obviously feel free to change this if required. Configuration is provided in both `next.config.js` and `.storybook/main.js` to allow this to work properly, and if it is removed the associated configuration will need to be updated.

The primary reason for these loader is to allow simple augmentation of the icon set provided by [Feather Icons](https://feathericons.com/) with custom icons. These can be found in `assets/icons`. To ensure consistant appearance and behaviour between feather icons and custom icons, any custom icons should be on a 24px x 24px canvas, with stroke / fill set to currentColor.
