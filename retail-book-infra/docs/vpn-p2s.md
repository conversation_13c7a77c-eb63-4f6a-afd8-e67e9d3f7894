# VPN P2S

Ease of use for Dev: ★★★★★
Security of the solution: ★★★
Auditability: ★★
Data hosting: ★★★★★
Maintainability: ★★★★
Pricing: ★★★★★
Scalability:  ★★★

## Description

VPN P2S will let the developers to connect directly to the private network of AKS and call the AKS api directly from their dev machines.

This solution requires a VPN client and may not work in certain network conditions

![image.png](assets/vpn-p2s.png)

## Ease of use

This solution will requires less configuration in order to connect to the AKS but will requires to install a VPN client on the developers workstation. This may difficult in some environment and may not be available for all OS type.
Also, depending on the VPN solution used, this may imply some network problems in some situations (no UDP connection allowed on some Company network for example)

## Security

VPN access is authenticated through a certificate or an Entra ID (prefered solution). The communication is encrypted between the client and the internal network.

However, security group on the VPN gateway subnet are not supported ([https://learn.microsoft.com/en-us/azure/vpn-gateway/tutorial-site-to-site-portal#create-a-gateway-subnet](https://learn.microsoft.com/en-us/azure/vpn-gateway/tutorial-site-to-site-portal#create-a-gateway-subnet)). 

Therefore, you should secure each resource of your infrastructure to allow the specific resources to access it (not the whole VNET).

This security is important as you connecting directly an external computer to you network that is not verified, may contain virus…

An other solution is to use a Hub and Spoke architecture to add a security layer.

## Maintainability

VPN Gateways are managed resources.

## Pricing

For a basic VPN (up to 100Mbps and 128 connexion), the cost is limited:

£20.90 / month.

If a higher throughput is neede cost can increase: [Pricing](https://azure.microsoft.com/en-us/pricing/details/vpn-gateway/)

## Datahosting

the solution is available on UK South

## Scalability

VPN gateway can be manually resize with little downtime.

## Auditability

Only connection to the VPN and API call can be logged
