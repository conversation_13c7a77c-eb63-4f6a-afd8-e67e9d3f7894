# How to connect to AKS ?

## Requirements

- Azure CLI [How to install](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli)
- The [bastion](https://portal.azure.com/#browse/Microsoft.Network%2FbastionHosts) and the [AKS cluster](https://portal.azure.com/#view/HubsExtension/BrowseResource/resourceType/Microsoft.ContainerService%2FmanagedClusters) deployed

For the next steps, we will consider `retailbookstaging-d8c1df36.a6ebd97b-ec00-4f6f-b142-4bdfaa512e08.privatelink.uksouth.azmk8s.io` is the kubernetes API address.

## Configure cluster access

- Run `az login` to authenticate

- Switch your subscription to `RetailBook-Dev|Prod` in the azure context using the following command: `az account set --subscription "RetailBook-Prod"`

- Type `az aks get-credentials --resource-group retailbook-staging --name retailbook-staging` to get the AKS credentials.

- Type `kubectl get nodes`, you should get an error like `Unable to connect to the server: dial tcp: lookup retailbookstaging-d8c1df36.a6ebd97b-ec00-4f6f-b142-4bdfaa512e08.privatelink.uksouth.azmk8s.io on *******:53: no such host`. You need to keep the address `retailbookstaging-d8c1df36.a6ebd97b-ec00-4f6f-b142-4bdfaa512e08.privatelink.uksouth.azmk8s.io`, it will be useful later.

- Add this address to your host file, pointing to localhost. (For Mac/Linux, it's on `/etc/hosts`, on windows, it's on `C:\Windows\System32\Drivers\etc\hosts`)
    Append the line : `127.0.0.1 <aks_pls_address>`.
    So in our case, append the line : `127.0.0.1 retailbookstaging-d8c1df36.a6ebd97b-ec00-4f6f-b142-4bdfaa512e08.privatelink.uksouth.azmk8s.io`.

## Access cluster

Forward the control plane https traffic locally through the bastion by using the `Azure CLI`:
  
- Run `az login` to authenticate

- Forward the control plane https locally: 
    ```
    az network bastion ssh --name retailbook-bastion-staging --resource-group retailbook-staging --target-resource-id /subscriptions/8f06ffc4-1cf1-4aaf-844a-a1567c3e31c7/resourceGroups/retailbook-staging/providers/Microsoft.Compute/virtualMachines/retailbook-staging --auth-type aad -- -L 443:retailbookstaging-d8c1df36.a6ebd97b-ec00-4f6f-b142-4bdfaa512e08.privatelink.uksouth.azmk8s.io:443
    ``` 
    You may need to use `sudo` if you use Mac/Linux because the local bind port is lower than 1024.

- Then, you can just open an other shell, and type `kubectl get nodes` to test if everything is working!
