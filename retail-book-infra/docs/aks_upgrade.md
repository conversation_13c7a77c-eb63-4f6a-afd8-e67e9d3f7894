# Upgrading the AKS cluster

Upgrading a Kubernetes cluster can take some time. The best way to do this is to be as prepared as possible and anticipate problems before they occur. Follow this process to upgrade clusters as easily as possible.

## When to upgrade

Best practice is to upgrade your AKS cluster as soon as a new version is GA, and before end of life of the current version.
To check what versions of k8s are supported by AKS, check out [Supported Kubernetes versions in Azure Kubernetes Service](https://learn.microsoft.com/en-us/azure/aks/supported-kubernetes-versions).

| K8s version | Upstream release | AKS preview | AKS GA | End of life |
|-------------|-----------------|-------------|--------|-------------|
| 1.24        | Apr-22-22       | May 2022    | Jul 2022 | Jul 2023    |
| 1.25        | Aug 2022        | Oct 2022    | Dec 2022 | Dec 2023    |
| 1.26        | Dec 2022        | Feb 2023    | Apr 2023 | Mar 2024    |
| 1.27        | Apr 2023        | Jun 2023    | Jul 2023 | Jul 2024    |

## To which version should I upgrade ?

It is recommended to select the default version supported by AKS. The easiest way to know this version is to go to the [cluster creation page in Azure portal](https://portal.azure.com/#create/Microsoft.AKS), open the list of available kubernetes version and check what version is tagged with "(default)".

## Process

### Be familiar with AKS release notes to anticipate breaking changes

First, go through the [AKS release notes](https://github.com/Azure/AKS/blob/master/CHANGELOG.md) and [AKS components breaking changes by version](https://learn.microsoft.com/en-us/azure/aks/supported-kubernetes-versions?tabs=azure-cli#aks-components-breaking-changes-by-version) to identify what breaking changes might apply to your infra.
  
### Find removed Kubernetes API resources

Some deprecated k8s resources are removed during a version upgrade, this is the main source of breaking changes during an k8s cluster upgrade. If your infrastructure code or installed charts use these resources, you have to make updates to your code or charts before upgrading the cluster.
Fortunately, there is a tool called [KubeNT](https://github.com/doitintl/kube-no-trouble) to help you achieve this. This small tool will check which manifest files will be deprecated so we change them before the upgrade.
Note that this tool is not perfect and in very specific cases, removed APIs usage can be hidden. This is why it is recommended to read the release notes before.
  
### Update removed Kubernetes API resources usage

Look up what you can do for every resource that shows up in kubent.

- If it is in your code, you will find kubernetes documentation to migrate the resource (sometimes it only involves updating the ApiVersion)
- If it involves a chart, you will fix the issue by following the process in the next section
  
### Check Chart & AppVersions of installed services

The goal of this step is to find installed services that are running a version that is not supported by the upgraded version of k8s.
The easiest way to do this is to fill the following table:

| Service | Current App Version | Current Chart Version | Required App Version for Upgraded K8s | Required Chart Version for Upgraded K8s | Upgrade Required | Documentation |
|---------|---------------------|----------------------|--------------------------------------|---------------------------------------|------------------|---------------|
| Service 1 | 1.0.0 | 1.2.3 | 2.0.0 | 2.1.0 | Yes | https://example.com/service1-docs |

To find information to fill this table:

- Current app version: Go in argoCD and check the image used by the service by clicking on the corresponding ArgoCD application or one of its pods
- Current chart version: In the `Chart.yaml` files of retailbook-infra-tooling
- Required App version / Required Chart Version: Go through the documentation (github page, releases, changelogs) of the service. This can be easy or really painful depending of the service. For instance, some services like cert-manager have very precise compatibility matrices: https://cert-manager.io/docs/installation/supported-releases/
  
### Upgrade services

After filling the table above, you must upgrade the services that requires it.
Most of the time, it will only require upgrading the chart version in a `Chart.yaml` file. Sometimes it will require difficult upgrading process and debugging.
  
### Upgrading the cluster

Now here comes the easy part, go to the azure portal, select your kubernetes cluster > cluster configuration > upgrade version. The control plane will be updated, and the nodes will be updated one by one. This process can take a while, so be patient.
Note that a downtime is expected on statefulsets migrations (like postgresql or nats)

## AKS 1.26 Upgrade
This documentation should not be considered as a tutorial to upgrade RetailBook's AKS clustrer to 1.26, as things may change between the time of upgrade and the very moment I am writing this documentation. However, reading the following is a good starting point. 

### Analyzing kubernetes changelog

The only major breaking changes for 1.26 are:
- Removal of the v1beta1 flow control API group
- Removal of the v2beta2 HorizontalPodAutoscaler API
- Removal of legacy command line arguments relating to logging
- Removal of kube-proxy userspace modes
- Removal of in-tree credential management code
- Removal of dynamic kubelet configuration

[Source](https://kubernetes.io/blog/2022/12/09/kubernetes-v1-26-release/#deprecations-and-removals)

These do not seem to affect the infra for retailbook.

### KubeNT

For version 1.26.3, kubent does not seem detect any usage of removed APIs.
Run the following to check by yourself (with AKS default version for 1.26, which may not be 1.26.3):
```bash
$ kubent -t <version>
```

### Upgrade services

As seen by the kubent result and the changelog, there is little chance that the services in their current versions are broken with 1.26. Nevertheless, don't hesitate to fill the table mentionned above to be certain of this. (An other way would be to upgrade staging to 1.26 and see what's broken, but that would involve downtime of the staging env)
