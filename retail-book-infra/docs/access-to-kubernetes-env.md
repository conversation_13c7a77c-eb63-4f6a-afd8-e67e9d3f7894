# Access to Kubernetes environment

Status: In progress
Created time: 7 janvier 2025 14:07
Personne: <PERSON><PERSON> (New DB): RetailBook (https://www.notion.so/RetailBook-1708f3776f4f8052a613f1bf905c4468?pvs=21)

## Context and Problem Statement

For the moment, the access to Kubernetes environment must go through different steps:

- Connect to a virtual desktop through AVD
- Create an SSH key and add it to the jumpbox vm
- Create a ssh tunnel to the jumpbox. Access to Kubernetes through the AVD

This setup is fastidious. Each developers need an AVD (increase cost).  

---

## Decision Drivers

(Ordered list of the most important criteria to decide)

1. Easy-to-use for the dev teams
2. Security of the solution
3. Maintainability
4. Pricing
5. Data hosting (France / Europe / other)
6. Scalability
7. Auditability

---

## Considered Options

| Name  | Description  |
|---|---|
| [VPN P2S](./vpn-p2s.md) | Usage of a VPN point to site to access AKS api  |
| [Azure Bastion](./azure-bastion.md) | Usage of Azure Bastion to access AKS through a Jumpbox |

## Evaluation of the Options

**Legend:**     ❌ KO     ★★ bad     ★★★ medium     ★★★★ good     ★★★★★ perfect

---

| Name | Ease of use for Dev | Security of the solution | Maintainability | Pricing | Data hosting | Scalability | Auditability |
|---|---|---|---|---|---|---|---|
| [VPN P2S](./vpn-p2s.md) | ★★★★★ | ★★★ | ★★★★ | ★★★★★ | ★★★★★ | ★★★ | ★★ |
| [Azure Bastion](./azure-bastion.md) | ★★★ | ★★★★ | ★★★ | ★★★ | ★★★★★ | ★★★ | ★★ |

---

## Decision Outcome

Chosen option: **"Fill me with an option"**, and the main reason why

## Positive consequences

- Reduce pricing and management by removing the AVD and the JumpBox
- AKS api is more easily accessible for the Devs
- No more SSH key to manage

## Negative consequences

- Auditability is reduced
- Additional configurations needed for NSG

## **Links and resources**

-
