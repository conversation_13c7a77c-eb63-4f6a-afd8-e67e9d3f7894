# Deploying Changes

## Infrastructure

All infrastructure changes are automatically deployed through our github actions in this repository. Simply open a pull request 
and merge it into the default branch.

## Participant

Participants are split from the infrastructure (in both permission and architecture terms) and rights are not granted to the 
pipelines to perform operations on sensitive participant areas - such as the key vault.

Instead, if you own the permission to do so, run the following locally **from the default branch** in the `layers/participant` directory
**replacing** the `{environment}` with staging or prod in the process:

```bash
terragrunt run-all plan --queue-include-dir=*/{environment}
terragrunt run-all apply --queue-include-dir=*/{environment}
```

Or instead, run `terragrunt plan` or `terragrunt apply` for a single participant.
