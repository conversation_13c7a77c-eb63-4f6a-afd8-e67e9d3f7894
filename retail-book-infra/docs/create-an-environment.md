# Create an environment

## Requirements

- An Azure subscription with owner access
- Having a minimum of 5 Standard_D2as_v5 in the resources quotas

## Prepare the environment

Let's create an environment named `prod` and getting inspiration from the staging environment.

/!\ Make sure that the `.terragrunt-cache` folder is deleted for each layers

**Ensure** you have a valid SendGrid admin API key set in your environment as follows:
```bash
export SENDGRID_API_KEY=(...)
```

## Bootstrap the environment

Let's create an environment named `prod`. First, we need to bootstrap the environment. Copy the `layers/bootstrap/staging` to `layers/bootstrap/prod` and delete the `layers/bootstrap/prod/.terragrunt-cache` folder.

We need then to init the remote state. To do this, edit the `layers/terragrunt.hcl` file and comment the `remote_state` object.

Then, in this file, add your subscription id to the `subscription_list` map. Like this :

```
subscription_list = {
  staging    = "8f06ffc4-1cf1-4aaf-844a-a1567c3e31c7"
  prod = "a7c3310e-0770-49d8-b053-8a79fbd5566c"
}
```

Then, go to `layers/bootstrap/prod`, and execute `terragrunt apply` to create the resources.

### Migrate the local state to remote state

Now that the remote state storage account is created, we can migrate the local state to a remote state.
Uncomment the `layers/terragrunt.hcl` the remote_state object you commented previously.
Run `terragrunt init` and enter `yes` when it asks to copy the local state to a remote state.

## Create the infrastructure

Then, copy the `layers/infrastructure/staging` to `layers/infrastructure/prod` and delete the `layers/infrastructure/prod/.terragrunt-cache` folder.

Edit `layers/infrastructure/prod/inputs.hcl` and update `dns_zone` to `prod.retailbook.com`.

Edit the `layers/infrastructure/prod/inputs.hcl` and set the `frontdoor_enabled` and `waf_enabled` to false. Those components can't be deployed yet, as they need an Azure PLS that is created by the Kong Service LoadBalancer.

If you want to have different access/IP whitelisting between those two environments, you will need to edit the `layers/infrastructure/prod/inputs.hcl` file.

You will also need to precise the AKS SKU Tier on `modules/infrastructure/aks.tf` like this :

```
aks_sku_tier_by_environment = {
  staging = "Free",
  prod    = "Paid",
}
```

Finally, go to `layers/infrastructure/prod`, and `terragrunt apply`.

### DNS Zone delegation

You will need then to delegate a DNS zone to azure. In our case, it is `prod.retailbook.com`

To do this, go first to the Azure Portal > DNS Zone > `prod.retailbook.com` and get the values Name server 1 to 4. For our example, we have :
Name server 1 : `ns1-34.azure-dns.com.`
Name server 2 : `ns1-34.azure-dns.net.`
Name server 3 : `ns1-34.azure-dns.org.`
Name server 4 : `ns1-34.azure-dns.info.`

Therefore, you need to add the following records on the `retailbook.com` DNS server

```
prod.retailbook.com. NS ns1-34.azure-dns.com.
prod.retailbook.com. NS ns2-34.azure-dns.net.
prod.retailbook.com. NS ns3-34.azure-dns.org.
prod.retailbook.com. NS ns4-34.azure-dns.info.
```

### Front-Door deployment

#### Requirements

- Kong must be deployed so that is creates an Azure PLS

#### Steps

Edit the `layers/infrastructure/prod/inputs.hcl` and set the `frontdoor_enabled` and `waf_enabled` to `true`. You can then re-apply the infrastructure

Then, go to Azure Portal > Private Link Service > Select `kong-proxy` that has a connection pending. Click on it, select the pending connection, and approve it.

## Deploy the security module

The process is the same as the other modules : copy the `layers/security/staging` to `layers/security/prod` and delete the `layers/security/prod/.terragrunt-cache` folder.

Edit the `layers/security/prod/inputs.hcl` if you want to change things.

Finally, go to `layers/infrastructure/prod`, and execute `terragrunt apply`.
