# Create a participant

## Steps

We will create a participant participant `jake` from a participant named `john`.

Copy `layers/participant/john` to `layers/participant/jake`, and delete all the `.terragrunt-cache` folders in it.

You can then cd to `layers/participant/jake/prod` and run `terragrunt apply`.

Note : the apply can fail while creating the secrets because the keyvault isn't fully initalized yet. You will need to re apply again if it fails

## Admin tool

The admin tool automates this process. See the tool in `retail-book` repo on the `src/admin/cmd/cli.go` path.
