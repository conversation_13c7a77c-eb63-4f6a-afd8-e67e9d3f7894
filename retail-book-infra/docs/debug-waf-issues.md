# Debug WAF issues

WAF is enabled for now in preventive mode in staging, and in detective mode in production.
Preventive mode is the default mode, and it will block requests that are detected as malicious.
Detective mode will only log the requests that are detected as malicious, but it will not block them.

## How to debug WAF issues

If on non production environments, if you get 403 unknown errors, it may come from the WAF.

To debug WAF issues, go to the Azure portal, and go the Log Analytics Workspace, than select retailbook-<environment>-waf-logs, and click on view logs.

You can then query the blocking logs with the following query:

```
AzureDiagnostics
| where action_s == "Block" or action_s == "AnomalyScoring"
```

/!\ The logs can take view minutes to be viewed by Azure.

The most important fields to check are:
`ruleName_s`: the name of the rule that was triggered
`details_matches_s`: different details parameters about the rule that was triggered

You can then enable or disable rules to correct the problem
