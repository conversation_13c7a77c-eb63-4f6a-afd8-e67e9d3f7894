# Azure Bastion

Ease of use for Dev: ★★★
Security of the solution: ★★★★
Auditability: ★★
Data hosting: ★★★★★
Maintainability: ★★★
Pricing:  ★★★
Scalability:  ★★★

## Description

Azure Bastion is a managed service from Azure in order to allow secure RDP and SSH connection to virtual machine without exposing them through a public IP.

This solution needs also a Jumpbox to work with AKS.

![image.png](assets/azure-bastion.png)

## Ease of use

Dev will need to use azure cli on their computer in order to generate a tunnel to the jump box. SSH connection can be managed through Entra ID instead of SSH key to avoid offboarding problems

However, Azure Bastion can provide client less connection through Azure portal, if azure cli  installation on the dev computer is not possible.

Example:

- [Access to AKS through Azure bastion and VS Code](https://techcommunity.microsoft.com/blog/fasttrackforazureblog/accessing-aks-private-clusters-with-azure-bastion-and-vs-code/3581367)

## Security

Security is assured through:

- No Public IP for the Jumpbox VM
- Authentication through Entra ID for the bastion and the jumpbox VM
- Network Security Groups to avoid accessing unwanted resources

However, because a jumpbox is required to access the API, the patch management of this VM should be managed in order to avoid security holes in the infrastructure.

## Maintainability

Even if Azure Bastion is a fully managed services, because a jumpbox should be deployed in order to access the AKS server make the solution a bit harder to maintain.

## Pricing

Cost engaged:

- Azure Bastion instances
- Jump Box instances
- Data transfer cost

[https://azure.microsoft.com/en-us/pricing/details/azure-bastion/](https://azure.microsoft.com/en-us/pricing/details/azure-bastion/)

Example:

- For a basic instance (sufficiant for 25 connections) that runs 24/7 this will cost:

  - 730h x £0.152 = £110.96 / month
  - 1 A1v2 instance: £50.48 / month
  - Data traffic:
    ![image.png](assets/azure-bastion-pricing.png)

## Datahosting

Azure bastion is available in UK south region

## Scalability

Azure Bastion can be scaled when using the standard SKU. Scaling is however manual

You can find the documentation [here](https://learn.microsoft.com/en-us/azure/bastion/bastion-overview#host-scaling)

## Auditability

Azure bastion allows session recording but only for graphical sessions and on the premium SKU.

If tunnel is used, only api calls can be recorded
