#!/usr/bin/env bash

set -e

# Helpers for readability.
bold=$(tput bold)
normal=$(tput sgr0)
function _info() {
    echo "${bold}${1}${normal}"
}

# Run script from directory where the script is stored.
cd "$( dirname "${BASH_SOURCE[0]}" )"

terraform_dirs="$(find .. -type f -name '*.tf' -not -path '*/.terraform/*' | sed -r 's|/[^/]+$||' | sort -u)"

for dir in $terraform_dirs; do
    _info "Generating documentation for directory: $dir"
    terraform-docs markdown table \
        --config=../.terraform-docs.yml \
        --output-file=README.md \
        "$dir"
    prettier --write --loglevel=warn "$dir/README.md"
done
