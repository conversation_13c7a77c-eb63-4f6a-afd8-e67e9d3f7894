# Azure AD B2C

We discovered that terraforming Azure B2C would be complicated because those reasons:

- The Azure AD B2C is in another Azure AD Directory
- Azure AD need high privileges to get deployed (almost global administrator)

We recommend to deploy AAD B2C manually using high privileges. However, the application can be terraformed easily. You can get inspiration from `modules/infrastructure/application_ci.tf` and `modules/infrastructure/terraform_ci.tf` and with the [AAD Application official documentation](https://registry.terraform.io/providers/hashicorp/azuread/latest/docs/resources/application). The only thing is that you need to use another Azure directory, you may need to declare another provider here : `layers/terragrunt.hcl`.
