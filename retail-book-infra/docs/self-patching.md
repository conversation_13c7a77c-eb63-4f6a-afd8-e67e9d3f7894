# Self-patching with Renovate

We use Renovate to propose version updates for:
- Terraform
- Terragrunt
- Terraform Providers

These updates are not applied automatically — our goal is not to update blindly or continuously, since breaking changes could unintentionally modify infrastructure behavior. Instead, Renovate opens pull requests that you can review and use to upgrade versions when appropriate.

Each Renovate PR triggers a GitHub Actions workflow that:

- Updates the terraform.lock.hcl files and commits the changes automatically within the same Renovate PR
- Checks for infrastructure drift

There is __no automatic merging__ of Renovate PRs in this repository. To upgrade a dependency:
- Review the PR opened by Renovate.
- Look at the result of the "Renovate check / Drift detection" check in the first commit of the PR.
  - ✅ If there is no drift, the upgrade is safe to merge. You can merge it manually.
  - ⚠️ If there is drift, you should check what the drift is and edit the codebase so that there is no drift anymore.

