# 🔥 How to Destroy an Environment

This guide explains the step-by-step process to fully destroy an environment in the `retail-book-infra` and `retail-book-infra-tooling` repositories.

The commands in this documentation use `preprod` as an example. Make sure to replace it with the name of the environment you actually want to destroy.

> ⚠️ **WARNING:** This operation is irreversible. Make sure you are deleting the correct environment before proceeding.

## 🧹 Step 1: Clean participants' infra components

In the `retail-book-infra` repository, run the following command from the `layers` folder to destroy infrastructure components used by participants:

Eg for preprod:
```bash
terragrunt run-all destroy --queue-include-dir "participant/**/preprod"
```

## 🔐 Step 2: Destroy the security layer
Navigate to the security layer for preprod:

Eg for preprod:
```bash
cd layers/security/preprod
terragrunt destroy
```

## 🏗️ Step 3: Destroy the infrastructure layer
Navigate to the infrastructure layer for preprod:

Eg for preprod:
```bash
cd layers/infrastructure/preprod
terragrunt destroy
```

_Troubleshooting:_ At the end of this step you might encouter these errors 
- Failed to save state
- Failed to persist state to backend
- Error releasing the state lock
The errors are normal since you deleted the state bucket. Check in the logs that you can see: `azurerm_resource_group.tfstate: Destruction complete`: it's ok.

## 🧰 Step 4: Destroy the bootstrap layer
Navigate to the bootstrap layer for preprod:

Eg for preprod:
```bash
cd layers/bootstrap/preprod
terragrunt destroy
```

_Troubleshooting: If you can not delete the resource group, go in Azure console and delete it manually._

## 🧼 Step 5: Clean up the repository

Once all resources have been destroyed:

- Delete all files related to the environment inside the layers/ directory.
- Open a Pull Request with the cleanup.
- Merge the PR into main.
  
## 🗃️ Step 6: Clean Up the Tooling Repo

In the `retail-book-infra-tooling` repository:
- Delete the folder related to the environment (e.g., `preprod` folder).
- Open a Pull Request with the deletion.
- Merge the PR into main.
