# Issues found on AKS

## AKS 1.24

We discovered that some part of the Service LoadBalancer aren't working on AKS 1.24 if they contain a `service.spec.ports.appProtocol`. This is present on the following charts :

- `ingress-nginx` (`external-ingress-nginx` and `internal-ingress-nginx`)
- `kong`

If Azure hasn't fixed this issue, and you need to update the cluster, make sure that those chart do not contain the `appProtocol` property in the Service manifest.

## AKS 1.24.10

We discovered an issue where the default health probes are set to HTTPS as opposed to TCP. After an upgrade you may need to change the health ports
to the nodePort of the LoadBalancer service - and change scheme from HTTP/HTTPS to TCP.

