# B2C Secrets rotation

Applicative pods use a secret from Azure B2C to perform user signup and login. It is recommended to rotate this secret once every 6 months. Usually, this kind of process can be done automatically with terraform, but as AzureB2C is not on the same tenant than the rest of the infra, it would be difficult to send secrets from Azure B2C resources to the infra with terraform. Therefore, we need to rotate the secrets manually when it is needed.
What follows is the process to rotate these secrets.

## Manual rotation process

Before starting, switch your kubernetes CLI context to the targeted env.

1. Connect to the **Retailbook B2C** Azure portal
2. Go to app registrations > "All applications” tab
3. Create a new secret for `userservice` and `gui`
   1. Click “Certificates & Secrets”
   2. "+ New client secret"
   3. Enter a description & expire the secret in 6 month
   4. **Do not delete the old secret,** delete it at the end of the rotation process
4. Connect to the **Retailbook** Azure portal
5. Go to Keyvault > `retailbook-<env>`
6. Update `userservice-client-secret` and `ui-azure-ad-b2c-client-id`
   1. Secrets > `userservice-client-secret` (for `userservice` secret) / `ui-azure-ad-b2c-client-id` (for `gui` secret)
   2. Click New Version
   3. Paste the secret and set expiration date 6 month later (same date as step 3.c)  
   ⚠️ The notification system relies on this expiration date, make sure to not miss this step.
7. Force resync k8s secrets with vault secrets:  
`kubectl annotate es ui userservice-client-secret force-sync=$(date +%s) -n global --overwrite`
8. In `retailbook-ui` repo, update the `AZURE_AD_B2C_CLIENT_SECRE` of the `.env.<env>` file with the new secret for `gui`
9. Create and deploy a new version of the UI docker image. 
10. Sync ArgoCD, UI pod should be recreated with the new image.
11. Trigger a rollout restart of `global/userservice` pod to refresh secrets  
`kubectl rollout restart -n global deploy/userservice`
12. (Optional) Check that the secret has successfully been rotated on both `userservice` & `ui` pods:  
Find pod names for `userservice` & `ui` pods:  
`kubectl get pods -n global`  
Print the secrets and verify that they are updated:  
   `kubectl exec -it <pod_name> -n global -- env | grep AZURE_AD_B2C_CLIENT_SECRET` → for UI pod  
   `kubectl exec -it <pod_name> -n global -- env | grep AZURE_CLIENT_SECRET` → for userservice pod
13. Check that everything works by logging in on `ui.<env>.retailbook.com`
14. Delete old secrets on B2C app registrations

→ This rotation process causes **0 downtime** as two secrets live together during the time of rotaton.

## When to rotate B2C Secrets

It is recommended to rotate B2C secrets once every 6 months.

### Secret expiration notifications

An Azure logic app monitors the expiration date of the secrets in the keyvault and sends an email when they are about to expire.
You can find these logic apps in the Retailbook tenant, in [logic apps](https://portal.azure.com/#view/HubsExtension/BrowseResource/resourceType/Microsoft.Logic%2Fworkflows).
There are 3 logic apps, each one monitoring the keyvault of either staging or prod environments.
They are composed of:

1. An Event Grid trigger that will run when a secret on the keyvault is about to expire (30 days before expiration)
2. A SendGrid action that sends an email to notify that secrets have to be rotated.

![logic_app_diagram.jpg](./assets/logic_app_diagram.jpg)

The EventGrid uses a System Identity to reach the KeyVault. It can be configured in the `Settings > Identity` tab of the logic app.
The SendGrid action uses the `Secret expiration notification Logic App API Key` defined in the production Sendgrid SaaS with email sending permissions.
