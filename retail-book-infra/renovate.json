{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "enabledManagers": ["custom.regex"], "customManagers": [{"customType": "regex", "fileMatch": ["^layers/root\\.hcl$", "^\\.github/workflows/.*\\.yaml$"], "matchStrings": ["terraform_version_constraint\\s*=\\s*\">=\\s*(?<currentValue>.*?)\"", "terraform_version\\s*:\\s*(?<currentValue>.*?)\\n"], "datasourceTemplate": "github-releases", "packageNameTemplate": "hashicorp/terraform"}, {"customType": "regex", "fileMatch": ["^layers/root\\.hcl$", "^\\.github/workflows/.*\\.yaml$"], "matchStrings": ["terragrunt_version_constraint\\s*=\\s*\">=\\s*(?<currentValue>.*?)\"", "terragrunt_version\\s*:\\s*(?<currentValue>.*?)\\n"], "datasourceTemplate": "github-releases", "packageNameTemplate": "gruntwork-io/terragrunt"}, {"customType": "regex", "fileMatch": ["^layers/root\\.hcl$"], "matchStrings": ["source\\s*=\\s*\"(?<repo>.*?)/(?<provider>.*?)\"\\n\\s*version\\s*=\\s*\"~>\\s*(?<currentValue>.*?)\""], "datasourceTemplate": "github-releases", "packageNameTemplate": "{{repo}}/terraform-provider-{{provider}}"}], "packageRules": [{"groupName": "Dependencies updates", "matchManagers": ["custom.regex"], "matchUpdateTypes": ["major", "minor", "patch"]}], "gitIgnoredAuthors": ["github-actions[bot]@users.noreply.github.com"]}