default_stages: [commit]
repos:
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v2.20.0
    hooks:
      - id: commitizen
        stages:
          - commit-msg
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.58.0
    hooks:
      # - id: checkov
      - id: terraform_docs
      - id: terraform_fmt
      # - id: terraform_tflint
      # - id: terraform_tfsec
      # - id: terraform_validate
      # - id: terrascan

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: check-added-large-files
        args: ["--maxkb=600"]
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: forbid-new-submodules
      - id: mixed-line-ending
      - id: trailing-whitespace
