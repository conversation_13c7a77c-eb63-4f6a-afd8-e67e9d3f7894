name: Prod - Infra deploy

on:
  workflow_dispatch:
      # only allow prod deployment from the main staging branch
      branches:
        - main

jobs:
  # Needed so that pipeline is successful if github.event.pull_request.user.login == 'renovate[bot]'
  noop:
    runs-on: ubuntu-latest
    steps:
      - name: No-op
        run: echo "Pipeline is successful even if there next job is skipped"
  apply-prod:
    # Needed so that it is not triggered on renovate pull request, it would create concurrency for terraform states
      if: github.event.pull_request.user.login != 'renovate[bot]'
      needs: noop
      name: Prod deploy
      uses: ./.github/workflows/apply-env.yaml
      with:
          environment: prod
      secrets: inherit
    
