name: Send Teams alert

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: The environment to apply

permissions:
  id-token: write
  contents: read

jobs:
  plan:
    name: Terraform
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Microsoft Teams Notification
        uses: skitionek/notify-microsoft-teams@master
        with:
          webhook_url: ${{ secrets.MS_WEBHOOK }}
          title: "Drift detected in ${{ inputs.environment }}"
          raw: >-
            {
                "type": "message",
                "attachments":
                [
                    {
                        "contentType": "application/vnd.microsoft.card.adaptive",
                        "content":
                        {
                          "type": "AdaptiveCard",
                          "body": [
                              {
                              "type": "TextBlock",
                              "size": "Medium",
                              "weight": "Bolder",
                              "text": "Drift detected in staging",
                              "style": "heading",
                              "wrap": true
                              },
                              {
                              "type": "ActionSet",
                              "actions": [
                                  {
                                  "type": "Action.OpenUrl",
                                  "title": "More details in job",
                                  "url": "https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}"
                                  }
                              ]
                              }
                          ],
                          "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                          "version": "1.5",
                          "msteams": {
                              "entities": [
                              {}
                              ]
                          }
                      }
                    }
                ]
            }
