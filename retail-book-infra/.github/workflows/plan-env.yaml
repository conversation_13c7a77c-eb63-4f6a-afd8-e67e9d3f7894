name: Infra plan - single env

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: The environment to apply

env:
  ARM_USE_OIDC: true
  ARM_TENANT_ID: ${{ vars.ARM_TENANT_ID }}
  ARM_CLIENT_ID: ${{ vars.ARM_CLIENT_ID }}

permissions:
  id-token: write
  contents: read

jobs:
  plan:
    name: Terraform
    runs-on: ubuntu-latest

    environment: ${{ inputs.environment }}

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # - name: Log in to Azure using OIDC
      #   uses: azure/login@v1
      #   with:
      #     client-id: 2ba7c011-458b-430a-ab71-edb745c640d7
      #     tenant-id: 6add0a26-d7dd-4271-8acf-b52c16ad9ea4
      #     subscription-id: 8f06ffc4-1cf1-4aaf-844a-a1567c3e31c7
      - uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.11.2
          terraform_wrapper: false
      - name: Setup Terragrunt
        uses: autero1/action-terragrunt@v1.1.0
        with:
          terragrunt_version: 0.75.0
      - name: Infrastructure - Terragrunt plan
        run: terragrunt plan -input=false --working-dir=layers/infrastructure/${{ inputs.environment }}
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          PROMETHEUS_SENDGRID_API_KEY: ${{ secrets.PROMETHEUS_SENDGRID_API_KEY }}
