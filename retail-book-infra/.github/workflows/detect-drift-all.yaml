name: Drift detection - all envs

on:
  schedule:
    - cron: '0 12 * * 1'  # All Mondays at 12:00

jobs:
  drift-detection-staging:
    name: Drift detection plan
    uses: ./.github/workflows/detect-drift-env.yaml
    with:
      environment: staging
    secrets: inherit
  drift-detection-prod:
    if: ${{ always() }}
    needs: drift-detection-staging # we don't want to run this in parallel
    name: Drift detection prod
    uses: ./.github/workflows/detect-drift-env.yaml
    with:
      environment: prod
    secrets: inherit
  teams-alert-staging:
    if: ${{ needs.drift-detection-staging.outputs.drift == 'true' }}
    needs: drift-detection-staging # we don't want to run this in parallel
    name: Send Teams alert
    uses: ./.github/workflows/teams-alert.yaml
    with:
      environment: staging
    secrets: inherit
  teams-alert-prod:
    if: ${{ needs.drift-detection-prod.outputs.drift == 'true' }}
    needs: drift-detection-prod # we don't want to run this in parallel
    name: Send Teams alert
    uses: ./.github/workflows/teams-alert.yaml
    with:
      environment: prod
    secrets: inherit
