name: Infra plan - all envs (inc. prod)

on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - reopened
      - synchronize
  pull_request_review:
    branches:
      - staging
    types:
      - edited
      - dismissed

jobs:
  # Needed so that pipeline is successful if github.event.pull_request.user.login == 'renovate[bot]'
  noop:
    runs-on: ubuntu-latest
    steps:
      - name: No-op
        run: echo "Pipeline is successful even if there next job is skipped"
  plan-staging:
    # Needed so that it is not triggered on renovate pull request, it would create concurrency for terraform states
    if: github.event.pull_request.user.login != 'renovate[bot]' 
    needs: noop
    name: Staging plan
    uses: ./.github/workflows/plan-env.yaml
    with:
      environment: staging
    secrets: inherit
  plan-prod:
    if: github.event.pull_request.user.login != 'renovate[bot]'
    needs: plan-staging # we don't want to run this in parallel
    name: Prod plan
    uses: ./.github/workflows/plan-env.yaml
    with:
      environment: prod
    secrets: inherit
