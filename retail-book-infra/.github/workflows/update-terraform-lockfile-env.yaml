name: Update Terraform Lockfiles

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: The environment to apply

concurrency:
  group: terraform-lock-${{ inputs.environment }}
  cancel-in-progress: false

env:
  ARM_USE_OIDC: true
  ARM_TENANT_ID: ${{ vars.ARM_TENANT_ID }}
  ARM_CLIENT_ID: ${{ vars.ARM_CLIENT_ID }}

permissions:
  id-token: write
  contents: write
jobs:
  plan:
    name: Update Terraform lockfiles
    runs-on: ubuntu-latest

    environment: ${{ inputs.environment }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Configure Git
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git fetch --all
          git checkout -b ${{ github.event.pull_request.head.ref }} origin/${{ github.event.pull_request.head.ref }}
          git pull
          
      - uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.11.2
          terraform_wrapper: false

      - name: Setup Terragrunt
        uses: autero1/action-terragrunt@v1.1.0
        with:
          terragrunt_version: 0.75.0

      - name: Infrastructure - Terragrunt init upgrade
        run: terragrunt run-all init -upgrade --queue-include-dir "**/**/${{ inputs.environment }}"

      - name: Commit and Push Changes for Terraform Lockfile Changes
        run: |
          git add '**/.terraform.lock.hcl'
          if git diff --cached --quiet; then
            echo "No changes to commit."
            exit 0
          fi
          git commit -m "Update all Terraform lockfiles in ${{ inputs.environment }}"
          git push origin HEAD:${{ github.event.pull_request.head.ref }}
