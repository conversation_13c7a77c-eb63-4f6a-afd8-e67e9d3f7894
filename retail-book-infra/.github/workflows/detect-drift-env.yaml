name: Drift detection - single env

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: The environment to apply
    outputs:
      drift:
        description: Whether there is drift in layers
        value: ${{ jobs.plan.outputs.drift }}

env:
  ARM_USE_OIDC: true
  ARM_TENANT_ID: ${{ vars.ARM_TENANT_ID }}
  ARM_CLIENT_ID: ${{ vars.ARM_CLIENT_ID }}

permissions:
  id-token: write
  contents: read

jobs:
  plan:
    name: Terraform
    runs-on: ubuntu-latest

    environment: ${{ inputs.environment }}

    outputs:
      drift: ${{ steps.plan_group.outputs.result }}

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.11.2
          terraform_wrapper: false
      - name: Setup Terragrunt
        uses: autero1/action-terragrunt@v1.1.0
        with:
          terragrunt_version: 0.75.0
      - name: Pull last commit
        run: |
          git checkout -b ${{ github.event.pull_request.head.ref }} origin/${{ github.event.pull_request.head.ref }}
          git pull
      - name: Infrastructure - Terragrunt plan
        id: plan_group
        run: |
          # Plan all layers for environment
          terragrunt run-all plan --queue-include-dir "**/**/${{ inputs.environment }}" >> plan_output.txt

          # Filter noise and if no changes at all return true to not break the pipeline
          grep -E '(^.*[#~+-] .*|^[[:punct:]]|Plan|Changes)' plan_output.txt >> betterplan.txt || true 

          # Check if there are state changes
          state_changes=$(grep -e "[[:digit:]]* to add, [[:digit:]]* to change, [[:digit:]]* to destroy" betterplan.txt && echo 1 || echo 0)

          # If no state changes, exit 0, else exit 1
          if [[ "$state_changes" == "0" ]]; then
              echo "No changes detected."
              echo "result=false" >> $GITHUB_OUTPUT
          else
              echo "Changes detected!"
              echo "result=true" >> $GITHUB_OUTPUT
          fi
          cat betterplan.txt
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          PROMETHEUS_SENDGRID_API_KEY: ${{ secrets.PROMETHEUS_SENDGRID_API_KEY }}


