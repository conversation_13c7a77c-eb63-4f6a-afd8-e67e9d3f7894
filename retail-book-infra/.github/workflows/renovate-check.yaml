name: Renovate check

on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - reopened
      - synchronize
    paths:
      - 'layers/root.hcl'

env:
  ARM_USE_OIDC: true
  ARM_TENANT_ID: ${{ vars.ARM_TENANT_ID }}
  ARM_CLIENT_ID: ${{ vars.ARM_CLIENT_ID }}

jobs:
  update-lockfiles-staging:
    if: github.event.pull_request.user.login == 'renovate[bot]'
    name: Update Terraform Lockfiles staging
    uses: ./.github/workflows/update-terraform-lockfile-env.yaml
    with:
      environment: staging
    secrets: inherit
  drift-detection-staging:
    needs: update-lockfiles-staging # we don't want to run this in parallel
    name: Staging plan
    uses: ./.github/workflows/detect-drift-env.yaml
    with:
      environment: staging
    secrets: inherit
  update-lockfiles-prod:
    name: Update Terraform Lockfiles prod
    needs: drift-detection-staging # we don't want to run this in parallel
    uses: ./.github/workflows/update-terraform-lockfile-env.yaml
    with:
      environment: prod
    secrets: inherit
  drift-detection-prod:
    needs: update-lockfiles-prod # we don't want to run this in parallel
    name: Prod plan
    uses: ./.github/workflows/detect-drift-env.yaml
    with:
      environment: prod
    secrets: inherit
  drift-detection:
    if: ${{ needs.drift-detection-staging.outputs.drift == 'true' || needs.drift-detection-prod.outputs.drift == 'true' }}
    name: Drift detection
    needs: [drift-detection-staging, drift-detection-prod]
    runs-on: ubuntu-latest
    steps:
      - name: Drift detected
        run: |
          echo "Drift detected"
          exit 1
