inputs = {
  vnet_name          = "main-vnet"
  vnet_address_space = ["********/16"]
  subnets = {
    "public" = {
      cidr = "********/24"
    },
    "private" = {
      cidr = "********/24"
    },
    "kubernetes" = {
      cidr                                          = "********/24"
      private_link_service_network_policies_enabled = false
    },
    "AzureBastionSubnet" = {
      cidr = "*********/26"
    }
  }

  kubernetes_version = "1.31.7"
  #TODO: remove when NATS can restart without breaking everything
  aks_node_os_upgrade_channel = "None"
  #TODO: remove when NATS can restart without breaking everything
  aks_automatic_upgrade_channel = null
  aks_upgrade_override          = false

  location                      = "uksouth"
  environment                   = basename(get_original_terragrunt_dir())

  dns_zone      = "prod.retailbook.com"
  api_subdomain = "api"

  bastion_authorized_principals = {
    // Name of the user or group = Object ID (you can get Object ID from Microsoft Entra ID)
    SG-DevTeam = "2fd8c299-8c62-4477-b903-e024c45eace8"
  }

  frontdoor_enabled = true
  waf_enabled       = true
  waf_mode          = "Detection"


  cleartext_secrets = {
    "grafana-admin-username" : "admin"
  }

  random_secrets = {
    "grafana-admin-password" : null
    "userservice-superuser-password" : null
    "ui-nextauth-secret" : null
  }

  mounted_secrets = {}
}
