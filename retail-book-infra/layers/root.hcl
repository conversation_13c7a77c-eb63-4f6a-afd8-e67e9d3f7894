# ---------------------------------------------------------------------------------------------------------------------
# TERRAGRUNT CONFIGURATION
# Terragrunt is a thin wrapper for Terraform that provides extra tools for working with multiple Terraform modules,
# remote state, and locking: https://github.com/gruntwork-io/terragrunt
# ---------------------------------------------------------------------------------------------------------------------
terragrunt_version_constraint = ">= 0.75.0"
terraform_version_constraint  = ">= 1.11.0"

locals {
  # Automatically load subscription variables
  # These are hard-coded here because they are shared by github actions and the CLI admin tool
  subscription_list = {
    staging = "8f06ffc4-1cf1-4aaf-844a-a1567c3e31c7"
    preprod = "c35bc1ed-2b77-43ef-ab1f-7d94abf50626"
    prod    = "a7c3310e-0770-49d8-b053-8a79fbd5566c"
  }

  environment = basename(get_original_terragrunt_dir())

  subscription_id = local.subscription_list[local.environment]

  # Automatically load region-level variables
  region = "uksouth"

  location = "uksouth"
}

# Generate Azure providers

generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
  terraform {
    required_providers {
      azurerm = {
        source = "hashicorp/azurerm"
        version = "~> 4.23.0"
      }
      azapi = {
        source = "azure/azapi"
        version = "~> 2.3.0"
      }
      random = {
        source = "hashicorp/random"
        version = "~> 3.4.3"
      }

      sendgrid = {
        source = "anna-money/sendgrid"
        version = "~> 1.0.4"
      }
    }
  }
  provider "azurerm" {
      subscription_id = "${local.subscription_id}"
      features {}
      # skip_provider_registration = "true"
  }
  provider "azapi" {
      subscription_id = "${local.subscription_id}"
  }

  provider "sendgrid" {}
  provider "random" {}
EOF
}

remote_state {
  backend = "azurerm"
  config  = {
    subscription_id      = "${local.subscription_id}"
    key                  = "${path_relative_to_include()}/terraform.tfstate"
    resource_group_name  = "retailbook-${local.environment}-tfstate"
    storage_account_name = "retailbook${local.environment}tf"
    container_name       = "tfstate"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

# ---------------------------------------------------------------------------------------------------------------------
# GLOBAL PARAMETERS
# These variables apply to all configurations in this subfolder. These are automatically merged into the child
# `terragrunt.hcl` config via the include block.
# ---------------------------------------------------------------------------------------------------------------------

# Configure root level variables that all resources can inherit. This is especially helpful with multi-account configs
# where terraform_remote_state data sources are placed directly into the modules.
