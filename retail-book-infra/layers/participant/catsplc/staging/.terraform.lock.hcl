# This file is maintained automatically by "terraform init".
# Manual edits may be lost in future updates.

provider "registry.terraform.io/anna-money/sendgrid" {
  version     = "1.0.5"
  constraints = "~> 1.0.4"
  hashes = [
    "h1:SAVlfWOlIBFRrq9pcknEvQFtBNq5YJiX/1SVNTTPkAo=",
    "zh:0d0d4a5e36a08fdc17d8ffed9ba0afe180501e5a157545d38ed47e442b2e120e",
    "zh:111f544695f9869134321c898a9ca939f80293ff58ff137662e93769706cfaa7",
    "zh:1f8def0eb3a722bbd90407e0032e06affc42603733ee47d2b2a3e28f7bd681eb",
    "zh:363487c6c1c5bf60c9107717df9dab35f8cfb18145cd8518d8e8b07f639c22cd",
    "zh:57d310f8048347909b0a5f5f46a19aff92788a2a5aa2310dbd76a285636b7bb5",
    "zh:609ddd979fb19d4719e699702ec9379ca4c5370ef591a8e9f6a37bcd15b2b9a4",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:84170854c1d596b3758de7d0c83f3f8cc89c660048321893362257b9eadd8ef2",
    "zh:864effad9662adb4212c3a9739c4920a308f0dc9e516a6c4a18772054ac0dca2",
    "zh:99ff3e09a5df1777d3e383da83be089d54282ad75df26ce6972627ec0ff1f64f",
    "zh:9ca487d404263ca88b75df4cf9e3404e51b6475425d9c527c545bd1918303617",
    "zh:9cc29f4ea3b230f1786087ccaf6fc54c11659bf58ce9d6c028bf434c251ad6f1",
    "zh:e2bb491f33e203a0b3adeef1303ad6ec87ef96f3ed8507ed1c1c5fd8b45ecd61",
    "zh:eaf3be7942358351855aacf04faaf3a0ae6ba60e57ce61d65ca4988f13d5a257",
    "zh:f23aeeada1253c3055ddf6da2dcf0b91e2c1915336f0a6f9836cb582d30f64f0",
  ]
}

provider "registry.terraform.io/azure/azapi" {
  version     = "2.3.0"
  constraints = "~> 2.3"
  hashes = [
    "h1:oPUOB78DfRam5NynxPimR0MmbvjjRspetXuWJlADjbE=",
    "zh:038dfc9190827e4fe5635d7f81a44bb568e19c37847896ad2af8cc580ac28f77",
    "zh:14b438540f81fc40957885009df0fed93bb9c771a9a8d37dd1d29bd8c5d85545",
    "zh:1bb16bd852f28a1f369e199104330efefdfaccacf55bcae829235f553343f58b",
    "zh:22a1138f522b1fd9b924e7bfbee66063f8d616a0ce6a0a0bdcc77b445350e1b6",
    "zh:488746623bc1be052663a407589e3101c6c2d2d7e9ad56b5d69d4773f77a6f0e",
    "zh:49a207406ee3b211fa89bacec844db58157fdb764bedf216706af05bb9068bd0",
    "zh:55115837d92f0d4dce803c6180fab10f93314134e5e0a8aadcc977348fbc4b9c",
    "zh:9ac58303d128c6c2a7a68984273ec76ceee9d14fca030cd22ae176e7b936495a",
    "zh:aadb9712d3358790d96dead8ea73eb09a7e971be8b81765fbbb3af4219f77d63",
    "zh:e39fe6b9f06819ec9a18d69ddb3c681cdbaadf184d2c8d6739f56f1e47d5947b",
    "zh:e68f46ac504f1641bc419f4cfee5003fc596932598e09fa56a09a3d52f3851e4",
    "zh:f50b37fb8f0b57cc97f99ff48018e2f97a7aa9d72f06cba3e3b2919d61d5f483",
  ]
}

provider "registry.terraform.io/hashicorp/azuread" {
  version = "3.1.0"
  hashes = [
    "h1:QY/V8YuAw2phme+ryKEbZ/9B+Xi7SfXAOVr4uBoRqpk=",
    "zh:01b796cf12e93cc811cb15c8465605e75de170802060f9e2fe114835968960dd",
    "zh:12005fbffb84467ff1d4ce9317370834d1279743bc201d3db95f36315cdf8157",
    "zh:1c3e89cf19118fc07d7b04257251fc9897e722c16e0a0df7b07fcd261f8c12e7",
    "zh:1daf7d4ade44e69593488c1f6571b4fbdaf01ec41538207de1f12609b3830907",
    "zh:386965c0529ed083b94968c25441385378d8643a5748591b221e6d6d3cea4dbc",
    "zh:46ede0628c300c6d584135daa93733400b9ce968d8aebb3f925d904b3fcfa781",
    "zh:7af453bf5217e1818ca5c2126edb8fe573c85f17a0557415a3bc7ae92a8652f5",
    "zh:b6014600409715ca37aa85ddb066698f592b7d104f09c12a68d45c5b00404272",
    "zh:bca84d10cd1e805e6d31a888eb6737a96aee14e1b5b919dee73d2a5a8ff85beb",
    "zh:bd7d6e6c2a086bafdeeb33d5d4f919a8789ef3acf1a0baf2b8ea43996b96c213",
    "zh:e5b7840b1b9d90c3f6be9a59400b7d0580376415a79aa740eba7f97bf35c25ef",
    "zh:e94e114b205de36d60bc17a3758f9c4bfc6b01e63be81ae1d9699f9bf9650362",
  ]
}

provider "registry.terraform.io/hashicorp/azurerm" {
  version     = "4.23.0"
  constraints = "~> 4.0"
  hashes = [
    "h1:fHnHlU3IjCzpmsZRIT1Wrpjau/V27e1Z1xv3IdfOLVU=",
    "zh:08d950618a2bf14445171a0eae3721b69be731d6732fad72e902e56e49d4d3b0",
    "zh:1a9c84376a30cc890830d433ec21057417c012135b40af672dc915010160595a",
    "zh:2db0422d03840b078a03a33599bbd1f6ea9569bf38ef2a2b99e6bcfc65e14400",
    "zh:35884b51dcf73acc5fcc0ea22414226e4c3709798fac8948239d8601bfd23592",
    "zh:5d84f4f588b478bbb7f4f3a943d722c9c14177b8003347b2d29978271bc19632",
    "zh:5e232ba768cf8d7eb0adcf599f93cd89253f9c2af8e22160451ba6e0c7799101",
    "zh:80807af8909c00df853c13b9cf363ba31ce013733ef353ac206f81033a1747fa",
    "zh:8ced180e4377e5cd8a5138ec7eb64493ace692f5e5aa8bb5681154f431232f8a",
    "zh:bd5b5cbbd0b38b91217251c1d977410bc930f11063e4e97c798c834dc00ef369",
    "zh:c021b1ecf0a3c02bbb56d8e8ac14bdde04e9c1e5b2346563b53450aea468847e",
    "zh:f569b65999264a9416862bca5cd2a6177d94ccb0424f3a4ef424428912b9cb3c",
    "zh:fc036967ce8f8322c91e93b20af0b68512df177018e975d742a91445d6b94b37",
  ]
}

provider "registry.terraform.io/hashicorp/random" {
  version     = "3.4.3"
  constraints = "~> 3.4.3"
  hashes = [
    "h1:4kzIdBbx+4u2d8YSOu5Osgul16gtnUeZqWrJA+GbNeY=",
    "h1:hXUPrH8igYBhatzatkp80RCeeUJGu9lQFDyKemOlsTo=",
    "h1:saZR+mhthL0OZl4SyHXZraxyaBNVMxiZzks78nWcZ2o=",
    "h1:tL3katm68lX+4lAncjQA9AXL4GR/VM+RPwqYf4D2X8Q=",
    "h1:xZGZf18JjMS06pFa4NErzANI98qi59SEcBsOcS2P2yQ=",
    "zh:41c53ba47085d8261590990f8633c8906696fa0a3c4b384ff6a7ecbf84339752",
    "zh:59d98081c4475f2ad77d881c4412c5129c56214892f490adf11c7e7a5a47de9b",
    "zh:686ad1ee40b812b9e016317e7f34c0d63ef837e084dea4a1f578f64a6314ad53",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:84103eae7251384c0d995f5a257c72b0096605048f757b749b7b62107a5dccb3",
    "zh:8ee974b110adb78c7cd18aae82b2729e5124d8f115d484215fd5199451053de5",
    "zh:9dd4561e3c847e45de603f17fa0c01ae14cae8c4b7b4e6423c9ef3904b308dda",
    "zh:bb07bb3c2c0296beba0beec629ebc6474c70732387477a65966483b5efabdbc6",
    "zh:e891339e96c9e5a888727b45b2e1bb3fcbdfe0fd7c5b4396e4695459b38c8cb1",
    "zh:ea4739860c24dfeaac6c100b2a2e357106a89d18751f7693f3c31ecf6a996f8d",
    "zh:f0c76ac303fd0ab59146c39bc121c5d7d86f878e9a69294e29444d4c653786f8",
    "zh:f143a9a5af42b38fed328a161279906759ff39ac428ebcfe55606e05e1518b93",
  ]
}
