locals {
  environment = basename(get_original_terragrunt_dir())
}

inputs = {
  location    = "uksouth"
  environment = local.environment

  storage_account_id = dependency.infrastructure.outputs.participant_storage_account_id

  external_secrets_user_assigned_identity = dependency.infrastructure.outputs.external_secrets_user_assigned_identity

  cleartext_secrets = {
    "couchdb-admin-username" : "admin"
    "couchdb-application-username" : "retailbook"

    "postgres-username": "postgres"
    "postgres-writer-username": "writer"
    "postgres-reader-username": "reader"
    "postgres-auditor-username": "auditor"
  }

  random_secrets = {
    "postgres-password": null
    "postgres-writer-password": null
    "postgres-reader-password": null
    "postgres-auditor-password": null
  }

  mounted_secrets = {
    "sendgrid-api-key": null
  }

  participant_manager = "SG-DevTeam"

  terraform_cicd_object_id = dependency.infrastructure.outputs.terraform_cicd_object_id

  aks_cluster_oidc_url = dependency.infrastructure.outputs.aks_cluster_oidc_url

  global_resource_group_name = dependency.infrastructure.outputs.global_resource_group_name

  backup_resource_group_name = dependency.infrastructure.outputs.backup_resource_group_name

  db_backup_user_assigned_identity = dependency.infrastructure.outputs.db_backup_user_assigned_identity

  backup_storage_account_id = dependency.infrastructure.outputs.backup_storage_account_id
}
