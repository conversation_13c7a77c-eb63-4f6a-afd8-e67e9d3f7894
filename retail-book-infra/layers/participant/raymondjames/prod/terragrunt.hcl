include "root" {
  path           = find_in_parent_folders("root.hcl")
  merge_strategy = "deep"
}

include "module" {
  path           = find_in_parent_folders("module.hcl")
  merge_strategy = "deep"
}

include "inputs" {
  path           = "inputs.hcl"
  merge_strategy = "deep"
}

include "inputs" {
  path           = find_in_parent_folders("inputs.hcl")
  merge_strategy = "deep"
}

dependency "infrastructure" {
  config_path = "../../../infrastructure/${basename(get_original_terragrunt_dir())}"
}
