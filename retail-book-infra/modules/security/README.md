<!-- BEGIN_TF_DOCS -->

## Requirements

No requirements.

## Providers

| Name                                                         | Version |
| ------------------------------------------------------------ | ------- |
| <a name="provider_azurerm"></a> [azurerm](#provider_azurerm) | n/a     |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                                     | Type     |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| [azurerm_security_center_subscription_pricing.containers_registry](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_subscription_pricing) | resource |
| [azurerm_security_center_subscription_pricing.keyvaults](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_subscription_pricing)           | resource |
| [azurerm_security_center_subscription_pricing.kubernetes](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_subscription_pricing)          | resource |
| [azurerm_security_center_subscription_pricing.storage_accounts](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_subscription_pricing)    | resource |
| [azurerm_security_center_subscription_pricing.virtual_machines](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_subscription_pricing)    | resource |
| [azurerm_subscription_policy_assignment.asb_assignment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/subscription_policy_assignment)                  | resource |

## Inputs

| Name                                                               | Description                                              | Type     | Default | Required |
| ------------------------------------------------------------------ | -------------------------------------------------------- | -------- | ------- | :------: |
| <a name="input_environment"></a> [environment](#input_environment) | The environment name.                                    | `string` | n/a     |   yes    |
| <a name="input_location"></a> [location](#input_location)          | The location/region where the resources will be created. | `string` | n/a     |   yes    |
| <a name="input_tier"></a> [tier](#input_tier)                      | The tier of the environment.                             | `string` | n/a     |   yes    |

## Outputs

No outputs.

<!-- END_TF_DOCS -->
