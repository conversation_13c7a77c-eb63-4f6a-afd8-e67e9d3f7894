data "azurerm_subscription" "current" {}

// Enables Azure Security Benchmark policy
resource "azurerm_subscription_policy_assignment" "asb_assignment" {
  name                 = "azuresecuritybenchmark"
  display_name         = "Azure Security Benchmark"
  policy_definition_id = "/providers/Microsoft.Authorization/policySetDefinitions/1f3afdf9-d0c9-4c3d-847f-89da613e70a8"
  subscription_id      = data.azurerm_subscription.current.id
}

// Enables Azure Security Center for Kubernetes
resource "azurerm_security_center_subscription_pricing" "kubernetes" {
  tier          = var.tier
  resource_type = "KubernetesService"
}


// Enables Azure Security Center for Virtual Machines
resource "azurerm_security_center_subscription_pricing" "virtual_machines" {
  tier          = var.tier
  resource_type = "VirtualMachines"
}

// Enables Azure Security Center for Storage Accounts
resource "azurerm_security_center_subscription_pricing" "storage_accounts" {
  tier          = var.tier
  resource_type = "StorageAccounts"
}

// Enables Azure Security Center for ContainersRegistry
resource "azurerm_security_center_subscription_pricing" "containers_registry" {
  tier          = var.tier
  resource_type = "ContainerRegistry"
}

// Enables Azure Security Center for Keyvaults
resource "azurerm_security_center_subscription_pricing" "keyvaults" {
  tier          = var.tier
  resource_type = "KeyVaults"
}