<!-- BEGIN_TF_DOCS -->

## Requirements

No requirements.

## Providers

| Name                                                         | Version |
| ------------------------------------------------------------ | ------- |
| <a name="provider_azurerm"></a> [azurerm](#provider_azurerm) | n/a     |

## Modules

No modules.

## Resources

| Name                                                                                                                                   | Type     |
| -------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| [azurerm_resource_group.backup](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/resource_group)        | resource |
| [azurerm_resource_group.tfstate](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/resource_group)       | resource |
| [azurerm_storage_account.tfstate](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/storage_account)     | resource |
| [azurerm_storage_account.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/storage_account)        | resource |
| [azurerm_storage_container.tfstate](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/storage_container) | resource |

## Inputs

| Name                                                               | Description                                              | Type     | Default | Required |
| ------------------------------------------------------------------ | -------------------------------------------------------- | -------- | ------- | :------: |
| <a name="input_environment"></a> [environment](#input_environment) | The environment name.                                    | `string` | n/a     |   yes    |
| <a name="input_location"></a> [location](#input_location)          | The location/region where the resources will be created. | `string` | n/a     |   yes    |

## Outputs

No outputs.

<!-- END_TF_DOCS -->
