resource "azurerm_resource_group" "tfstate" {
  name     = "retailbook-${var.environment}-tfstate"
  location = var.location
}

resource "azurerm_storage_account" "tfstate" {
  name                             = "retailbook${var.environment}tf"
  resource_group_name              = azurerm_resource_group.tfstate.name
  location                         = azurerm_resource_group.tfstate.location
  account_tier                     = "Standard"
  account_replication_type         = "LRS"
  cross_tenant_replication_enabled = true
}

resource "azurerm_storage_container" "tfstate" {
  name                  = "tfstate"
  storage_account_id    = azurerm_storage_account.tfstate.id
  container_access_type = "private"
}
