resource "azurerm_storage_container" "backup" {
  name                  = "db-backup-${var.participant.id}"
  storage_account_id    = var.backup_storage_account_id
  container_access_type = "private"
}

resource "azurerm_user_assigned_identity" "participant_db_backup" {
  resource_group_name = var.backup_resource_group_name
  location            = azurerm_resource_group.this.location
  name                = "db-backup-identity-${var.participant.id}"
}

resource "azurerm_role_assignment" "participant_db_backup" {
  scope                = var.backup_storage_account_id
  role_definition_name = "Owner" # TODO: Update with least privilege, e.g. "Storage Blob Data Contributor" afterb building backup script
  principal_id         = azurerm_user_assigned_identity.participant_db_backup.principal_id
}

resource "azurerm_federated_identity_credential" "participant_db_backup" {
  name                = "dedicated-db-backup-${var.participant.id}"
  resource_group_name = var.backup_resource_group_name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.aks_cluster_oidc_url
  parent_id           = azurerm_user_assigned_identity.participant_db_backup.id
  subject             = "system:serviceaccount:participant-${var.participant.id}:db-backup"
}
