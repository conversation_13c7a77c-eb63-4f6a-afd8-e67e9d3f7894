variable "environment" {
  type        = string
  description = "The environment name."
}

variable "location" {
  type        = string
  description = "The location/region where the resources will be created."
}

variable "participant" {
  description = "Participant attributes"
  type        = object({
    id           = string
    display_name = string
  })
}

variable "storage_account_id" {
  description = "Participant storage account ID"
  type        = string
}

variable "external_secrets_user_assigned_identity" {
  description = "User Assigned Identity assigned to external secrets"
  type        = object({
    principal_id = string
    id = string
  })
}

variable "db_backup_user_assigned_identity" {
  description = "User Assigned Identity used for database backups"
  type        = object({
    id = string
  })
}

variable "aks_cluster_oidc_url" {
  description = "AKS Cluster OIDC issuer URL for current environment"
  type        = string
}

variable "global_resource_group_name" {
  description = "The name of the resource group where the global resources are created."
  type        = string
}

variable "backup_resource_group_name" {
  description = "The name of the resource group for backup resources."
  type        = string
}

variable "backup_storage_account_id" {
  description = "The ID of the storage account for backup resources."
  type        = string
}


variable "cleartext_secrets" {
  description = "Secrets created on keyvault that can be in cleartext (like usernames)"
  type        = map(string)
}

variable "random_secrets" {
  description = "Secrets created on keyvault with random values. (Only the key is used, for the secret name)"
  type        = map(string)
}

variable "mounted_secrets" {
  description = "Secrets created on keyvault that have been mounted by the environment + infrastructure (i.e. results of terragrunt apply)"
  type        = map(string)
}

variable "participant_manager" {
  description = "Dev group that manages the keyvault"
  type        = string
}

variable "terraform_cicd_object_id" {
  description = "Object ID of Terraform CI/CD service principal"
  type        = string
}
