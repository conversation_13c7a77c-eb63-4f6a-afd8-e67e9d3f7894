# This assigned identity has rights assigned by the user service
resource "azurerm_user_assigned_identity" "api_container_identity" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "${var.participant.id}_api_identity"
}

resource "azurerm_federated_identity_credential" "api_container_identity" {
  name                = "api-identity-credential-${var.participant.id}"
  resource_group_name = azurerm_resource_group.this.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.aks_cluster_oidc_url
  parent_id           = azurerm_user_assigned_identity.api_container_identity.id
  subject             = "system:serviceaccount:participant-${var.participant.id}:${var.participant.id}-apigateway"
}