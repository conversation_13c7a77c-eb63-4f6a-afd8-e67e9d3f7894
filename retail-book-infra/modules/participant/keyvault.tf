resource "azurerm_key_vault" "this" {
  name                        = "${var.environment}-${var.participant.id}"
  location                    = azurerm_resource_group.this.location
  resource_group_name         = azurerm_resource_group.this.name
  enabled_for_disk_encryption = true
  tenant_id                   = data.azurerm_client_config.current.tenant_id
  soft_delete_retention_days  = 7
  purge_protection_enabled    = true
  enable_rbac_authorization   = true

  sku_name = "standard"
}

resource "azurerm_key_vault_secret" "cleartext_secrets" {
  for_each     = var.cleartext_secrets
  name         = each.key
  value        = each.value
  key_vault_id = azurerm_key_vault.this.id
  content_type = "Managed by Terraform"

  depends_on = [
    # azurerm_role_assignment.this,
    azurerm_role_assignment.participant_manager
  ]
}

resource "random_password" "this" {
  for_each = var.random_secrets
  length   = 20
  special  = false
}

resource "azurerm_key_vault_secret" "random_secrets" {
  for_each     = var.random_secrets
  name         = each.key
  value        = random_password.this[each.key].result
  key_vault_id = azurerm_key_vault.this.id
  content_type = "Managed by Terraform"

  depends_on = [
    #    azurerm_role_assignment.this,
    azurerm_role_assignment.participant_manager
  ]
}

// add a role assigment that allows the current user to create and manage keys in the key vault
resource "azurerm_role_assignment" "this" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Administrator"
  principal_id         = data.azuread_group.padok.object_id
}

// add a role assigment that allows the current user to create and manage keys in the key vault
resource "azurerm_role_assignment" "participant_manager" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Administrator"
  principal_id         = data.azuread_group.participant_manager.object_id
}

// add a role assigment that allows CI/CD to plan the layer
resource "azurerm_role_assignment" "cicd" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Administrator"
  principal_id         = var.terraform_cicd_object_id
}

# Dedicated user assigned identity for external-secrets
resource "azurerm_user_assigned_identity" "participant_external_secrets" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location
  name                = "aks-external-secrets-${var.participant.id}"
}

resource "azurerm_role_assignment" "participant_external_secrets" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Secrets User"
  principal_id         = azurerm_user_assigned_identity.participant_external_secrets.principal_id
}

resource "azurerm_federated_identity_credential" "participant_external_secrets" {
  name                = "dedicated-external-secrets-${var.participant.id}"
  resource_group_name = azurerm_resource_group.this.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.aks_cluster_oidc_url
  parent_id           = azurerm_user_assigned_identity.participant_external_secrets.id
  subject             = "system:serviceaccount:participant-${var.participant.id}:dedicated-external-secrets-identity"
}
