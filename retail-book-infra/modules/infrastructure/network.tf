resource "azurerm_virtual_network" "this" {
  name = var.vnet_name

  location            = azurerm_resource_group.this.location
  resource_group_name = azurerm_resource_group.this.name

  address_space = var.vnet_address_space
  dns_servers   = var.dns_servers

  tags = var.tags
}

resource "azurerm_subnet" "these" {
  for_each = var.subnets

  name = each.key

  resource_group_name = azurerm_resource_group.this.name

  virtual_network_name = azurerm_virtual_network.this.name
  address_prefixes     = [each.value.cidr]

  service_endpoints = lookup(var.subnets_service_endpoints, each.key, [])

  private_endpoint_network_policies = each.value.private_endpoint_network_policies
  private_link_service_network_policies_enabled = each.value.private_link_service_network_policies_enabled
  
  dynamic "delegation" {
    for_each = contains([for k, v in var.subnets_delegations : k], each.key) ? [1] : []

    content {
      name = var.subnets_delegations[each.key].name
      service_delegation {
        name    = var.subnets_delegations[each.key].service_delegation.name
        actions = var.subnets_delegations[each.key].service_delegation.actions
      }
    }
  }


}
