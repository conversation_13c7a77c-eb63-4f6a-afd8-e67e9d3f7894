locals {
  owners_with_terraform_ci = concat(local.owners, [azuread_service_principal.terraform_cicd.object_id])
}

resource "azuread_application" "application_cicd" {
  display_name = "Application CI/CD ${var.environment}"
  owners       = local.owners_with_terraform_ci
}

resource "azuread_service_principal" "application_cicd" {
  client_id = azuread_application.application_cicd.client_id
  owners         = local.owners_with_terraform_ci
}

resource "azurerm_role_assignment" "application_cicd" {
  scope                = azurerm_container_registry.this.id
  role_definition_name = "AcrPush"
  principal_id         = azuread_service_principal.application_cicd.object_id
  // If new SP there may be replication lag this disables validation
  skip_service_principal_aad_check = true
}

resource "azuread_application_federated_identity_credential" "application_cicd" {
  application_id        = azuread_application.application_cicd.id
  display_name          = "github-retailbook"
  audiences             = ["api://AzureADTokenExchange"]
  issuer                = "https://token.actions.githubusercontent.com"
  subject               = "repo:RETAIL-BOOK-LIMITED/retail-book:environment:${var.environment}"
}

resource "azuread_application_federated_identity_credential" "application_ui_cicd" {
  application_id        = azuread_application.application_cicd.id
  display_name          = "github-retailbook-ui"
  audiences             = ["api://AzureADTokenExchange"]
  issuer                = "https://token.actions.githubusercontent.com"
  subject               = "repo:RETAIL-BOOK-LIMITED/retail-book-ui:environment:${var.environment}"
}
