#######################################################
# Setup Bastion Networking
#######################################################

resource "azurerm_public_ip" "bastion" {
  name                = "retailbook-bastion-${var.environment}"
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location
  allocation_method   = "Static"
  sku                 = "Standard"
}

#######################################################
# Setup Bastion
#######################################################

resource "azurerm_bastion_host" "this" {
  name                = "retailbook-bastion-${var.environment}"
  location            = azurerm_resource_group.this.location
  resource_group_name = azurerm_resource_group.this.name
  sku                 = "Standard"
  tunneling_enabled   = true

  ip_configuration {
    name                 = "configuration"
    subnet_id            = azurerm_subnet.these["AzureBastionSubnet"].id
    public_ip_address_id = azurerm_public_ip.bastion.id
  }
}

#######################################################
# Setup an SSH key pair for the VM 
# Required, as at least one key pair is needed for VM creation
#######################################################


resource "azurerm_key_vault_key" "bastion" {
  name         = "retailbook-vm-bastion-${var.environment}"

  key_vault_id = azurerm_key_vault.this.id
  key_type     = "RSA"
  key_size     = 2048

  key_opts = [
    "decrypt",
    "encrypt",
    "sign",
    "unwrapKey",
    "verify",
    "wrapKey"
  ]

  depends_on = [
    azurerm_role_assignment.keyvault_padok,
    azurerm_role_assignment.keyvault_cicd
  ]
}

resource "azurerm_ssh_public_key" "bastion" {
  name                = "retailbook-vm-bastion-${var.environment}"
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location
  public_key          = azurerm_key_vault_key.bastion.public_key_openssh
}

#######################################################
# Setup VM Bastion
#######################################################

resource "azurerm_network_interface" "vm_bastion" {
  name                = "retailbook-vm-bastion-${var.environment}"
  location            = azurerm_resource_group.this.location
  resource_group_name = azurerm_resource_group.this.name
  ip_configuration {
    name                          = "internal"
    subnet_id                     = azurerm_subnet.these["private"].id
    private_ip_address_allocation = "Dynamic"
  }
}

resource "azurerm_linux_virtual_machine" "bastion" {
  name                            = "retailbook-${var.environment}"
  resource_group_name             = azurerm_resource_group.this.name
  location                        = azurerm_resource_group.this.location
  size                            = "Standard_B1ms"
  admin_username                  = "retailbook"

  network_interface_ids = [
    azurerm_network_interface.vm_bastion.id,
  ]

  identity {
    type = "SystemAssigned"
  }

  admin_ssh_key {
    username   = "retailbook"
    public_key = azurerm_ssh_public_key.bastion.public_key
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
  }

  source_image_reference {
    publisher = "canonical"
    offer     = "ubuntu-24_04-lts"
    sku       = "server"
    version   = "latest"
  }
}

resource "azurerm_virtual_machine_extension" "aad_login" {
  name                       = "AADSSHLogin"
  virtual_machine_id         = azurerm_linux_virtual_machine.bastion.id
  publisher                  = "Microsoft.Azure.ActiveDirectory"
  type                       = "AADSSHLoginForLinux" # For Windows VMs: AADLoginForWindows
  type_handler_version       = "1.0"                 # There may be a more recent version
  auto_upgrade_minor_version = true
}
# https://learn.microsoft.com/en-us/rest/api/defenderforcloud/jit-network-access-policies/create-or-update?view=rest-defenderforcloud-2020-01-01&tabs=HTTP
# Workaround with azapi : https://github.com/hashicorp/terraform-provider-azurerm/issues/3661

resource "azurerm_role_assignment" "bastion" {
  for_each             = var.bastion_authorized_principals
  principal_id         = each.value
  scope                = azurerm_linux_virtual_machine.bastion.id
  role_definition_name = "Virtual Machine Administrator Login"
}
