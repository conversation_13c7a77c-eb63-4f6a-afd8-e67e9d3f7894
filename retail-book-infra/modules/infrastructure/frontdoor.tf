module "frontdoor" {
  source                  = "./modules/frontdoor"
  aks_resource_group_name = azurerm_kubernetes_cluster.this.node_resource_group
  dns_zone                = azurerm_dns_zone.this
  resource_group          = azurerm_resource_group.this
  environment             = var.environment
  count                   = var.frontdoor_enabled ? 1 : 0
  waf_enabled             = var.waf_enabled
  waf_mode                = var.waf_mode
  waf_managed_rules       = var.waf_managed_rules
  waf_custom_rules        = var.waf_custom_rules
  waf_rate_limit          = var.waf_rate_limit
}
