output "vnet" {
  description = "A virtual network resource instance."
  value       = azurerm_virtual_network.this
}

output "subnets" {
  description = "A map of subnets."
  value       = azurerm_subnet.these
}

output "participant_storage_account_id" {
  description = "The participant storage account"
  value       = azurerm_storage_account.documents.id
}

output "terraform_cicd_object_id" {
  description = "Object ID of Terraform CI/CD service principal"
  value       = azuread_service_principal.terraform_cicd.object_id
}

output "external_secrets_user_assigned_identity" {
  description = "User Assigned Identity assigned to external secrets"
  value       = azurerm_user_assigned_identity.aks_external_secrets
}

output "db_backup_user_assigned_identity" {
  description = "User Assigned Identity used for database backups"
  value       = azurerm_user_assigned_identity.db_backup
}

output "aks_cluster_oidc_url" {
  description = "AKS Cluster for current environment"
  value       = azurerm_kubernetes_cluster.this.oidc_issuer_url
}

output "global_resource_group_name" {
  description = "The name of the resource group where the global resources are created."
  value       = azurerm_resource_group.this.name
}

output "backup_resource_group_name" {
  description = "The name of the resource group for backup resources."
  value       = azurerm_resource_group.backup.name
}

output "backup_storage_account_id" {
  description = "The ID of storage account used for backup resources."
  value       = azurerm_storage_account.backup.id
}
