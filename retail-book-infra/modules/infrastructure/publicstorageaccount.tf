//Storage account
resource "azurerm_storage_account" "publicdocuments" {
  name                             = "rb${var.environment}publicdocuments"
  location                         = azurerm_resource_group.this.location
  resource_group_name              = azurerm_resource_group.this.name
  account_tier                     = "Standard"
  account_replication_type         = "LRS"
  public_network_access_enabled    = true
  allow_nested_items_to_be_public  = true
  https_traffic_only_enabled       = false
  cross_tenant_replication_enabled = true

  identity {
    type = "SystemAssigned"
  }

  # Current drift on customer_managed_key
  # Linked issue : https://github.com/hashicorp/terraform-provider-azurerm/issues/19427
  lifecycle {
    ignore_changes = [
      customer_managed_key
    ]
  }
}

resource "azurerm_storage_container" "publicdocumentcontainer" {
  name                  = "content"
  storage_account_id  = azurerm_storage_account.publicdocuments.id
  container_access_type = "blob"
}

//Add the files to it
resource "azurerm_storage_blob" "cookiesnotice" {
  name                   = "rb-cookies-notice.pdf"
  storage_account_name   = azurerm_storage_account.publicdocuments.name
  storage_container_name = azurerm_storage_container.publicdocumentcontainer.name
  type                   = "Block"
  source                 = "assets/publicdocs/rb-cookies-notice.pdf"
  content_type           = "application/pdf"
}

resource "azurerm_storage_blob" "privacynotice" {
  name                   = "rb-external-privacy-notice.pdf"
  storage_account_name   = azurerm_storage_account.publicdocuments.name
  storage_container_name = azurerm_storage_container.publicdocumentcontainer.name
  type                   = "Block"
  source                 = "assets/publicdocs/rb-external-privacy-notice.pdf"
  content_type           = "application/pdf"
}

resource "azurerm_storage_blob" "termsofuse" {
  name                   = "rb-terms-of-use.pdf"
  storage_account_name   = azurerm_storage_account.publicdocuments.name
  storage_container_name = azurerm_storage_container.publicdocumentcontainer.name
  type                   = "Block"
  source                 = "assets/publicdocs/rb-terms-of-use.pdf"
  content_type           = "application/pdf"
}

//Set up a front door to it so we can use trusted HTTPS
resource "azurerm_cdn_frontdoor_endpoint" "this" {
  name                     = "publicdocs-${var.environment}"
  cdn_frontdoor_profile_id = module.frontdoor[0].frontdoor_profile_id
}

resource "azurerm_cdn_frontdoor_origin_group" "this" {
  name                     = "publicdocs-${var.environment}-origin-group"
  cdn_frontdoor_profile_id = module.frontdoor[0].frontdoor_profile_id

  load_balancing {}
}

resource "azurerm_cdn_frontdoor_origin" "this" {
  name                          = "publicdocs-${var.environment}-origin"
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.this.id
  enabled                       = true

  certificate_name_check_enabled = true
  host_name                      = azurerm_storage_account.publicdocuments.primary_blob_host
  origin_host_header             = azurerm_storage_account.publicdocuments.primary_blob_host
  priority                       = 1
  weight                         = 500
}

resource "azurerm_cdn_frontdoor_custom_domain" "this" {
  name                     = "publicdocs-${var.environment}-custom-domain"
  cdn_frontdoor_profile_id = module.frontdoor[0].frontdoor_profile_id
  dns_zone_id              = azurerm_dns_zone.this.id
  host_name                = "documents.${var.environment}.retailbook.com"

  tls {
    certificate_type    = "ManagedCertificate"
    minimum_tls_version = "TLS12"
  }
}

resource "azurerm_cdn_frontdoor_route" "this" {
  name                          = "default-route"
  cdn_frontdoor_endpoint_id     = azurerm_cdn_frontdoor_endpoint.this.id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.this.id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.this.id]
  //cdn_frontdoor_rule_set_ids    = [azurerm_cdn_frontdoor_rule_set.cors.id]
  enabled = true

  forwarding_protocol    = "MatchRequest"
  https_redirect_enabled = true
  patterns_to_match      = ["/*"]
  supported_protocols    = ["Http", "Https"]

  cdn_frontdoor_custom_domain_ids = [azurerm_cdn_frontdoor_custom_domain.this.id]
  link_to_default_domain          = false
}

resource "azurerm_cdn_frontdoor_custom_domain_association" "this" {
  cdn_frontdoor_custom_domain_id = azurerm_cdn_frontdoor_custom_domain.this.id
  cdn_frontdoor_route_ids        = [azurerm_cdn_frontdoor_route.this.id]
}

resource "azurerm_dns_cname_record" "this" {
  name                = "documents"
  zone_name           = azurerm_dns_zone.this.name
  resource_group_name = azurerm_resource_group.this.name
  ttl                 = 3600
  record              = azurerm_cdn_frontdoor_endpoint.this.host_name
}

resource "azurerm_dns_txt_record" "this" {
  name                = "_dnsauth.documents"
  zone_name           = var.dns_zone
  resource_group_name = azurerm_resource_group.this.name
  ttl                 = 3600

  record {
    value = azurerm_cdn_frontdoor_custom_domain.this.validation_token
  }
}
