variable "aks_resource_group_name" {
  type        = string
  description = "The group name where AKS creates its resources"
}

variable "api_subdomain" {
  type        = string
  description = "Subdomain used for API"
  default     = "api"
}

variable "front_subdomain" {
  type        = string
  description = "Subdomain used for the front"
  default     = "ui"
}

variable "dns_zone" {
  type = object({
    id   = string
    name = string
  })

  description = "DNS zone used"
}

variable "resource_group" {
  type = object({
    name     = string
    location = string
  })
}

variable "environment" {
  description = "The environment name."
  type        = string
}

variable "private_link_service_name" {
  description = "The private link service name Azure will gather from Kubernetes"
  type        = string
  default     = "kong-proxy"
}

variable "waf_enabled" {
  description = "Is the WAF and its rules enabled ?"
  type        = bool
}

variable "waf_mode" {
  description = "The type of WAF behaviour"
  type        = string
  default     = "Detection"

  validation {
    condition     = contains(["Detection", "Prevention"], var.waf_mode)
    error_message = "waf_mode must be either Detection or Prevention"
  }
}

variable "waf_rate_limit" {
  description = "Configs about rate limiting"
  type = object({
    enabled                  = bool
    max_requests_per_minutes = number
  })
}

variable "waf_managed_rules" {
  description = "Sets of WAF managed rules"
  type = list(object({
    type    = string
    version = string
    action  = string
  }))
}

variable "waf_custom_rules" {
  description = "Sets of WAF custom rules"
  type = list(object({
    name     = string
    action   = string
    type     = string
    priority = number
    match_conditions = list(object({
      match_variable = string
      match_values   = list(string)
      operator       = string
      transforms     = list(string)
    }))
  }))
}
