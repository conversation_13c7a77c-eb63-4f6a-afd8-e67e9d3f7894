resource "azurerm_cdn_frontdoor_rule_set" "cors" {
  name                     = "CORS"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.this.id
}

resource "azurerm_cdn_frontdoor_rule" "cors" {
  depends_on = [azurerm_cdn_frontdoor_origin_group.this, azurerm_cdn_frontdoor_origin.this]

  name                      = "CORS"
  cdn_frontdoor_rule_set_id = azurerm_cdn_frontdoor_rule_set.cors.id
  order                     = 1
  behavior_on_match         = "Continue"

  actions {
    response_header_action {
      header_action = "Overwrite"
      header_name   = "Access-Control-Allow-Origin"
      # value         = "https://${var.front_subdomain}.${var.dns_zone.name}" TODO: This is a temporary measure - change this before we go live!
      value         = "*"
    }

    response_header_action {
      header_action = "Overwrite"
      header_name   = "Access-Control-Allow-Methods"
      value         = "*"
    }

    response_header_action {
      header_action = "Overwrite"
      header_name   = "Access-Control-Allow-Headers"
      value         = "*"
    }
  }
}
