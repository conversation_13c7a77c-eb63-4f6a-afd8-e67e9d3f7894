data "azurerm_private_link_service" "kong_proxy" {
  name                = var.private_link_service_name
  resource_group_name = var.aks_resource_group_name
}

locals {
  hostname = "${var.api_subdomain}.${var.dns_zone.name}"
}

resource "azurerm_cdn_frontdoor_profile" "this" {
  name                = "retailbook-${var.environment}"
  resource_group_name = var.resource_group.name
  sku_name            = "Premium_AzureFrontDoor" # We use premium because we use private links to connect to the AKS cluster
  depends_on = [
    data.azurerm_private_link_service.kong_proxy
  ]
}

output "frontdoor_profile_id" {
  value = azurerm_cdn_frontdoor_profile.this.id
  depends_on = [
    azurerm_cdn_frontdoor_profile.this
  ]
}

resource "azurerm_cdn_frontdoor_endpoint" "this" {
  name                     = "retailbook-${var.environment}"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.this.id
}

resource "azurerm_cdn_frontdoor_custom_domain" "this" {
  name                     = "retailbook-${var.environment}"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.this.id
  dns_zone_id              = var.dns_zone.id
  host_name                = local.hostname

  tls {
    certificate_type    = "ManagedCertificate"
    minimum_tls_version = "TLS12"
  }
}

resource "azurerm_dns_txt_record" "this" {
  name                = "_dnsauth.${var.api_subdomain}"
  zone_name           = var.dns_zone.name
  resource_group_name = var.resource_group.name
  ttl                 = 3600

  record {
    value = azurerm_cdn_frontdoor_custom_domain.this.validation_token
  }
}

resource "azurerm_dns_cname_record" "this" {
  name                = var.api_subdomain
  zone_name           = var.dns_zone.name
  resource_group_name = var.resource_group.name
  ttl                 = 3600
  record              = azurerm_cdn_frontdoor_endpoint.this.host_name
}

resource "azurerm_cdn_frontdoor_origin_group" "this" {
  name                     = "default-origin-group"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.this.id

  load_balancing {}
}

resource "azurerm_cdn_frontdoor_origin" "this" {
  name                          = "retailbook-${var.environment}"
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.this.id
  enabled                       = true

  certificate_name_check_enabled = true
  host_name                      = local.hostname
  origin_host_header             = local.hostname
  priority                       = 1
  weight                         = 500

  private_link {
    request_message        = "Request access for CDN Frontdoor Private Link Origin Load Balancer Example"
    location               = var.resource_group.location
    private_link_target_id = data.azurerm_private_link_service.kong_proxy.id
  }
}

resource "azurerm_cdn_frontdoor_route" "this" {
  name                          = "default-route"
  cdn_frontdoor_endpoint_id     = azurerm_cdn_frontdoor_endpoint.this.id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.this.id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.this.id]
  cdn_frontdoor_rule_set_ids    = [azurerm_cdn_frontdoor_rule_set.cors.id]
  enabled                       = true

  forwarding_protocol    = "HttpOnly"
  https_redirect_enabled = true
  patterns_to_match      = ["/*"]
  supported_protocols    = ["Http", "Https"]

  cdn_frontdoor_custom_domain_ids = [azurerm_cdn_frontdoor_custom_domain.this.id]
  link_to_default_domain          = false
}

resource "azurerm_cdn_frontdoor_custom_domain_association" "this" {
  cdn_frontdoor_custom_domain_id = azurerm_cdn_frontdoor_custom_domain.this.id
  cdn_frontdoor_route_ids        = [azurerm_cdn_frontdoor_route.this.id]
}
