resource "azurerm_log_analytics_workspace" "waf" {
  name                = "retailbook-${var.environment}-waf-logs"
  location            = var.resource_group.location
  resource_group_name = var.resource_group.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

resource "azurerm_monitor_diagnostic_setting" "waf" {
  name               = "retailbook-${var.environment}-waf-logs"
  target_resource_id = azurerm_cdn_frontdoor_profile.this.id

  log_analytics_workspace_id = azurerm_log_analytics_workspace.waf.id

  enabled_log {
    category_group = "allLogs"
  }

  enabled_log {
    category_group = "audit"
  }

  metric {
    category = "AllMetrics"
    enabled  = false
  }
}
