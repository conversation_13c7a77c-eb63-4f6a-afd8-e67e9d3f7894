resource "azurerm_cdn_frontdoor_firewall_policy" "this" {
  name                = "WAF"
  resource_group_name = var.resource_group.name
  sku_name            = azurerm_cdn_frontdoor_profile.this.sku_name
  enabled             = true
  mode                = var.waf_mode
  count               = var.waf_enabled ? 1 : 0

  dynamic "managed_rule" {
    for_each = var.waf_managed_rules
    content {
      type    = managed_rule.value.type
      version = managed_rule.value.version
      action  = managed_rule.value.action
    }
  }

  dynamic "custom_rule" {
    for_each = var.waf_custom_rules
    content {
      name     = custom_rule.value.name
      action   = custom_rule.value.action
      type     = custom_rule.value.type
      priority = custom_rule.value.priority

      dynamic "match_condition" {
        for_each = custom_rule.value.match_conditions
        content {
          match_variable = match_condition.value.match_variable
          match_values   = match_condition.value.match_values
          operator       = match_condition.value.operator
          transforms     = match_condition.value.transforms
        }
      }
    }
  }

  custom_rule {
    name                 = "RateLimit"
    action               = "Block"
    type                 = "RateLimitRule"
    priority             = 1
    enabled              = var.waf_rate_limit.enabled
    rate_limit_threshold = var.waf_rate_limit.max_requests_per_minutes
    match_condition {
      match_variable = "RequestMethod"
      match_values   = ["GET", "POST", "PUT", "DELETE", "HEAD"]
      operator       = "Equal"
    }
  }

  # Disable body parsing issues
  managed_rule {
    type    = "Microsoft_DefaultRuleSet"
    version = "2.1"
    action  = "Block"

    override {
      rule_group_name = "General"

      rule {
        action  = "AnomalyScoring"
        enabled = false
        rule_id = "200002"
      }

      rule {
        action  = "AnomalyScoring"
        enabled = false
        rule_id = "200003"
      }
    }
  }
}


resource "azurerm_cdn_frontdoor_security_policy" "this" {
  name                     = "WAF-Policy"
  count                    = var.waf_enabled ? 1 : 0
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.this.id
  security_policies {
    firewall {
      cdn_frontdoor_firewall_policy_id = azurerm_cdn_frontdoor_firewall_policy.this.0.id
      association {
        domain {
          cdn_frontdoor_domain_id = azurerm_cdn_frontdoor_custom_domain.this.id
        }
        patterns_to_match = ["/*"]
      }
    }
  }
}
