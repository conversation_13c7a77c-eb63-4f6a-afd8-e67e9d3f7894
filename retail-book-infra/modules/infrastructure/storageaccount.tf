resource "azurerm_storage_account" "documents" {
  name                             = "rb${var.environment}documents"
  location                         = azurerm_resource_group.this.location
  resource_group_name              = azurerm_resource_group.this.name
  account_tier                     = "Standard"
  account_replication_type         = "LRS"
  cross_tenant_replication_enabled = true

  identity {
    type = "SystemAssigned"
  }


  # Current drift on customer_managed_key
  # Linked issue : https://github.com/hashicorp/terraform-provider-azurerm/issues/19427
  lifecycle {
    ignore_changes = [
      customer_managed_key
    ]
  }
}

resource "azurerm_storage_account_customer_managed_key" "documents" {
  storage_account_id = azurerm_storage_account.documents.id
  key_vault_id       = azurerm_key_vault.this.id
  key_name           = azurerm_key_vault_key.this.name

  depends_on = [
    azurerm_role_assignment.storage_account_documents
  ]
}

resource "azurerm_role_assignment" "storage_account_documents" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_storage_account.documents.identity[0].principal_id
}
