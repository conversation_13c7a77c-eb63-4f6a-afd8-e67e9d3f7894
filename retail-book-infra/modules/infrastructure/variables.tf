#################
###  BASTION  ##
#################

variable "bastion_authorized_principals" {
  type        = map(string)
  description = "A list of principals authorized to connect to the bastion host."
  default     = {}
}

#################
###    AKS    ###
#################
variable "dns_zone" {
  type        = string
  description = "DNS zone used"
}

variable "kubernetes_version" {
  type        = string
  description = "Kubernetes cluster version"
}

variable "aks_node_pool_retailbook_min_count" {
  type        = number
  description = "Minimum number of nodes in the retailbook node pool"
  default     = 1
}

variable "aks_node_pool_retailbook_max_count" {
  type        = number
  description = "Maximum number of nodes in the retailbook node pool"
  default     = 10
}

variable "aks_node_os_upgrade_channel" {
  type    = string
  default = "NodeImage"
}

variable "aks_automatic_upgrade_channel" {
  type    = string
  default = "patch"
}

variable "aks_upgrade_override" {
  type    = bool
  default = null
}
#################
###    AKS    ###
#################

variable "environment" {
  description = "The environment name."
  type        = string
}

variable "location" {
  description = "The location/region where the resources will be created."
  type        = string
}


#################
###  NETWORK  ###
#################


variable "vnet_name" {
  description = "The VNET name"
  type        = string
}
variable "vnet_address_space" {
  description = "The address space that is used the virtual network. You can supply more than one address space."
  type        = list(string)
}

variable "dns_servers" {
  description = "DNS servers associated with the virtual network."
  type        = list(string)
  default     = null
}

variable "subnets" {
  description = "A map of subnets with their CIDR block and configuration."
  type = map(object({
    cidr                                          = string
    private_endpoint_network_policies             = optional(string, "Enabled")
    private_link_service_network_policies_enabled = optional(bool, true)
  }))
  default = {}
}

variable "subnets_service_endpoints" {
  description = "A map of service endpoint list for each subnet keys."
  type        = map(list(string))
  default     = {}
}

variable "subnets_delegations" {
  description = "A map of delegations configurations for each subnet keys."
  type = map(object({
    name = string
    service_delegation = object({
      name    = string
      actions = list(string)
    })
  }))
  default = {}
}

variable "tags" {
  description = "A mapping of tags to assign to the resource."
  type        = map(string)
  default     = null
}

#################
###  FRONTDOOR  ###
#################
variable "frontdoor_enabled" {
  description = "Choose to deploy frontdoor or not"
  type        = bool
  default     = false
}

variable "waf_enabled" {
  description = "Is the WAF and its rules enabled ?"
  type        = bool
}

variable "waf_mode" {
  description = "The type of WAF behaviour"
  type        = string
  default     = "Prevention"

  validation {
    condition     = contains(["Detection", "Prevention"], var.waf_mode)
    error_message = "waf_mode must be either Detection or Prevention"
  }
}

variable "waf_managed_rules" {
  description = "Sets of WAF managed rules"
  type = list(object({
    type    = string
    version = string
    action  = string
  }))
  default = []
}

variable "waf_custom_rules" {
  description = "Sets of WAF custom rules"
  type = list(object({
    name     = string
    action   = string
    type     = string
    priority = number
    match_conditions = list(object({
      match_variable = string
      match_values   = list(string)
      operator       = string
      transforms     = list(string)
    }))
  }))
  default = []
}

variable "waf_rate_limit" {
  description = "Configs about rate limiting"
  type = object({
    enabled                  = bool
    max_requests_per_minutes = number
  })
  default = {
    enabled                  = true
    max_requests_per_minutes = 200
  }
}

#################
###  KEYVAULT  ###
#################
variable "cleartext_secrets" {
  description = "Secrets created on keyvault that can be in cleartext (like usernames)"
  type        = map(string)
}

variable "random_secrets" {
  description = "Secrets created on keyvault with random values. (Only the key is used, for the secret name)"
  type        = map(string)
}

variable "mounted_secrets" {
  description = "Secrets created on keyvault that have been mounted by the environment + infrastructure (i.e. results of terragrunt apply)"
  type        = map(string)
}

#################
###  SENDGRID  ###
#################

variable "sendgrid_templates" {
  description = "The name of the templates (and therefore their corresponding files) to create"
  type        = set(string)
  default     = ["notification", "offer", "signup"]
}
