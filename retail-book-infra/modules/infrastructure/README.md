<!-- BEGIN_TF_DOCS -->

## Requirements

No requirements.

## Providers

| Name                                                         | Version |
| ------------------------------------------------------------ | ------- |
| <a name="provider_azuread"></a> [azuread](#provider_azuread) | n/a     |
| <a name="provider_azurerm"></a> [azurerm](#provider_azurerm) | n/a     |

## Modules

| Name                                                  | Source                                                 | Version |
| ----------------------------------------------------- | ------------------------------------------------------ | ------- |
| <a name="module_logger"></a> [logger](#module_logger) | **************:padok-team/terraform-azurerm-logger.git | v0.3.0  |

## Resources

| Name                                                                                                                                                                                         | Type     |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| [azurerm_container_registry.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/container_registry)                                                        | resource |
| [azurerm_dns_zone.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/dns_zone)                                                                            | resource |
| [azurerm_key_vault.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/key_vault)                                                                          | resource |
| [azurerm_key_vault_key.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/key_vault_key)                                                                  | resource |
| [azurerm_kubernetes_cluster.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/kubernetes_cluster)                                                        | resource |
| [azurerm_linux_virtual_machine.bastion](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/linux_virtual_machine)                                               | resource |
| [azurerm_network_interface.bastion](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/network_interface)                                                       | resource |
| [azurerm_network_interface_security_group_association.bastion](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/network_interface_security_group_association) | resource |
| [azurerm_network_security_group.bastion](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/network_security_group)                                             | resource |
| [azurerm_network_security_rule.bastion_ssh_ip_whitelist](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/network_security_rule)                              | resource |
| [azurerm_public_ip.bastion](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/public_ip)                                                                       | resource |
| [azurerm_resource_group.logger](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/resource_group)                                                              | resource |
| [azurerm_resource_group.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/resource_group)                                                                | resource |
| [azurerm_role_assignment.acr_keyvault](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                      | resource |
| [azurerm_role_assignment.aks_acr](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                           | resource |
| [azurerm_role_assignment.aks_external_dns](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                  | resource |
| [azurerm_role_assignment.aks_identity](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                      | resource |
| [azurerm_role_assignment.aks_node_rg_identity](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                              | resource |
| [azurerm_role_assignment.aks_node_rg_vms](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                   | resource |
| [azurerm_role_assignment.aks_rg](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                            | resource |
| [azurerm_role_assignment.aks_vnet](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                          | resource |
| [azurerm_role_assignment.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment)                                                              | resource |
| [azurerm_subnet.these](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/subnet)                                                                               | resource |
| [azurerm_user_assigned_identity.acr](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/user_assigned_identity)                                                 | resource |
| [azurerm_user_assigned_identity.aks](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/user_assigned_identity)                                                 | resource |
| [azurerm_virtual_network.this](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/virtual_network)                                                              | resource |

## Inputs

| Name                                                                                                         | Description                                                                                     | Type                                                                                                                                    | Default | Required |
| ------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- | ------- | :------: |
| <a name="input_dns_zone"></a> [dns_zone](#input_dns_zone)                                                    | DNS zone used                                                                                   | `string`                                                                                                                                | n/a     |   yes    |
| <a name="input_environment"></a> [environment](#input_environment)                                           | The environment name.                                                                           | `string`                                                                                                                                | n/a     |   yes    |
| <a name="input_location"></a> [location](#input_location)                                                    | The location/region where the resources will be created.                                        | `string`                                                                                                                                | n/a     |   yes    |
| <a name="input_vnet_address_space"></a> [vnet_address_space](#input_vnet_address_space)                      | The address space that is used the virtual network. You can supply more than one address space. | `list(string)`                                                                                                                          | n/a     |   yes    |
| <a name="input_vnet_name"></a> [vnet_name](#input_vnet_name)                                                 | The VNET name                                                                                   | `string`                                                                                                                                | n/a     |   yes    |
| <a name="input_bastion_ip_whitelist"></a> [bastion_ip_whitelist](#input_bastion_ip_whitelist)                | A list of IP addresses to whitelist for SSH access to the bastion host.                         | `list(string)`                                                                                                                          | `[]`    |    no    |
| <a name="input_bastion_ssh_public_keys"></a> [bastion_ssh_public_keys](#input_bastion_ssh_public_keys)       | A map of SSH public keys to add to the bastion host.                                            | `map(string)`                                                                                                                           | `{}`    |    no    |
| <a name="input_dns_servers"></a> [dns_servers](#input_dns_servers)                                           | DNS servers associated with the virtual network.                                                | `list(string)`                                                                                                                          | `null`  |    no    |
| <a name="input_subnets"></a> [subnets](#input_subnets)                                                       | A map of subnets with their CIDR block.                                                         | `map(string)`                                                                                                                           | `{}`    |    no    |
| <a name="input_subnets_delegations"></a> [subnets_delegations](#input_subnets_delegations)                   | A map of delegations configurations for each subnet keys.                                       | <pre>map(object({<br> name = string<br> service_delegation = object({<br> name = string<br> actions = list(string)<br> })<br> }))</pre> | `{}`    |    no    |
| <a name="input_subnets_service_endpoints"></a> [subnets_service_endpoints](#input_subnets_service_endpoints) | A map of service endpoint list for each subnet keys.                                            | `map(list(string))`                                                                                                                     | `{}`    |    no    |
| <a name="input_tags"></a> [tags](#input_tags)                                                                | A mapping of tags to assign to the resource.                                                    | `map(string)`                                                                                                                           | `null`  |    no    |

## Outputs

| Name                                                     | Description                          |
| -------------------------------------------------------- | ------------------------------------ |
| <a name="output_subnets"></a> [subnets](#output_subnets) | A map of subnets.                    |
| <a name="output_vnet"></a> [vnet](#output_vnet)          | A virtual network resource instance. |

<!-- END_TF_DOCS -->
