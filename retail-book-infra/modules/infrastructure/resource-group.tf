resource "azurerm_resource_group" "this" {
  name     = "retailbook-${var.environment}"
  location = var.location
}


resource "azurerm_resource_group" "logger" {
  name     = "retailbook-${var.environment}-logger"
  location = var.location
}

resource "azurerm_role_definition" "storage_account_containers_creator" {
  name  = "Storage Account Container Creator ${title(var.environment)}"
  scope = "/subscriptions/${data.azurerm_client_config.current.subscription_id}/resourcegroups/${azurerm_resource_group.this.name}"

  permissions {
    actions = ["Microsoft.Storage/storageAccounts/blobServices/containers/read", "Microsoft.Storage/storageAccounts/blobServices/containers/write"]
  }
}
