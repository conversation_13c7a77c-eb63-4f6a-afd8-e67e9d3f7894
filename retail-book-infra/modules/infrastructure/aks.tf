locals {
  aks_sku_tier_by_environment = {
    staging = "Free",
    preprod = "Standard",
    prod    = "Standard",
  }
  aks_sku_tier = local.aks_sku_tier_by_environment[var.environment]
}

resource "azurerm_kubernetes_cluster" "this" {
  name       = "retailbook-${var.environment}"
  dns_prefix = "retailbook${var.environment}"
  sku_tier   = local.aks_sku_tier

  location                  = azurerm_resource_group.this.location
  resource_group_name       = azurerm_resource_group.this.name
  private_cluster_enabled   = true
  oidc_issuer_enabled       = true
  kubernetes_version        = var.kubernetes_version
  node_os_upgrade_channel   = var.aks_node_os_upgrade_channel
  automatic_upgrade_channel = var.aks_automatic_upgrade_channel

  default_node_pool {
    name           = "default"
    node_count     = 1
    vm_size        = "Standard_D2as_v5"
    vnet_subnet_id = azurerm_subnet.these["kubernetes"].id

    node_public_ip_enabled = false
    # enable_host_encryption = true # Prerequisite : https://docs.microsoft.com/en-us/azure/virtual-machines/linux/disks-enable-host-based-encryption-cli
    auto_scaling_enabled = true
    min_count            = 1
    max_count            = 10
    tags = {
      "type" = "aks"
    }
  }

  network_profile {
    network_plugin = "kubenet"
    network_policy = "calico"
  }

  #   oms_agent {
  #     log_analytics_workspace_id = azurerm_log_analytics_workspace.kubernetes.id
  #   }

  identity {
    type = "UserAssigned"
    identity_ids = [
      azurerm_user_assigned_identity.aks.id
    ]
  }

  dynamic "upgrade_override" {
    for_each = var.aks_upgrade_override != null ? [1] : []
    content {
      force_upgrade_enabled = var.aks_upgrade_override
    }
  }

  lifecycle {
    ignore_changes = [
      # Ignore changes to node_count, e.g. because the cluster autoscaler may
      # updates it based on load in the cluster.
      default_node_pool.0.node_count,
      kubernetes_version
    ]
  }
}

# This node pool is only used to run the retailbook application.
resource "azurerm_kubernetes_cluster_node_pool" "retailbook" {
  name                  = "retailbook"
  kubernetes_cluster_id = azurerm_kubernetes_cluster.this.id
  vm_size               = "Standard_F4s"
  auto_scaling_enabled  = true
  min_count             = var.aks_node_pool_retailbook_min_count
  max_count             = var.aks_node_pool_retailbook_max_count
  node_count            = var.aks_node_pool_retailbook_min_count
  vnet_subnet_id        = azurerm_subnet.these["kubernetes"].id

  node_labels = {
    "application" = "retailbook.com"
  }

  node_taints = ["application=retailbook.com:NoSchedule"]

  lifecycle {
    ignore_changes = [
      # Ignore changes to node_count, e.g. because the cluster autoscaler may
      # updates it based on load in the cluster.
      node_count
    ]
  }
  tags = {
    "type" = "aks"
  }
}


######################################################
# Setup AKS Identity
######################################################
resource "azurerm_user_assigned_identity" "aks" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "aks"
}

resource "azurerm_role_assignment" "aks_rg" {
  scope                = azurerm_resource_group.this.id
  role_definition_name = "Contributor"
  principal_id         = azurerm_user_assigned_identity.aks.principal_id
}

resource "azurerm_role_assignment" "aks_vnet" {
  scope                = azurerm_virtual_network.this.id
  role_definition_name = "Network Contributor"
  principal_id         = azurerm_user_assigned_identity.aks.principal_id
}

resource "azurerm_role_assignment" "aks_acr" {
  scope                = azurerm_container_registry.this.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_kubernetes_cluster.this.kubelet_identity[0].object_id
}


resource "azurerm_role_assignment" "aks_node_rg_vms" {
  scope                = "/subscriptions/${data.azurerm_client_config.current.subscription_id}/resourceGroups/${azurerm_kubernetes_cluster.this.node_resource_group}"
  role_definition_name = "Virtual Machine Contributor"
  principal_id         = azurerm_kubernetes_cluster.this.kubelet_identity[0].object_id
}

resource "azurerm_role_assignment" "aks_node_rg_identity" {
  scope                = "/subscriptions/${data.azurerm_client_config.current.subscription_id}/resourceGroups/${azurerm_kubernetes_cluster.this.node_resource_group}"
  role_definition_name = "Managed Identity Operator"
  principal_id         = azurerm_kubernetes_cluster.this.kubelet_identity[0].object_id
}

resource "azurerm_role_assignment" "aks_identity" {
  scope                = azurerm_resource_group.this.id
  role_definition_name = "Managed Identity Operator"
  principal_id         = azurerm_kubernetes_cluster.this.kubelet_identity[0].object_id
}

resource "azurerm_user_assigned_identity" "aks_external_dns" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "aks_external_dns"
}

resource "azurerm_role_assignment" "aks_external_dns" {
  scope                = azurerm_dns_zone.this.id
  role_definition_name = "DNS Zone Contributor"
  principal_id         = azurerm_user_assigned_identity.aks_external_dns.principal_id
}

resource "azurerm_federated_identity_credential" "aks_external_dns" {
  name                = "external-dns"
  resource_group_name = azurerm_resource_group.this.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = azurerm_kubernetes_cluster.this.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.aks_external_dns.id
  subject             = "system:serviceaccount:external-dns:external-dns-identity"
}

resource "azurerm_user_assigned_identity" "aks_cert_manager" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "aks_cert_manager"
}

resource "azurerm_role_assignment" "aks_cert_manager" {
  scope                = azurerm_dns_zone.this.id
  role_definition_name = "DNS Zone Contributor"
  principal_id         = azurerm_user_assigned_identity.aks_cert_manager.principal_id
}

resource "azurerm_federated_identity_credential" "aks_cert_manager" {
  name                = "cert-manager"
  resource_group_name = azurerm_resource_group.this.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = azurerm_kubernetes_cluster.this.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.aks_cert_manager.id
  subject             = "system:serviceaccount:cert-manager:cert-manager"
}

resource "azurerm_user_assigned_identity" "aks_external_secrets" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "aks-external-secrets"
}

resource "azurerm_role_assignment" "aks_external_secrets" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Secrets User"
  principal_id         = azurerm_user_assigned_identity.aks_external_secrets.principal_id
}

resource "azurerm_federated_identity_credential" "aks_external_secrets" {
  name                = "external-secrets"
  resource_group_name = azurerm_resource_group.this.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = azurerm_kubernetes_cluster.this.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.aks_external_secrets.id
  subject             = "system:serviceaccount:external-secrets:external-secrets-identity"
}

resource "azurerm_user_assigned_identity" "aks_document_service" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "aks_document_service"
}

resource "azurerm_role_assignment" "aks_document_service_user_admin" {
  scope                = azurerm_storage_account.documents.id
  role_definition_name = "Owner"
  principal_id         = azurerm_user_assigned_identity.aks_document_service.principal_id
}

resource "azurerm_role_assignment" "aks_document_service_create_containers" {
  scope                = azurerm_storage_account.documents.id
  role_definition_name = azurerm_role_definition.storage_account_containers_creator.name
  principal_id         = azurerm_user_assigned_identity.aks_document_service.principal_id
}

resource "azurerm_federated_identity_credential" "aks_document_service" {
  name                = "document-service"
  resource_group_name = azurerm_resource_group.this.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = azurerm_kubernetes_cluster.this.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.aks_document_service.id
  subject             = "system:serviceaccount:global:documentservice"
}

resource "azurerm_user_assigned_identity" "aks_notification_service" {
  # In preparation for e-mail provider with an SSO connection (as opposed to using API keys). If you find this and
  # SSO is not required; this SHOULD be safe to remove.

  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "aks_notification_service"
}
