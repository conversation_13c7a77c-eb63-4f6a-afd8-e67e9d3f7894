resource "azurerm_resource_group" "backup" {
  name     = "retailbook-${var.environment}-backup"
  location = var.location
}

resource "azurerm_storage_account" "backup" {
  name                             = "retailbook${var.environment}backup"
  resource_group_name              = azurerm_resource_group.backup.name
  location                         = azurerm_resource_group.backup.location
  account_kind                     = "StorageV2"
  account_tier                     = "Standard"
  account_replication_type         = "GRS"
  cross_tenant_replication_enabled = true
  access_tier                      = "Cool"
}

resource "azurerm_storage_management_policy" "backup" {
  storage_account_id = azurerm_storage_account.backup.id

  rule {
    name    = "expire-after-1-month"
    enabled = true
    filters {
      blob_types = ["blockBlob"]
    }
    actions {
      base_blob {
        delete_after_days_since_modification_greater_than = 30
      }
    }
  }
}


resource "azurerm_user_assigned_identity" "db_backup" {
  resource_group_name = azurerm_resource_group.backup.name
  location            = azurerm_resource_group.backup.location

  name = "db-backup-identity"
}

resource "azurerm_role_assignment" "db_backup" {
  scope                = azurerm_storage_account.backup.id
  role_definition_name = "Owner" # TODO: Update with least privilege, e.g. "Storage Blob Data Contributor" afterb building backup script
  principal_id         = azurerm_user_assigned_identity.db_backup.principal_id
}
