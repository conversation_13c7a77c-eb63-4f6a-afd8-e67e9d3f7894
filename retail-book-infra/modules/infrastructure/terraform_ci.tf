data "azurerm_subscription" "current" {}

locals {
  owners = [data.azuread_user.olaf.object_id] # TODO Correct this
}

resource "azuread_application" "terraform_cicd" {
  display_name = "Terraform CI/CD ${var.environment}"
  owners       = local.owners

  required_resource_access {
    resource_app_id = "00000003-0000-0000-c000-000000000000" # Microsoft Graph

    resource_access {
      id   = "97235f07-e226-4f63-ace3-39588e11d3a1" # User.ReadBasic.All (app)
      type = "Role"
    }

    resource_access {
      id   = "5b567255-7703-4780-807c-7be8301ae99b" # Group.Read.All (app)
      type = "Role"
    }
    resource_access {
      id   = "df021288-bdef-4463-88db-98f22de89214" # User.Read.All (app)
      type = "Role"
    }
    resource_access {
      id   = "98830695-27a2-44f7-8c18-0c3ebc9698f6" # GroupMember.Read.All (app)
      type = "Role"
    }

    resource_access {
      id   = "18a4783c-866b-4cc7-a460-3d5e5662c884" # Application.ReadWrite.OwnedBy
      type = "Role"
    }
  }

}

resource "azuread_service_principal" "terraform_cicd" {
  client_id = azuread_application.terraform_cicd.client_id
  owners         = local.owners
}

resource "azurerm_role_assignment" "terraform_cicd" {
  scope                = data.azurerm_subscription.current.id
  role_definition_name = "Owner"
  principal_id         = azuread_service_principal.terraform_cicd.object_id
  // If new SP there  may be replication lag this disables validation
  skip_service_principal_aad_check = true
}


resource "azuread_application_federated_identity_credential" "terraform_cicd" {
  application_id = azuread_application.terraform_cicd.id
  display_name          = "GitHub"
  audiences             = ["api://AzureADTokenExchange"]
  issuer                = "https://token.actions.githubusercontent.com"
  subject               = "repo:RETAIL-BOOK-LIMITED/retail-book-infra:environment:${var.environment}"
}
