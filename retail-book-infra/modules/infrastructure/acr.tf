resource "azurerm_container_registry" "this" {
  name                = "retailbook${var.environment}"
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location
  sku                 = "Premium"

  identity {
    type         = "UserAssigned"
    identity_ids = [
      azurerm_user_assigned_identity.acr.id
    ]
  }

  encryption {
    key_vault_key_id   = azurerm_key_vault_key.this.id
    identity_client_id = azurerm_user_assigned_identity.acr.client_id
  }
}

resource "azurerm_user_assigned_identity" "acr" {
  resource_group_name = azurerm_resource_group.this.name
  location            = azurerm_resource_group.this.location

  name = "acr"
}

resource "azurerm_role_assignment" "acr_keyvault" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_user_assigned_identity.acr.principal_id
}
