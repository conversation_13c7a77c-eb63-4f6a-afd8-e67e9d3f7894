resource "sendgrid_api_key" "api_key" {
  name   = title("${var.environment} Notification Service API Key")
  scopes = [
    "mail.send",
    "sender_verification_eligible"
  ]

  # Scope drift ?
  lifecycle {
    ignore_changes = [
      scopes
    ]
  }
}

resource "sendgrid_api_key" "prometheus_api_key" {
  name   = title("${var.environment} API Key for prometheus alertmanager to send emails")
  scopes = [
    "mail.send",
    "sender_verification_eligible"
  ]

  # Scope drift ?
  lifecycle {
    ignore_changes = [
      scopes
    ]
  }
}

resource "sendgrid_template" "template" {
  for_each   = var.sendgrid_templates
  name       = title("${var.environment} ${each.value}")
  # Intention is that a single SendGrid subscription exists here - so templates are prepended with the env.
  generation = "dynamic"
}

locals {
  # Build a template name to template ID map (to be inserted as a mounted secret).
  template_pairs   = flatten([
  for t in sendgrid_template.template : {
    trimspace(trimprefix(lower(t.name), lower(var.environment))) = t.id
  }
  ])
  templates_secret = {for template_pair in local.template_pairs : format("sendgrid-template-%s", keys(template_pair)[0]) => values(template_pair)[0]}

  # Build a API key variable (to be inserted as a mounted secret).
  sendgrid_secret = tomap({ sendgrid-api-key = sendgrid_api_key.api_key.api_key })
  prometheus_sendgrid_secret = tomap({ prometheus-sendgrid-api-key = sendgrid_api_key.prometheus_api_key.api_key })
}

resource "sendgrid_template_version" "template_version" {
  for_each = sendgrid_template.template

  name                   = "Main"
  template_id            = each.value.id
  active                 = 1
  html_content           = file(format("%s/assets/sendgrid/%s.html", path.module, trimprefix(lower(each.value.name), lower("${var.environment} "))))
  generate_plain_content = true
  subject                = "{{{subject}}}" # Will be personalized accordingly by the sender (in the main code).
}

