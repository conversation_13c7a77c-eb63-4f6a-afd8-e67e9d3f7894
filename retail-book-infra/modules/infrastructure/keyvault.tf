resource "azurerm_key_vault" "this" {
  name                        = "retailbook-${var.environment}"
  location                    = azurerm_resource_group.this.location
  resource_group_name         = azurerm_resource_group.this.name
  enabled_for_disk_encryption = true
  tenant_id                   = data.azurerm_client_config.current.tenant_id
  soft_delete_retention_days  = 7
  purge_protection_enabled    = true
  enable_rbac_authorization   = true

  sku_name = "standard"
}

resource "azurerm_key_vault_key" "this" {
  name         = "retailbook-encryption-${var.environment}"
  key_vault_id = azurerm_key_vault.this.id
  key_type     = "RSA"
  key_size     = 2048

  key_opts = [
    "decrypt",
    "encrypt",
    "sign",
    "unwrapKey",
    "verify",
    "wrapKey"
  ]

  depends_on = [
    azurerm_role_assignment.keyvault_padok,
    azurerm_role_assignment.keyvault_cicd
  ]
}

# TODO add a role assigment that allows the current user to create and manage keys in the key vault
resource "azurerm_role_assignment" "keyvault_padok" {
 scope                = azurerm_key_vault.this.id
 role_definition_name = "Key Vault Administrator"
 principal_id         = data.azuread_group.padok.object_id
}

resource "azurerm_role_assignment" "keyvault_cicd" {
  scope                = azurerm_key_vault.this.id
  role_definition_name = "Key Vault Administrator"
  principal_id         = azuread_service_principal.terraform_cicd.object_id
}

# ##################  #
#  CLEARTEXT SECRETS  #
# ##################  #
resource "azurerm_key_vault_secret" "cleartext_secrets" {
  for_each     = var.cleartext_secrets
  name         = each.key
  value        = each.value
  key_vault_id = azurerm_key_vault.this.id
  content_type = "Managed by Terraform"
}

# ###############  #
#  RANDOM SECRETS  #
# ###############  #
resource "random_password" "this" {
  for_each = var.random_secrets
  length   = 20
  special  = false
}

resource "azurerm_key_vault_secret" "random_secrets" {
  for_each     = var.random_secrets
  name         = each.key
  value        = random_password.this[each.key].result
  key_vault_id = azurerm_key_vault.this.id
  content_type = "Managed by Terraform"
}

# ################# #
#  MOUNTED SECRETS  #
# ################# #

# Merge all mounted secrets with values that are from outputs. To add a new output to the mounted secrets add a new
# key to the map below and a value in depends_on on azurerm_key_vault_secret.mounted_secrets.
locals {
  mounted_secrets = merge(
    local.templates_secret,
    local.sendgrid_secret,
    local.prometheus_sendgrid_secret,
    var.mounted_secrets,
  )
}

resource "azurerm_key_vault_secret" "mounted_secrets" {
  for_each     = local.mounted_secrets
  name         = each.key
  value        = each.value
  key_vault_id = azurerm_key_vault.this.id
  content_type = "Managed by Terraform"

  depends_on = [
    sendgrid_api_key.api_key,
    sendgrid_api_key.prometheus_api_key
  ]
}
