# Retailbook Infrastructure

This repository contains the infrastructure code for the Retailbook project. The infrastructure is built using Terraform and Terragrunt, which are tools for managing cloud infrastructure as code.

## Infrastructure Overview

![infrastructure diagram](docs/schema/architecture-schema.png)

## Operate the infrastructure

Terragrunt is a thin wrapper that provides extra tools for keeping your configurations DRY, working with multiple Terraform modules, and managing remote state.

- Terraform [Installation link](https://developer.hashicorp.com/terraform/downloads?product_intent=terraform)
- Terragrunt [Installation link](https://terragrunt.gruntwork.io/docs/getting-started/install/)
- Azure logged in. [Installation link](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli)
  - Then run `az login`

### Set up

The `layers` folder contains multiple layers that will be used to set up the infrastructure. For example, the `bootstrap` layer is used to create a shared storage on azure used on next layers to have a shared state. The `infrastructure` layer sets up all the infrastructure object (ACR, AKS, Network, ...). Let's say we want to use this layer in staging.

```bash
cd layers/infrastructure/staging
```

The first thing to do after you cloned the repo is to init Terragrunt to download all dependencies.

```bash
terragrunt init
```

You can see we used terragrunt instead of terraform. This will be useful later, but now, just see it as a terraform wrapper.

If you want to make modifications, like add a public key on the bastion, you can add it here `layers/infrastructure/staging/inputs.hcl`.

```bash
terragrunt plan # To check the what changes terragrunt will make
terragrunt apply # To apply the changes planned by terragrunt
```

Note : you can see the terraform code created by terragrunt on the folder : `layers/infrastructure/staging/.terragrunt-cache`.

### Terragrunt run-all

On thing you can do with terragrunt is to init/plan/apply multiple environments at once with the `run-all` command. Let's say we want to operate on the `infrastructure` layer.

```bash
cd layers/infrastructure
terragrunt run-all init
terragrunt run-all plan
terragrunt run-all apply
```
