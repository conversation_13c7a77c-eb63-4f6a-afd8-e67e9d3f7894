# RetailBook Database Schema for Intermediaries/Participants

## Table of Contents
1. [Introduction](#introduction)
2. [Core Data Models](#core-data-models)
3. [Entity Relationship Diagrams](#entity-relationship-diagrams)
   - [Participant Schema](#participant-schema)
   - [Organization Schema](#organization-schema)
   - [Invite <PERSON>](#invite-schema)
   - [Order Schema](#order-schema)
   - [Allocation Schema](#allocation-schema)
   - [Settlement Schema](#settlement-schema)
4. [Database Tables](#database-tables)
5. [Key Relationships](#key-relationships)
6. [Data Flow](#data-flow)
7. [Appendix](#appendix)

## Introduction

This document provides a comprehensive overview of the database schemas and relationships for intermediaries/participants in the RetailBook system. It focuses on how participant data is stored, managed, and related to other entities in the system such as offers, orders, allocations, and settlements.

The RetailBook system uses a combination of configuration files (YAML) and database tables to manage participant data. The core participant information is stored in YAML configuration files, while related data such as organizations, invites, orders, and allocations are stored in database tables.

## Core Data Models

### Participant Data Model

Participants (intermediaries and banks) are core entities in the RetailBook system. Their basic information is stored in YAML configuration files rather than in the database.

```yaml
- displayName: "Test Retail Intermediary"
  systemId: "retail"
  principalId: "principal_id_here"
  roles: ["intermediary"]
  masterAgreementSigned: true
```

### Organization Data Model

Organizations represent the business entities associated with participants. They contain details about the participant's business, including address and account information.

```go
type Organisation struct {
    SystemId            string
    SystemRole          string
    Name                string
    CrestAccountName    string
    CrestParticipantId  string
    AccountType         string
    AccountName         string
    AccountBankName     string
    AccountSwiftBic     string
    AccountNumber       string
    AccountSortCode     string
    Address1            string
    Address2            string
    Address3            string
    Address4            string
    AddressCity         string
    AddressPostalCode   string
    AddressCountry      string
    SettlementReference string
}
```

### Invite Data Model

Invites represent the connection between offers and intermediaries. They track the invitation of intermediaries to participate in offers.

```go
type Invite struct {
    OfferId                string
    Status                 InviteStatus
    IntermediarySystemId   string
    IntermediaryDisplayName string
    MasterAgreementSigned  bool
    TimeOfInvite           time.Time
    TimeOfSending          time.Time
    Terms                  Terms
}

type Terms struct {
    Accepted            bool
    AcceptedTime        time.Time
    AcceptedBy          string
    AcceptedByEmail     string
    ReceiveCommission   bool
    RetailOfferNoticeId string
}
```

## Entity Relationship Diagrams

### Participant Schema

The participant schema shows the relationship between participants and their related entities.

```mermaid
erDiagram
    PARTICIPANT {
        string DisplayName
        string SystemId
        string PrincipalId
        boolean MasterAgreementSigned
        string[] Roles
    }
    
    ORGANISATION {
        string system_id
        string system_role
        string name
        string crest_account_name
        string crest_participant_id
        string account_type
        string account_name
        string account_bank_name
        string account_swift_bic
        string account_number
        string account_sort_code
        string settlement_reference
    }
    
    ADDRESS {
        string address1
        string address2
        string address3
        string address4
        string address_city
        string address_postal_code
        string address_country
    }
    
    USER {
        string id
        string ext_id
        string name
        string email
        string role
        string organisational_role
        boolean gatekeeper
        string status
    }
    
    PARTICIPANT ||--o| ORGANISATION : has
    ORGANISATION ||--o| ADDRESS : has
    PARTICIPANT ||--o{ USER : has
```

### Organization Schema

The organization schema shows the structure of organization data and its relationships.

```mermaid
erDiagram
    ORGANISATION {
        string system_id
        string system_role
        string name
        string crest_account_name
        string crest_participant_id
        string account_type
        string account_name
        string account_bank_name
        string account_swift_bic
        string account_number
        string account_sort_code
        string settlement_reference
    }
    
    ADDRESS {
        string address1
        string address2
        string address3
        string address4
        string address_city
        string address_postal_code
        string address_country
    }
    
    ACCOUNT {
        string account_type
        string account_name
        string account_bank_name
        string account_swift_bic
        string account_number
        string account_sort_code
    }
    
    ORGANISATION ||--o| ADDRESS : has
    ORGANISATION ||--o| ACCOUNT : has
```

### Invite Schema

The invite schema shows how intermediaries are invited to participate in offers.

```mermaid
erDiagram
    OFFER {
        string id
        string name
        string type
        string status
        string currency
        string issued_by
    }
    
    PARTICIPANT {
        string DisplayName
        string SystemId
        string PrincipalId
        boolean MasterAgreementSigned
        string[] Roles
    }
    
    INVITE {
        string offer_id
        string status
        string intermediary_system_id
        string intermediary_display_name
        boolean master_agreement_signed
        datetime time_of_invite
        datetime time_of_sending
    }
    
    TERMS {
        boolean accepted
        datetime accepted_time
        string accepted_by
        string accepted_by_email
        boolean receive_commission
        string retail_offer_notice_id
    }
    
    OFFER ||--o{ INVITE : has
    PARTICIPANT ||--o{ INVITE : receives
    INVITE ||--o| TERMS : has
```

### Order Schema

The order schema shows how intermediaries place orders on offers.

```mermaid
erDiagram
    OFFER {
        string id
        string name
        string type
        string status
        string currency
        string issued_by
    }
    
    PARTICIPANT {
        string DisplayName
        string SystemId
        string PrincipalId
        boolean MasterAgreementSigned
        string[] Roles
    }
    
    ORDER_BOOK {
        string offer_id
        datetime timestamp
        boolean errored
    }
    
    ORDER_SUMMARY {
        string offer_id
        string offer_name
        string entered_by
        string entered_by_email
        datetime date_created
        datetime date_updated
        string status
        string order_type
        string order_book_id
    }
    
    SUMMARY_TOTAL {
        string ordersummary_id
        string aggregation
        number applications
        number notional_value
        number existing_holding
    }
    
    ORDER {
        string orderbook_id
        string client_order_ref
        number notional_value
        number existing_holding
        boolean tax_wrapper
        string validation_error
        boolean validation_failed
    }
    
    INTERMEDIARY_ORDER_MAPPING {
        string offer_id
        string intermediary
        string order_mapping_id
    }
    
    OFFER ||--o| ORDER_BOOK : has
    PARTICIPANT ||--o{ ORDER_SUMMARY : places
    ORDER_BOOK ||--o{ ORDER : contains
    ORDER_SUMMARY ||--o{ SUMMARY_TOTAL : has
    OFFER ||--o{ INTERMEDIARY_ORDER_MAPPING : maps
    INTERMEDIARY_ORDER_MAPPING ||--o{ ORDER_SUMMARY : references
```

### Allocation Schema

The allocation schema shows how allocations are distributed to intermediaries.

```mermaid
erDiagram
    OFFER {
        string id
        string name
        string type
        string status
        string currency
        string issued_by
    }
    
    PARTICIPANT {
        string DisplayName
        string SystemId
        string PrincipalId
        boolean MasterAgreementSigned
        string[] Roles
    }
    
    ALLOCATION_BOOK {
        string offer_id
        datetime created
        number offer_price_at_creation
    }
    
    OFFER_ALLOCATION {
        string entered_by
        datetime date_created
        string status
    }
    
    OFFER_ALLOCATION_TOTAL {
        string offerallocation_id
        string aggregation
        number applications
        number notional_value
        number order_quantity
        number num_orders
        number allocated_orders
        number unallocated_orders
        number allocation_value
        number allocation_quantity
    }
    
    ALLOCATION_SUMMARY {
        string entered_by
        datetime date_created
        string allocationbook_id
        string offerallocation_id
    }
    
    ALLOCATION_SUMMARY_TOTAL {
        string allocationsummary_id
        string aggregation
        number applications
        number notional_value
        number order_quantity
        number num_orders
        number allocated_orders
        number unallocated_orders
        number allocation_value
        number allocation_quantity
    }
    
    ALLOCATION {
        string allocationbook_id
        string client_order_ref
        number notional_value
        number existing_holding
        boolean commission_due
        boolean tax_wrapper
        number applications
        number allocation
        string intermediary
        number order_quantity
        string validation_error
        boolean validation_failed
    }
    
    OFFER ||--o| ALLOCATION_BOOK : has
    OFFER ||--o| OFFER_ALLOCATION : has
    OFFER_ALLOCATION ||--o{ OFFER_ALLOCATION_TOTAL : has
    ALLOCATION_BOOK ||--o{ ALLOCATION : contains
    ALLOCATION_BOOK ||--o{ ALLOCATION_SUMMARY : has
    ALLOCATION_SUMMARY ||--o{ ALLOCATION_SUMMARY_TOTAL : has
    PARTICIPANT ||--o{ ALLOCATION : receives
```

### Settlement Schema

The settlement schema shows how settlements are processed for intermediaries.

```mermaid
erDiagram
    OFFER {
        string id
        string name
        string type
        string status
        string currency
        string issued_by
    }
    
    PARTICIPANT {
        string DisplayName
        string SystemId
        string PrincipalId
        boolean MasterAgreementSigned
        string[] Roles
    }
    
    SETTLEMENT_BOOK {
        string offer_id
        datetime created
    }
    
    SETTLEMENT_INSTRUCTION {
        string settlementbook_id
        string intermediary
        string status
        datetime created
        datetime completed
    }
    
    SETTLEMENT_OBLIGATION {
        string settlementinstruction_id
        string client_order_ref
        number allocation
        number notional_value
        boolean commission_due
        boolean tax_wrapper
    }
    
    OFFER ||--o| SETTLEMENT_BOOK : has
    SETTLEMENT_BOOK ||--o{ SETTLEMENT_INSTRUCTION : contains
    SETTLEMENT_INSTRUCTION ||--o{ SETTLEMENT_OBLIGATION : has
    PARTICIPANT ||--o{ SETTLEMENT_INSTRUCTION : receives
```

## Database Tables

The RetailBook system uses the following database tables to store intermediary-related data:

### Organization Tables
- `organisation`: Stores organization details
- `address`: Stores address information
- `account`: Stores account information

### Invite Tables
- `invite`: Stores invitation details
- `terms`: Stores terms acceptance information

### Order Tables
- `order_book`: Stores order book information
- `order_summary`: Stores summary of orders
- `summary_total`: Stores totals for order summaries
- `order`: Stores individual order details
- `intermediary_order_mapping`: Maps intermediaries to orders

### Allocation Tables
- `allocation_book`: Stores allocation book information
- `offer_allocation`: Stores offer allocation information
- `offer_allocation_total`: Stores totals for offer allocations
- `allocation_summary`: Stores summary of allocations
- `allocation_summary_total`: Stores totals for allocation summaries
- `allocation`: Stores individual allocation details

### Settlement Tables
- `settlement_book`: Stores settlement book information
- `settlement_instruction`: Stores settlement instruction information
- `settlement_obligation`: Stores settlement obligation details

## Key Relationships

The following are the key relationships between intermediaries and other entities in the system:

1. **Participant to Organization**: Each participant has one organization record that stores their business details.

2. **Participant to User**: Each participant can have multiple users associated with their organization.

3. **Participant to Invite**: Participants (specifically intermediaries) receive invitations to participate in offers.

4. **Participant to Order**: Intermediaries place orders on offers they've been invited to.

5. **Participant to Allocation**: Intermediaries receive allocations based on their orders.

6. **Participant to Settlement**: Intermediaries complete settlements for their allocations.

## Data Flow

The following diagram shows the flow of data through the system from the perspective of an intermediary:

```mermaid
flowchart TD
    A[Participant Created] --> B[Organization Details Added]
    B --> C[Users Created]
    C --> D[Invited to Offer]
    D --> E[Terms Accepted]
    E --> F[Orders Placed]
    F --> G[Allocations Received]
    G --> H[Settlements Completed]
```

## Appendix

### Enums

The system uses the following enums for intermediary-related data:

#### InviteStatus
- `Draft`: Invitation is in draft state
- `Active`: Invitation is active

#### OrderStatus
- `Pending`: Order is pending approval
- `ReplacePending`: Order is pending replacement
- `Unconfirmed`: Order is unconfirmed
- `Accepted`: Order is accepted
- `Rejected`: Order is rejected
- `Replaced`: Order is replaced
- `Discarded`: Order is discarded
- `Deleted`: Order is deleted
- `Errored`: Order has errors
- `Unknown`: Order status is unknown

#### OrderType
- `Itemised`: Detailed order with line items
- `Summary`: Summary order with totals

#### AllocationStatus
- `Unnotified`: Allocation is not yet notified
- `Notified`: Allocation is notified
- `Invalid`: Allocation is invalid

#### AggregationType
- `total`: Total aggregation
- `shareholder`: Shareholder aggregation
- `nonshareholder`: Non-shareholder aggregation
