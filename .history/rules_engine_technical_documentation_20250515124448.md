# Rules Engine - Technical Documentation

## 1. Overview

This document provides a detailed technical overview of the Rules Engine, a Go-based application designed for automated allocation of financial products based on a configurable ruleset.

**Purpose:** To provide a flexible, scriptable, and auditable mechanism for allocating amounts, shares, or units for various financial instruments like IPOs, follow-ons, bonds, and T-bills.

**Core Technologies:**
*   **Go:** The primary programming language for the engine's logic.
*   **Tengo:** An embeddable scripting language for Go, used for defining dynamic rule conditions and logic. (`github.com/d5/tengo/v2`)
*   **Excel (XLSX):** Used as the primary input format for order data and associated tags. (`github.com/thedatashed/xlsxreader`)
*   **JSON:** Used for the main rule configuration file.
*   **Cap'n Proto:** Used for schema definitions (e.g., `proto/schema.capnp`), though its direct runtime use in the core allocation logic is less prominent than Tengo scripting and direct Go structs.

## 2. Directory Structure

A brief overview of the key directories within the `rulesengine` module:

*   `cmd/rulesengine/`: Contains the main application entry point (`main.go`).
*   `pkg/`: Contains the core library code, broken into sub-packages:
    *   `common/`: Defines common data structures like `OrderRow`.
    *   `config/`: Handles parsing and validation of the JSON rule configuration.
    *   `exec/`: Contains the rule execution logic, Tengo script generation, and action implementations.
    *   `hydrate/`: (Indirectly used via `xls.XlsReader` which implements `hydrate.Hydrator`) Defines interfaces for data hydration.
    *   `xls/`: Provides utilities for reading and parsing data from XLSX files.
    *   Other packages like `server/`, `fetcher/`, `static_config/` exist but are less central to the core allocation rule processing described herein.
*   `proto/`: Contains Cap'n Proto schema definitions. The `schema.capnp` file defines data structures, and `schema.capnp.go` is the generated Go code.
*   `integration_tests/`: Contains integration tests for the rules engine (not detailed in this document).
*   `go.mod`, `go.sum`: Go module management files. 