# RetailBook UI – Product Requirements

## Overview

RetailBook is a B2B web application for managing, distributing, and settling financial offers (such as IPOs, bonds, and follow-ons) between banks and retail intermediaries. The platform supports offer creation, order management, wall crossing, notifications, settlements, and team management, with robust authentication and role-based access.

---

## User Roles & Permissions

- **Bank (+bank)**: Can create and manage offers, view and manage orders, allocations, settlements, and team members.
- **Retail (+retail)**: Can review offers, place orders, and manage their own team.
- **Roles**: Each org type has `editor` and `manager` roles. Managers can manage team members.

---

## Core Functionalities

### 1. Authentication & Authorization

- Azure B2C via NextAuth.
- All pages are protected; session-based access.
- Role and org-type based access control.

```mermaid
flowchart TD
    A[User visits app] --> B{Authenticated?}
    B -- No --> C[Redirect to Azure B2C login]
    B -- Yes --> D[Load user dashboard]
```

---

### 2. Offer Lifecycle Management

#### Offer Types

- Follow On
- Retail Bond
- IPO

#### Offer Statuses

- Building, Pre-Launch, Applications Open, Applications Closed, Allocating, Allocated, Instructions Sent, Settled, Abandoned, On Hold variants

#### Offer Flow

```mermaid
flowchart TD
    A[Create Offer] --> B[Pre-Launch]
    B --> C[Applications Open]
    C --> D[Applications Closed]
    D --> E[Allocating]
    E --> F[Allocated]
    F --> G[Instructions Sent]
    G --> H[Settled]
    D --> I[Abandoned]
    B --> J[On Hold]
    C --> J
```

- Offers have registration, open, and close dates.
- Offers can be edited, viewed, and have documents attached.
- Status transitions are managed by the bank user.

---

### 3. Wall Crossing

- Invite intermediaries to receive inside information before public launch.
- Track responses (accept/reject).
- Manage wall crossing status per invitee.

```mermaid
flowchart TD
    A[Bank sends wall crossing invite] --> B[Intermediary receives invite]
    B --> C{Accept?}
    C -- Yes --> D[Access inside info]
    C -- No --> E[No access]
```

---

### 4. Order Management

- Retail users can place orders on open offers.
- Orders can be aggregate or detailed.
- Orders have statuses: Unconfirmed, Pending, Accepted, Rejected, etc.
- Orders can be edited or deleted before close.

```mermaid
flowchart TD
    A[Offer Applications Open] --> B[Retail places order]
    B --> C[Order status: Pending]
    C --> D[Bank reviews order]
    D --> E{Accept/Reject}
    E -- Accept --> F[Order Accepted]
    E -- Reject --> G[Order Rejected]
```

---

### 5. Allocations

- After applications close, bank allocates securities to orders.
- Allocations can be notified to intermediaries.
- Allocation statuses: Unnotified, Notified, Invalid.

---

### 6. Settlements

- After allocation, settlement instructions are generated.
- Settlement obligations and books are managed.
- Users can view and amend settlement instructions.

---

### 7. Notifications

- In-app notifications for offer updates, allocations, wall crossing, etc.
- Users can mark notifications as read/unread.

---

### 8. Team & Organisation Management

- Managers can invite, edit, and remove team members.
- Team roles and statuses are managed.

---

### 9. Documents

- Offers can have documents (e.g., Retail Offer Notice) attached.
- Users can download/view documents.

---

### 10. Metrics & Reporting

- Metrics dashboard for offer activity, order volumes, allocations, etc.
- Data visualized with charts.

---

## UI Components

- **OfferCard**: Summary of offer status, price, applications, etc.
- **SettlementCard**: Settlement details and actions.
- **Definition/TimeDefinition**: Key-value display with optional countdowns.
- **Modals**: For confirmations, forms, and info.
- **Data Grids**: For orders, allocations, notifications.

---

## API & Data

- All API calls require auth token.
- Main entities: Offer, Order, Allocation, Settlement, Notification, User, Organisation.
- API endpoints for CRUD operations on all entities.

---

## Testing

- Playwright for E2E tests.
- Authenticated test accounts for bank and retail.
- Tests for all major flows: auth, offer creation, order placement, allocation, settlement.

---

## Example: Offer Status Flow

```mermaid
flowchart TD
    A[Offer Created] --> B[Pre-Launch]
    B --> C[Applications Open]
    C --> D[Applications Closed]
    D --> E[Allocating]
    E --> F[Allocated]
    F --> G[Instructions Sent]
    G --> H[Settled]
    D --> I[Abandoned]
```

---

## Example: Order Placement

```mermaid
flowchart TD
    A[User views open offer] --> B[User fills order form]
    B --> C[Order submitted]
    C --> D[Order status: Pending]
    D --> E[Bank reviews]
    E --> F{Accept/Reject}
    F -- Accept --> G[Order Accepted]
    F -- Reject --> H[Order Rejected]
```

---

## Example: Wall Crossing

```mermaid
flowchart TD
    A[Bank initiates wall crossing] --> B[Invite sent to intermediary]
    B --> C{Intermediary responds}
    C -- Accept --> D[Access inside info]
    C -- Reject --> E[No access]
```

---

## Non-Functional Requirements

- Responsive, accessible UI (Tailwind, Headless UI).
- Secure authentication and data access.
- Robust error handling and user feedback (toasts, modals).
- Type-safe (TypeScript).
- Automated E2E testing.

---

## Glossary

- **Offer**: A financial product being distributed (IPO, bond, etc.).
- **Order**: Application for allocation in an offer.
- **Allocation**: Distribution of securities to orders.
- **Settlement**: Final transfer of securities and cash.
- **Wall Crossing**: Pre-launch sharing of inside information with select intermediaries.

---

Let me know if you want to expand any section, add more diagrams, or focus on a specific flow!
