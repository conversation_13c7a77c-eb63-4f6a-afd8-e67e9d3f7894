---
query: Create a C4 diagram that shows a suite of products and their features as well
  as a hierarchy
generationTime: 2025-04-10T08:48:37.855Z
---
C4Context
title Suite of Products and Features

Enterprise_Boundary(suite, "Product Suite") {
  System(productA, "Product A", "Core product for managing tasks.")
  System(productB, "Product B", "Analytics and reporting tool.")
  
  System_Boundary(featuresA, "Features of Product A") {
    System(featureA1, "Feature A1", "Task creation and management.")
    System(featureA2, "Feature A2", "Collaboration tools.")
  }
  
  System_Boundary(featuresB, "Features of Product B") {
    System(featureB1, "Feature B1", "Data visualization.")
    System(featureB2, "Feature B2", "Custom reporting.")
  }
}

Rel(productA, productB, "Integrates with")
Rel(featureA1, featureB1, "Exports data to")
Rel(featureA2, featureB2, "Collaborates with")