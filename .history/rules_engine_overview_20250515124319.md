# Understanding Our Allocation Rules Engine

## Introduction

Hello [Order and Settlement Manager Name - Placeholder],

This document is designed to give you a clear understanding of our automated Allocation Rules Engine. Think of this engine as a dedicated, highly efficient assistant that helps us determine how financial products (like IPO shares, bonds, etc.) are allocated to different orders.

Its main purpose is to ensure that allocations are carried out consistently, accurately, and according to the specific rules and policies we define. This helps us save time, reduce manual errors, and maintain fairness in the allocation process, especially when demand is high.

## How it Works: A Bird's Eye View

Imagine you have a very meticulous assistant who is excellent at math and following instructions to the letter. The Rules Engine works much like that:

1.  **Inputs (What it needs to start):**
    *   **Order Details:** A list of all orders that need allocation. This typically includes who placed the order, what product they want, and how much they've requested. This information often comes from Excel spreadsheets.
    *   **The Rulebook:** A set of predefined instructions that tell the engine exactly how to make allocation decisions under various circumstances.

2.  **Processing (How it "thinks"):**
    *   The engine takes each order and compares it against the rules in the Rulebook.
    *   It applies these rules systematically. For example, a rule might say, "For VIP clients, allocate at least 50% of their requested amount, provided shares are available."

3.  **Outputs (What it produces):**
    *   **Allocation Results:** A finalized list showing how much of the product has been allocated to each order.

Here's a simple visual:

```mermaid
graph TD
    A[Input Data: Order Lists & Rulebook] --> B{Rules Engine};
    B --> C[Allocation Results: Who Gets What];
```

## The "Brain" of the Engine: Rules and Configuration

The real power of the engine comes from its "Rulebook." This isn't a physical book, but rather a digital configuration file (a JSON file, for the technically curious) that our team maintains. This Rulebook contains all the specific instructions the engine follows.

*   **What are Rules?**
    Rules are precise instructions that guide allocation decisions. For example:
    *   "Orders tagged as 'Priority' should be processed first."
    *   "If an order is for less than 100 units, allocate it in full."
    *   "For 'Bond X', no single order should receive more than 10% of the total available units."

*   **How Rules are Defined:**
    Each rule generally has two parts:
    1.  **Condition:** This is the "IF" part. It specifies *when* a rule should apply. Conditions can be based on various factors, such as:
        *   **Order Tags:** Special labels you might add to orders in your Excel file (e.g., "VIP," "Employee," "SmallCapFund"). The engine can read these tags and use them to make decisions.
        *   **Order Size:** Whether the order is large or small.
        *   **Investor Type:** (If this data is available and tagged).
        *   ...and many other criteria.
    2.  **Action:** This is the "THEN" part. It specifies *what* the engine should do if the condition is met (we'll cover specific actions in the next section).

*   **A Flexible Rule Language:**
    To allow for very specific and sometimes complex conditions, the engine uses an internal scripting language (called Tengo). This gives us a lot of flexibility to define precise criteria, rather than being limited to very simple choices. You don't need to know this language, but it's what gives the engine its smarts to handle diverse scenarios.

## Key Actions the Engine Can Perform (The "Tools" it Uses)

When a rule's condition is met, the engine performs an "action." These are the main tools the engine has at its disposal to allocate units/shares:

1.  **Percentage Fill (`applyFill`):**
    *   **What it does:** Allocates a specific percentage of an order's requested quantity.
    *   **Example:** "Allocate 75% of the requested shares to all orders in the 'Retail Investor' category."
    *   **Parameter:** The percentage to apply (e.g., 75 for 75%).

2.  **Minimum Unit Fill (`minFill`):**
    *   **What it does:** Ensures an order receives at least a certain number of units, if possible.
    *   **Example:** "Ensure all 'Preferred Client' orders receive a minimum of 500 units, if their original request was for at least that many."
    *   **Parameter:** The minimum number of units.

3.  **Minimum Cash Value Fill (`minCashFill`):**
    *   **What it does:** Similar to Minimum Unit Fill, but aims for a minimum total cash value for the allocation.
    *   **Example:** "For orders participating in the 'New Fund Launch', ensure the allocation value is at least $5,000."
    *   **Parameter:** The minimum cash value.

4.  **Pro-Rata Distribution (`proRata`):**
    *   **What it does:** This is a common method for fairly distributing a limited supply of units among many orders. It allocates to each order based on its size relative to the total size of all orders being considered for this pro-rata step.
    *   **Example:** "After initial high-priority allocations, distribute the remaining 1,000,000 shares on a pro-rata basis among all remaining eligible orders."
    *   **Parameters:** This action considers the total available for allocation and the total demand from eligible orders to calculate fair shares.

These actions can be combined and sequenced through different rules to achieve complex allocation outcomes.

Here's a conceptual diagram of how some of these actions might look:

```mermaid
pie title Share Distribution Example (Pro-Rata)
    "Order A (40%)" : 40
    "Order B (30%)" : 30
    "Order C (20%)" : 20
    "Order D (10%)" : 10
```

```mermaid
graph BT
    subgraph Percentage Fill Example
        O[Order Request: 1000 units] -->|Rule: Fill 60%| A[Allocated: 600 units]
    end
    subgraph Minimum Unit Fill Example
        R[Order Request: 300 units] -->|Rule: Min 500 units| AL[Allocated: 300 units (cannot exceed request)]
        S[Order Request: 800 units] -->|Rule: Min 500 units| ALL[Allocated: 500 units (or more, up to request)]
    end
``` 