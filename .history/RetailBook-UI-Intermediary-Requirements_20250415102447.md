# RetailBook UI - Intermediary Product Requirements

## Table of ContentsCan you create a detail product requirements markdown document for all the functionality in @retail-book-ui/ 
Include mermaidjs diagrams that use the flowchart syntax 
1. [Introduction](#introduction)
2. [Intermediary User Overview](#intermediary-user-overview)
3. [User Roles and Permissions](#user-roles-and-permissions)
4. [Core Intermediary Features](#core-intermediary-features)
   - [Offer Discovery and Review](#offer-discovery-and-review)
   - [Order Management](#order-management)
   - [Allocation Review](#allocation-review)
   - [Settlement Processing](#settlement-processing)
   - [Team Management](#team-management)
   - [Notifications](#notifications)
5. [User Flows](#user-flows)
6. [Interface Requirements](#interface-requirements)
7. [Integration Points](#integration-points)
8. [Appendix](#appendix)

## Introduction

This document outlines the product requirements specifically for intermediary users of the RetailBook platform. Intermediaries are retail brokers who review offers created by issuers (banks) and place orders on behalf of their clients. This document focuses on the features, workflows, and requirements that are specific to or primarily used by intermediary users.

## Intermediary User Overview

Intermediaries in RetailBook are financial institutions or brokers that act as middlemen between issuers (banks) and retail investors. Their primary functions include:

1. Reviewing offers published by issuers
2. Placing orders on behalf of their clients
3. Managing their order book
4. Reviewing allocations
5. Completing settlement processes

```mermaid
flowchart LR
    A[Issuers/Banks] -->|Create Offers| B[RetailBook Platform]
    B -->|View Offers| C[Intermediaries]
    C -->|Place Orders| B
    B -->|Process Orders| A
    A -->|Create Allocations| B
    B -->|View Allocations| C
    C -->|Complete Settlement| B
    B -->|Finalize Settlement| A
```

## User Roles and Permissions

Intermediary organizations have two types of user roles:

### Editor Role
- View available offers
- Review offer details and documents
- Place orders on offers
- View and manage their own orders
- View allocations for their orders
- View settlement instructions
- Access metrics and reports for their organization

### Manager Role
All Editor permissions plus:
- Manage team members (add, edit, remove)
- View team activity
- Configure organization settings
- Access advanced reporting

## Core Intermediary Features

### Offer Discovery and Review

Intermediaries need to discover, review, and understand offers before placing orders.

#### Requirements:

1. **Offer Dashboard**
   - View all available offers in a dashboard format
   - Filter offers by status, type, and other criteria
   - Sort offers by various attributes (close date, name, etc.)

2. **Offer Detail View**
   - View comprehensive offer details including:
     - Basic information (name, type, issuer)
     - Key dates (registration, open, close)
     - Financial details (price, minimum order amount)
     - Terms and conditions
   - Access offer documents
   - View offer status and timeline

3. **Document Access**
   - Download offer documents (prospectus, terms, etc.)
   - View document history and versions
   - Receive notifications for document updates

```mermaid
flowchart TD
    A[Intermediary Dashboard] --> B[Offers List]
    B --> C[Filter/Sort Offers]
    B --> D[Select Offer]
    D --> E[Offer Detail View]
    E --> F[Basic Information]
    E --> G[Financial Details]
    E --> H[Key Dates]
    E --> I[Documents]
    I --> J[Download Documents]
    E --> K[Place Order]
```

### Order Management

Intermediaries need to place, track, and manage orders on behalf of their clients.

#### Requirements:

1. **Order Creation**
   - Place aggregate orders (summary of multiple client orders)
   - Place detailed orders (individual line items for each client)
   - Upload orders via spreadsheet template
   - Validate orders against offer criteria
   - Submit orders for processing

2. **Order Types**
   - **Aggregate Orders**: Summary-level orders with totals for:
     - Shareholding (existing shareholders)
     - Non-shareholding (new investors)
   - **Detailed Orders**: Line-item level with client references

3. **Order Tracking**
   - View order status (pending, accepted, rejected)
   - View order history and changes
   - Receive notifications for order status changes
   - Download order details and confirmations

4. **Order Modification**
   - Update pending orders
   - Replace existing orders with new versions
   - Cancel orders (if permitted by offer status)

```mermaid
flowchart TD
    A[Select Offer] --> B{Order Type}
    B -->|Aggregate| C[Enter Summary Data]
    B -->|Detailed| D[Enter Line Items]
    D -->|Alternative| E[Upload Spreadsheet]
    C --> F[Submit Order]
    D --> F
    E --> F
    F --> G[Order Pending]
    G --> H{Issuer Review}
    H -->|Accept| I[Order Accepted]
    H -->|Reject| J[Order Rejected]
    G --> K[Update Order]
    K --> G
```

### Allocation Review

Intermediaries need to review allocations made by issuers against their orders.

#### Requirements:

1. **Allocation Dashboard**
   - View allocations for all orders
   - Filter and sort allocations
   - See allocation status (unnotified, notified)

2. **Allocation Details**
   - View detailed allocation information
   - Compare allocations against original orders
   - Download allocation details
   - View allocation history

3. **Client Allocation**
   - Tools to help distribute allocations to individual clients
   - Download client allocation templates
   - Upload completed client allocations

```mermaid
flowchart TD
    A[Intermediary Dashboard] --> B[Allocations List]
    B --> C[Filter/Sort Allocations]
    B --> D[Select Allocation]
    D --> E[Allocation Detail View]
    E --> F[Compare with Original Order]
    E --> G[Download Allocation Details]
    E --> H[Client Allocation Tools]
    H --> I[Download Template]
    H --> J[Upload Client Allocations]
```

### Settlement Processing

Intermediaries need to complete the settlement process for allocated offers.

#### Requirements:

1. **Settlement Instructions**
   - View settlement instructions for each allocation
   - Download settlement details
   - Confirm settlement readiness

2. **Settlement Confirmation**
   - Confirm settlement completion
   - Upload settlement evidence (if required)
   - View settlement history

```mermaid
flowchart TD
    A[Allocation Received] --> B[View Settlement Instructions]
    B --> C[Download Settlement Details]
    C --> D[Process Settlement Internally]
    D --> E[Confirm Settlement Completion]
    E --> F[Upload Settlement Evidence]
    F --> G[Settlement Completed]
```

### Team Management

Intermediary managers need to manage their team members and access.

#### Requirements:

1. **Team Dashboard**
   - View all team members
   - See team member roles and permissions
   - Monitor team activity

2. **User Management**
   - Add new team members
   - Edit team member details
   - Remove team members
   - Adjust permissions

```mermaid
flowchart TD
    A[Manager Dashboard] --> B[Team Management]
    B --> C[View Team Members]
    B --> D[Add Team Member]
    B --> E[Edit Team Member]
    B --> F[Remove Team Member]
    D --> G[Set Permissions]
    E --> G
```

### Notifications

Intermediaries need to stay informed about important events and updates.

#### Requirements:

1. **Notification Center**
   - View all notifications
   - Filter notifications by type
   - Mark notifications as read
   - Set notification preferences

2. **Notification Types**
   - New offer available
   - Offer status changes
   - Document updates
   - Order status changes
   - Allocation received
   - Settlement instructions available

```mermaid
flowchart TD
    A[Notification Center] --> B[View All Notifications]
    A --> C[Filter Notifications]
    A --> D[Mark as Read]
    A --> E[Notification Preferences]
    E --> F[Email Notifications]
    E --> G[In-App Notifications]
```

## User Flows

### Offer Review and Order Placement Flow

```mermaid
flowchart TD
    A[Login] --> B[Dashboard]
    B --> C[View Available Offers]
    C --> D[Select Offer]
    D --> E[Review Offer Details]
    E --> F[Review Documents]
    F --> G[Decision to Place Order]
    G -->|Yes| H{Order Type}
    G -->|No| C
    H -->|Aggregate| I[Enter Summary Data]
    H -->|Detailed| J[Enter Line Items/Upload Spreadsheet]
    I --> K[Submit Order]
    J --> K
    K --> L[Receive Order Confirmation]
    L --> M[Monitor Order Status]
```

### Allocation and Settlement Flow

```mermaid
flowchart TD
    A[Login] --> B[Dashboard]
    B --> C[View Allocations]
    C --> D[Select Allocation]
    D --> E[Review Allocation Details]
    E --> F[Download Allocation Information]
    F --> G[Process Client Allocations]
    G --> H[View Settlement Instructions]
    H --> I[Process Settlement]
    I --> J[Confirm Settlement Completion]
```

## Interface Requirements

### Dashboard

The intermediary dashboard should provide:
- Summary of available offers
- Recent orders and their status
- Recent allocations
- Notifications
- Quick access to frequently used functions

### Offer Listing

The offer listing should include:
- Offer name and issuer
- Offer type
- Key dates (open, close)
- Status
- Quick action buttons

### Order Forms

Order forms should include:
- Clear distinction between aggregate and detailed order options
- Validation of inputs against offer criteria
- Clear error messages
- Preview before submission
- Confirmation after submission

### Allocation Views

Allocation views should include:
- Comparison with original order
- Clear breakdown of allocation amounts
- Download options
- Settlement instruction access

## Integration Points

### Data Import/Export

- Spreadsheet templates for detailed orders
- CSV/Excel export of orders and allocations
- PDF export of confirmations and reports

### Notifications

- Email notifications for critical events
- In-app notifications for all events
- Optional SMS notifications for urgent items

### API Access

- Potential API access for high-volume intermediaries to integrate with their systems

## Appendix

### Data Models

#### Order Data Model
```typescript
type TOrder = {
  id: string;
  order_type: EOrderType; // AGGREGATE or DETAILED
  order_date: string;
  status: EOrderStatus; // PENDING, ACCEPTED, REJECTED, etc.
  offer_id: string;
  offer_name: string;
  order_book_id?: string;
  update_date: string;
  non_shareholding?: IOrderBase; // For new investors
  shareholding?: IOrderBase; // For existing shareholders
  totals: IOrderBase;
  lineItems?: TOrderLineItem[]; // For detailed orders
  intermediary?: string;
  intermediary_system_id?: string;
  entered_by?: string;
};

// For detailed orders
type TOrderLineItem = {
  client_order_ref: string;
  notional_value: Decimal;
  commission_due: boolean;
  tax_wrapper: boolean;
  existing_holding?: Decimal;
};
```

#### Allocation Data Model
```typescript
type TAllocation = {
  id: string;
  entered_by?: string;
  date_created: string;
  totals: IAllocationBase;
  shareholdings?: IAllocationBase;
  non_shareholdings?: IAllocationBase;
  allocation_book_id?: string;
  status: EAllocationStatus; // UNNOTIFIED, NOTIFIED, INVALID
};

interface IAllocationBase {
  applications?: Decimal;
  notional_value?: Decimal;
  order_quantity?: Decimal;
  num_orders?: Decimal;
  allocated_orders?: Decimal;
  unallocated_orders?: Decimal;
  allocation_value?: Decimal;
  allocation_quantity?: Decimal;
}
```

### Order Status Flow

```mermaid
stateDiagram-v2
    [*] --> Pending: Order Created
    Pending --> Accepted: Issuer Accepts
    Pending --> Rejected: Issuer Rejects
    Pending --> ReplacePending: Update Initiated
    ReplacePending --> Replaced: Update Completed
    Pending --> Deleted: Order Cancelled
    Accepted --> [*]
    Rejected --> [*]
    Replaced --> [*]
    Deleted --> [*]
```
