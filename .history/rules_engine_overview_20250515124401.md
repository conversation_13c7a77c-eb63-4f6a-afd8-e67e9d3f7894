# Understanding Our Allocation Rules Engine

## Introduction

Hello [Order and Settlement Manager Name - Placeholder],

This document is designed to give you a clear understanding of our automated Allocation Rules Engine. Think of this engine as a dedicated, highly efficient assistant that helps us determine how financial products (like IPO shares, bonds, etc.) are allocated to different orders.

Its main purpose is to ensure that allocations are carried out consistently, accurately, and according to the specific rules and policies we define. This helps us save time, reduce manual errors, and maintain fairness in the allocation process, especially when demand is high.

## How it Works: A Bird's Eye View

Imagine you have a very meticulous assistant who is excellent at math and following instructions to the letter. The Rules Engine works much like that:

1.  **Inputs (What it needs to start):**
    *   **Order Details:** A list of all orders that need allocation. This typically includes who placed the order, what product they want, and how much they've requested. This information often comes from Excel spreadsheets.
    *   **The Rulebook:** A set of predefined instructions that tell the engine exactly how to make allocation decisions under various circumstances.

2.  **Processing (How it "thinks"):**
    *   The engine takes each order and compares it against the rules in the Rulebook.
    *   It applies these rules systematically. For example, a rule might say, "For VIP clients, allocate at least 50% of their requested amount, provided shares are available."

3.  **Outputs (What it produces):**
    *   **Allocation Results:** A finalized list showing how much of the product has been allocated to each order.

Here's a simple visual:

```mermaid
graph TD
    A[Input Data: Order Lists & Rulebook] --> B{Rules Engine};
    B --> C[Allocation Results: Who Gets What];
```

## The "Brain" of the Engine: Rules and Configuration

The real power of the engine comes from its "Rulebook." This isn't a physical book, but rather a digital configuration file (a JSON file, for the technically curious) that our team maintains. This Rulebook contains all the specific instructions the engine follows.

*   **What are Rules?**
    Rules are precise instructions that guide allocation decisions. For example:
    *   "Orders tagged as 'Priority' should be processed first."
    *   "If an order is for less than 100 units, allocate it in full."
    *   "For 'Bond X', no single order should receive more than 10% of the total available units."

*   **How Rules are Defined:**
    Each rule generally has two parts:
    1.  **Condition:** This is the "IF" part. It specifies *when* a rule should apply. Conditions can be based on various factors, such as:
        *   **Order Tags:** Special labels you might add to orders in your Excel file (e.g., "VIP," "Employee," "SmallCapFund"). The engine can read these tags and use them to make decisions.
        *   **Order Size:** Whether the order is large or small.
        *   **Investor Type:** (If this data is available and tagged).
        *   ...and many other criteria.
    2.  **Action:** This is the "THEN" part. It specifies *what* the engine should do if the condition is met (we'll cover specific actions in the next section).

*   **A Flexible Rule Language:**
    To allow for very specific and sometimes complex conditions, the engine uses an internal scripting language (called Tengo). This gives us a lot of flexibility to define precise criteria, rather than being limited to very simple choices. You don't need to know this language, but it's what gives the engine its smarts to handle diverse scenarios.

## Key Actions the Engine Can Perform (The "Tools" it Uses)

When a rule's condition is met, the engine performs an "action." These are the main tools the engine has at its disposal to allocate units/shares:

1.  **Percentage Fill (`applyFill`):**
    *   **What it does:** Allocates a specific percentage of an order's requested quantity.
    *   **Example:** "Allocate 75% of the requested shares to all orders in the 'Retail Investor' category."
    *   **Parameter:** The percentage to apply (e.g., 75 for 75%).

2.  **Minimum Unit Fill (`minFill`):**
    *   **What it does:** Ensures an order receives at least a certain number of units, if possible.
    *   **Example:** "Ensure all 'Preferred Client' orders receive a minimum of 500 units, if their original request was for at least that many."
    *   **Parameter:** The minimum number of units.

3.  **Minimum Cash Value Fill (`minCashFill`):**
    *   **What it does:** Similar to Minimum Unit Fill, but aims for a minimum total cash value for the allocation.
    *   **Example:** "For orders participating in the 'New Fund Launch', ensure the allocation value is at least $5,000."
    *   **Parameter:** The minimum cash value.

4.  **Pro-Rata Distribution (`proRata`):**
    *   **What it does:** This is a common method for fairly distributing a limited supply of units among many orders. It allocates to each order based on its size relative to the total size of all orders being considered for this pro-rata step.
    *   **Example:** "After initial high-priority allocations, distribute the remaining 1,000,000 shares on a pro-rata basis among all remaining eligible orders."
    *   **Parameters:** This action considers the total available for allocation and the total demand from eligible orders to calculate fair shares.

These actions can be combined and sequenced through different rules to achieve complex allocation outcomes.

Here's a conceptual diagram of how some of these actions might look:

```mermaid
pie title Share Distribution Example (Pro-Rata)
    "Order A (40%)" : 40
    "Order B (30%)" : 30
    "Order C (20%)" : 20
    "Order D (10%)" : 10
```

```mermaid
graph BT
    subgraph Percentage Fill Example
        O[Order Request: 1000 units] -->|Rule: Fill 60%| A[Allocated: 600 units]
    end
    subgraph Minimum Unit Fill Example
        R[Order Request: 300 units] -->|Rule: Min 500 units| AL[Allocated: 300 units (cannot exceed request)]
        S[Order Request: 800 units] -->|Rule: Min 500 units| ALL[Allocated: 500 units (or more, up to request)]
    end
```

## Connecting with Your World: Excel and the Engine

You mentioned that you currently use Excel to apply and adjust rules, and the good news is that Excel still plays a vital role with our Rules Engine.

*   **Excel as an Input Source:**
    *   **Order Data:** The primary way the engine receives order information (like Order ID, quantity requested, value, etc.) is by reading data directly from Excel spreadsheets. The system is designed to parse specific columns from these sheets.
    *   **Order Tags:** A key feature is the ability to add "Tags" to orders. These are essentially labels (e.g., "VIP_Client", "Employee_StockPlan", "Institutional") that you can define in a column in your Excel sheet. The Rules Engine can then use these tags in its rule conditions. For instance, a rule might say: "If an order has the tag 'VIP_Client', apply Action X."

*   **How Adjustments and Rule Application Works:**
    *   **Your Current Process:** You apply rules and make adjustments using Excel. The Rules Engine aims to automate much of the direct application of these rules.
    *   **The Engine's Role:** The engine takes the detailed rules (defined in its digital "Rulebook" or configuration) and applies them to the data it reads from your Excel files.
    *   **Managing Rule Parameters:** While complex rule *logic* is in the engine's configuration, the *parameters* for these rules (like the specific percentage for a fill, or the list of clients considered 'VIP') are often managed in a way that can be updated easily. It's possible that some of these parameters are themselves sourced from a master Excel sheet which is then used by the technical team to update the engine's main Rulebook.
    *   **Direct Excel Adjustments:** The system can read an "allocation" column from your spreadsheet. This means that if you need to make manual overrides or pre-allocations in Excel for specific orders, the engine can potentially take these into account as a starting point or as fixed values, depending on how the rules are set up.

The goal is for the engine to handle the heavy lifting of rule application based on the data you prepare and manage in Excel, along with the centrally defined Rulebook.

## What the Engine Handles

Our Rules Engine is designed to be versatile. While your primary focus might be on specific products, the engine can theoretically handle allocations for a variety of financial instruments, including:

*   **IPOs (Initial Public Offerings)**
*   **Follow-ons**
*   **Bonds**
*   **T-bills (Treasury Bills)**
*   And potentially other products where a systematic, rule-based allocation is required.

The key is that as long as order data can be provided (ideally via the standard Excel format) and the allocation rules can be defined in the Rulebook, the engine can be adapted to the task.

## The Allocation Process: Step-by-Step (Simplified)

While the internal workings can be quite detailed, here's a simplified view of how the engine typically processes allocations:

1.  **Data Loading:** The engine first loads all the necessary order data, often from your Excel files. This includes order details and any special tags.
2.  **Initial Rule Pass (Ordered Rules):** The engine goes through a sequence of specific rules (`OrderedRules`). These are often designed to handle high-priority cases, specific client categories, or foundational allocation steps. Each order is checked against these rules, and if a rule's condition is met, its action is applied.
3.  **Allocation Freezing (Conceptual Checkpoint):** After the initial pass of ordered rules, the engine can be set to "freeze" the allocations made so far. This means these initial allocations are set, and subsequent rules will work with the remaining amounts and demand.
4.  **Global Rule Pass (Global Rules):** Next, the engine applies another set of rules (`GlobalRules`). These are often used for broader adjustments, like a pro-rata distribution of any remaining shares across all still-eligible orders, or applying overall caps.
5.  **Finalizing Allocations:** Once all rules have been processed, the engine will have a final allocated amount for each order. This information is then available for reporting or further processing.

Here's a visual representation of this flow:

```mermaid
sequenceDiagram
    participant Excel as Excel Data Input
    participant Engine as Rules Engine
    participant Rules_Config as Rulebook (Config)

    Excel->>Engine: Provide Order Data & Tags
    Rules_Config->>Engine: Provide Defined Rules

    Engine->>Engine: Phase 1: Process Ordered Rules (e.g., VIP, Min Fills)
    Note right of Engine: Orders are updated based on specific rules

    Engine->>Engine: Phase 2: Freeze Initial Allocations
    Note right of Engine: Allocations from Phase 1 are locked

    Engine->>Engine: Phase 3: Process Global Rules (e.g., Pro-Rata, Caps)
    Note right of Engine: Remaining shares distributed, final checks
    
    Engine-->>Output: Final Allocation Results
```

## Benefits for You and the Team

Adopting this Rules Engine brings several key advantages:

*   **Consistency and Accuracy:** Rules are applied uniformly to all orders, reducing the chance of human error or inconsistent treatment.
*   **Speed and Efficiency:** The engine can process a large volume of orders and complex rule sets much faster than manual methods, freeing up your time for oversight and handling exceptions.
*   **Fairness and Transparency:** With clearly defined (and configurable) rules, the allocation process becomes more transparent and demonstrably fair.
*   **Flexibility and Adaptability:** While the core engine is robust, the Rulebook can be updated to reflect new products, changing market conditions, or evolving business priorities. This means the engine can adapt without requiring a complete overhaul.
*   **Auditability:** The inputs (Excel data, Rulebook) and outputs (allocations) provide a clear trail for review and auditing purposes.

## Conclusion

The Rules Engine is a powerful tool designed to streamline and improve our allocation process. It leverages the data you manage in Excel and combines it with a flexible and configurable Rulebook to deliver consistent, fair, and efficient allocation results.

We understand that moving to a new system can bring questions. We encourage you to ask any questions you may have. The goal is to make this engine a valuable assistant that supports your expertise and makes the allocation process smoother for everyone.

---

*This document provides a non-technical overview. Specific technical details of the implementation are maintained by the development team.* 