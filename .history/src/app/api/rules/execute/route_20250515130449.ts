import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "~/server/db";
import { RulesEngineService } from "~/server/services/rulesEngine";
import { ruleConfigurations } from "~/server/db/schema/ruleConfigurations.schema";
import { ruleExecutionLogs } from "~/server/db/schema/ruleExecutionLogs.schema";
import { eq } from "drizzle-orm";

// Validation schema for request body
const executeRulesSchema = z.object({
	offerId: z.string().uuid(),
	ruleConfigurationId: z.string().uuid(),
});

export async function POST(request: Request) {
	try {
		// Authenticate the request (example - implement your own auth logic)
		const authHeader = request.headers.get("Authorization");
		if (
			!authHeader ||
			!authHeader.startsWith("Bearer ") ||
			authHeader.split(" ")[1] !== process.env.RULES_ENGINE_API_KEY
		) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Parse and validate request body
		const body = await request.json();
		const validationResult = executeRulesSchema.safeParse(body);

		if (!validationResult.success) {
			return NextResponse.json(
				{ error: "Invalid request", details: validationResult.error.format() },
				{ status: 400 },
			);
		}

		const { offerId, ruleConfigurationId } = validationResult.data;

		// Create execution log entry
		const [logEntry] = await db
			.insert(ruleExecutionLogs)
			.values({
				ruleConfigurationId,
				offerId,
				triggeredBy: "api_request", // Or extract from auth token
				status: "started",
				startedAt: new Date(),
			})
			.returning();

		// Fetch rule configuration
		const ruleConfig = await db.query.ruleConfigurations.findFirst({
			where: eq(ruleConfigurations.id, ruleConfigurationId),
		});

		if (!ruleConfig) {
			// Update log entry with error
			await db
				.update(ruleExecutionLogs)
				.set({
					status: "completed_error",
					endedAt: new Date(),
					logSummary: { error: "Rule configuration not found" },
				})
				.where(eq(ruleExecutionLogs.id, logEntry.id));

			return NextResponse.json(
				{ error: "Rule configuration not found" },
				{ status: 404 },
			);
		}

		// Update log status to in_progress
		await db
			.update(ruleExecutionLogs)
			.set({
				status: "in_progress",
			})
			.where(eq(ruleExecutionLogs.id, logEntry.id));

		// Initialize rules engine service
		const rulesEngineService = new RulesEngineService(db);

		// Execute rules
		const result = await rulesEngineService.executeRules(
			offerId,
			ruleConfig.configJson,
			10000, // Batch size - can be configured based on memory/performance
		);

		// Update log entry with success
		await db
			.update(ruleExecutionLogs)
			.set({
				status: "completed_success",
				endedAt: new Date(),
				ordersProcessedCount: result.ordersProcessed,
				ordersAffectedCount: result.ordersAffected,
				logSummary: {
					totalAllocated: result.totalAllocated,
					message: "Rules executed successfully",
				},
			})
			.where(eq(ruleExecutionLogs.id, logEntry.id));

		// Return success response
		return NextResponse.json({
			message: "Rules execution completed successfully",
			logId: logEntry.id,
			ordersProcessed: result.ordersProcessed,
			ordersAffected: result.ordersAffected,
			totalAllocated: result.totalAllocated,
		});
	} catch (error) {
		console.error("Error executing rules:", error);

		// If we have a log entry ID, update it with error
		const errorLogId = error.logEntryId;
		if (errorLogId) {
			await db
				.update(ruleExecutionLogs)
				.set({
					status: "completed_error",
					endedAt: new Date(),
					logSummary: {
						error: error.message,
						stack: error.stack,
					},
				})
				.where(eq(ruleExecutionLogs.id, errorLogId));
		}

		// Return error response
		return NextResponse.json(
			{
				error: "Error executing rules",
				message: error.message,
			},
			{ status: 500 },
		);
	}
}

// Optionally implement a GET handler to retrieve execution status
export async function GET(request: Request) {
	const url = new URL(request.url);
	const logId = url.searchParams.get("logId");

	if (!logId) {
		return NextResponse.json(
			{ error: "Missing logId parameter" },
			{ status: 400 },
		);
	}

	// Authenticate the request (example - implement your own auth logic)
	const authHeader = request.headers.get("Authorization");
	if (
		!authHeader ||
		!authHeader.startsWith("Bearer ") ||
		authHeader.split(" ")[1] !== process.env.RULES_ENGINE_API_KEY
	) {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	try {
		// Fetch log entry
		const logEntry = await db.query.ruleExecutionLogs.findFirst({
			where: eq(ruleExecutionLogs.id, logId),
		});

		if (!logEntry) {
			return NextResponse.json(
				{ error: "Log entry not found" },
				{ status: 404 },
			);
		}

		return NextResponse.json(logEntry);
	} catch (error) {
		console.error("Error fetching execution log:", error);
		return NextResponse.json(
			{ error: "Error fetching execution log", message: error.message },
			{ status: 500 },
		);
	}
}
