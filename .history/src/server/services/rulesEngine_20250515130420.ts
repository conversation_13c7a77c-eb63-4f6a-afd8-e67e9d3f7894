/**
 * Rules Engine Service
 *
 * A high-performance engine for allocating financial products based on configurable rules.
 * Designed to process 1M+ orders within 300 seconds in a serverless Next.js environment.
 */

import { and, eq, gt, inArray } from "drizzle-orm";
import type { PostgresJsDatabase } from "drizzle-orm/postgres-js";
import { orders } from "../db/schema/orders.schema";
import { Decimal } from "decimal.js";

// ===============================================================
// Type Definitions
// ===============================================================

/**
 * Rule expression - can be an action expression or condition expression
 */
export interface RuleExpr {
	raw: string;
	// For action expressions, these will be populated
	actionName?: string;
	actionArgs?: any[];
	// For condition expressions, this will be populated
	conditionFn?: (order: EnrichedOrder, state: ExecutionState) => boolean;
}

/**
 * Category definition for reusable conditions
 */
export interface RuleCategory {
	name: string;
	filter: RuleExpr;
}

/**
 * Condition that determines if a rule applies
 */
export interface RuleCondition {
	category?: string;
	expression?: RuleExpr;
}

/**
 * Definition of an action to be performed
 */
export interface RuleActionConfig {
	name: string;
	action: RuleExpr;
}

/**
 * A rule that applies an action when a condition is met
 */
export interface Rule {
	name: string;
	condition: RuleCondition;
	action: string;
	immediate: boolean;
	complete: boolean;
}

/**
 * A global rule applied after initial rules
 */
export interface OrderedGlobalRule {
	name: string;
	action: string;
}

/**
 * The complete rule configuration
 */
export interface RuleConfiguration {
	categories: RuleCategory[];
	actions: RuleActionConfig[];
	rules: Rule[];
	globalRules: OrderedGlobalRule[];
}

/**
 * Additional attributes stored in the order.attributes JSONB column
 */
export interface OrderAttributes {
	tags?: string[];
	intermediary?: string;
	taxWrapper?: boolean;
	commission?: boolean;
	applications?: number;
	existingHolding?: number;
	[key: string]: any;
}

/**
 * Order data enriched for rule processing
 */
export interface EnrichedOrder {
	// Base order fields
	id: string;
	offerId: string;
	partnerId: string;
	memberId?: string;
	status: string;
	quantity: Decimal;
	allocatedQuantity: Decimal;
	attributes: OrderAttributes;

	// Rule engine fields
	isProcessed?: boolean;
	__originalAllocatedQuantity?: Decimal; // Track changes for database updates
}

/**
 * Global state during rule execution
 */
export interface ExecutionState {
	ordersState: Map<string, EnrichedOrder>;
	allocatedSoFar: Decimal;
	allocatedBeforeGlobalRules: Decimal;
	demandBeforeEnd: Decimal;
	availableForAllocation: Decimal;
	offerDetails: {
		id: string;
		totalSize: Decimal;
		pricePerUnit?: number;
	};
}

/**
 * Action function signature
 */
export type ActionFunction = (
	order: EnrichedOrder,
	args: any[],
	state: ExecutionState,
	complete: boolean,
) => void;

// ===============================================================
// Rule Parser Component
// ===============================================================

/**
 * Parses and validates rule configurations
 */
export class RuleParser {
	/**
	 * Parse and validate a rule configuration
	 */
	parse(configJson: any): RuleConfiguration {
		// Validate schema structure
		if (!configJson.categories || !Array.isArray(configJson.categories)) {
			throw new Error("Invalid configuration: categories must be an array");
		}
		if (!configJson.actions || !Array.isArray(configJson.actions)) {
			throw new Error("Invalid configuration: actions must be an array");
		}
		if (!configJson.rules || !Array.isArray(configJson.rules)) {
			throw new Error("Invalid configuration: rules must be an array");
		}
		if (!configJson.globalRules || !Array.isArray(configJson.globalRules)) {
			configJson.globalRules = []; // Default to empty array
		}

		// Parse and pre-compile expressions
		const config: RuleConfiguration = {
			categories: this.parseCategories(configJson.categories),
			actions: this.parseActions(configJson.actions),
			rules: this.parseRules(configJson.rules),
			globalRules: this.parseGlobalRules(configJson.globalRules),
		};

		// Validate rule references
		this.validateReferences(config);

		return config;
	}

	private parseCategories(categories: any[]): RuleCategory[] {
		return categories.map((cat) => ({
			name: cat.name,
			filter: this.parseExpression(cat.filter, "condition"),
		}));
	}

	private parseActions(actions: any[]): RuleActionConfig[] {
		return actions.map((act) => ({
			name: act.name,
			action: this.parseExpression(act.action, "action"),
		}));
	}

	private parseRules(rules: any[]): Rule[] {
		return rules.map((rule) => ({
			name: rule.name,
			condition: this.parseCondition(rule.condition),
			action: rule.action,
			immediate: rule.immediate !== false, // Default to true if not specified
			complete: !!rule.complete, // Default to false if not specified
		}));
	}

	private parseGlobalRules(globalRules: any[]): OrderedGlobalRule[] {
		return globalRules.map((rule) => ({
			name: rule.name,
			action: rule.action,
		}));
	}

	private parseCondition(condition: any): RuleCondition {
		if (!condition) {
			throw new Error("Rule condition cannot be null or undefined");
		}

		const result: RuleCondition = {};

		if (condition.category) {
			result.category = condition.category;
		}

		if (condition.expression) {
			result.expression = this.parseExpression(
				condition.expression,
				"condition",
			);
		}

		if (!result.category && !result.expression) {
			throw new Error("Rule condition must have either category or expression");
		}

		return result;
	}

	private parseExpression(
		expr: string,
		type: "action" | "condition",
	): RuleExpr {
		if (!expr || typeof expr !== "string") {
			throw new Error(`Invalid ${type} expression: ${expr}`);
		}

		const result: RuleExpr = { raw: expr };

		if (type === "action") {
			// Action format: "actionName,arg1,arg2,..."
			const parts = expr.split(",");
			result.actionName = parts[0];
			result.actionArgs = parts.slice(1);
		} else if (type === "condition") {
			// For conditions, we'll compile them later in the ExpressionEvaluator
		}

		return result;
	}

	private validateReferences(config: RuleConfiguration): void {
		// Create lookup maps
		const categoryMap = new Map(config.categories.map((c) => [c.name, c]));
		const actionMap = new Map(config.actions.map((a) => [a.name, a]));

		// Validate category references in rules
		for (const rule of config.rules) {
			if (
				rule.condition.category &&
				!categoryMap.has(rule.condition.category)
			) {
				throw new Error(
					`Rule "${rule.name}" references undefined category "${rule.condition.category}"`,
				);
			}

			if (!actionMap.has(rule.action)) {
				throw new Error(
					`Rule "${rule.name}" references undefined action "${rule.action}"`,
				);
			}
		}

		// Validate action references in global rules
		for (const rule of config.globalRules) {
			if (!actionMap.has(rule.action)) {
				throw new Error(
					`Global rule "${rule.name}" references undefined action "${rule.action}"`,
				);
			}
		}
	}
}

// ===============================================================
// Expression Evaluator Component
// ===============================================================

/**
 * Evaluates JavaScript expressions for rule conditions
 */
export class ExpressionEvaluator {
	private compiledExpressions: Map<string, Function> = new Map();

	/**
	 * Compile and cache expressions for efficient evaluation
	 */
	compileExpressions(config: RuleConfiguration): void {
		// Compile category filters
		for (const category of config.categories) {
			this.compileExpression(category.filter);
		}

		// Compile direct rule expressions
		for (const rule of config.rules) {
			if (rule.condition.expression) {
				this.compileExpression(rule.condition.expression);
			}
		}
	}

	/**
	 * Evaluate a condition for an order
	 */
	evaluateCondition(
		condition: RuleCondition,
		categoryMap: Map<string, RuleCategory>,
		order: EnrichedOrder,
		state: ExecutionState,
	): boolean {
		// If using a category, get its filter expression
		if (condition.category) {
			const category = categoryMap.get(condition.category);
			if (!category) {
				throw new Error(`Category not found: ${condition.category}`);
			}
			return this.evaluateExpression(category.filter, order, state);
		}

		// Otherwise evaluate the direct expression
		if (condition.expression) {
			return this.evaluateExpression(condition.expression, order, state);
		}

		return false;
	}

	private compileExpression(expr: RuleExpr): void {
		try {
			// Create a function that evaluates the expression
			const fn = new Function(
				"order",
				"state",
				"tags",
				`try { return !!(${expr.raw}); } catch (e) { console.error('Expression error:', e); return false; }`,
			);

			// Store the compiled function in the expression
			expr.conditionFn = (order, state) => {
				// Create a tags helper object
				const tags = {
					has: (tagName: string) =>
						order.attributes.tags?.includes(tagName) || false,
					hasAny: (tagNames: string[]) =>
						tagNames.some((tag) => order.attributes.tags?.includes(tag)) ||
						false,
					hasAll: (tagNames: string[]) =>
						tagNames.every((tag) => order.attributes.tags?.includes(tag)) ||
						false,
				};

				return fn(order, state, tags);
			};

			// Also cache in the map
			this.compiledExpressions.set(expr.raw, fn);
		} catch (error) {
			throw new Error(
				`Failed to compile expression "${expr.raw}": ${error.message}`,
			);
		}
	}

	private evaluateExpression(
		expr: RuleExpr,
		order: EnrichedOrder,
		state: ExecutionState,
	): boolean {
		if (!expr.conditionFn) {
			throw new Error(`Expression not compiled: ${expr.raw}`);
		}

		try {
			return expr.conditionFn(order, state);
		} catch (error) {
			console.error(`Error evaluating expression "${expr.raw}":`, error);
			return false;
		}
	}
}

// ===============================================================
// Action Implementations
// ===============================================================

/**
 * Core allocation action implementations
 */
export class ActionImplementations {
	/**
	 * Get a map of all available actions
	 */
	getActionMap(): Map<string, ActionFunction> {
		return new Map<string, ActionFunction>([
			["applyFill", this.applyPctFill.bind(this)],
			["minFill", this.applyMinValFill.bind(this)],
			["minCashFill", this.applyMinCashFill.bind(this)],
			["proRata", this.proRata.bind(this)],
		]);
	}

	/**
	 * Allocate a percentage of the requested quantity
	 */
	applyPctFill(
		order: EnrichedOrder,
		args: any[],
		state: ExecutionState,
		complete: boolean,
	): void {
		if (order.isProcessed) {
			return;
		}

		// Get percentage from args
		const pct = new Decimal(args[0]);
		if (pct.isNaN() || pct.lt(0) || pct.gt(100)) {
			throw new Error(`Invalid percentage for applyFill: ${args[0]}`);
		}

		// Calculate allocation
		const allocation = order.quantity.mul(pct).div(100).floor();

		// Update order and state
		this.truncateAndUpdateState(order, allocation, state);

		// Mark as complete if requested and fully allocated
		if (complete || order.allocatedQuantity.eq(order.quantity)) {
			order.isProcessed = true;
		}
	}

	/**
	 * Allocate a minimum number of units
	 */
	applyMinValFill(
		order: EnrichedOrder,
		args: any[],
		state: ExecutionState,
		complete: boolean,
	): void {
		if (order.isProcessed) {
			return;
		}

		// Get minimum units from args
		const min = new Decimal(args[0]);
		if (min.isNaN() || min.lt(0)) {
			throw new Error(`Invalid minimum for minFill: ${args[0]}`);
		}

		// If min is greater than order quantity or we already have >= min, no allocation
		if (min.gt(order.quantity) || order.allocatedQuantity.gte(min)) {
			// No additional allocation
			return;
		}

		// Otherwise allocate the minimum
		this.truncateAndUpdateState(order, min, state);

		// Mark as complete if requested and fully allocated
		if (complete || order.allocatedQuantity.eq(order.quantity)) {
			order.isProcessed = true;
		}
	}

	/**
	 * Allocate based on a minimum cash value
	 */
	applyMinCashFill(
		order: EnrichedOrder,
		args: any[],
		state: ExecutionState,
		complete: boolean,
	): void {
		if (order.isProcessed || !order.allocatedQuantity.isZero()) {
			return; // Only apply to unallocated orders
		}

		// Get minimum cash value from args
		const minCash = new Decimal(args[0]);
		if (minCash.isNaN() || minCash.lt(0)) {
			throw new Error(`Invalid minimum cash for minCashFill: ${args[0]}`);
		}

		// Calculate price per unit
		const pricePerUnit = state.offerDetails.pricePerUnit || 1;

		// Calculate order value
		const orderValue = order.quantity.mul(pricePerUnit);

		// If min cash is greater than order value, no allocation
		if (minCash.gte(orderValue)) {
			return;
		}

		// Calculate units needed to meet min cash
		const unitsForMinCash = minCash.div(pricePerUnit).ceil();

		// Allocate
		this.truncateAndUpdateState(order, unitsForMinCash, state);

		// Mark as complete if requested and fully allocated
		if (complete || order.allocatedQuantity.eq(order.quantity)) {
			order.isProcessed = true;
		}
	}

	/**
	 * Perform pro-rata allocation
	 */
	proRata(
		order: EnrichedOrder,
		args: any[],
		state: ExecutionState,
		complete: boolean,
	): void {
		if (order.isProcessed || order.allocatedQuantity.eq(order.quantity)) {
			return;
		}

		// Get roundUp flag (default to false)
		const roundUp = args[0] === true || args[0] === "true";

		// Calculate remaining demand
		const totalRemainingDemand = state.demandBeforeEnd.minus(
			state.allocatedBeforeGlobalRules,
		);

		// If no remaining demand, nothing to do
		if (totalRemainingDemand.isZero()) {
			return;
		}

		// Calculate remaining to allocate
		const remainingToAllocate = state.availableForAllocation.minus(
			state.allocatedBeforeGlobalRules,
		);

		// If nothing left to allocate, nothing to do
		if (remainingToAllocate.isZero() || remainingToAllocate.isNegative()) {
			return;
		}

		// Calculate remaining for this order
		const remainingForOrder = order.quantity.minus(order.allocatedQuantity);

		// Calculate pro-rata share
		const proRataShare = remainingForOrder
			.div(totalRemainingDemand)
			.mul(remainingToAllocate);

		// Round as appropriate
		const allocation = roundUp ? proRataShare.ceil() : proRataShare.floor();

		// Allocate
		this.truncateAndUpdateState(order, allocation, state);

		// Mark as complete if requested and fully allocated
		if (complete || order.allocatedQuantity.eq(order.quantity)) {
			order.isProcessed = true;
		}
	}

	/**
	 * Helper to ensure allocation doesn't exceed order quantity
	 * and updates execution state.
	 */
	private truncateAndUpdateState(
		order: EnrichedOrder,
		allocation: Decimal,
		state: ExecutionState,
	): void {
		// Ensure we don't over-allocate
		const oldAllocation = order.allocatedQuantity || new Decimal(0);
		const remainingForOrder = order.quantity.minus(oldAllocation);

		// Calculate the actual additional allocation (don't exceed order quantity)
		const additionalAllocation = Decimal.min(allocation, remainingForOrder);

		// If nothing to allocate, return early
		if (additionalAllocation.isZero()) {
			return;
		}

		// Update order
		order.allocatedQuantity = oldAllocation.plus(additionalAllocation);

		// Update state
		state.allocatedSoFar = state.allocatedSoFar.plus(additionalAllocation);
	}
}

// ===============================================================
// Rule Executor Component
// ===============================================================

/**
 * Core engine that applies rules to orders
 */
export class RuleExecutor {
	private expressionEvaluator: ExpressionEvaluator;
	private actionMap: Map<string, ActionFunction>;
	private categoryMap: Map<string, RuleCategory>;
	private actionConfigMap: Map<string, RuleActionConfig>;

	constructor(
		private config: RuleConfiguration,
		actionImplementations: ActionImplementations,
		expressionEvaluator: ExpressionEvaluator,
	) {
		this.expressionEvaluator = expressionEvaluator;
		this.actionMap = actionImplementations.getActionMap();

		// Create lookup maps
		this.categoryMap = new Map(config.categories.map((c) => [c.name, c]));
		this.actionConfigMap = new Map(config.actions.map((a) => [a.name, a]));

		// Pre-compile all expressions
		expressionEvaluator.compileExpressions(config);
	}

	/**
	 * Execute rules on a batch of orders
	 */
	async execute(
		orders: EnrichedOrder[],
		state: ExecutionState,
	): Promise<ExecutionState> {
		// First, apply ordered rules to each order
		for (const rule of this.config.rules) {
			for (const order of orders) {
				if (order.isProcessed) continue;

				// Evaluate rule condition
				const conditionMet = this.expressionEvaluator.evaluateCondition(
					rule.condition,
					this.categoryMap,
					order,
					state,
				);

				if (conditionMet) {
					// Apply rule action
					await this.applyAction(rule.action, order, state, rule.complete);
				}
			}
		}

		// Freeze allocations before global rules
		this.freezeAllocations(state);

		// Apply global rules
		for (const globalRule of this.config.globalRules) {
			for (const order of orders) {
				if (order.isProcessed) continue;

				// Global rules apply to all unfilled orders
				await this.applyAction(globalRule.action, order, state, true);
			}
		}

		return state;
	}

	private async applyAction(
		actionName: string,
		order: EnrichedOrder,
		state: ExecutionState,
		complete: boolean,
	): Promise<void> {
		const actionConfig = this.actionConfigMap.get(actionName);
		if (!actionConfig) {
			throw new Error(`Action not found: ${actionName}`);
		}

		const actionFn = this.actionMap.get(actionConfig.action.actionName);
		if (!actionFn) {
			throw new Error(
				`Action implementation not found: ${actionConfig.action.actionName}`,
			);
		}

		// Apply the action
		actionFn(order, actionConfig.action.actionArgs, state, complete);
	}

	private freezeAllocations(state: ExecutionState): void {
		state.allocatedBeforeGlobalRules = state.allocatedSoFar;
	}
}

// ===============================================================
// Order Repository Component
// ===============================================================

/**
 * Handles database operations for orders
 */
export class OrderRepository {
	constructor(private db: PostgresJsDatabase) {}

	/**
	 * Fetch orders for an offer in batches
	 */
	async fetchOrdersForOffer(
		offerId: string,
		batchSize = 10000,
		offset = 0,
		status = "submitted",
	): Promise<EnrichedOrder[]> {
		const result = await this.db
			.select()
			.from(orders)
			.where(and(eq(orders.offerId, offerId), eq(orders.status, status)))
			.limit(batchSize)
			.offset(offset);

		return result.map((order) => this.mapToEnrichedOrder(order));
	}

	/**
	 * Get information about an offer for allocation
	 */
	async getOfferDetails(offerId: string): Promise<{
		id: string;
		totalDemand: Decimal;
		totalAvailable: Decimal;
		pricePerUnit?: number;
	}> {
		// Here you would fetch offer details from your offers table
		// For now, we'll simulate this with a query on the orders

		// Calculate total demand
		const totalDemandResult = await this.db
			.select({ sum: orders.quantity })
			.from(orders)
			.where(and(eq(orders.offerId, offerId), eq(orders.status, "submitted")));

		const totalDemand = totalDemandResult[0]?.sum
			? new Decimal(totalDemandResult[0].sum.toString())
			: new Decimal(0);

		// In a real implementation, you'd fetch the offer record to get the total available
		// For now, we'll assume 80% of demand is available
		const totalAvailable = totalDemand.mul(0.8).floor();

		return {
			id: offerId,
			totalDemand,
			totalAvailable,
			pricePerUnit: 1.0, // Default price, replace with actual price from offer
		};
	}

	/**
	 * Update order status for all orders in an offer
	 */
	async updateOrderStatusForOffer(
		offerId: string,
		fromStatus: string,
		toStatus: string,
		onlyAllocated = false,
	): Promise<void> {
		let query = this.db
			.update(orders)
			.set({
				status: toStatus,
				updatedAt: new Date(),
			})
			.where(and(eq(orders.offerId, offerId), eq(orders.status, fromStatus)));

		if (onlyAllocated) {
			query = query.where(gt(orders.allocatedQuantity, 0));
		}

		await query;
	}

	/**
	 * Update allocations for a batch of orders
	 */
	async batchUpdateAllocations(updatedOrders: EnrichedOrder[]): Promise<void> {
		if (updatedOrders.length === 0) return;

		const updates = updatedOrders.map((order) => {
			return this.db
				.update(orders)
				.set({
					allocatedQuantity: order.allocatedQuantity.toString(),
					allocatedAt: new Date(),
					updatedAt: new Date(),
				})
				.where(eq(orders.id, order.id));
		});

		// Execute all updates in parallel
		await Promise.all(updates);
	}

	/**
	 * Map from database record to EnrichedOrder
	 */
	private mapToEnrichedOrder(dbOrder: any): EnrichedOrder {
		const attributes =
			typeof dbOrder.attributes === "string"
				? JSON.parse(dbOrder.attributes)
				: dbOrder.attributes || {};

		const quantity = new Decimal(dbOrder.quantity.toString());
		const allocatedQuantity = dbOrder.allocatedQuantity
			? new Decimal(dbOrder.allocatedQuantity.toString())
			: new Decimal(0);

		return {
			id: dbOrder.id,
			offerId: dbOrder.offerId,
			partnerId: dbOrder.partnerId,
			memberId: dbOrder.memberId,
			status: dbOrder.status,
			quantity,
			allocatedQuantity,
			attributes,
			isProcessed: false,
			__originalAllocatedQuantity: allocatedQuantity,
		};
	}
}

// ===============================================================
// Rules Engine Service
// ===============================================================

/**
 * Main service coordinating the rule execution process
 */
export class RulesEngineService {
	private ruleParser: RuleParser;
	private expressionEvaluator: ExpressionEvaluator;
	private actionImplementations: ActionImplementations;
	private orderRepository: OrderRepository;

	constructor(private db: PostgresJsDatabase) {
		this.ruleParser = new RuleParser();
		this.expressionEvaluator = new ExpressionEvaluator();
		this.actionImplementations = new ActionImplementations();
		this.orderRepository = new OrderRepository(db);
	}

	/**
	 * Execute rule allocation for an offer
	 */
	async executeRules(
		offerId: string,
		ruleConfig: any,
		batchSize = 10000,
	): Promise<{
		ordersProcessed: number;
		ordersAffected: number;
		totalAllocated: string;
	}> {
		try {
			// Parse and validate the rule configuration
			const config = this.ruleParser.parse(ruleConfig);

			// Mark orders as processing
			await this.orderRepository.updateOrderStatusForOffer(
				offerId,
				"submitted",
				"processing",
			);

			// Get offer details for allocation
			const offerDetails = await this.orderRepository.getOfferDetails(offerId);

			// Initialize execution state
			const state: ExecutionState = {
				ordersState: new Map(),
				allocatedSoFar: new Decimal(0),
				allocatedBeforeGlobalRules: new Decimal(0),
				demandBeforeEnd: offerDetails.totalDemand,
				availableForAllocation: offerDetails.totalAvailable,
				offerDetails: {
					id: offerId,
					totalSize: offerDetails.totalAvailable,
					pricePerUnit: offerDetails.pricePerUnit,
				},
			};

			let offset = 0;
			let ordersProcessed = 0;
			let ordersAffected = 0;
			let hasMoreOrders = true;

			// Process orders in batches
			while (hasMoreOrders) {
				// Fetch next batch of orders
				const orders = await this.orderRepository.fetchOrdersForOffer(
					offerId,
					batchSize,
					offset,
					"processing",
				);

				if (orders.length === 0) {
					hasMoreOrders = false;
					break;
				}

				ordersProcessed += orders.length;

				// Create rule executor for this batch
				const executor = new RuleExecutor(
					config,
					this.actionImplementations,
					this.expressionEvaluator,
				);

				// Execute rules on this batch
				await executor.execute(orders, state);

				// Count affected orders and update database
				const updatedOrders = orders.filter(
					(order) =>
						!order.allocatedQuantity.eq(order.__originalAllocatedQuantity),
				);

				if (updatedOrders.length > 0) {
					await this.orderRepository.batchUpdateAllocations(updatedOrders);
					ordersAffected += updatedOrders.length;
				}

				offset += batchSize;
			}

			// Update status of successfully allocated orders
			await this.orderRepository.updateOrderStatusForOffer(
				offerId,
				"processing",
				"allocated",
				true, // Only update orders with allocatedQuantity > 0
			);

			// Update status of unallocated orders back to submitted
			await this.orderRepository.updateOrderStatusForOffer(
				offerId,
				"processing",
				"submitted",
			);

			return {
				ordersProcessed,
				ordersAffected,
				totalAllocated: state.allocatedSoFar.toString(),
			};
		} catch (error) {
			// Ensure we don't leave orders in processing state
			await this.orderRepository.updateOrderStatusForOffer(
				offerId,
				"processing",
				"submitted",
			);

			throw error;
		}
	}
}
