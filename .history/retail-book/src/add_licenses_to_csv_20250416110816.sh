#!/bin/bash

# Script to identify license types for modules listed in a CSV file.
# Reads input CSV, uses 'go mod download -json' to find module dirs,
# then runs 'go-licenses report' on each dir to identify the license type.
# Creates a new CSV with an added 'LicenseType' column.
# Requires 'jq' and 'go-licenses' to be installed.
# Should be run from the retail-book/src directory.

INPUT_CSV="external_dependencies.csv"
OUTPUT_CSV="dependencies_with_licenses.csv"

# --- Script Start ---
echo "Starting license type identification..."

# Check for input file
if [ ! -f "$INPUT_CSV" ]; then
  echo "Error: Input file '$INPUT_CSV' not found in the current directory."
  exit 1
fi

# Check for jq dependency
if ! command -v jq &> /dev/null; then
  echo "Error: 'jq' command not found. Please install jq (e.g., 'brew install jq' or 'apt install jq')."
  exit 1
fi

# Check for go-licenses dependency
if ! command -v go-licenses &> /dev/null; then
  echo "Error: 'go-licenses' command not found. Please install it (go install github.com/google/go-licenses@latest)."
  exit 1
fi

# Clean up previous output file and write header
rm -f "$OUTPUT_CSV"
echo "Module,Version,LicenseType" > "$OUTPUT_CSV" || { echo "Error: Could not write header to $OUTPUT_CSV"; exit 1; }

echo "Processing modules from $INPUT_CSV..."

# Read input CSV line by line, skipping the header
tail -n +2 "$INPUT_CSV" | while IFS=, read -r module version; do
  # Trim potential quotes or whitespace
  module=$(echo "$module" | xargs)
  version=$(echo "$version" | xargs)

  if [ -z "$module" ] || [ -z "$version" ]; then
    echo "  Warning: Skipping empty module or version."
    continue
  fi

  echo -n "  Processing $module@$version..."

  # Download module info and get directory path
  json_output=$(go mod download -json "$module@$version" 2>&1)
  dl_exit_code=$?

  if [ $dl_exit_code -ne 0 ]; then
    echo " Failed (go mod download error)."
    echo "\"$module\",\"$version\",\"Error: go mod download failed\"" >> "$OUTPUT_CSV"
    continue
  fi

  module_dir=$(echo "$json_output" | jq -r '.Dir // empty')

  if [ -z "$module_dir" ]; then
    echo " Failed (could not find module directory in JSON output)."
    echo "\"$module\",\"$version\",\"Error: Could not parse module directory\"" >> "$OUTPUT_CSV"
    continue
  fi

  if [ ! -d "$module_dir" ]; then
    echo " Failed (module directory '$module_dir' not found)."
     echo "\"$module\",\"$version\",\"Error: Module directory not found\"" >> "$OUTPUT_CSV"
    continue
  fi

  # Run go-licenses report on the specific module directory
  # Redirect stderr to /dev/null to suppress noise about dependencies etc.
  # We only care about the license reported for the main module path
  go_licenses_output=$(go-licenses report "$module_dir" 2>/dev/null)
  report_exit_code=$?

  license_type="Error: go-licenses failed" # Default in case of failure

  if [ $report_exit_code -eq 0 ] && [ -n "$go_licenses_output" ]; then
      # Extract the license type for the *specific module* being processed
      # Use awk to get the last field ($NF) from the line matching the module path
      # Note: This assumes module paths don't contain spaces, which is standard for Go.
      extracted_type=$(echo "$go_licenses_output" | awk -v mod="$module" '$1 == mod {print $NF}')

      if [ -n "$extracted_type" ]; then
          license_type="$extracted_type"
          echo " Found ($license_type)."
      else
          # go-licenses ran but didn't report a type for this specific module path
          license_type="Unknown/Not Found"
          echo " License type not found by go-licenses."
      fi
  else
      if [ $report_exit_code -ne 0 ]; then
           echo " Failed (go-licenses execution error)."
           # Keep license_type as "Error: go-licenses failed"
      else
           # go-licenses ran but produced no output
           license_type="Unknown/Not Found"
           echo " License type not found (no output from go-licenses)."
      fi
  fi

  # Append to output CSV
  echo "\"$module\",\"$version\",\"$license_type\"" >> "$OUTPUT_CSV"
  # Add check for write error
  if [ $? -ne 0 ]; then
    echo " Failed (Error writing to $OUTPUT_CSV). Exiting loop."
    break # Exit the while loop
  fi

done

echo "Finished license type identification."
echo "Results written to $OUTPUT_CSV"
echo "Done."

exit 0 