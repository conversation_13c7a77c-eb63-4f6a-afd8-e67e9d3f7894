#!/bin/bash

# <PERSON>ript to find potential license files for modules listed in a CSV file.
# Reads input CSV, uses 'go mod download -json' and 'jq' to find module dirs,
# then searches for common license files within those dirs.
# Creates a new CSV with an added 'LicenseFile' column.
# Should be run from the retail-book/src directory.

INPUT_CSV="external_dependencies.csv"
OUTPUT_CSV="dependencies_with_licenses.csv"

# License file patterns to search for (case-insensitive)
# Using find's -iregex for case-insensitivity
LICENSE_REGEX='./(LICENSE|COPYING|NOTICE|README)(\..*)?'

# --- Script Start ---
echo "Starting license file search..."

# Check for input file
if [ ! -f "$INPUT_CSV" ]; then
  echo "Error: Input file '$INPUT_CSV' not found in the current directory."
  exit 1
fi

# Check for jq dependency
if ! command -v jq &> /dev/null; then
  echo "Error: 'jq' command not found. Please install jq (e.g., 'brew install jq' or 'apt install jq')."
  exit 1
fi

# Clean up previous output file and write header
rm -f "$OUTPUT_CSV"
echo "Module,Version,LicenseFile" > "$OUTPUT_CSV" || { echo "Error: Could not write header to $OUTPUT_CSV"; exit 1; }

echo "Processing modules from $INPUT_CSV..."

# Read input CSV line by line, skipping the header
tail -n +2 "$INPUT_CSV" | while IFS=, read -r module version; do
  # Trim potential quotes or whitespace (though previous script shouldn't add them)
  module=$(echo "$module" | xargs)
  version=$(echo "$version" | xargs)

  if [ -z "$module" ] || [ -z "$version" ]; then
    echo "  Warning: Skipping empty module or version."
    continue
  fi

  echo -n "  Processing $module@$version..."

  # Download module info and get directory path
  json_output=$(go mod download -json "$module@$version" 2>&1)
  exit_code=$?

  if [ $exit_code -ne 0 ]; then
    echo " Failed (go mod download error)."
    echo "\"$module\",\"$version\",\"Error: go mod download failed\"" >> "$OUTPUT_CSV"
    continue
  fi

  module_dir=$(echo "$json_output" | jq -r '.Dir // empty')

  if [ -z "$module_dir" ]; then
    echo " Failed (could not find module directory in JSON output)."
    echo "\"$module\",\"$version\",\"Error: Could not parse module directory\"" >> "$OUTPUT_CSV"
    continue
  fi

  if [ ! -d "$module_dir" ]; then
    echo " Failed (module directory '$module_dir' not found)."
     echo "\"$module\",\"$version\",\"Error: Module directory not found\"" >> "$OUTPUT_CSV"
    continue
  fi

  # Find the first matching license file in the module's root directory (case-insensitive)
  license_file_path=$(find "$module_dir" -maxdepth 1 -regextype posix-extended -iregex "$LICENSE_REGEX" -print -quit)

  license_file_name="Not Found"
  if [ -n "$license_file_path" ]; then
      license_file_name=$(basename "$license_file_path")
      echo " Found ($license_file_name)."
  else
      echo " License file not found."
  fi

  # Append to output CSV (escaping quotes in module/version just in case)
  printf "\"%%s\",\"%%s\",\"%%s\"\n" "${module//\"/\"\"}" "${version//\"/\"\"}" "$license_file_name" >> "$OUTPUT_CSV"

done

echo "Finished license file search."
echo "Results written to $OUTPUT_CSV"
echo "Done."

exit 0 