#!/bin/bash

# Script to generate a CSV of unique external Go dependencies for the workspace.
# Should be run from the retail-book/src directory.

MODULES=(
  apigateway
  cache
  commandhandler
  common
  viewserver
  interappgateway
  generator
  centraldata
  eventauditor
  eventprocessor
  integrationtester
  statesyncer
  documentservice
  userservice
  notification
  admin
  rulesengine
)

TEMP_FILE="temp_deps.txt"
OUTPUT_CSV="external_dependencies.csv"
INTERNAL_PREFIX="retailbook.com/rb/" # Adjust if your internal module prefix is different

# --- Script Start ---
echo "Starting dependency generation..."

# Clean up previous files
rm -f "$TEMP_FILE" "$OUTPUT_CSV"
touch "$TEMP_FILE" || { echo "Error: Could not create temporary file $TEMP_FILE"; exit 1; }

echo "Processing modules..."

processed_count=0
failed_count=0

# Loop through each module
for module in "${MODULES[@]}"; do
  if [ -d "$module" ]; then
    echo -n "  Processing $module..."
    # Use a subshell to avoid directory change issues
    (
      cd "$module" || exit 1 # Exit subshell if cd fails
      go list -m all >> "../$TEMP_FILE" || exit 1 # Exit subshell if go list fails
    )
    if [ $? -eq 0 ]; then
      echo " Done."
      processed_count=$((processed_count + 1))
    else
      echo " Failed."
      failed_count=$((failed_count + 1))
    fi
  else
    echo "  Warning: Module directory '$module' not found. Skipping."
    failed_count=$((failed_count + 1))
  fi
done

echo "Finished processing modules ($processed_count processed, $failed_count failed/skipped)."

if [ $processed_count -eq 0 ]; then
  echo "Error: No modules were processed successfully."
  rm -f "$TEMP_FILE"
  exit 1
fi

echo "Generating CSV report..."

# Write header
echo "Module,Version" > "$OUTPUT_CSV" || { echo "Error: Could not write header to $OUTPUT_CSV"; rm -f "$TEMP_FILE"; exit 1; }

# Process dependencies: sort unique, remove internal, format as CSV
# Added quotes around fields in awk to handle potential commas in module paths/versions
sort -u "$TEMP_FILE" | grep -v "^$INTERNAL_PREFIX" | awk '{gsub(/"/, """", $1); gsub(/"/, """", $2); print """ $1 "","" $2 """}' >> "$OUTPUT_CSV"


# Check if awk command succeeded (more reliable than checking file content directly)
if [ $? -ne 0 ]; then
    echo "Error: Failed to process dependencies and write to CSV."
    rm -f "$TEMP_FILE" "$OUTPUT_CSV"
    exit 1
fi

# Clean up temporary file
rm -f "$TEMP_FILE"

echo "Successfully created $OUTPUT_CSV"
echo "Done."

exit 0 