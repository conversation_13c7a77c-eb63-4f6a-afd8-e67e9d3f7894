Type,Package,License,Risk,Repository
ruby,webrick@1.7.0,"<PERSON>,BSD-2-Clause",Medium,retail-book
go,github.com/hashicorp/hcl@v1.0.0,MPL-2.0,Medium,retail-book
ruby,rouge@3.29.0,"['MIT', 'BSD-2-Clause']",Unknown,retail-book
go,github.com/xi2/xz@v0.0.0-20171230120015-48954b6210f8,Other,Unknown,retail-book
go,github.com/russross/blackfriday/v2@v2.1.0,Other,Unknown,retail-book
ruby,wdm@0.1.1,MIT,Low,retail-book
go,sigs.k8s.io/yaml@v1.4.0,"BSD-3-<PERSON><PERSON>, MIT",Low,retail-book
go,sigs.k8s.io/structured-merge-diff/v4@v4.6.0,Apache 2.0,Low,retail-book
go,sigs.k8s.io/json@v0.0.0-20241014173422-cfa47c3a1cc8,"Apache-2.0, BSD-3-Clause",Low,retail-book
ruby,sass-listen@4.0.0,MIT,Low,retail-book
ruby,sass@3.7.4,MIT,Low,retail-book
ruby,safe_yaml@1.0.5,MIT,Low,retail-book
ruby,rubyzip@2.3.2,BSD-2-Clause,Low,retail-book
ruby,rexml@3.2.5,BSD-2-Clause,Low,retail-book
go,retailbook.com/statesyncer@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/viewserver@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/userservice@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/notification@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/eventprocessor@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/eventauditor@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/documentservice@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/common@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/commandhandler@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/centraldata@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/cache@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/rb/apigateway@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
go,retailbook.com/interappgateway@v0.0.0-00010101000000-000000000000,Unknown,Low,retail-book
ruby,rb-inotify@0.10.1,MIT,Low,retail-book
ruby,rb-fsevent@0.11.1,MIT,Low,retail-book
ruby,rake@13.0.6,MIT,Low,retail-book
ruby,public_suffix@4.0.7,MIT,Low,retail-book
ruby,pathutil@0.16.2,MIT,Low,retail-book
ruby,mercenary@0.3.6,MIT,Low,retail-book
ruby,listen@3.7.1,MIT,Low,retail-book
ruby,liquid@4.0.3,MIT,Low,retail-book
ruby,kramdown-parser-gfm@1.1.0,MIT,Low,retail-book
ruby,kramdown@2.4.0,MIT,Low,retail-book
go,k8s.io/utils@v0.0.0-20250321185631-1f6e0b77f77e,Apache 2.0,Low,retail-book
go,k8s.io/klog/v2@v2.130.1,Apache 2.0,Low,retail-book
go,k8s.io/client-go@v0.32.3,Apache 2.0,Low,retail-book
go,k8s.io/apimachinery@v0.32.3,Apache 2.0,Low,retail-book
ruby,just-the-docs@0.3.3,MIT,Low,retail-book
ruby,jekyll-watch@2.2.1,MIT,Low,retail-book
ruby,jekyll-sitemap@1.4.0,MIT,Low,retail-book
ruby,jekyll-seo-tag@2.8.0,MIT,Low,retail-book
ruby,jekyll-sass-converter@1.5.2,MIT,Low,retail-book
ruby,jekyll-remote-theme@0.4.3,MIT,Low,retail-book
ruby,jekyll-paginate@1.1.0,MIT,Low,retail-book
ruby,jekyll@3.9.2,MIT,Low,retail-book
ruby,i18n@0.9.5,MIT,Low,retail-book
ruby,http_parser.rb@0.8.0,MIT,Low,retail-book
go,gopkg.in/yaml.v3@v3.0.1,"Apache-2.0, MIT",Low,retail-book
go,gopkg.in/yaml.v2@v2.4.0,Apache 2.0,Low,retail-book
go,gopkg.in/natefinch/lumberjack.v2@v2.2.1,MIT,Low,retail-book
go,gopkg.in/ini.v1@v1.67.0,Apache 2.0,Low,retail-book
go,gopkg.in/inf.v0@v0.9.1,BSD-3-Clause,Low,retail-book
go,gopkg.in/errgo.v2@v2.1.0,BSD-3-Clause,Low,retail-book
go,gopkg.in/check.v1@v1.0.0-20201130134442-10cb98267c6c,BSD-2-Clause,Low,retail-book
go,google.golang.org/protobuf@v1.36.6,BSD-3-Clause,Low,retail-book
go,google.golang.org/grpc@v1.71.1,Apache 2.0,Low,retail-book
go,google.golang.org/genproto/googleapis/rpc@v0.0.0-20250407143221-ac9807e6c755,Apache 2.0,Low,retail-book
go,google.golang.org/genproto/googleapis/api@v0.0.0-20250404141209-ee84b53bf3d0,Apache 2.0,Low,retail-book
go,google.golang.org/genproto@v0.0.0-20230822172742-b8732ec3820d,Apache 2.0,Low,retail-book
go,google.golang.org/appengine@v1.6.7,Apache 2.0,Low,retail-book
go,golang.org/x/time@v0.11.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/text@v0.8.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/text@v0.24.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/term@v0.6.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/term@v0.31.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/sys@v0.6.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/sys@v0.32.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/sync@v0.13.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/oauth2@v0.29.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/net@v0.38.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/mod@v0.24.0,BSD-3-Clause,Low,retail-book
go,golang.org/x/crypto@v0.37.0,BSD-3-Clause,Low,retail-book
go,go.uber.org/zap@v1.27.0,MIT,Low,retail-book
go,go.uber.org/multierr@v1.11.0,MIT,Low,retail-book
go,go.uber.org/atomic@v1.11.0,MIT,Low,retail-book
go,go.etcd.io/etcd/client/v3@v3.5.21,Apache 2.0,Low,retail-book
go,go.etcd.io/etcd/client/pkg/v3@v3.5.21,Apache 2.0,Low,retail-book
go,go.etcd.io/etcd/api/v3@v3.5.21,Apache 2.0,Low,retail-book
go,github.com/xuri/nfp@v0.0.0-20250226145837-86d5fc24b2ba,BSD-3-Clause,Low,retail-book
go,github.com/xuri/excelize/v2@v2.9.0,BSD-3-Clause,Low,retail-book
go,github.com/xuri/efp@v0.0.0-20250227110027-3491fafc2b79,BSD-3-Clause,Low,retail-book
go,github.com/xrash/smetrics@v0.0.0-20240521201337-686a1a2994c1,MIT,Low,retail-book
go,github.com/x448/float16@v0.8.4,MIT,Low,retail-book
go,github.com/werf/lockgate@v0.1.1,Apache 2.0,Low,retail-book
go,github.com/urfave/cli/v2@v2.27.6,MIT,Low,retail-book
go,github.com/thedatashed/xlsxreader@v1.2.8,MIT,Low,retail-book
go,github.com/subosito/gotenv@v1.6.0,MIT,Low,retail-book
go,github.com/stretchr/testify@v1.10.0,MIT,Low,retail-book
go,github.com/spf13/viper@v1.20.1,MIT,Low,retail-book
go,github.com/spf13/pflag@v1.0.6,BSD-3-Clause,Low,retail-book
go,github.com/spf13/jwalterweatherman@v1.1.0,MIT,Low,retail-book
go,github.com/spf13/cast@v1.7.1,MIT,Low,retail-book
go,github.com/spf13/afero@v1.14.0,Apache 2.0,Low,retail-book
go,github.com/spaolacci/murmur3@v1.1.0,BSD-3-Clause,Low,retail-book
go,github.com/sourcegraph/conc@v0.3.0,MIT,Low,retail-book
go,github.com/shopspring/decimal@v1.4.0,MIT,Low,retail-book
go,github.com/sendgrid/sendgrid-go@v3.16.0+incompatible,MIT,Low,retail-book
go,github.com/sendgrid/rest@v2.6.9+incompatible,MIT,Low,retail-book
go,github.com/segmentio/asm@v1.2.0,MIT,Low,retail-book
go,github.com/sagikazarmark/locafero@v0.9.0,MIT,Low,retail-book
go,github.com/rs/zerolog@v1.34.0,MIT,Low,retail-book
go,github.com/rs/cors@v1.11.1,MIT,Low,retail-book
go,github.com/rogpeppe/go-internal@v1.8.0,BSD-3-Clause,Low,retail-book
go,github.com/rogpeppe/go-internal@v1.14.1,BSD-3-Clause,Low,retail-book
go,github.com/rivo/uniseg@v0.4.2,MIT,Low,retail-book
go,github.com/rivo/tview@v0.0.0-20230130130022-4a1b7a76c01c,MIT,Low,retail-book
go,github.com/richardlehane/msoleps@v1.0.4,Apache 2.0,Low,retail-book
go,github.com/richardlehane/mscfb@v1.0.4,Apache 2.0,Low,retail-book
go,github.com/prometheus/procfs@v0.16.0,Apache 2.0,Low,retail-book
go,github.com/prometheus/common@v0.63.0,Apache 2.0,Low,retail-book
go,github.com/prometheus/client_model@v0.6.1,Apache 2.0,Low,retail-book
go,github.com/prometheus/client_golang@v1.21.1,Apache 2.0,Low,retail-book
go,github.com/pmezard/go-difflib@v1.0.1-0.20181226105442-5d4384ee4fb2,BSD-3-Clause,Low,retail-book
go,github.com/pmezard/go-difflib@v1.0.0,BSD-3-Clause,Low,retail-book
go,github.com/pkg/errors@v0.9.1,BSD-2-Clause,Low,retail-book
go,github.com/pkg/browser@v0.0.0-20240102092130-5ac0b6a4141c,BSD-2-Clause,Low,retail-book
go,github.com/pelletier/go-toml/v2@v2.2.4,MIT,Low,retail-book
go,github.com/pelletier/go-toml@v1.9.5,MIT,Low,retail-book
go,github.com/onsi/gomega@v1.37.0,MIT,Low,retail-book
go,github.com/onsi/ginkgo@v1.16.5,MIT,Low,retail-book
go,github.com/nats-io/nuid@v1.0.1,Apache 2.0,Low,retail-book
go,github.com/nats-io/nkeys@v0.4.10,Apache 2.0,Low,retail-book
go,github.com/nats-io/nats.go@v1.41.0,Apache 2.0,Low,retail-book
go,github.com/nats-io/nats-server/v2@v2.11.0,Apache 2.0,Low,retail-book
go,github.com/nats-io/jwt/v2@v2.7.3,Apache 2.0,Low,retail-book
go,github.com/munnerz/goautoneg@v0.0.0-20191010083416-a7dc8b61c822,BSD-3-Clause,Low,retail-book
go,github.com/mohae/deepcopy@v0.0.0-20170929034955-c48cc78d4826,MIT,Low,retail-book
go,github.com/modern-go/reflect2@v1.0.2,Apache 2.0,Low,retail-book
go,github.com/modern-go/concurrent@v0.0.0-20180306012644-bacd9c7ef1dd,Apache 2.0,Low,retail-book
go,github.com/mitchellh/mapstructure@v1.5.0,MIT,Low,retail-book
go,github.com/minio/highwayhash@v1.0.3,Apache 2.0,Low,retail-book
go,github.com/matttproud/golang_protobuf_extensions@v1.0.4,Apache 2.0,Low,retail-book
go,github.com/mattn/go-runewidth@v0.0.13,MIT,Low,retail-book
go,github.com/mattn/go-isatty@v0.0.20,MIT,Low,retail-book
go,github.com/mattn/go-colorable@v0.1.14,MIT,Low,retail-book
go,github.com/markbates/pkger@v0.17.1,MIT,Low,retail-book
go,github.com/magiconair/properties@v1.8.9,BSD-2-Clause,Low,retail-book
go,github.com/lucasb-eyer/go-colorful@v1.2.0,MIT,Low,retail-book
go,github.com/lib/pq@v1.10.9,MIT,Low,retail-book
go,github.com/lestrrat-go/option@v1.0.1,MIT,Low,retail-book
go,github.com/lestrrat-go/jwx/v2@v2.1.4,MIT,Low,retail-book
go,github.com/lestrrat-go/iter@v1.0.2,MIT,Low,retail-book
go,github.com/lestrrat-go/httprc@v1.0.6,MIT,Low,retail-book
go,github.com/lestrrat-go/httpcc@v1.0.1,MIT,Low,retail-book
go,github.com/lestrrat-go/blackmagic@v1.0.2,MIT,Low,retail-book
go,github.com/leodido/go-urn@v1.4.0,MIT,Low,retail-book
go,github.com/kylelemons/godebug@v1.1.0,Apache 2.0,Low,retail-book
go,github.com/kr/pretty@v0.3.0,MIT,Low,retail-book
go,github.com/klauspost/compress@v1.18.0,BSD-3-Clause,Low,retail-book
go,github.com/json-iterator/go@v1.1.12,MIT,Low,retail-book
go,github.com/iancoleman/strcase@v0.3.0,MIT,Low,retail-book
go,github.com/iancoleman/strcase@v0.2.0,MIT,Low,retail-book
go,github.com/gorilla/mux@v1.8.1,BSD-3-Clause,Low,retail-book
go,github.com/google/uuid@v1.6.0,BSD-3-Clause,Low,retail-book
go,github.com/google/gofuzz@v1.2.0,Apache 2.0,Low,retail-book
go,github.com/google/go-tpm@v0.9.3,Apache 2.0,Low,retail-book
go,github.com/google/go-cmp@v0.7.0,BSD-3-Clause,Low,retail-book
go,github.com/golang/protobuf@v1.5.4,BSD-3-Clause,Low,retail-book
go,github.com/golang/mock@v1.6.0,Apache 2.0,Low,retail-book
go,github.com/golang-jwt/jwt/v5@v5.2.2,MIT,Low,retail-book
go,github.com/golang-jwt/jwt/v4@v4.5.2,MIT,Low,retail-book
go,github.com/golang-jwt/jwt@v3.2.2+incompatible,MIT,Low,retail-book
go,github.com/gogo/protobuf@v1.3.2,BSD-3-Clause,Low,retail-book
go,github.com/gocomply/xsd2go@v0.1.9,Unlicense,Low,retail-book
go,github.com/goccy/go-json@v0.10.5,MIT,Low,retail-book
go,github.com/gobuffalo/here@v0.6.7,MIT,Low,retail-book
go,github.com/go-viper/mapstructure/v2@v2.2.1,MIT,Low,retail-book
go,github.com/go-redis/redis@v6.15.9+incompatible,BSD-2-Clause,Low,retail-book
go,github.com/go-playground/validator/v10@v10.26.0,MIT,Low,retail-book
go,github.com/go-playground/universal-translator@v0.18.1,MIT,Low,retail-book
go,github.com/go-playground/locales@v0.14.1,MIT,Low,retail-book
go,github.com/go-logr/logr@v1.4.2,Apache 2.0,Low,retail-book
go,github.com/gdamore/tcell/v2@v2.5.3,Apache 2.0,Low,retail-book
go,github.com/gdamore/encoding@v1.0.0,Apache 2.0,Low,retail-book
go,github.com/gabriel-vasile/mimetype@v1.4.8,MIT,Low,retail-book
go,github.com/fxamacker/cbor/v2@v2.8.0,MIT,Low,retail-book
go,github.com/fsnotify/fsnotify@v1.9.0,BSD-3-Clause,Low,retail-book
go,github.com/fergusstrange/embedded-postgres@v1.30.0,MIT,Low,retail-book
go,github.com/fergusstrange/embedded-postgres@v1.25.0,MIT,Low,retail-book
go,github.com/fatih/camelcase@v1.0.0,MIT,Low,retail-book
go,github.com/decred/dcrd/dcrec/secp256k1/v4@v4.4.0,ISC,Low,retail-book
go,github.com/davecgh/go-spew@v1.1.2-0.20180830191138-d8f796af33cc,ISC,Low,retail-book
go,github.com/davecgh/go-spew@v1.1.1,ISC,Low,retail-book
go,github.com/d5/tengo/v2@v2.17.0,MIT,Low,retail-book
go,github.com/cpuguy83/go-md2man/v2@v2.0.6,MIT,Low,retail-book
go,github.com/coreos/go-systemd/v22@v22.5.0,Apache 2.0,Low,retail-book
go,github.com/coreos/go-semver@v0.3.1,Apache 2.0,Low,retail-book
go,github.com/colega/zeropool@v0.0.0-20230505084239-6fb4a4f75381,Apache 2.0,Low,retail-book
go,github.com/cockroachdb/apd/v3@v3.2.1,Apache 2.0,Low,retail-book
go,github.com/cespare/xxhash/v2@v2.3.0,MIT,Low,retail-book
go,github.com/casbin/govaluate@v1.3.0,MIT,Low,retail-book
go,github.com/casbin/casbin/v2@v2.104.0,Apache 2.0,Low,retail-book
go,github.com/caarlos0/env/v11@v11.3.1,MIT,Low,retail-book
go,github.com/bojanz/currency@v1.3.1,MIT,Low,retail-book
go,github.com/bmatcuk/doublestar/v4@v4.8.1,MIT,Low,retail-book
go,github.com/bits-and-blooms/bitset@v1.22.0,BSD-3-Clause,Low,retail-book