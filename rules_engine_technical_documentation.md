# Rules Engine - Technical Documentation

## 1. Overview

This document provides a detailed technical overview of the Rules Engine, a Go-based application designed for automated allocation of financial products based on a configurable ruleset.

**Purpose:** To provide a flexible, scriptable, and auditable mechanism for allocating amounts, shares, or units for various financial instruments like IPOs, follow-ons, bonds, and T-bills.

**Core Technologies:**
*   **Go:** The primary programming language for the engine's logic.
*   **Tengo:** An embeddable scripting language for Go, used for defining dynamic rule conditions and logic. (`github.com/d5/tengo/v2`)
*   **Excel (XLSX):** Used as the primary input format for order data and associated tags. (`github.com/thedatashed/xlsxreader`)
*   **JSON:** Used for the main rule configuration file.
*   **Cap'n Proto:** Used for schema definitions (e.g., `proto/schema.capnp`), though its direct runtime use in the core allocation logic is less prominent than Tengo scripting and direct Go structs.

## 2. Directory Structure

A brief overview of the key directories within the `rulesengine` module:

*   `cmd/rulesengine/`: Contains the main application entry point (`main.go`).
*   `pkg/`: Contains the core library code, broken into sub-packages:
    *   `common/`: Defines common data structures like `OrderRow`.
    *   `config/`: Handles parsing and validation of the JSON rule configuration.
    *   `exec/`: Contains the rule execution logic, Tengo script generation, and action implementations.
    *   `hydrate/`: (Indirectly used via `xls.XlsReader` which implements `hydrate.Hydrator`) Defines interfaces for data hydration.
    *   `xls/`: Provides utilities for reading and parsing data from XLSX files.
    *   Other packages like `server/`, `fetcher/`, `static_config/` exist but are less central to the core allocation rule processing described herein.
*   `proto/`: Contains Cap'n Proto schema definitions. The `schema.capnp` file defines data structures, and `schema.capnp.go` is the generated Go code.
*   `integration_tests/`: Contains integration tests for the rules engine (not detailed in this document).
*   `go.mod`, `go.sum`: Go module management files.

## 3. Core Components & System Flow

The engine operates by taking order data (from Excel), a rule configuration (from JSON), and processing them to produce final allocations.

```mermaid
graph LR
    subgraph Inputs
        direction LR
        ExcelOrders["Order Data (Excel)"]
        ExcelTags["Tags Data (Excel, optional)"]
        JSONConfig["Rule Configuration (JSON)"]
    end

    subgraph Processing Core
        direction TB
        A[main.go Start] --> B(Load Config & Data);
        B --> C{XLS Parsing (`pkg/xls`)};
        C --> D[Hydrate Tags];
        D --> E{Rule Config Parsing (`pkg/config`)};
        E --> F[Instantiate Tengo Runtime (`pkg/exec`)];
        F --> G[Generate Tengo Script];
        G --> H[Execute Script (Ordered & Global Rules)];
        H --> I[Update Order Allocations (`TengoExecutorState`)];
    end

    subgraph Outputs
        direction LR
        AllocResults["Allocation Results (JSON stdout)"]
    end

    Inputs --> A;
    I --> AllocResults
```

### 3.1. Application Entry Point (`cmd/rulesengine/main.go`)

The `main()` function orchestrates the entire process:
1.  **Environment Variables:** Reads paths for orders Excel (`ORDERS_PATH`), tags Excel (`TAGS_PATH`), and JSON config (`CONFIG_PATH`) via `github.com/caarlos0/env/v11`.
2.  **File Loading:** Reads the content of these files.
3.  **XLS Parsing & Hydration:**
    *   Uses `xls.New()` to create `xls.XlsReader` instances for orders and tags files.
    *   Calls `ordersReader.ParseAllocationSpreadsheet()` to get `map[common.OrderID]*common.OrderRow`.
    *   If tags data is present, `tagsReader.Hydrate()` is called for each order to populate `OrderRow.Tags`.
4.  **Configuration Parsing:** Calls `config.New()` with the JSON file content to get a `*config.Config` object.
5.  **Runtime Initialization:** Creates the `exec.TengoRuntime` using `exec.NewAllocationRuntime(cfg, ordersSlice)`.
6.  **Execution:** Calls `runtime.Execute()` to run the allocation logic.
7.  **Output:** The resulting `state.OrdersState` is marshalled to JSON and printed via `spew.Dump()`.

### 3.2. Common Data Structures (`pkg/common/types.go`)

*   `OrderID`: `type OrderID string`
*   `OrderRow`: The central struct representing an order. It's populated from Excel and updated during execution.
    ```go
    type OrderRow struct {
        ID              OrderID
        Intermediary    string
        Applications    uint64
        ExistingHolding uint64
        OrderValue      float64 // Requested value
        OrderQty        uint64  // Requested quantity
        TaxWrapper      bool
        Commission      bool
        Allocation      uint64  // Initially from Excel (if present), updated by engine
        Tags            []string // Populated by Hydrate() from tags Excel
    }
    ```

### 3.3. Rule Configuration (`pkg/config/`)

Handles the structure and parsing of the JSON rule configuration file.

*   **`types.go`:** Defines the configuration structures:
    *   `Expr`: A string representing a Tengo expression or comma-separated elements for actions (e.g., `actionName,arg1,arg2`). `GetElems()` splits it.
    *   `Category`: Named filter expression. `struct { Name string; Filter Expr }`.
    *   `Condition`: Determines if a rule applies. Can be a reference to a `Category` or a direct `Expr`. `struct { Cat *string; Expr *Expr }`.
    *   `Action`: Defines an action to be performed. `struct { Name string; Action Expr }` (e.g., `Name: "fill_50_pct", Action: "applyFill,50"`).
    *   `Rule`: Combines a `Condition` with an `Action` name, and flags. `struct { Name string; Cond Condition; Act string; Immediate bool; Complete bool }`.
    *   `OrderedGlobalRule`: A simplified rule for global actions. `struct { Name string; Action string }`.
*   **`config.go`:**
    *   `Config`: The root configuration object holding slices of `Categories`, `Actions`, `Rules`, and `OrderedGlobalRules`.
        ```go
        type Config struct {
            Categories []*Category `json:"categories"`
            Actions    []*Action   `json:"actions"`
            Rules      []*Rule     `json:"rules"`
            OrderedGlobalRules []*OrderedGlobalRule `json:"global"`
            // internal maps for quick lookup
        }
        ```
    *   `New(body []byte) (*Config, error)`: Unmarshals the JSON `body` into the `Config` struct. It performs validation, ensuring that referenced categories and actions exist and that rule conditions are well-formed.
    *   `Rule.GetConditionExpression() Expr`: Returns the final Tengo filter expression for a rule, resolving category references if needed.

### 3.4. Excel Data Input (`pkg/xls/xls.go`)

Responsible for reading and parsing data from `.xlsx` files using `github.com/thedatashed/xlsxreader`.

*   `XlsReader`: Struct holding the XLSX file reader.
*   `New(body []byte) (*XlsReader, error)`: Constructor for `XlsReader`.
*   `Hydrate(ctx context.Context, val *common.OrderRow, opts ...hydrate.Opts) error`:
    *   Implements the `hydrate.Hydrator` interface.
    *   Populates the `val.Tags` field of an `OrderRow`.
    *   Expects the Excel sheet to have `OrderID` in column 0 and comma-separated tags in column 1.
*   `ParseAllocationSpreadsheet() (map[common.OrderID]*common.OrderRow, error)`:
    *   Parses the main orders spreadsheet.
    *   Expects a single sheet with data starting from row 3 (rows 1 and 2 are skipped).
    *   Column mapping:
        *   0: `OrderID`
        *   1: `Intermediary`
        *   2: `Applications` (uint64)
        *   3: `ExistingHolding` (uint64)
        *   4: `OrderValue` (float64)
        *   5: `OrderQty` (uint64)
        *   6: `TaxWrapper` (bool, "yes"/"no")
        *   7: `Commission` (bool, "yes"/"no")
        *   8 (optional): `Allocation` (uint64) - pre-existing allocation amount.

### 3.5. Rule Execution Engine (`pkg/exec/`)

This is the heart of the engine, where rules are processed using Tengo scripting.

*   **`types.go` (in `pkg/exec/`)**
    *   `AllocatedOrder`: Wraps `*common.OrderRow` and adds execution-specific state like `Complete (bool)` and `CurrentSharesPrice (float64)` (though `CurrentSharesPrice` isn't explicitly set in the snippets reviewed, `Allocation` is the key field modified).
        ```go
        type AllocatedOrder struct {
            *common.OrderRow // Embedded
            Complete           bool
            CurrentSharesPrice float64 // Note: Usage not fully clear from current code snippets
        }
        ```
    *   `DeferredAction`: Struct for actions that might be deferred (not fully elaborated in the provided code snippets, but present as a field in `TengoExecutorState`).

*   **`tengo.go`:** Orchestrates Tengo script generation and execution.
    *   `TengoRuntime`: Holds the configuration, execution state, generated script, and compiled Tengo script.
    *   `TengoExecutorState`: Global state during execution. Passed to Tengo helper functions.
        ```go
        type TengoExecutorState struct {
            OrdersState     map[common.OrderID]*AllocatedOrder
            DeferredActions map[common.OrderID][]*DeferredAction
            AllocatedSoFar                 uint64
            AllocatedBeforeDeferredActions uint64
            DemandBeforeEnd uint64
            Actions         map[string]Action // Map of Go functions for actions
        }
        ```
    *   `BASE_TPL (const string)`: A Go text template used to generate the Tengo script. This template iterates over `cfg.Rules` and `cfg.GlobalRules` from the JSON config to create Tengo functions for each rule. The rule's `Filter` expression becomes the `if` condition, and the `ActionName` and its arguments are passed to a helper `exec.execute()`.
    *   `NewAllocationRuntime(cfg *config.Config, orders []*common.OrderRow) (*TengoRuntime, error)`:
        1.  Initializes `TengoExecutorState`, converting `config.Action` definitions into callable Go `Action` functions using `parseAction()`.
        2.  Populates `tplMap` with rule data from `cfg`.
        3.  Executes the `BASE_TPL` Go template to generate the Tengo script string.
        4.  Creates a `tengo.Script` object, sets up importable modules (`exec`, `tags` helpers).
        5.  Compiles the Tengo script.
    *   `Execute() (*TengoExecutorState, error)`:
        1.  Converts `OrderRow` data into `tengo.Map` objects so script can access fields (e.g., `order.id`, `order.tags`, `order.cost`, `order.amount_shares`).
        2.  Sets the `orders` variable in the compiled Tengo script.
        3.  Runs the compiled Tengo script (`r.compiled.Run()`).
    *   **Tengo Script Flow (generated from `BASE_TPL`):**
        1.  Imports `exec` and `tags` modules.
        2.  Defines Tengo functions for each rule in `OrderedRules`.
        3.  Defines Tengo functions for each rule in `GlobalRules`.
        4.  Iterates through `orders`:
            *   Executes each `OrderedRule` function for the current order.
        5.  Calls `exec.freezeAllocatedSoFar()` (a Go helper function callable from Tengo).
        6.  Iterates through `orders` again:
            *   Executes each `GlobalRule` function for the current order.
        7.  Calls `exec.deferredActions(orders)`.
    *   **Helper Modules for Tengo:**
        *   `getExecHelpers(state *TengoExecutorState)`: Provides functions to Tengo scripts, like `execute()` (which calls the Go action function), `freezeAllocatedSoFar()`, `deferredActions()`.
        *   `getTagsHelpers(state *TengoExecutorState)`: Provides functions to check tags on orders (e.g., `tags.has(order, "VIP")`).

*   **`actions.go`:** Defines the Go functions that implement the allocation actions.
    *   `type Action func(order *AllocatedOrder, totalAllocatedBeforeEnd, totalAllocatedSoFar, demandBeforeEnd uint64, complete bool) (uint64, error)`: Signature for action functions.
    *   `parseAction(e *config.Action) (Action, error)`: Takes a `config.Action` (from JSON config, e.g., `Action: "applyFill,50"`) and returns the corresponding Go `Action` function (e.g., `applyPctFill` with 50 as an argument).
    *   **Core Action Implementations:**
        *   `applyPctFill(order *AllocatedOrder, complete bool, args ...tengo.Object)`: Allocates `pct` (from `args[0]`) of `order.OrderQty`. Updates `order.Allocation`.
        *   `applyMinValFill(order *AllocatedOrder, complete bool, args ...tengo.Object)`: Allocates `minUnits` (from `args[0]`). Updates `order.Allocation`.
        *   `applyMinCashFill(order *AllocatedOrder, complete bool, args ...tengo.Object)`: Allocates based on a minimum cash value (from `args[0]`). Logic involves order value and current allocation. Updates `order.Allocation`.
        *   `proRata(order *AllocatedOrder, complete bool, args ...tengo.Object)`: Performs pro-rata allocation based on `allocatedBeforeEnd`, `totalLeftoverDemand`, `totalAllocated`, and `roundUp` flag passed as arguments. Updates `order.Allocation`.
    *   `truncateAndUpdateState(o *AllocatedOrder, desired uint64) uint64`: Helper to ensure allocation doesn't exceed `OrderQty` and updates `o.Allocation`.

*   **`bindings.go`:** (Likely contains the implementation for `getExecHelpers` and `getTagsHelpers` which expose Go functions to the Tengo environment, forming the bridge between Tengo scripts and Go state/logic).

### 3.6. Data Hydration/Tagging Flow

1.  Order data is loaded from the primary Excel sheet into `map[common.OrderID]*common.OrderRow` by `xls.ParseAllocationSpreadsheet()`.
2.  If a `TagsPath` is provided, a separate Excel sheet is read.
3.  The `xls.XlsReader.Hydrate()` method is called for each `OrderRow`. This method reads the tags Excel (expected: OrderID in col 0, CSV tags in col 1) and populates the `OrderRow.Tags []string` field.
4.  Within Tengo scripts, the `tags` module (e.g., `tags.has(order, "TAG_NAME")`) can then be used in rule `Filter` expressions to make decisions based on these tags.

## 4. How to Define Rules (JSON Configuration)

The engine's behavior is primarily controlled by a JSON configuration file. This file defines categories, actions, and the rules themselves.

**Root Structure (`config.Config`):**
```json
{
  "categories": [],
  "actions": [],
  "rules": [],
  "global": []
}
```

**4.1. Categories (`config.Category`):**
Reusable, named filter conditions.
```json
{
  "name": "VIP_Clients",
  "filter": "tags.has(order, \"VIP\") && order.cost > 10000"
}
```
*   `name`: Unique name for the category.
*   `filter`: A Tengo expression that returns `true` if an order belongs to this category. `order` is the implicit variable representing the current order being processed. Available `order` fields in Tengo (from `structToTengo`):
    *   `order.id` (string)
    *   `order.shares` (float, order.OrderValue / order.OrderQty)
    *   `order.cost` (float, order.OrderValue)
    *   `order.amount_shares` (float, order.OrderQty)
    *   `order.tags` (array of strings)

**4.2. Actions (`config.Action`):**
Defines an action and its parameters.
```json
{
  "name": "fill_50_percent",
  "action": "applyFill,50"
},
{
  "name": "min_100_units",
  "action": "minFill,100"
}
```
*   `name`: Unique name for this action configuration.
*   `action`: An `Expr` string. The first part is the internal action key (e.g., `applyFill`, `minFill`, `minCashFill`, `proRata`), followed by comma-separated arguments that will be passed to the corresponding Go function in `pkg/exec/actions.go`.

**4.3. Rules (`config.Rule`):**
These are processed sequentially for each order before global rules.
```json
{
  "name": "VIP_Order_Special_Fill",
  "condition": {
    "category": "VIP_Clients" // References a defined category
  },
  "action": "fill_50_percent", // References a defined action
  "immediate": true,
  "complete": false
},
{
  "name": "Small_Order_Full_Fill",
  "condition": {
    "expression": "order.amount_shares < 100" // Direct Tengo expression
  },
  "action": "fill_100_percent", // Assumes an action "fill_100_percent" is defined
  "immediate": true,
  "complete": true
}
```
*   `name`: Unique name for the rule.
*   `condition` (`config.Condition`):
    *   `category`: Name of a predefined `Category`.
    *   `expression`: A direct Tengo expression string (use one or the other).
*   `action`: Name of a predefined `Action`.
*   `immediate`: Boolean, passed to `exec.execute()` in the generated Tengo script. Controls if the action is applied immediately or potentially deferred (though current actions seem immediate).
*   `complete`: Boolean, passed to `exec.execute()`. If true, and the action supports it, the order might be marked as fully allocated and potentially skipped by subsequent rules/actions.

**4.4. Global Rules (`config.OrderedGlobalRule`):**
Processed sequentially for each order *after* all standard `rules` and `exec.freezeAllocatedSoFar()`.
```json
{
  "name": "Final_Pro_Rata",
  "action": "pro_rata_remaining" // Assumes a pro-rata action is defined
}
```
*   `name`: Unique name for the global rule.
*   `action`: Name of a predefined `Action`. These actions also execute via `exec.execute()`, typically with `immediate: true` hardcoded in the Tengo template for global rules.

**Tengo Filter Expressions:**
*   Filters are Tengo scripts that should return a boolean value.
*   The `order` variable is pre-defined and contains the fields listed in section 4.1.
*   The `tags` module provides helpers like `tags.has(order, "TAG_NAME")`, `tags.has_any(order, ["TAG1", "TAG2"])` (assuming `has_any` or similar exists or could be added to `bindings.go`).
*   Standard Tengo syntax and stdlib can be used (see Tengo documentation).

## 5. Extensibility

The engine can be extended in several ways:

**5.1. Adding New Actions:**
1.  Define the new action logic as a Go function in `pkg/exec/actions.go` matching the `exec.Action` signature: `func(order *AllocatedOrder, allocatedBeforeEnd, allocatedSoFar, demandBeforeEnd uint64, complete bool) (uint64, error)`.
2.  Update the `parseAction` function in `pkg/exec/actions.go` to include a `case` for your new action's key (e.g., "myNewAction"). This case should parse arguments from `config.Action.Action` string and return your new Go function.
3.  Users can then reference this new action key and its arguments in the JSON configuration file under the `actions` array.

**5.2. Modifying Tengo Helper Modules:**
*   Functions available to Tengo scripts (like those in the `exec` and `tags` modules) are defined in Go, likely within `pkg/exec/bindings.go` (or a similar file that implements `getExecHelpers` and `getTagsHelpers`).
*   New helper functions can be added here and exposed to Tengo scripts.

**5.3. Changing Excel Input Format:**
*   Modify `pkg/xls/xls.go`, specifically `ParseAllocationSpreadsheet()` for order data or `Hydrate()` for tags data, to accommodate new column structures or data types.
*   Update `pkg/common/types.go` if `OrderRow` fields change.

## 6. Running the Engine

The engine is run as a command-line application from `cmd/rulesengine/main.go`.

**Inputs:**
*   **Environment Variables:**
    *   `ORDERS_PATH`: Absolute or relative path to the primary Excel file containing order data.
    *   `TAGS_PATH`: Absolute or relative path to the Excel file containing order tags (optional).
    *   `CONFIG_PATH`: Absolute or relative path to the JSON rule configuration file.

**Execution:**
```bash
# Example (paths are illustrative)
export ORDERS_PATH="/path/to/your/orders.xlsx"
export TAGS_PATH="/path/to/your/tags.xlsx"       # Optional
export CONFIG_PATH="/path/to/your/rules_config.json"

go run cmd/rulesengine/main.go
```

**Output:**
*   The primary output is a JSON representation of the `map[common.OrderID]*exec.AllocatedOrder` (the `OrdersState` from `TengoExecutorState`), printed to standard output via `spew.Dump()`.
*   Log messages are printed to standard error/output, including errors during parsing or execution.
*   A "Total running" sum is printed, its specific meaning (related to `float64(v.Allocation) / 0.0011`) would require further clarification from domain context. 