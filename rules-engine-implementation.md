# Rules Engine Implementation Plan

## Overview

This document outlines the implementation of a high-performance rules engine for financial product order allocation processing within a Next.js serverless environment. The system is designed to process large volumes of orders (1 million+ in under 300 seconds) by applying configurable business rules to determine allocation amounts for various financial instruments such as IPOs, bonds, and other securities.

## Core Requirements

1. **Performance:** Process 1 million orders within 300 seconds (~3,333 orders/second)
2. **Rule Flexibility:** Support a variety of allocation strategies including percentage-based fills, minimum allocations, and pro-rata distributions
3. **Stateless Operation:** Function within serverless constraints while maintaining consistency
4. **Database Integration:** Use Drizzle ORM with PostgreSQL for data persistence
5. **Auditability:** Log rule execution for compliance and debugging purposes

## System Architecture

```mermaid
graph TD
    subgraph User/System Interaction
        A[API Request to /api/rules/execute] --> B{Next.js Serverless Route};
    end

    subgraph Serverless Function - RulesEngineService
        B --> C[Auth & Validation];
        C --> D{RuleConfigLoader};
        D -- Rule Config JSON --> E{RuleParser};
        E -- Parsed Rules --> F[RuleExecutor];
        C --> G{OrderRepository};
        G -- Batched Orders --> F;
        F -- Updates to Orders --> G;
        G -- Batch DB Updates --> H[(PostgreSQL Database)];
        D -- Fetches Config --> H;
        C --> I[ExecutionLogger];
        I -- Logs --> H;
    end

    subgraph Database (PostgreSQL)
        H -- Stores --> OrdersTable[orders Table];
        H -- Stores --> RuleConfigsTable[rule_configurations Table];
        H -- Stores --> ExecutionLogsTable[rule_execution_logs Table];
    end

    F -- Execution Stats --> I;
```

## Database Schema

### Existing Schema: `orders` Table

We'll use the existing `orders` table with its current structure:

```typescript
export const orders = pgTable('orders', {
  id: uuid().primaryKey().defaultRandom(),
  offerId: uuid().notNull().references(() => offers.id, { onDelete: 'cascade' }),
  partnerId: uuid().references(() => organizations.id).notNull(),
  memberId: uuid().references(() => members.id),
  tranche: text().$type<Tranche>(),
  status: text().$type<OrderStatus>().notNull().default('draft'),
  quantity: decimal({ precision: 19, scale: 6 }).notNull(),
  limit: decimal({ precision: 19, scale: 6 }),
  priceType: text().$type<OrderPriceType>(),
  allocatedQuantity: decimal({ precision: 19, scale: 6 }),
  allocationPrice: decimal({ precision: 19, scale: 6 }),
  settlementAmount: decimal({ precision: 19, scale: 4 }),
  submittedAt: timestamp({ withTimezone: true }),
  cancelledAt: timestamp({ withTimezone: true }),
  allocatedAt: timestamp({ withTimezone: true }),
  settledAt: timestamp({ withTimezone: true }),
  attributes: jsonb(),
  createdAt: timestamp().defaultNow().notNull(),
  updatedAt: timestamp().defaultNow().notNull(),
});
```

The `attributes` JSONB column will be extended to store additional metadata needed for rule processing, such as:
- `tags`: String array for order categorization
- `intermediary`: Intermediary details
- `taxWrapper`: Boolean flag
- `commission`: Boolean flag
- Any other rule-specific attributes

### New Tables

#### `rule_configurations` Table

Stores versioned rule sets that can be applied to offers.

```typescript
export const ruleConfigurations = pgTable('rule_configurations', {
  id: uuid().primaryKey().defaultRandom(),
  offerId: uuid().references(() => offers.id, { onDelete: 'set null' }),
  name: text().notNull(),
  version: integer().notNull(),
  configJson: jsonb().notNull(),
  description: text(),
  isActive: boolean().default(true),
  createdAt: timestamp({ withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp({ withTimezone: true }).defaultNow().notNull()
});
```

#### `rule_execution_logs` Table

Logs execution runs for auditability.

```typescript
export const ruleExecutionLogs = pgTable('rule_execution_logs', {
  id: uuid().primaryKey().defaultRandom(),
  ruleConfigurationId: uuid().notNull().references(() => ruleConfigurations.id),
  offerId: uuid().references(() => offers.id),
  triggeredBy: text(),
  status: text().notNull(), // 'started', 'in_progress', 'completed_success', 'completed_error', 'timed_out'
  ordersIntendedCount: integer(),
  ordersProcessedCount: integer(),
  ordersAffectedCount: integer(),
  startedAt: timestamp({ withTimezone: true }).defaultNow().notNull(),
  endedAt: timestamp({ withTimezone: true }),
  logSummary: jsonb(),
  createdAt: timestamp({ withTimezone: true }).defaultNow().notNull()
});
```

## Rule Configuration Schema

The rule configuration will be stored as JSON with a structure similar to:

```json
{
  "categories": [
    {
      "name": "VIP_Clients",
      "filter": "order.attributes.tags && order.attributes.tags.includes('VIP')"
    }
  ],
  "actions": [
    {
      "name": "fill_75_percent",
      "action": "applyFill,75"
    },
    {
      "name": "min_100_units",
      "action": "minFill,100"
    },
    {
      "name": "pro_rata_remaining",
      "action": "proRata,true"
    }
  ],
  "rules": [
    {
      "name": "VIP_Client_Special_Fill",
      "condition": {
        "category": "VIP_Clients"
      },
      "action": "fill_75_percent",
      "immediate": true,
      "complete": false
    },
    {
      "name": "Small_Order_Full_Fill",
      "condition": {
        "expression": "order.quantity <= 100"
      },
      "action": "fill_100_percent",
      "immediate": true,
      "complete": true
    }
  ],
  "globalRules": [
    {
      "name": "Final_Pro_Rata_Distribution",
      "action": "pro_rata_remaining"
    }
  ]
}
```

## Core Components

### 1. Rule Engine Types

```typescript
// Key types for rule engine operation
export interface EnrichedOrder {
  // Base order fields from Drizzle query
  id: string;
  offerId: string;
  status: string;
  quantity: string | bigint;
  allocatedQuantity: string | bigint;
  attributes: OrderAttributes;
  // Rule-specific fields
  isProcessed?: boolean;
}

export interface OrderAttributes {
  tags?: string[];
  intermediary?: string;
  taxWrapper?: boolean;
  commission?: boolean;
  [key: string]: any;
}

export interface ExecutionState {
  ordersState: Map<string, EnrichedOrder>;
  allocatedSoFar: bigint;
  allocatedBeforeDeferredActions: bigint;
  demandBeforeEnd: bigint;
  availableForAllocation: bigint;
  offerDetails: {
    totalSize: bigint;
    pricePerUnit?: number;
  };
}
```

### 2. Rule Implementation

#### Actions

The following core actions will be implemented:

1. **Percentage Fill (`applyFill`):** 
   - Allocates a specific percentage of the requested quantity
   - Example: "Allocate 50% of requested shares to all orders"

2. **Minimum Unit Fill (`minFill`):**
   - Ensures orders receive at least a minimum number of units
   - Example: "Allocate minimum 100 units to each order"

3. **Minimum Cash Value Fill (`minCashFill`):**
   - Allocates based on minimum cash value
   - Example: "Ensure allocation value is at least $5,000"

4. **Pro-Rata Distribution (`proRata`):**
   - Distributes remaining units on a proportional basis across eligible orders
   - Example: "Distribute remaining 1,000,000 units pro-rata across all orders"

#### Rule Processing Flow

1. **Orders processed in batches** (optimally sized for performance)
2. **Two-phase rule application:**
   - First phase: Apply ordered rules to each order
   - Checkpoint: Freeze allocated amounts so far
   - Second phase: Apply global rules to each order
3. **Database updates in batches** after each processing batch

## Performance Optimization Strategy

### 1. Database Interaction

- **Batched Reads:** Fetch orders in configurable batches (10,000-50,000 rows)
- **Batched Updates:** Update orders in transaction batches
- **Indexing:** Ensure proper indexing on `offerId`, `status`, and other frequently queried columns
- **Connection Pooling:** Configure Drizzle for optimal connection management

### 2. Processing Optimization

- **Expression Evaluation:** Pre-compile JavaScript expressions for rule conditions
- **In-Memory Processing:** Process batches entirely in memory before database updates
- **Numeric Precision:** Use `bigint` for quantity calculations to prevent floating-point issues
- **Memory Management:** Monitor and optimize memory usage within serverless constraints

### 3. Parallel Processing (If Needed)

- If a single execution might exceed serverless function limits, implement a distributed approach:
  - Break large offers into sub-batches processed by multiple function invocations
  - Use a coordination mechanism (e.g., Redis, database flags) to track overall progress

## API Endpoint

The rules engine will be exposed through a Next.js API route:

```
POST /api/rules/execute
```

**Request Body:**
```json
{
  "offerId": "uuid-of-offer",
  "ruleConfigurationId": "uuid-of-rule-configuration"
}
```

**Response:**
```json
{
  "message": "Rules execution completed successfully.",
  "logId": "uuid-of-execution-log",
  "ordersProcessed": 1000000,
  "ordersAffected": 750000
}
```

## Execution Flow

1. **Initialization:**
   - Validate request parameters
   - Create execution log entry
   - Load rule configuration
   - Parse and compile rule conditions and actions

2. **Batch Processing:**
   - Mark orders as 'processing'
   - Fetch orders in batches
   - Apply rules according to configuration
   - Update database with allocations
   - Track progress in execution log

3. **Finalization:**
   - Mark successfully allocated orders as 'allocated'
   - Complete execution log
   - Return execution summary

## Error Handling

- **Validation Errors:** Return 400 status with specific error message
- **Rule Configuration Errors:** Log detailed issues, return 400/500 as appropriate
- **Processing Errors:** 
  - Log detailed error information
  - Roll back current batch if possible
  - Mark execution as failed
  - Return 500 with error details

## Testing Strategy

1. **Unit Tests:**
   - Expression evaluation
   - Action implementations
   - Rule parsing and validation

2. **Integration Tests:**
   - End-to-end flow with test database
   - Various rule configurations and order scenarios

3. **Performance Tests:**
   - Load testing with 1M+ orders
   - Resource usage monitoring
   - Batch size optimization

## Implementation Timeline

1. **Phase 1: Core Engine** (1-2 weeks)
   - Rule parser implementation
   - Action implementations
   - Expression evaluator
   - Basic execution flow

2. **Phase 2: Database Integration** (1-2 weeks)
   - OrderRepository implementation
   - Batch processing refinement
   - Error handling and logging

3. **Phase 3: API & Testing** (1-2 weeks)
   - Next.js API route implementation
   - Test suite development
   - Performance optimization

## Conclusion

This implementation plan provides a roadmap for building a high-performance rule engine for financial product allocation within Next.js serverless environment. By leveraging efficient data processing techniques, batched database operations, and optimized JavaScript execution, the system aims to meet the performance target of processing 1 million orders in under 300 seconds while maintaining the flexibility needed for complex financial allocation rules. 