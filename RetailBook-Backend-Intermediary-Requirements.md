# RetailBook Backend - Intermediary/Participant Requirements

## Table of Contents
1. [Introduction](#introduction)
2. [Participant Data Model](#participant-data-model)
3. [Core Backend Services](#core-backend-services)
   - [Participant Management](#participant-management)
   - [Offer Invitation System](#offer-invitation-system)
   - [Order Processing](#order-processing)
   - [Allocation Processing](#allocation-processing)
   - [Settlement Processing](#settlement-processing)
   - [Organization Management](#organization-management)
4. [API Endpoints](#api-endpoints)
5. [Permissions and Security](#permissions-and-security)
6. [Integration Points](#integration-points)
7. [System Flows](#system-flows)
8. [Technical Implementation](#technical-implementation)
9. [Appendix](#appendix)

## Introduction

This document outlines the backend requirements for handling intermediaries (also referred to as participants) in the RetailBook system. Intermediaries are financial institutions or brokers that act as middlemen between issuers (banks) and retail investors. The backend system must provide robust services to manage participants, their interactions with offers, and the processing of orders, allocations, and settlements.

## Participant Data Model

### Core Participant Entity

The central data service maintains participant information in a YAML configuration file. The core participant data model includes:

```mermaid
classDiagram
    class Participant {
        +string DisplayName
        +string SystemId
        +string PrincipalId
        +boolean MasterAgreementSigned
        +string[] Roles
    }
    
    class Organisation {
        +string system_id
        +string system_role
        +string name
        +string crest_account_name
        +string crest_participant_id
        +string account_type
        +string account_name
        +string account_bank_name
        +string account_swift_bic
        +string account_number
        +string account_sort_code
        +Address address
        +string settlement_reference
    }
    
    class Address {
        +string address1
        +string address2
        +string address3
        +string address4
        +string address_city
        +string address_postal_code
        +string address_country
    }
    
    Participant -- Organisation : has
    Organisation -- Address : has
```

### Participant Roles

Participants can have different roles in the system:
- `intermediary`: Retail brokers who place orders on offers
- `bank`: Issuers who create and manage offers

### Participant Relationships

```mermaid
flowchart TD
    A[Participant] -->|invited to| B[Offer]
    A -->|places| C[Order]
    A -->|receives| D[Allocation]
    A -->|completes| E[Settlement]
    A -->|has| F[Organisation Details]
    A -->|has| G[Users]
```

## Core Backend Services

### Participant Management

The system must provide services to manage participants in the RetailBook platform.

#### Requirements:

1. **Participant Registration**
   - Register new participants in the system
   - Assign system IDs and principal IDs
   - Define participant roles (intermediary, bank)
   - Set master agreement status

2. **Participant Retrieval**
   - Retrieve participant information by system ID
   - List all participants in the system
   - Filter participants by role

3. **Participant Configuration**
   - Store participant configuration in YAML files
   - Load participant data at system startup
   - Provide API access to participant data

```mermaid
flowchart TD
    A[Admin] -->|Creates| B[Participant]
    B -->|Stored in| C[YAML Configuration]
    C -->|Loaded by| D[CentralData Service]
    D -->|Provides| E[Participant API]
    E -->|Used by| F[Other Services]
```

### Offer Invitation System

The system must manage the invitation of intermediaries to participate in offers.

#### Requirements:

1. **Invite Creation**
   - Allow banks to invite intermediaries to offers
   - Store invitation details including timestamps
   - Track invitation status

2. **Invite Management**
   - Update invitation status
   - Resend invitations
   - Cancel invitations

3. **Terms Acceptance**
   - Track terms acceptance by intermediaries
   - Record acceptance timestamps and user information
   - Handle commission preferences

```mermaid
flowchart TD
    A[Bank] -->|Creates| B[Offer]
    B -->|Invites| C[Intermediaries]
    C -->|Accept Terms| D[Terms Acceptance]
    D -->|Recorded in| E[Database]
    C -->|Can now place| F[Orders]
```

### Order Processing

The system must process orders placed by intermediaries on offers.

#### Requirements:

1. **Order Submission**
   - Accept order submissions from intermediaries
   - Validate orders against offer criteria
   - Store order details in the database

2. **Order Management**
   - Update order status (pending, accepted, rejected)
   - Track order history
   - Calculate order metrics

3. **Order Book Management**
   - Maintain order books for each offer
   - Calculate order totals and breakdowns
   - Provide order book views for banks and intermediaries

```mermaid
flowchart TD
    A[Intermediary] -->|Submits| B[Order]
    B -->|Validated by| C[Order Service]
    C -->|Stored in| D[Order Database]
    D -->|Added to| E[Order Book]
    E -->|Viewed by| F[Bank]
    F -->|Accepts/Rejects| B
```

### Allocation Processing

The system must handle the allocation of offers to intermediaries based on their orders.

#### Requirements:

1. **Allocation Creation**
   - Allow banks to create allocations
   - Calculate allocation amounts based on orders
   - Store allocation details

2. **Allocation Management**
   - Update allocation status
   - Notify intermediaries of allocations
   - Track allocation history

3. **Allocation Book Management**
   - Maintain allocation books for each offer
   - Calculate allocation totals and breakdowns
   - Provide allocation book views for banks and intermediaries

```mermaid
flowchart TD
    A[Bank] -->|Creates| B[Allocation]
    B -->|Based on| C[Order Book]
    B -->|Stored in| D[Allocation Database]
    D -->|Added to| E[Allocation Book]
    E -->|Notified to| F[Intermediary]
```

### Settlement Processing

The system must facilitate the settlement process between banks and intermediaries.

#### Requirements:

1. **Settlement Instruction Creation**
   - Generate settlement instructions based on allocations
   - Include payment and delivery details
   - Store settlement instructions

2. **Settlement Management**
   - Track settlement status
   - Record settlement completion
   - Handle settlement failures

3. **Settlement Reporting**
   - Generate settlement reports
   - Provide settlement history
   - Calculate settlement metrics

```mermaid
flowchart TD
    A[Allocation] -->|Generates| B[Settlement Instructions]
    B -->|Sent to| C[Intermediary]
    C -->|Completes| D[Settlement]
    D -->|Confirmed by| E[Bank]
    D -->|Recorded in| F[Settlement Database]
```

### Organization Management

The system must manage organization details for participants.

#### Requirements:

1. **Organization Creation**
   - Create organization records for participants
   - Store organization details including addresses and accounts
   - Link organizations to participants

2. **Organization Management**
   - Update organization details
   - Manage settlement references
   - Handle CREST account information

3. **Organization Retrieval**
   - Retrieve organization details by system ID
   - Provide organization information for settlement

```mermaid
flowchart TD
    A[Participant] -->|Has| B[Organisation]
    B -->|Includes| C[Address]
    B -->|Includes| D[Account Details]
    B -->|Used for| E[Settlement]
```

## API Endpoints

The backend must provide API endpoints for intermediary-related operations.

### Participant Endpoints

1. **GET /static/intermediary**
   - Retrieve a list of all intermediaries
   - No special permissions required beyond authentication

2. **GET /organisation**
   - Retrieve organization details for the authenticated user
   - Requires `organisation:read` or `organisation:write` permission

3. **PATCH /organisation**
   - Update organization details
   - Requires `organisation:write` permission

### Offer Invitation Endpoints

1. **POST /offer/{offer_id}/invite**
   - Invite intermediaries to an offer
   - Requires `invite:create` permission

2. **GET /offer/{offer_id}/invite**
   - Retrieve invitations for an offer
   - Requires `invite:read` permission

3. **PATCH /offer/{offer_id}/invite/{invite_id}**
   - Update invitation status
   - Requires `invite:write` permission

### Order Endpoints

1. **POST /offer/{offer_id}/order**
   - Submit an order for an offer
   - Requires `order:create` permission

2. **GET /offer/{offer_id}/order**
   - Retrieve orders for an offer
   - Requires `order:read` permission

3. **GET /offer/{offer_id}/order/metrics**
   - Get order metrics for an offer
   - Requires `metrics:read` permission

### Allocation Endpoints

1. **GET /offer/{offer_id}/allocation**
   - Retrieve allocations for an offer
   - Requires `offer_allocation:read` permission

2. **GET /offer/{offer_id}/allocation_book**
   - Retrieve the allocation book for an offer
   - Requires `allocation_book:read` permission

## Permissions and Security

The backend implements a comprehensive permission system to control access to intermediary-related functionality.

### Permission Resources

Resources related to intermediaries include:
- `offer`
- `order`
- `invite`
- `order_summary`
- `allocation_summary`
- `offer_allocation`
- `allocation_book`
- `organisation`

### Permission Actions

Actions that can be performed on these resources:
- `create`
- `read`
- `write`
- `delete`
- `accept`
- `reject`

### Permission Rules

1. **Intermediary Access to Offers**
   - Intermediaries can only access offers they've been invited to
   - Intermediaries can only view their own orders and allocations

2. **Bank Access to Intermediary Data**
   - Banks can view all intermediaries
   - Banks can view orders and allocations from all intermediaries for their offers

3. **Organization Data Access**
   - Participants can only view and edit their own organization data

```mermaid
flowchart TD
    A[User] -->|Has| B[Role]
    B -->|Has| C[Permissions]
    C -->|Controls access to| D[Resources]
    D -->|Include| E[Offers, Orders, Allocations, etc.]
```

## Integration Points

The backend must integrate with various systems and services.

### Internal Integration

1. **Central Data Service**
   - Provides participant information to other services
   - Maintains participant configuration

2. **Command Handler**
   - Processes commands related to intermediaries
   - Enforces permissions and business rules

3. **View Server**
   - Provides views of intermediary-related data
   - Filters data based on permissions

### External Integration

1. **Azure B2C**
   - Authenticates users from intermediary organizations
   - Manages user identities

2. **CREST**
   - Handles settlement instructions
   - Processes delivery of securities

## System Flows

### Participant Registration Flow

```mermaid
sequenceDiagram
    Admin->>+CentralData: Create Participant
    CentralData->>+YAML Config: Update participants.yaml
    YAML Config-->>-CentralData: Configuration Updated
    CentralData->>+Azure B2C: Create User Identities
    Azure B2C-->>-CentralData: Identities Created
    CentralData-->>-Admin: Participant Created
```

### Offer Invitation Flow

```mermaid
sequenceDiagram
    Bank->>+API Gateway: Invite Intermediary to Offer
    API Gateway->>+Command Handler: Create Offer Invite
    Command Handler->>+Central Data: Get Participant Info
    Central Data-->>-Command Handler: Participant Details
    Command Handler->>+Database: Store Invitation
    Database-->>-Command Handler: Invitation Stored
    Command Handler->>+Event Bus: Publish Invitation Event
    Event Bus-->>-Command Handler: Event Published
    Command Handler-->>-API Gateway: Invitation Created
    API Gateway-->>-Bank: Success Response
    Event Bus->>+Notification Service: Send Notification
    Notification Service->>+Intermediary: Email Notification
    Intermediary-->>-Notification Service: Notification Received
```

### Order Submission Flow

```mermaid
sequenceDiagram
    Intermediary->>+API Gateway: Submit Order
    API Gateway->>+Command Handler: Create Order
    Command Handler->>+Database: Check Invitation
    Database-->>-Command Handler: Invitation Exists
    Command Handler->>+Database: Store Order
    Database-->>-Command Handler: Order Stored
    Command Handler->>+Order Book: Update Order Book
    Order Book-->>-Command Handler: Order Book Updated
    Command Handler->>+Event Bus: Publish Order Event
    Event Bus-->>-Command Handler: Event Published
    Command Handler-->>-API Gateway: Order Created
    API Gateway-->>-Intermediary: Success Response
    Event Bus->>+Notification Service: Send Notification
    Notification Service->>+Bank: Order Notification
    Bank-->>-Notification Service: Notification Received
```

## Technical Implementation

### Participant Management Implementation

The participant management system is implemented in the `centraldata` service:

1. **Participant List**
   - Loads participants from YAML configuration
   - Provides in-memory access to participant data
   - Exposes API for participant queries

2. **System to Participant Mapping**
   - Maps system IDs to participant objects
   - Handles lookups for multiple system IDs
   - Returns participant details for API use

3. **Get Intermediaries View**
   - Filters participants with the "intermediary" role
   - Returns intermediary information for UI display
   - Enforces permission checks

### Organization Management Implementation

The organization management system is implemented in the database and API layers:

1. **Organization Database Schema**
   - Stores organization details
   - Links to address and account information
   - Tracks settlement references

2. **Organization API**
   - Provides CRUD operations for organization data
   - Enforces permission checks
   - Validates organization updates

## Appendix

### Data Models

#### Participant Data Model
```yaml
- displayName: "Test Retail Intermediary"
  systemId: "retail"
  principalId: "principal_id_here"
  roles: ["intermediary"]
  masterAgreementSigned: true
```

#### Organization Data Model
```go
type Organisation struct {
    SystemId            string
    SystemRole          string
    Name                string
    CrestAccountName    string
    CrestParticipantId  string
    AccountType         string
    AccountName         string
    AccountBankName     string
    AccountSwiftBic     string
    AccountNumber       string
    AccountSortCode     string
    Address1            string
    Address2            string
    Address3            string
    Address4            string
    AddressCity         string
    AddressPostalCode   string
    AddressCountry      string
    SettlementReference string
}
```

#### Invite Data Model
```go
type Invite struct {
    OfferId                string
    Status                 InviteStatus
    IntermediarySystemId   string
    IntermediaryDisplayName string
    MasterAgreementSigned  bool
    TimeOfInvite           time.Time
    TimeOfSending          time.Time
    Terms                  Terms
}

type Terms struct {
    Accepted            bool
    AcceptedTime        time.Time
    AcceptedBy          string
    AcceptedByEmail     string
    ReceiveCommission   bool
    RetailOfferNoticeId string
}
```
