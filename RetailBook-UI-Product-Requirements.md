# RetailBook UI Product Requirements Document

## Table of Contents
1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [User Types and Roles](#user-types-and-roles)
4. [Authentication and Authorization](#authentication-and-authorization)
5. [Core Features](#core-features)
   - [Offers Management](#offers-management)
   - [Orders Management](#orders-management)
   - [Allocations Management](#allocations-management)
   - [Documents Management](#documents-management)
   - [User Management](#user-management)
   - [Notifications](#notifications)
   - [Metrics and Reporting](#metrics-and-reporting)
6. [User Flows](#user-flows)
7. [Technical Architecture](#technical-architecture)
8. [Appendix](#appendix)

## Introduction

RetailBook is a B2B application designed to facilitate the management of retail offers, orders, and allocations in the financial sector. The platform connects issuers (banks) with intermediaries (retail brokers) to streamline the process of creating, managing, and fulfilling financial offers.

### Purpose

The RetailBook UI provides a comprehensive interface for:
- Banks to create and manage offers
- Intermediaries to review offers and place orders
- Both parties to track the lifecycle of offers from creation to settlement

## System Overview

RetailBook UI is built as a Next.js application with a component-driven architecture. The application uses Storybook for component development and documentation. The system integrates with a backend API to manage data and business logic.

```mermaid
flowchart TD
    A[RetailBook UI] --> B[Authentication]
    A --> C[Offers Management]
    A --> D[Orders Management]
    A --> E[Allocations Management]
    A --> F[User Management]
    A --> G[Metrics & Reporting]
    
    B --> B1[Azure B2C]
    
    C --> C1[Create Offer]
    C --> C2[Manage Offer]
    C --> C3[View Offers]
    
    D --> D1[Place Order]
    D --> D2[Manage Orders]
    D --> D3[View Orders]
    
    E --> E1[Create Allocation]
    E --> E2[Manage Allocations]
    E --> E3[View Allocations]
    
    F --> F1[User Profiles]
    F --> F2[Team Management]
    
    G --> G1[Order Metrics]
    G --> G2[Allocation Metrics]
```

## User Types and Roles

### Organizational Types

1. **Bank (Issuer)**
   - Creates and manages offers
   - Reviews and processes orders
   - Creates allocations
   - Manages the offer lifecycle

2. **Retail (Intermediary)**
   - Reviews offers
   - Places orders on offers
   - Views allocations

### User Roles

For each organizational type, there are two roles:

1. **Editor**
   - Basic access to create, view, and edit content relevant to their organization type

2. **Manager**
   - All editor permissions
   - Additional ability to manage team members

## Authentication and Authorization

RetailBook uses Azure B2C for authentication. The authentication flow is configured in `pages/api/auth/[...nextauth].ts`.

```mermaid
flowchart LR
    A[User] --> B[Login Page]
    B --> C[Azure B2C]
    C --> D[Authentication]
    D --> E{Valid?}
    E -->|Yes| F[RetailBook Dashboard]
    E -->|No| G[Error Message]
```

### User Creation Process

1. Admin creates a new user in the system
2. User receives an email with verification link
3. User sets up their password
4. User can now log in to the system

## Core Features

### Offers Management

Offers are the central entity in RetailBook. They represent financial products that banks make available to intermediaries.

#### Offer Types
- Follow-on
- IPO (Initial Public Offering)
- Retail Bond

#### Offer Statuses
- Building
- Pre-Launch
- Applications Open
- Applications Closed
- Allocating
- Allocated
- Instructions Sent
- Settled
- Abandoned
- Building On Hold
- Pre-Launch On Hold
- Applications Open On Hold

#### Offer Workflow

```mermaid
flowchart TD
    A[Create Offer] --> B[Building]
    B --> C[Pre-Launch]
    C --> D[Applications Open]
    D --> E[Applications Closed]
    E --> F[Allocating]
    F --> G[Allocated]
    G --> H[Instructions Sent]
    H --> I[Settled]
    
    B -.-> J[Building On Hold]
    J -.-> B
    
    C -.-> K[Pre-Launch On Hold]
    K -.-> C
    
    D -.-> L[Applications Open On Hold]
    L -.-> D
    
    B -.-> M[Abandoned]
    C -.-> M
    D -.-> M
    E -.-> M
    F -.-> M
    G -.-> M
```

#### Offer Creation
1. Bank user creates a new offer with basic details
2. Bank user adds offer terms, documents, and settlement details
3. Bank user can preview the offer before publishing
4. Bank user can invite intermediaries to the offer

#### Offer Management
1. Bank user can edit offer details while in Building status
2. Bank user can change offer status according to the workflow
3. Bank user can view orders placed on the offer
4. Bank user can create allocations for orders

### Orders Management

Orders represent requests from intermediaries to participate in offers.

#### Order Types
- Aggregate: Summary of multiple orders
- Detailed: Individual line items for each client

#### Order Statuses
- Unconfirmed
- Pending
- Accepted
- Rejected
- Superseded
- Deleted
- Replace Pending
- Replaced

#### Order Workflow

```mermaid
flowchart TD
    A[Create Order] --> B[Pending]
    B --> C{Bank Review}
    C -->|Accept| D[Accepted]
    C -->|Reject| E[Rejected]
    B --> F[Replace Pending]
    F --> G[Replaced]
    B --> H[Deleted]
```

#### Order Creation
1. Intermediary selects an offer
2. Intermediary chooses order type (aggregate or detailed)
3. For aggregate orders, intermediary enters summary information
4. For detailed orders, intermediary uploads a spreadsheet or enters line items
5. Intermediary submits the order

#### Order Management
1. Bank user reviews pending orders
2. Bank user can accept or reject orders
3. Bank user can download order details
4. Intermediary can view their order history

### Allocations Management

Allocations represent the distribution of an offer to intermediaries based on their orders.

#### Allocation Statuses
- Unnotified
- Notified
- Invalid

#### Allocation Workflow

```mermaid
flowchart TD
    A[Create Allocation] --> B[Unnotified]
    B --> C[Notified]
    B -.-> D[Invalid]
```

#### Allocation Creation
1. Bank user selects an offer with closed applications
2. Bank user creates an allocation based on orders
3. Bank user can review and adjust allocations
4. Bank user finalizes and notifies intermediaries

### Documents Management

Documents are attached to offers to provide additional information to intermediaries.

#### Document Types
- Prospectus
- Terms and Conditions
- Marketing Materials
- Other supporting documents

#### Document Management
1. Bank user uploads documents to an offer
2. Bank user can categorize documents by type
3. Intermediaries can view and download documents

### User Management

User management allows organizations to control access to the system.

#### User Management Features
1. View team members
2. Add new team members (Manager role only)
3. Edit team member details (Manager role only)
4. Remove team members (Manager role only)

### Notifications

Notifications keep users informed about important events in the system.

#### Notification Types
- Info
- Success
- Failure
- Warning

#### Notification Statuses
- Read
- Unread

#### Notification Features
1. View notifications
2. Mark notifications as read
3. Filter notifications by type

### Metrics and Reporting

Metrics and reporting provide insights into system activity.

#### Metrics Features
1. View offer metrics
2. View order metrics
3. View allocation metrics
4. Filter metrics by date range
5. Export metrics data

## User Flows

### Bank User Flow

```mermaid
flowchart TD
    A[Login] --> B[Dashboard]
    B --> C[Create Offer]
    C --> D[Add Offer Details]
    D --> E[Upload Documents]
    E --> F[Set Settlement Details]
    F --> G[Invite Intermediaries]
    G --> H[Publish Offer]
    H --> I[Monitor Orders]
    I --> J[Accept/Reject Orders]
    J --> K[Create Allocations]
    K --> L[Notify Intermediaries]
    L --> M[Complete Settlement]
```

### Intermediary User Flow

```mermaid
flowchart TD
    A[Login] --> B[Dashboard]
    B --> C[View Available Offers]
    C --> D[Review Offer Details]
    D --> E[Review Documents]
    E --> F[Place Order]
    F --> G[Monitor Order Status]
    G --> H[View Allocations]
    H --> I[Complete Settlement]
```

## Technical Architecture

RetailBook UI is built with the following technologies:

### Frontend
- Next.js (React framework)
- TypeScript
- Tailwind CSS for styling
- Headless UI for accessible components
- React Query for state management
- React Hook Form for form handling

### Authentication
- NextAuth.js
- Azure B2C

### API Integration
- Axios for API requests
- React Query for data fetching and caching

### Testing
- Playwright for end-to-end testing

### Development Tools
- Storybook for component development
- ESLint and Prettier for code quality

## Appendix

### Data Models

#### Offer
```typescript
type TOffer = {
  name: string;
  type: EOfferType;
  status: EOfferStatus;
  close_date: string;
  min_order_amount?: Decimal;
  shareholders_only: boolean;
  inside_information: boolean;
  currency: string;
  issued_by: string;
  raise_amount?: Decimal;
  id: string;
  issuer_logo?: string;
  dateCreated: string;
  dateUpdated: string;
  registration_date: string;
  open_date: string;
  description: string;
  details: string;
  allocation_principles: string;
  settlement_details: string;
  offer_price?: Decimal;
  slug: string;
  isin?: string;
  sedol?: string;
  crest_id?: string;
  ticker?: string;
  security_name?: string;
  documents: Resource[];
  contracts?: Contract[];
  wall_cross_info_id?: string;
  is_launched: boolean;
  price_range_low?: Decimal;
  price_range_high?: Decimal;
  settlement_date: string;
};
```

#### Order
```typescript
type TOrder = {
  id: string;
  order_type: EOrderType;
  order_date: string;
  status: EOrderStatus;
  offer_id: string;
  offer_name: string;
  order_book_id?: string;
  update_date: string;
  non_shareholding?: IOrderBase;
  shareholding?: IOrderBase;
  totals: IOrderBase;
  lineItems?: TOrderLineItem[];
  intermediary?: string;
  intermediary_system_id?:string;
  entered_by?: string;
};
```

#### User
```typescript
type TUser = {
  role_timestamps: string;
  id: string;
  ext_id: string;
  name: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  organisational_role: EUserRole;
  gatekeeper: boolean;
  status: string;
  last_error: string;
};
```

#### Allocation
```typescript
type TAllocation = {
  id: string;
  entered_by?: string;
  date_created: string;
  totals: IAllocationBase;
  shareholdings?: IAllocationBase;
  non_shareholdings?: IAllocationBase;
  allocation_book_id?: string;
  status: EAllocationStatus;
};
```
